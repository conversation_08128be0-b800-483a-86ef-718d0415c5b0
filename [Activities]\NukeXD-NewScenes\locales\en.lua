local Translations = {
    notify = {
        ["laser_error"] = "<PERSON><PERSON> did not hit anything.",
        ["scene_delete"] = "Scene deleted!",
        ["scene_error"] = "No scene was close enough.",
        ["title_error"] = "Error",
        ["title_success"] = "Success",
    }
}

Lang = {
    t = function(key)
        local category, subkey = key:match("([^%.]+)%.(.+)")
        if category and subkey and Translations[category] and Translations[category][subkey] then
            return Translations[category][subkey]
        end
        return key
    end
}