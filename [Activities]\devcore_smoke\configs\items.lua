
---- ROLLING PAPERS ITEMS ----
Config.RollingPapers = { -- Any amount of rolling papers can be added
    --
    ['ocb_paper'] = { -- ITEM NAME
        Quantity = 30, -- Number of pieces of paper in the package 
        Prop = {Paper = 'p_cs_papers_02', Weed = 'bkr_prop_weed_bud_pruned_01a'},
        Positions = { -- Post
                ['PAPER'] = {
                    BoneID = 18905,
                    Offset = vector3(0.12, 0.0, 0.02),
                    Rot = vector3(180.0, -145.0, 120.0),},
                ['WEED'] = {
                    BoneID = 57005,
                    Offset = vector3(0.13, 0.07, -0.03),
                    Rot = vector3(-89.0, 192.0, -6.0),},
                ['ROLL_ITEM'] = { -- Rolling item prop positions
                    BoneID = 64097,
                    Offset = vector3(0.020, 0.0, 0.04),
                    Rot = vector3(90.0, 0.0, -198.0),},
        },
        Anims = {
            ['ROLL'] = { -- Animation rolling joints
                [1] = {anim = 'clip_rolling', dict = 'foxyando@rolling', params = {1.0, -1.0, 14000, 49, 1}},
            },
        },
        RollingItems = {  -- Here you set the names of the items from which it will be possible to rolling the joint and also specify the names of the items for the joints
            ['og_kush_weed'] = { joint = 'og_kush_joint'}, 
            ['banana_kush_weed'] = { joint = 'banana_kush_joint'},
            ['purple_haze_weed'] = { joint = 'purple_haze_joint'},
            ['blue_dream_weed'] = { joint = 'blue_dream_joint'},
           -- ['example_weed'] = { joint = 'example_joint'},
        }
    },

    --
    
    ['raw_paper'] = { -- ITEM NAME
        Quantity = 35, -- Number of pieces of paper in the package 
        Prop = {Paper = 'p_cs_papers_02', Weed = 'bkr_prop_weed_bud_pruned_01a'},
        Positions = {
                ['PAPER'] = { 
                    BoneID = 18905,
                    Offset = vector3(0.12, 0.0, 0.02),
                    Rot = vector3(180.0, -145.0, 120.0),},
                ['WEED'] = {
                    BoneID = 57005,
                    Offset = vector3(0.13, 0.07, -0.03),
                    Rot = vector3(-89.0, 192.0, -6.0),},
                ['ROLL_ITEM'] = {
                    BoneID = 64097,
                    Offset = vector3(0.020, 0.0, 0.04),
                    Rot = vector3(90.0, 0.0, -198.0),},
        },
        Anims = {
            ['ROLL'] = { -- Animation rolling joints
                [1] = {anim = 'clip_rolling', dict = 'foxyando@rolling', params = {1.0, -1.0, 14000, 49, 1}},
            },
        },
        RollingItems = { -- Here you set the names of the items from which it will be possible to rolling the joint and also specify the names of the items for the joints
            ['og_kush_weed'] = { joint = 'og_kush_joint'},
            ['banana_kush_weed'] = { joint = 'banana_kush_joint'},
            ['purple_haze_weed'] = { joint = 'purple_haze_joint'},
            ['blue_dream_weed'] = { joint = 'blue_dream_joint'},
             -- ['example_weed'] = { joint = 'example_joint'},
        }
    },
    --

}

---- LIGHTERS ITEMS ----

Config.Lighters = {-- Any amount of Lighters can be added
    ['classic_lighter'] = { -- ITEM NAME
        Prop = {Default = 'ex_prop_exec_lighter_01'},
        Remove_Durability = {min = 1, max = 5}, -- Only for ox_inventory
        Positions = { -- 
            ['DEFAULT'] = { -- Lighter position if you are lighting an item with Type NEED_LIGHTER 
                BoneID = 57005,
                Offset = vector3(0.14, 0.01, 0.0),
                Rot = vector3(-89.0, 192.0, -6.0),},
            ['BONG'] = { --Lighter position if you are lighting an item with Type BONG 
                BoneID = 18905,
                Offset = vector3(0.12, 0.01, 0.02),
                Rot = vector3(-119.0, 36.0, -32.0),},
        },
        Particles = {
            ['Light'] = {-- light effect 
                particleName = 'ent_amb_torch_fire', -- ent_amb_fire_ring
                particleDict = 'core',
                Scale = 0.03,
                Offset = vector3(0.0, 0.0, 0.050),
                Rot = vector3(0.0, 0.0, 0.0),},
        },
        Anims = {
            ['LIGHT'] = { -- Animation Lighting a Cigarette
                [1] = {anim = 'devcore_light_c_1', dict = 'lightcig_1@devcore', params = {1.0, -1.0, 3000, 49, 1}, sound = {url = './sounds/devcore_light_1.mp3', volume = 0.3, distance = 5}},
                [2] = {anim = 'devcore_light_c_2', dict = 'lightcig_2@devcore', params = {1.0, -1.0, 3000, 49, 1}, sound = {url = './sounds/devcore_light_1.mp3', volume = 0.3, distance = 5}},
                [3] = {anim = 'devcore_light_c', dict = 'lightcig@devcore', params = {1.0, -1.0, 3000, 49, 1}, sound = {url = './sounds/devcore_light_1.mp3', volume = 0.3, distance = 5}},},
            ['TAKEOUT'] = { -- Animation of putting the cigarette in the pocket and taking it out of the pocket
                [1] = {anim = 'enter', dict = 'amb@world_human_smoking@male@male_a@enter', params = {1.0, -1.0, 1300, 49, 1}},},
        },
    }, 

    ['gold_lighter'] = { -- ITEM NAME
        Prop = {Default = 'lux_prop_lighter_luxe'},
        Remove_Durability = {min = 1, max = 5}, -- Only for ox_inventory
        Positions = {
            ['DEFAULT'] = { -- Lighter position if you are lighting an item with Type NEED_LIGHTER 
                BoneID = 57005,
                Offset = vector3(0.14, 0.02, 0.0),
                Rot = vector3(-89.0, 192.0, -6.0),},
            ['BONG'] = { -- Lighter position if you are lighting an item with Type BONG 
                BoneID = 18905,
                Offset = vector3(0.12, 0.01, 0.02),
                Rot = vector3(-119.0, 36.0, -32.0),},
        },
        Particles = {
            ['Light'] = {-- light effect 
                particleName = 'ent_amb_torch_fire',
                particleDict = 'core',
                Scale = 0.03,
                Offset = vector3(0.0, 0.0, 0.050),
                Rot = vector3(0.0, 0.0, 0.0),},
        },
        Anims = {
            ['LIGHT'] = { -- Animation Lighting a Cigarette
                [1] = {anim = 'devcore_light_c', dict = 'lightcig@devcore', params = {1.0, -1.0, 3000, 49, 1}, sound = {url = './sounds/devcore_light_1.mp3', volume = 0.5, distance = 10}},
                [2] = {anim = 'devcore_light_c_2', dict = 'lightcig_2@devcore', params = {1.0, -1.0, 3000, 49, 1}, sound = {url = './sounds/devcore_light_1.mp3', volume = 0.5, distance = 10}},},
            ['TAKEOUT'] = {-- Animation of putting the cigarette in the pocket and taking it out of the pocket
                [1] = {anim = 'enter', dict = 'amb@world_human_smoking@male@male_a@enter', params = {1.0, -1.0, 1300, 49, 1}},},
        },
    }, 
    
}


---- CIGARETTE PACKS ITEMS ----
Config.CigarettePack = {-- Any amount of Cigarette Packages can be added
--
    ['redwood_pack'] = { -- Item name 
        Prop = {Default = 'ng_proc_cigpak01a'}, -- Prop model name 
        Quantity = 20, -- Number of items in a packet of cigarettes
        ItemInPack = 'cig_redwood', -- Name of the item that will be in the cigarette pack
        Positions = {  -- Prop position in hand
                BoneID = 57005,
                Offset = vector3(0.14, 0.02, 0.0),
                Rot = vector3(-89.0, 192.0, -6.0),
        },
        Anims = {
            ['OPEN'] = { -- Animation when taking items out of a pack of cigarettes
                [1] = {anim = 'a_uncuff', dict = 'mp_arresting', params = {1.0, -1.0, 2000, 49, 1}},},
            ['TAKEOUT'] = { -- Animation of putting the cigarette in the pocket and taking it out of the pocket
                [1] = {anim = 'enter', dict = 'amb@world_human_smoking@male@male_a@enter', params = {1.0, -1.0, 1300, 49, 1}},},
         }
    },
    -- 
    ['marlboro_pack'] = { -- Item name
        Prop = {Default = 'ng_proc_cigpak01a'}, -- Prop model name 
        Quantity = 20, -- Number of items in a packet of cigarettes
        ItemInPack = 'cig_marlboro', -- Name of the item that will be in the cigarette pack
        Positions = {  -- Prop position in hand
                BoneID = 57005,
                Offset = vector3(0.14, 0.02, 0.0),
                Rot = vector3(-89.0, 192.0, -6.0),
        },
        Anims = {
            ['OPEN'] = { -- Animation when taking items out of a pack of cigarettes
                [1] = {anim = 'a_uncuff', dict = 'mp_arresting', params = {1.0, -1.0, 2000, 49, 1}},},
            ['TAKEOUT'] = { -- Animation of putting the cigarette in the pocket and taking it out of the pocket
                [1] = {anim = 'enter', dict = 'amb@world_human_smoking@male@male_a@enter', params = {1.0, -1.0, 1300, 49, 1}},},
         }
    },
    -- 

}


---- SMOKING ITEMS ----

Config.SmokingItems = { -- Any amount of Smoking Items can be added

    -- CIGARETTE -- TYPE : NEED_LIGHTER
    ['cig_redwood'] = { -- ITEM NAME
        Type = 'NEED_LIGHTER', -- NEED_LIGHTER (joints, cigarette, cigars), BONG(bongs), VAPE(Must be topped up with liquid), VAPE-PUFF(It has an adjustable maximum amount of puffs)
        Prop = {Default = 'prop_cs_ciggy_01', Light = 'prop_cs_ciggy_01b'}, -- Default is prop before lighting and Light is prop after lighting the cigarette (if you want to use only one prop, remove the Light prop and leave only the Default prop)
        Setting = {
            Size = 10, -- The size for the NEED_LIGHTER type sets a value that decreases gradually after the item is lit, the larger the number you set, the longer the item can be smoked.
            Remove = 0.1, Time = 1}, -- The size is subtracted by 0.3 every 3 seconds during smoking, depending on the setting (ONLY FOR: NEED_LIGHTER).
        AfterExhale = {
            ExhaleTime = {min = 1, max = 1}, -- Smoke exhalation time
            RemoveAfterExhale = 5, -- The set value is taken after each exhalation of smoke from the size 
            ExhaleEffect = {enable = true, intensity = 60, effect = false}, -- screen blackout effect after smoke exhalation and Animpostfx effect-- https://forge.plebmasters.de/animpostfx
            ShakeCam = {effect = 'VIBRATE_SHAKE', value = 0.5}, -- Shake cam effect after exhale smoke for disable, effect = false
            CoughChance = false, -- false if you don't want to have any chance of coughing / 1 is a 100% chance, the higher the value you set, the lower the chance will be.
        Status = {
            ['ARMOR'] = 0, -- Adds armor after each puff of smoke
            ['STRESS'] = math.random(5, 10), -- Removes stress after smoke exhalation
            ['HIGH'] = 1, -- Adds a high after exhaling smoke
            }
        },
        Positions = {
            ['HAND'] = { -- Prop position in hand
                BoneID = 64097,
                Offset = vector3(0.020, 0.02, -0.008),
                Rot = vector3(100.0, 0.0, 100.0),},
            ['MOUTH'] = { -- Prop position in mouth 
                BoneID = 47419,
                Offset = vector3(0.014, -0.003, 0.008),
                Rot = vector3(55.0, 0.0, 110.0),},
            ['EAR'] = { -- Prop position behind the ear
                BoneID = 12844,
                Offset = vector3(0.05, -0.03, -0.08),
                Rot = vector3(80.0, 7.0, -118.0),},
        },
        Particles = {
            ['Exhale'] = {-- Smoke particles after exhale
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.1,
                Offset = vector3(0.014, -0.003, 0.008),
                Rot = vector3(55.0, 0.0, 110.0),},
            ['Prop'] = {-- Smoke particles on prop 
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.02,
                Offset = vector3(-0.050, 0.0, 0.0),
                Rot = vector3(0.0, 0.0, 0.0),}
        },
        Anims = { -- New animations can be added to each category and are always selected randomly
            ['USE'] = {
                [1] = {anim = 'idle_a', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [2] = {anim = 'idle_b', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [3] = {anim = 'idle_c', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [4] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1600, 49, 1}},
            },
            ['MOUTH'] = {
                [1] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['EAR'] = {
                [1] = {anim = 'takecig_clip', dict = 'takecig@devcore', params = {1.0, -1.0, 1500, 49, 1}},
            },
            ['COUGH'] = {
                [1] = {anim = 'idle_cough', dict = 'timetable@gardener@smoking_joint', params = {1.0, -1.0, 2500, 49, 1}},
            },
            ['THROW'] = {
                [1] = {anim = 'putdown_low', dict = 'pickup_object', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['GIVE'] = {
                [1] = {anim = 'givetake1_a', dict = 'mp_common', params = {1.0, -1.0, 1800, 49, 1}},
            },
            ['TAKEOUT'] = {
                [1] = {anim = 'enter', dict = 'amb@world_human_smoking@male@male_a@enter', params = {1.0, -1.0, 1300, 49, 1}},
            },
        },
    },

    -- CIGARETTE -- TYPE : NEED_LIGHTER
    ['cig_marlboro'] = { -- ITEM NAME
        Type = 'NEED_LIGHTER', -- NEED_LIGHTER (joints, cigarette, cigars), BONG(bongs), VAPE(Must be topped up with liquid), VAPE-PUFF(It has an adjustable maximum amount of puffs)
        Prop = {Default = 'prop_cs_ciggy_01', Light = 'prop_cs_ciggy_01b'}, -- Default is prop before lighting and Light is prop after lighting the cigarette (if you want to use only one prop, remove the Light prop and leave only the Default prop)
        Setting = {
            Size = 10, -- The size for the NEED_LIGHTER type sets a value that decreases gradually after the item is lit, the larger the number you set, the longer the item can be smoked.
            Remove = 0.1, Time = 1}, -- The size is subtracted by 0.3 every 3 seconds during smoking, depending on the setting (ONLY FOR: NEED_LIGHTER).
        AfterExhale = {
            ExhaleTime = {min = 1, max = 1}, -- Smoke exhalation time
            RemoveAfterExhale = 5, -- The set value is taken after each exhalation of smoke from the size 
            ExhaleEffect = {enable = true, intensity = 60, effect = false}, -- screen blackout effect after smoke exhalation and Animpostfx effect-- https://forge.plebmasters.de/animpostfx
            ShakeCam = {effect = 'VIBRATE_SHAKE', value = 0.5}, -- Shake cam effect after exhale smoke for disable, effect = false
            CoughChance = false, -- false if you don't want to have any chance of coughing / 1 is a 100% chance, the higher the value you set, the lower the chance will be.
        Status = {
            ['ARMOR'] = 0, -- Adds armor after each puff of smoke
            ['STRESS'] = math.random(5, 10), -- Removes stress after smoke exhalation
            ['HIGH'] = 1, -- Adds a high after exhaling smoke
            }
        },
        Positions = {
            ['HAND'] = { -- Prop position in hand
                BoneID = 64097,
                Offset = vector3(0.020, 0.02, -0.008),
                Rot = vector3(100.0, 0.0, 100.0),},
            ['MOUTH'] = { -- Prop position in mouth 
                BoneID = 47419,
                Offset = vector3(0.014, -0.003, 0.008),
                Rot = vector3(55.0, 0.0, 110.0),},
            ['EAR'] = { -- Prop position behind the ear
                BoneID = 12844,
                Offset = vector3(0.05, -0.03, -0.08),
                Rot = vector3(80.0, 7.0, -118.0),},
        },
        Particles = {
            ['Exhale'] = {-- Smoke particles after exhale
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.1,
                Offset = vector3(0.014, -0.003, 0.008),
                Rot = vector3(55.0, 0.0, 110.0),},
            ['Prop'] = {-- Smoke particles on prop 
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.02,
                Offset = vector3(-0.050, 0.0, 0.0),
                Rot = vector3(0.0, 0.0, 0.0),}
        },
        Anims = { -- New animations can be added to each category and are always selected randomly
            ['USE'] = {
                [1] = {anim = 'idle_a', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [2] = {anim = 'idle_b', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [3] = {anim = 'idle_c', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [4] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1600, 49, 1}},
            },
            ['MOUTH'] = {
                [1] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['EAR'] = {
                [1] = {anim = 'takecig_clip', dict = 'takecig@devcore', params = {1.0, -1.0, 1500, 49, 1}},
            },
            ['COUGH'] = {
                [1] = {anim = 'idle_cough', dict = 'timetable@gardener@smoking_joint', params = {1.0, -1.0, 2500, 49, 1}},
            },
            ['THROW'] = {
                [1] = {anim = 'putdown_low', dict = 'pickup_object', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['GIVE'] = {
                [1] = {anim = 'givetake1_a', dict = 'mp_common', params = {1.0, -1.0, 1800, 49, 1}},
            },
            ['TAKEOUT'] = {
                [1] = {anim = 'enter', dict = 'amb@world_human_smoking@male@male_a@enter', params = {1.0, -1.0, 1300, 49, 1}},
            },
        },
    },

    -- CIGARS -- TYPE : NEED_LIGHTER

    ['cigar_cuban'] = { -- ITEM NAME
        Type = 'NEED_LIGHTER', -- NEED_LIGHTER (joints, cigarette, cigars) , BONG(bongs), VAPE(Must be topped up with liquid) , VAPE-PUFF(It has an adjustable maximum amount of puffs)
        Prop = {Default = 'prop_cigar_02', Light = 'prop_cigar_01'},  -- Default is prop before lighting and Light is prop after lighting the cigarette (if you want to use only one prop, remove the Light prop and leave only the Default prop)
        Setting = {
            Size = 150,  --  TYPE : NEED_LIGHTER : set a value that decreases gradually after the item is lit, the larger the number you set, the longer the item can be smoked. 
            Remove = 0.3, Time = 3}, -- The size is subtracted by 0.3 every 3 seconds during smoking, depending on the setting (ONLY FOR: NEED_LIGHTER).
        AfterExhale = {
            ExhaleTime = {min = 1, max = 2}, -- Smoke exhalation time
            RemoveAfterExhale = 5, -- The set value is taken after each exhalation of smoke from the size
            ExhaleEffect = {enable = true, intensity = 90, effect = false}, -- screen blackout effect after smoke exhalation and Animpostfx effect-- https://forge.plebmasters.de/animpostfx
            ShakeCam = {effect = 'VIBRATE_SHAKE', value = 0.7}, -- Shake cam effect after exhale smoke for disable, effect = false
            CoughChance = 5, -- false if you don't want to have any chance of coughing / 1 is a 100% chance, the higher the value you set, the lower the chance will be.
        Status = {
            ['ARMOR'] = 10, -- Adds armor after each puff of smoke
            ['STRESS'] = math.random(5, 10), -- Removes stress after smoke exhalation
            ['HIGH'] = 1, -- Adds a high after exhaling smoke
            }
        },
        Positions = {
            ['HAND'] = { -- Prop position in hand
                BoneID = 64097,
                Offset = vector3(0.020, 0.02, -0.008),
                Rot = vector3(100.0, 0.0, -100.0),},
            ['MOUTH'] = { -- Prop position in mouth 
                BoneID = 47419,
                Offset = vector3(0.01, 0.0, 0.01),
                Rot = vector3(6.0, 32.0, -86.0),},
            ['EAR'] = { -- Prop position behind the ear
                BoneID = 12844,
                Offset = vector3(0.05, -0.03, -0.08),
                Rot = vector3(-1.0, 1.0, 63.0),},
        },
        Particles = {
            ['Exhale'] = {-- Smoke particles after exhale
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.1,
                Offset = vector3(0.014, -0.003, 0.008),
                Rot = vector3(55.0, 0.0, 110.0),},
            ['Prop'] = {-- Smoke particles on prop 
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.04,
                Offset = vector3(0.050, 0.0, 0.0),
                Rot = vector3(0.0, 0.0, 0.0),}
        },
        Anims = {-- New animations can be added to each category and are always selected randomly
            ['USE'] = {
                [1] = {anim = 'idle_a', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [2] = {anim = 'idle_b', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [3] = {anim = 'idle_c', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [4] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1300, 49, 1}},
            },
            ['MOUTH'] = {
                [1] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['COUGH'] = {
                [1] = {anim = 'idle_cough', dict = 'timetable@gardener@smoking_joint', params = {1.0, -1.0, 2500, 49, 1}},
            },
            ['EAR'] = {
                [1] = {anim = 'takecig_clip', dict = 'takecig@devcore', params = {1.0, -1.0, 1500, 49, 1}},
            },
            ['THROW'] = {
                [1] = {anim = 'putdown_low', dict = 'pickup_object', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['GIVE'] = {
                [1] = {anim = 'givetake1_a', dict = 'mp_common', params = {1.0, -1.0, 1800, 49, 1}},
            },
            ['TAKEOUT'] = {
                [1] = {anim = 'enter', dict = 'amb@world_human_smoking@male@male_a@enter', params = {1.0, -1.0, 1300, 49, 1}},
            },
        },
    },

    -- CIGARS -- TYPE : NEED_LIGHTER

    ['cigar_davidoff'] = { -- ITEM NAME
        Type = 'NEED_LIGHTER', -- NEED_LIGHTER (joints, cigarette, cigars) , BONG(bongs), VAPE(Must be topped up with liquid) , VAPE-PUFF(It has an adjustable maximum amount of puffs)
        Prop = {Default = 'prop_cigar_02', Light = 'prop_cigar_01'},  -- Default is prop before lighting and Light is prop after lighting the cigarette (if you want to use only one prop, remove the Light prop and leave only the Default prop)
        Setting = {
            Size = 100,  --  TYPE : NEED_LIGHTER : set a value that decreases gradually after the item is lit, the larger the number you set, the longer the item can be smoked. 
            Remove = 0.3, Time = 3}, -- The size is subtracted by 0.3 every 3 seconds during smoking, depending on the setting (ONLY FOR: NEED_LIGHTER).
        AfterExhale = {
            ExhaleTime = {min = 1, max = 2}, -- Smoke exhalation time
            RemoveAfterExhale = 5, -- The set value is taken after each exhalation of smoke from the size
            ExhaleEffect = {enable = true, intensity = 80, effect = false}, -- screen blackout effect after smoke exhalation and Animpostfx effect-- https://forge.plebmasters.de/animpostfx
            ShakeCam = {effect = 'VIBRATE_SHAKE', value = 0.6}, -- Shake cam effect after exhale smoke for disable, effect = false
            CoughChance = 5, -- false if you don't want to have any chance of coughing / 1 is a 100% chance, the higher the value you set, the lower the chance will be.
        Status = {
            ['ARMOR'] = 10, -- Adds armor after each puff of smoke
            ['STRESS'] = math.random(5, 10), -- Removes stress after smoke exhalation
            ['HIGH'] = 1, -- Adds a high after exhaling smoke
            }
        },
        Positions = {
            ['HAND'] = { -- Prop position in hand
                BoneID = 64097,
                Offset = vector3(0.020, 0.02, -0.008),
                Rot = vector3(100.0, 0.0, -100.0),},
            ['MOUTH'] = { -- Prop position in mouth 
                BoneID = 47419,
                Offset = vector3(0.01, 0.0, 0.01),
                Rot = vector3(6.0, 32.0, -86.0),},
            ['EAR'] = { -- Prop position behind the ear
                BoneID = 12844,
                Offset = vector3(0.05, -0.03, -0.08),
                Rot = vector3(-1.0, 1.0, 63.0),},
        },
        Particles = {
            ['Exhale'] = {-- Smoke particles after exhale
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.1,
                Offset = vector3(0.014, -0.003, 0.008),
                Rot = vector3(55.0, 0.0, 110.0),},
            ['Prop'] = {-- Smoke particles on prop 
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.03,
                Offset = vector3(0.050, 0.0, 0.0),
                Rot = vector3(0.0, 0.0, 0.0),}
        },
        Anims = {-- New animations can be added to each category and are always selected randomly
            ['USE'] = {
                [1] = {anim = 'idle_a', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [2] = {anim = 'idle_b', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [3] = {anim = 'idle_c', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [4] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1300, 49, 1}},
            },
            ['MOUTH'] = {
                [1] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['COUGH'] = {
                [1] = {anim = 'idle_cough', dict = 'timetable@gardener@smoking_joint', params = {1.0, -1.0, 2500, 49, 1}},
            },
            ['EAR'] = {
                [1] = {anim = 'takecig_clip', dict = 'takecig@devcore', params = {1.0, -1.0, 1500, 49, 1}},
            },
            ['THROW'] = {
                [1] = {anim = 'putdown_low', dict = 'pickup_object', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['GIVE'] = {
                [1] = {anim = 'givetake1_a', dict = 'mp_common', params = {1.0, -1.0, 1800, 49, 1}},
            },
            ['TAKEOUT'] = {
                [1] = {anim = 'enter', dict = 'amb@world_human_smoking@male@male_a@enter', params = {1.0, -1.0, 1300, 49, 1}},
            },
        },
    },

    -- JOINTS -- TYPE : NEED_LIGHTER

    --------------------------- START COSMIC

    ['cosmic_nattypinkdelightjoint'] = { -- ITEM NAME
        Type = 'NEED_LIGHTER', -- NEED_LIGHTER (joints, cigarette, cigars), BONG(bongs), VAPE(Must be topped up with liquid), VAPE-PUFF(It has an adjustable maximum amount of puffs)
        Prop = {Default = 'p_cs_joint_02', Light = 'p_cs_joint_01'}, -- Default is prop before lighting and Light is prop after lighting the cigarette (if you want to use only one prop, remove the Light prop and leave only the Default prop)
        Setting = {
            Size = 100,  --  TYPE : NEED_LIGHTER : set a value that decreases gradually after the item is lit, the larger the number you set, the longer the item can be smoked. 
            Remove = 0.3, Time = 3}, -- The size is subtracted by 0.3 every 3 seconds during smoking, depending on the setting (ONLY FOR: NEED_LIGHTER).
        AfterExhale = {
            ExhaleTime = {min = 1, max = 2}, -- Smoke exhalation time in sec
            RemoveAfterExhale = 5, -- The set value is taken after each exhalation of smoke from the size
            ExhaleEffect = {enable = true, intensity = 40, effect = false}, -- screen blackout effect after smoke exhalation and Animpostfx effect-- https://forge.plebmasters.de/animpostfx
            ShakeCam = {effect = 'VIBRATE_SHAKE', value = 2.0}, -- Shake cam effect after exhale smoke for disable, effect = false
            CoughChance = 2, -- false if you don't want to have any chance of coughing / 1 is a 100% chance, the higher the value you set, the lower the chance will be.
        Status = {
            ['ARMOR'] = 35, -- Adds armor after each puff of smoke
            ['STRESS'] = math.random(5, 10), -- Removes stress after smoke exhalation
            ['HIGH'] = 1, -- Adds a high after exhaling smoke
            }
        },
        Positions = {
            ['HAND'] = { -- Prop position in hand 
                BoneID = 64097,
                Offset = vector3(0.020, 0.02, -0.008),
                Rot = vector3(100.0, 0.0, 100.0),},
            ['MOUTH'] = { -- Prop position in mouth 
                BoneID = 47419,
                Offset = vector3(0.01, -0.01, 0.01),
                Rot = vector3(-2.0, 0.0, 96.0),},
            ['EAR'] = { -- Prop position behind the ear
                BoneID = 12844,
                Offset = vector3(0.05, -0.03, -0.08),
                Rot = vector3(80.0, 7.0, -118.0),},
        },
        Particles = {
            ['Exhale'] = {-- Smoke particles after exhale
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.1,
                Offset = vector3(0.014, -0.003, 0.008),
                Rot = vector3(55.0, 0.0, 110.0),},
            ['Prop'] = {-- Smoke particles on prop 
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.03,
                Offset = vector3(-0.050, 0.0, 0.0),
                Rot = vector3(0.0, 0.0, 0.0),}
        },
        Anims = {-- New animations can be added to each category and are always selected randomly
            ['USE'] = {
                [1] = {anim = 'idle_a', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [2] = {anim = 'idle_b', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [3] = {anim = 'idle_c', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [4] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1300, 49, 1}},
            },
            ['MOUTH'] = {
                [1] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['EAR'] = {
                [1] = {anim = 'takecig_clip', dict = 'takecig@devcore', params = {1.0, -1.0, 1500, 49, 1}},
            },
            ['COUGH'] = {
                [1] = {anim = 'idle_cough', dict = 'timetable@gardener@smoking_joint', params = {1.0, -1.0, 2500, 49, 1}},
            },
            ['THROW'] = {
                [1] = {anim = 'putdown_low', dict = 'pickup_object', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['GIVE'] = {
                [1] = {anim = 'givetake1_a', dict = 'mp_common', params = {1.0, -1.0, 1800, 49, 1}},
            },
            ['TAKEOUT'] = {
                [1] = {anim = 'enter', dict = 'amb@world_human_smoking@male@male_a@enter', params = {1.0, -1.0, 1300, 49, 1}},
            },
        },
    },

    ['cosmic_lockheartlovejoint'] = { -- ITEM NAME
        Type = 'NEED_LIGHTER', -- NEED_LIGHTER (joints, cigarette, cigars), BONG(bongs), VAPE(Must be topped up with liquid), VAPE-PUFF(It has an adjustable maximum amount of puffs)
        Prop = {Default = 'p_cs_joint_02', Light = 'p_cs_joint_01'}, -- Default is prop before lighting and Light is prop after lighting the cigarette (if you want to use only one prop, remove the Light prop and leave only the Default prop)
        Setting = {
            Size = 100,  --  TYPE : NEED_LIGHTER : set a value that decreases gradually after the item is lit, the larger the number you set, the longer the item can be smoked. 
            Remove = 0.3, Time = 3}, -- The size is subtracted by 0.3 every 3 seconds during smoking, depending on the setting (ONLY FOR: NEED_LIGHTER).
        AfterExhale = {
            ExhaleTime = {min = 1, max = 2}, -- Smoke exhalation time in sec
            RemoveAfterExhale = 5, -- The set value is taken after each exhalation of smoke from the size
            ExhaleEffect = {enable = true, intensity = 40, effect = false}, -- screen blackout effect after smoke exhalation and Animpostfx effect-- https://forge.plebmasters.de/animpostfx
            ShakeCam = {effect = 'VIBRATE_SHAKE', value = 2.0}, -- Shake cam effect after exhale smoke for disable, effect = false
            CoughChance = 2, -- false if you don't want to have any chance of coughing / 1 is a 100% chance, the higher the value you set, the lower the chance will be.
        Status = {
            ['ARMOR'] = 35, -- Adds armor after each puff of smoke
            ['STRESS'] = math.random(5, 10), -- Removes stress after smoke exhalation
            ['HIGH'] = 1, -- Adds a high after exhaling smoke
            }
        },
        Positions = {
            ['HAND'] = { -- Prop position in hand 
                BoneID = 64097,
                Offset = vector3(0.020, 0.02, -0.008),
                Rot = vector3(100.0, 0.0, 100.0),},
            ['MOUTH'] = { -- Prop position in mouth 
                BoneID = 47419,
                Offset = vector3(0.01, -0.01, 0.01),
                Rot = vector3(-2.0, 0.0, 96.0),},
            ['EAR'] = { -- Prop position behind the ear
                BoneID = 12844,
                Offset = vector3(0.05, -0.03, -0.08),
                Rot = vector3(80.0, 7.0, -118.0),},
        },
        Particles = {
            ['Exhale'] = {-- Smoke particles after exhale
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.1,
                Offset = vector3(0.014, -0.003, 0.008),
                Rot = vector3(55.0, 0.0, 110.0),},
            ['Prop'] = {-- Smoke particles on prop 
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.03,
                Offset = vector3(-0.050, 0.0, 0.0),
                Rot = vector3(0.0, 0.0, 0.0),}
        },
        Anims = {-- New animations can be added to each category and are always selected randomly
            ['USE'] = {
                [1] = {anim = 'idle_a', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [2] = {anim = 'idle_b', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [3] = {anim = 'idle_c', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [4] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1300, 49, 1}},
            },
            ['MOUTH'] = {
                [1] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['EAR'] = {
                [1] = {anim = 'takecig_clip', dict = 'takecig@devcore', params = {1.0, -1.0, 1500, 49, 1}},
            },
            ['COUGH'] = {
                [1] = {anim = 'idle_cough', dict = 'timetable@gardener@smoking_joint', params = {1.0, -1.0, 2500, 49, 1}},
            },
            ['THROW'] = {
                [1] = {anim = 'putdown_low', dict = 'pickup_object', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['GIVE'] = {
                [1] = {anim = 'givetake1_a', dict = 'mp_common', params = {1.0, -1.0, 1800, 49, 1}},
            },
            ['TAKEOUT'] = {
                [1] = {anim = 'enter', dict = 'amb@world_human_smoking@male@male_a@enter', params = {1.0, -1.0, 1300, 49, 1}},
            },
        },
    },

    ['cosmic_greengodjoint'] = { -- ITEM NAME
        Type = 'NEED_LIGHTER', -- NEED_LIGHTER (joints, cigarette, cigars), BONG(bongs), VAPE(Must be topped up with liquid), VAPE-PUFF(It has an adjustable maximum amount of puffs)
        Prop = {Default = 'p_cs_joint_02', Light = 'p_cs_joint_01'}, -- Default is prop before lighting and Light is prop after lighting the cigarette (if you want to use only one prop, remove the Light prop and leave only the Default prop)
        Setting = {
            Size = 100,  --  TYPE : NEED_LIGHTER : set a value that decreases gradually after the item is lit, the larger the number you set, the longer the item can be smoked. 
            Remove = 0.3, Time = 3}, -- The size is subtracted by 0.3 every 3 seconds during smoking, depending on the setting (ONLY FOR: NEED_LIGHTER).
        AfterExhale = {
            ExhaleTime = {min = 1, max = 2}, -- Smoke exhalation time in sec
            RemoveAfterExhale = 5, -- The set value is taken after each exhalation of smoke from the size
            ExhaleEffect = {enable = true, intensity = 40, effect = false}, -- screen blackout effect after smoke exhalation and Animpostfx effect-- https://forge.plebmasters.de/animpostfx
            ShakeCam = {effect = 'VIBRATE_SHAKE', value = 2.0}, -- Shake cam effect after exhale smoke for disable, effect = false
            CoughChance = 2, -- false if you don't want to have any chance of coughing / 1 is a 100% chance, the higher the value you set, the lower the chance will be.
        Status = {
            ['ARMOR'] = 35, -- Adds armor after each puff of smoke
            ['STRESS'] = math.random(5, 10), -- Removes stress after smoke exhalation
            ['HIGH'] = 1, -- Adds a high after exhaling smoke
            }
        },
        Positions = {
            ['HAND'] = { -- Prop position in hand 
                BoneID = 64097,
                Offset = vector3(0.020, 0.02, -0.008),
                Rot = vector3(100.0, 0.0, 100.0),},
            ['MOUTH'] = { -- Prop position in mouth 
                BoneID = 47419,
                Offset = vector3(0.01, -0.01, 0.01),
                Rot = vector3(-2.0, 0.0, 96.0),},
            ['EAR'] = { -- Prop position behind the ear
                BoneID = 12844,
                Offset = vector3(0.05, -0.03, -0.08),
                Rot = vector3(80.0, 7.0, -118.0),},
        },
        Particles = {
            ['Exhale'] = {-- Smoke particles after exhale
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.1,
                Offset = vector3(0.014, -0.003, 0.008),
                Rot = vector3(55.0, 0.0, 110.0),},
            ['Prop'] = {-- Smoke particles on prop 
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.03,
                Offset = vector3(-0.050, 0.0, 0.0),
                Rot = vector3(0.0, 0.0, 0.0),}
        },
        Anims = {-- New animations can be added to each category and are always selected randomly
            ['USE'] = {
                [1] = {anim = 'idle_a', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [2] = {anim = 'idle_b', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [3] = {anim = 'idle_c', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [4] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1300, 49, 1}},
            },
            ['MOUTH'] = {
                [1] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['EAR'] = {
                [1] = {anim = 'takecig_clip', dict = 'takecig@devcore', params = {1.0, -1.0, 1500, 49, 1}},
            },
            ['COUGH'] = {
                [1] = {anim = 'idle_cough', dict = 'timetable@gardener@smoking_joint', params = {1.0, -1.0, 2500, 49, 1}},
            },
            ['THROW'] = {
                [1] = {anim = 'putdown_low', dict = 'pickup_object', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['GIVE'] = {
                [1] = {anim = 'givetake1_a', dict = 'mp_common', params = {1.0, -1.0, 1800, 49, 1}},
            },
            ['TAKEOUT'] = {
                [1] = {anim = 'enter', dict = 'amb@world_human_smoking@male@male_a@enter', params = {1.0, -1.0, 1300, 49, 1}},
            },
        },
    },

    ['cosmic_ogcosmicjoint'] = { -- ITEM NAME
        Type = 'NEED_LIGHTER', -- NEED_LIGHTER (joints, cigarette, cigars), BONG(bongs), VAPE(Must be topped up with liquid), VAPE-PUFF(It has an adjustable maximum amount of puffs)
        Prop = {Default = 'p_cs_joint_02', Light = 'p_cs_joint_01'}, -- Default is prop before lighting and Light is prop after lighting the cigarette (if you want to use only one prop, remove the Light prop and leave only the Default prop)
        Setting = {
            Size = 100,  --  TYPE : NEED_LIGHTER : set a value that decreases gradually after the item is lit, the larger the number you set, the longer the item can be smoked. 
            Remove = 0.3, Time = 3}, -- The size is subtracted by 0.3 every 3 seconds during smoking, depending on the setting (ONLY FOR: NEED_LIGHTER).
        AfterExhale = {
            ExhaleTime = {min = 1, max = 2}, -- Smoke exhalation time in sec
            RemoveAfterExhale = 5, -- The set value is taken after each exhalation of smoke from the size
            ExhaleEffect = {enable = true, intensity = 40, effect = false}, -- screen blackout effect after smoke exhalation and Animpostfx effect-- https://forge.plebmasters.de/animpostfx
            ShakeCam = {effect = 'VIBRATE_SHAKE', value = 2.0}, -- Shake cam effect after exhale smoke for disable, effect = false
            CoughChance = 2, -- false if you don't want to have any chance of coughing / 1 is a 100% chance, the higher the value you set, the lower the chance will be.
        Status = {
            ['ARMOR'] = 35, -- Adds armor after each puff of smoke
            ['STRESS'] = math.random(5, 10), -- Removes stress after smoke exhalation
            ['HIGH'] = 1, -- Adds a high after exhaling smoke
            }
        },
        Positions = {
            ['HAND'] = { -- Prop position in hand 
                BoneID = 64097,
                Offset = vector3(0.020, 0.02, -0.008),
                Rot = vector3(100.0, 0.0, 100.0),},
            ['MOUTH'] = { -- Prop position in mouth 
                BoneID = 47419,
                Offset = vector3(0.01, -0.01, 0.01),
                Rot = vector3(-2.0, 0.0, 96.0),},
            ['EAR'] = { -- Prop position behind the ear
                BoneID = 12844,
                Offset = vector3(0.05, -0.03, -0.08),
                Rot = vector3(80.0, 7.0, -118.0),},
        },
        Particles = {
            ['Exhale'] = {-- Smoke particles after exhale
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.1,
                Offset = vector3(0.014, -0.003, 0.008),
                Rot = vector3(55.0, 0.0, 110.0),},
            ['Prop'] = {-- Smoke particles on prop 
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.03,
                Offset = vector3(-0.050, 0.0, 0.0),
                Rot = vector3(0.0, 0.0, 0.0),}
        },
        Anims = {-- New animations can be added to each category and are always selected randomly
            ['USE'] = {
                [1] = {anim = 'idle_a', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [2] = {anim = 'idle_b', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [3] = {anim = 'idle_c', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [4] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1300, 49, 1}},
            },
            ['MOUTH'] = {
                [1] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['EAR'] = {
                [1] = {anim = 'takecig_clip', dict = 'takecig@devcore', params = {1.0, -1.0, 1500, 49, 1}},
            },
            ['COUGH'] = {
                [1] = {anim = 'idle_cough', dict = 'timetable@gardener@smoking_joint', params = {1.0, -1.0, 2500, 49, 1}},
            },
            ['THROW'] = {
                [1] = {anim = 'putdown_low', dict = 'pickup_object', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['GIVE'] = {
                [1] = {anim = 'givetake1_a', dict = 'mp_common', params = {1.0, -1.0, 1800, 49, 1}},
            },
            ['TAKEOUT'] = {
                [1] = {anim = 'enter', dict = 'amb@world_human_smoking@male@male_a@enter', params = {1.0, -1.0, 1300, 49, 1}},
            },
        },
    },

    ['cosmic_fossilfueljoint'] = { -- ITEM NAME
        Type = 'NEED_LIGHTER', -- NEED_LIGHTER (joints, cigarette, cigars), BONG(bongs), VAPE(Must be topped up with liquid), VAPE-PUFF(It has an adjustable maximum amount of puffs)
        Prop = {Default = 'p_cs_joint_02', Light = 'p_cs_joint_01'}, -- Default is prop before lighting and Light is prop after lighting the cigarette (if you want to use only one prop, remove the Light prop and leave only the Default prop)
        Setting = {
            Size = 100,  --  TYPE : NEED_LIGHTER : set a value that decreases gradually after the item is lit, the larger the number you set, the longer the item can be smoked. 
            Remove = 0.3, Time = 3}, -- The size is subtracted by 0.3 every 3 seconds during smoking, depending on the setting (ONLY FOR: NEED_LIGHTER).
        AfterExhale = {
            ExhaleTime = {min = 1, max = 2}, -- Smoke exhalation time in sec
            RemoveAfterExhale = 5, -- The set value is taken after each exhalation of smoke from the size
            ExhaleEffect = {enable = true, intensity = 40, effect = false}, -- screen blackout effect after smoke exhalation and Animpostfx effect-- https://forge.plebmasters.de/animpostfx
            ShakeCam = {effect = 'VIBRATE_SHAKE', value = 2.0}, -- Shake cam effect after exhale smoke for disable, effect = false
            CoughChance = 2, -- false if you don't want to have any chance of coughing / 1 is a 100% chance, the higher the value you set, the lower the chance will be.
        Status = {
            ['ARMOR'] = 35, -- Adds armor after each puff of smoke
            ['STRESS'] = math.random(5, 10), -- Removes stress after smoke exhalation
            ['HIGH'] = 1, -- Adds a high after exhaling smoke
            }
        },
        Positions = {
            ['HAND'] = { -- Prop position in hand 
                BoneID = 64097,
                Offset = vector3(0.020, 0.02, -0.008),
                Rot = vector3(100.0, 0.0, 100.0),},
            ['MOUTH'] = { -- Prop position in mouth 
                BoneID = 47419,
                Offset = vector3(0.01, -0.01, 0.01),
                Rot = vector3(-2.0, 0.0, 96.0),},
            ['EAR'] = { -- Prop position behind the ear
                BoneID = 12844,
                Offset = vector3(0.05, -0.03, -0.08),
                Rot = vector3(80.0, 7.0, -118.0),},
        },
        Particles = {
            ['Exhale'] = {-- Smoke particles after exhale
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.1,
                Offset = vector3(0.014, -0.003, 0.008),
                Rot = vector3(55.0, 0.0, 110.0),},
            ['Prop'] = {-- Smoke particles on prop 
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.03,
                Offset = vector3(-0.050, 0.0, 0.0),
                Rot = vector3(0.0, 0.0, 0.0),}
        },
        Anims = {-- New animations can be added to each category and are always selected randomly
            ['USE'] = {
                [1] = {anim = 'idle_a', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [2] = {anim = 'idle_b', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [3] = {anim = 'idle_c', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [4] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1300, 49, 1}},
            },
            ['MOUTH'] = {
                [1] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['EAR'] = {
                [1] = {anim = 'takecig_clip', dict = 'takecig@devcore', params = {1.0, -1.0, 1500, 49, 1}},
            },
            ['COUGH'] = {
                [1] = {anim = 'idle_cough', dict = 'timetable@gardener@smoking_joint', params = {1.0, -1.0, 2500, 49, 1}},
            },
            ['THROW'] = {
                [1] = {anim = 'putdown_low', dict = 'pickup_object', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['GIVE'] = {
                [1] = {anim = 'givetake1_a', dict = 'mp_common', params = {1.0, -1.0, 1800, 49, 1}},
            },
            ['TAKEOUT'] = {
                [1] = {anim = 'enter', dict = 'amb@world_human_smoking@male@male_a@enter', params = {1.0, -1.0, 1300, 49, 1}},
            },
        },
    },

    ['cosmic_afterlifejoint'] = { -- ITEM NAME
        Type = 'NEED_LIGHTER', -- NEED_LIGHTER (joints, cigarette, cigars), BONG(bongs), VAPE(Must be topped up with liquid), VAPE-PUFF(It has an adjustable maximum amount of puffs)
        Prop = {Default = 'p_cs_joint_02', Light = 'p_cs_joint_01'}, -- Default is prop before lighting and Light is prop after lighting the cigarette (if you want to use only one prop, remove the Light prop and leave only the Default prop)
        Setting = {
            Size = 100,  --  TYPE : NEED_LIGHTER : set a value that decreases gradually after the item is lit, the larger the number you set, the longer the item can be smoked. 
            Remove = 0.3, Time = 3}, -- The size is subtracted by 0.3 every 3 seconds during smoking, depending on the setting (ONLY FOR: NEED_LIGHTER).
        AfterExhale = {
            ExhaleTime = {min = 1, max = 2}, -- Smoke exhalation time in sec
            RemoveAfterExhale = 5, -- The set value is taken after each exhalation of smoke from the size
            ExhaleEffect = {enable = true, intensity = 40, effect = false}, -- screen blackout effect after smoke exhalation and Animpostfx effect-- https://forge.plebmasters.de/animpostfx
            ShakeCam = {effect = 'VIBRATE_SHAKE', value = 2.0}, -- Shake cam effect after exhale smoke for disable, effect = false
            CoughChance = 2, -- false if you don't want to have any chance of coughing / 1 is a 100% chance, the higher the value you set, the lower the chance will be.
        Status = {
            ['ARMOR'] = 35, -- Adds armor after each puff of smoke
            ['STRESS'] = math.random(5, 10), -- Removes stress after smoke exhalation
            ['HIGH'] = 1, -- Adds a high after exhaling smoke
            }
        },
        Positions = {
            ['HAND'] = { -- Prop position in hand 
                BoneID = 64097,
                Offset = vector3(0.020, 0.02, -0.008),
                Rot = vector3(100.0, 0.0, 100.0),},
            ['MOUTH'] = { -- Prop position in mouth 
                BoneID = 47419,
                Offset = vector3(0.01, -0.01, 0.01),
                Rot = vector3(-2.0, 0.0, 96.0),},
            ['EAR'] = { -- Prop position behind the ear
                BoneID = 12844,
                Offset = vector3(0.05, -0.03, -0.08),
                Rot = vector3(80.0, 7.0, -118.0),},
        },
        Particles = {
            ['Exhale'] = {-- Smoke particles after exhale
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.1,
                Offset = vector3(0.014, -0.003, 0.008),
                Rot = vector3(55.0, 0.0, 110.0),},
            ['Prop'] = {-- Smoke particles on prop 
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.03,
                Offset = vector3(-0.050, 0.0, 0.0),
                Rot = vector3(0.0, 0.0, 0.0),}
        },
        Anims = {-- New animations can be added to each category and are always selected randomly
            ['USE'] = {
                [1] = {anim = 'idle_a', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [2] = {anim = 'idle_b', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [3] = {anim = 'idle_c', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [4] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1300, 49, 1}},
            },
            ['MOUTH'] = {
                [1] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['EAR'] = {
                [1] = {anim = 'takecig_clip', dict = 'takecig@devcore', params = {1.0, -1.0, 1500, 49, 1}},
            },
            ['COUGH'] = {
                [1] = {anim = 'idle_cough', dict = 'timetable@gardener@smoking_joint', params = {1.0, -1.0, 2500, 49, 1}},
            },
            ['THROW'] = {
                [1] = {anim = 'putdown_low', dict = 'pickup_object', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['GIVE'] = {
                [1] = {anim = 'givetake1_a', dict = 'mp_common', params = {1.0, -1.0, 1800, 49, 1}},
            },
            ['TAKEOUT'] = {
                [1] = {anim = 'enter', dict = 'amb@world_human_smoking@male@male_a@enter', params = {1.0, -1.0, 1300, 49, 1}},
            },
        },
    },

    ['cosmic_pineappleexpressjoint'] = { -- ITEM NAME
        Type = 'NEED_LIGHTER', -- NEED_LIGHTER (joints, cigarette, cigars), BONG(bongs), VAPE(Must be topped up with liquid), VAPE-PUFF(It has an adjustable maximum amount of puffs)
        Prop = {Default = 'p_cs_joint_02', Light = 'p_cs_joint_01'}, -- Default is prop before lighting and Light is prop after lighting the cigarette (if you want to use only one prop, remove the Light prop and leave only the Default prop)
        Setting = {
            Size = 100,  --  TYPE : NEED_LIGHTER : set a value that decreases gradually after the item is lit, the larger the number you set, the longer the item can be smoked. 
            Remove = 0.3, Time = 3}, -- The size is subtracted by 0.3 every 3 seconds during smoking, depending on the setting (ONLY FOR: NEED_LIGHTER).
        AfterExhale = {
            ExhaleTime = {min = 1, max = 2}, -- Smoke exhalation time in sec
            RemoveAfterExhale = 5, -- The set value is taken after each exhalation of smoke from the size
            ExhaleEffect = {enable = true, intensity = 40, effect = false}, -- screen blackout effect after smoke exhalation and Animpostfx effect-- https://forge.plebmasters.de/animpostfx
            ShakeCam = {effect = 'VIBRATE_SHAKE', value = 2.0}, -- Shake cam effect after exhale smoke for disable, effect = false
            CoughChance = 2, -- false if you don't want to have any chance of coughing / 1 is a 100% chance, the higher the value you set, the lower the chance will be.
        Status = {
            ['ARMOR'] = 35, -- Adds armor after each puff of smoke
            ['STRESS'] = math.random(5, 10), -- Removes stress after smoke exhalation
            ['HIGH'] = 1, -- Adds a high after exhaling smoke
            }
        },
        Positions = {
            ['HAND'] = { -- Prop position in hand 
                BoneID = 64097,
                Offset = vector3(0.020, 0.02, -0.008),
                Rot = vector3(100.0, 0.0, 100.0),},
            ['MOUTH'] = { -- Prop position in mouth 
                BoneID = 47419,
                Offset = vector3(0.01, -0.01, 0.01),
                Rot = vector3(-2.0, 0.0, 96.0),},
            ['EAR'] = { -- Prop position behind the ear
                BoneID = 12844,
                Offset = vector3(0.05, -0.03, -0.08),
                Rot = vector3(80.0, 7.0, -118.0),},
        },
        Particles = {
            ['Exhale'] = {-- Smoke particles after exhale
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.1,
                Offset = vector3(0.014, -0.003, 0.008),
                Rot = vector3(55.0, 0.0, 110.0),},
            ['Prop'] = {-- Smoke particles on prop 
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.03,
                Offset = vector3(-0.050, 0.0, 0.0),
                Rot = vector3(0.0, 0.0, 0.0),}
        },
        Anims = {-- New animations can be added to each category and are always selected randomly
            ['USE'] = {
                [1] = {anim = 'idle_a', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [2] = {anim = 'idle_b', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [3] = {anim = 'idle_c', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [4] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1300, 49, 1}},
            },
            ['MOUTH'] = {
                [1] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['EAR'] = {
                [1] = {anim = 'takecig_clip', dict = 'takecig@devcore', params = {1.0, -1.0, 1500, 49, 1}},
            },
            ['COUGH'] = {
                [1] = {anim = 'idle_cough', dict = 'timetable@gardener@smoking_joint', params = {1.0, -1.0, 2500, 49, 1}},
            },
            ['THROW'] = {
                [1] = {anim = 'putdown_low', dict = 'pickup_object', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['GIVE'] = {
                [1] = {anim = 'givetake1_a', dict = 'mp_common', params = {1.0, -1.0, 1800, 49, 1}},
            },
            ['TAKEOUT'] = {
                [1] = {anim = 'enter', dict = 'amb@world_human_smoking@male@male_a@enter', params = {1.0, -1.0, 1300, 49, 1}},
            },
        },
    },
    

    ----- smoking
    ['smoking_nattypinkdelightjoint'] = { -- ITEM NAME
        Type = 'NEED_LIGHTER', -- NEED_LIGHTER (joints, cigarette, cigars), BONG(bongs), VAPE(Must be topped up with liquid), VAPE-PUFF(It has an adjustable maximum amount of puffs)
        Prop = {Default = 'p_cs_joint_02', Light = 'p_cs_joint_01'}, -- Default is prop before lighting and Light is prop after lighting the cigarette (if you want to use only one prop, remove the Light prop and leave only the Default prop)
        Setting = {
            Size = 100,  --  TYPE : NEED_LIGHTER : set a value that decreases gradually after the item is lit, the larger the number you set, the longer the item can be smoked. 
            Remove = 0.3, Time = 3}, -- The size is subtracted by 0.3 every 3 seconds during smoking, depending on the setting (ONLY FOR: NEED_LIGHTER).
        AfterExhale = {
            ExhaleTime = {min = 1, max = 2}, -- Smoke exhalation time in sec
            RemoveAfterExhale = 5, -- The set value is taken after each exhalation of smoke from the size
            ExhaleEffect = {enable = true, intensity = 40, effect = false}, -- screen blackout effect after smoke exhalation and Animpostfx effect-- https://forge.plebmasters.de/animpostfx
            ShakeCam = {effect = 'VIBRATE_SHAKE', value = 2.0}, -- Shake cam effect after exhale smoke for disable, effect = false
            CoughChance = 2, -- false if you don't want to have any chance of coughing / 1 is a 100% chance, the higher the value you set, the lower the chance will be.
        Status = {
            ['ARMOR'] = 35, -- Adds armor after each puff of smoke
            ['STRESS'] = math.random(5, 10), -- Removes stress after smoke exhalation
            ['HIGH'] = 1, -- Adds a high after exhaling smoke
            }
        },
        Positions = {
            ['HAND'] = { -- Prop position in hand 
                BoneID = 64097,
                Offset = vector3(0.020, 0.02, -0.008),
                Rot = vector3(100.0, 0.0, 100.0),},
            ['MOUTH'] = { -- Prop position in mouth 
                BoneID = 47419,
                Offset = vector3(0.01, -0.01, 0.01),
                Rot = vector3(-2.0, 0.0, 96.0),},
            ['EAR'] = { -- Prop position behind the ear
                BoneID = 12844,
                Offset = vector3(0.05, -0.03, -0.08),
                Rot = vector3(80.0, 7.0, -118.0),},
        },
        Particles = {
            ['Exhale'] = {-- Smoke particles after exhale
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.1,
                Offset = vector3(0.014, -0.003, 0.008),
                Rot = vector3(55.0, 0.0, 110.0),},
            ['Prop'] = {-- Smoke particles on prop 
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.03,
                Offset = vector3(-0.050, 0.0, 0.0),
                Rot = vector3(0.0, 0.0, 0.0),}
        },
        Anims = {-- New animations can be added to each category and are always selected randomly
            ['USE'] = {
                [1] = {anim = 'idle_a', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [2] = {anim = 'idle_b', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [3] = {anim = 'idle_c', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [4] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1300, 49, 1}},
            },
            ['MOUTH'] = {
                [1] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['EAR'] = {
                [1] = {anim = 'takecig_clip', dict = 'takecig@devcore', params = {1.0, -1.0, 1500, 49, 1}},
            },
            ['COUGH'] = {
                [1] = {anim = 'idle_cough', dict = 'timetable@gardener@smoking_joint', params = {1.0, -1.0, 2500, 49, 1}},
            },
            ['THROW'] = {
                [1] = {anim = 'putdown_low', dict = 'pickup_object', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['GIVE'] = {
                [1] = {anim = 'givetake1_a', dict = 'mp_common', params = {1.0, -1.0, 1800, 49, 1}},
            },
            ['TAKEOUT'] = {
                [1] = {anim = 'enter', dict = 'amb@world_human_smoking@male@male_a@enter', params = {1.0, -1.0, 1300, 49, 1}},
            },
        },
    },

    ['smoking_lockheartlovejoint'] = { -- ITEM NAME
        Type = 'NEED_LIGHTER', -- NEED_LIGHTER (joints, cigarette, cigars), BONG(bongs), VAPE(Must be topped up with liquid), VAPE-PUFF(It has an adjustable maximum amount of puffs)
        Prop = {Default = 'p_cs_joint_02', Light = 'p_cs_joint_01'}, -- Default is prop before lighting and Light is prop after lighting the cigarette (if you want to use only one prop, remove the Light prop and leave only the Default prop)
        Setting = {
            Size = 100,  --  TYPE : NEED_LIGHTER : set a value that decreases gradually after the item is lit, the larger the number you set, the longer the item can be smoked. 
            Remove = 0.3, Time = 3}, -- The size is subtracted by 0.3 every 3 seconds during smoking, depending on the setting (ONLY FOR: NEED_LIGHTER).
        AfterExhale = {
            ExhaleTime = {min = 1, max = 2}, -- Smoke exhalation time in sec
            RemoveAfterExhale = 5, -- The set value is taken after each exhalation of smoke from the size
            ExhaleEffect = {enable = true, intensity = 40, effect = false}, -- screen blackout effect after smoke exhalation and Animpostfx effect-- https://forge.plebmasters.de/animpostfx
            ShakeCam = {effect = 'VIBRATE_SHAKE', value = 2.0}, -- Shake cam effect after exhale smoke for disable, effect = false
            CoughChance = 2, -- false if you don't want to have any chance of coughing / 1 is a 100% chance, the higher the value you set, the lower the chance will be.
        Status = {
            ['ARMOR'] = 35, -- Adds armor after each puff of smoke
            ['STRESS'] = math.random(5, 10), -- Removes stress after smoke exhalation
            ['HIGH'] = 1, -- Adds a high after exhaling smoke
            }
        },
        Positions = {
            ['HAND'] = { -- Prop position in hand 
                BoneID = 64097,
                Offset = vector3(0.020, 0.02, -0.008),
                Rot = vector3(100.0, 0.0, 100.0),},
            ['MOUTH'] = { -- Prop position in mouth 
                BoneID = 47419,
                Offset = vector3(0.01, -0.01, 0.01),
                Rot = vector3(-2.0, 0.0, 96.0),},
            ['EAR'] = { -- Prop position behind the ear
                BoneID = 12844,
                Offset = vector3(0.05, -0.03, -0.08),
                Rot = vector3(80.0, 7.0, -118.0),},
        },
        Particles = {
            ['Exhale'] = {-- Smoke particles after exhale
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.1,
                Offset = vector3(0.014, -0.003, 0.008),
                Rot = vector3(55.0, 0.0, 110.0),},
            ['Prop'] = {-- Smoke particles on prop 
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.03,
                Offset = vector3(-0.050, 0.0, 0.0),
                Rot = vector3(0.0, 0.0, 0.0),}
        },
        Anims = {-- New animations can be added to each category and are always selected randomly
            ['USE'] = {
                [1] = {anim = 'idle_a', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [2] = {anim = 'idle_b', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [3] = {anim = 'idle_c', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [4] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1300, 49, 1}},
            },
            ['MOUTH'] = {
                [1] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['EAR'] = {
                [1] = {anim = 'takecig_clip', dict = 'takecig@devcore', params = {1.0, -1.0, 1500, 49, 1}},
            },
            ['COUGH'] = {
                [1] = {anim = 'idle_cough', dict = 'timetable@gardener@smoking_joint', params = {1.0, -1.0, 2500, 49, 1}},
            },
            ['THROW'] = {
                [1] = {anim = 'putdown_low', dict = 'pickup_object', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['GIVE'] = {
                [1] = {anim = 'givetake1_a', dict = 'mp_common', params = {1.0, -1.0, 1800, 49, 1}},
            },
            ['TAKEOUT'] = {
                [1] = {anim = 'enter', dict = 'amb@world_human_smoking@male@male_a@enter', params = {1.0, -1.0, 1300, 49, 1}},
            },
        },
    },

    ['smoking_greengodjoint'] = { -- ITEM NAME
        Type = 'NEED_LIGHTER', -- NEED_LIGHTER (joints, cigarette, cigars), BONG(bongs), VAPE(Must be topped up with liquid), VAPE-PUFF(It has an adjustable maximum amount of puffs)
        Prop = {Default = 'p_cs_joint_02', Light = 'p_cs_joint_01'}, -- Default is prop before lighting and Light is prop after lighting the cigarette (if you want to use only one prop, remove the Light prop and leave only the Default prop)
        Setting = {
            Size = 100,  --  TYPE : NEED_LIGHTER : set a value that decreases gradually after the item is lit, the larger the number you set, the longer the item can be smoked. 
            Remove = 0.3, Time = 3}, -- The size is subtracted by 0.3 every 3 seconds during smoking, depending on the setting (ONLY FOR: NEED_LIGHTER).
        AfterExhale = {
            ExhaleTime = {min = 1, max = 2}, -- Smoke exhalation time in sec
            RemoveAfterExhale = 5, -- The set value is taken after each exhalation of smoke from the size
            ExhaleEffect = {enable = true, intensity = 40, effect = false}, -- screen blackout effect after smoke exhalation and Animpostfx effect-- https://forge.plebmasters.de/animpostfx
            ShakeCam = {effect = 'VIBRATE_SHAKE', value = 2.0}, -- Shake cam effect after exhale smoke for disable, effect = false
            CoughChance = 2, -- false if you don't want to have any chance of coughing / 1 is a 100% chance, the higher the value you set, the lower the chance will be.
        Status = {
            ['ARMOR'] = 35, -- Adds armor after each puff of smoke
            ['STRESS'] = math.random(5, 10), -- Removes stress after smoke exhalation
            ['HIGH'] = 1, -- Adds a high after exhaling smoke
            }
        },
        Positions = {
            ['HAND'] = { -- Prop position in hand 
                BoneID = 64097,
                Offset = vector3(0.020, 0.02, -0.008),
                Rot = vector3(100.0, 0.0, 100.0),},
            ['MOUTH'] = { -- Prop position in mouth 
                BoneID = 47419,
                Offset = vector3(0.01, -0.01, 0.01),
                Rot = vector3(-2.0, 0.0, 96.0),},
            ['EAR'] = { -- Prop position behind the ear
                BoneID = 12844,
                Offset = vector3(0.05, -0.03, -0.08),
                Rot = vector3(80.0, 7.0, -118.0),},
        },
        Particles = {
            ['Exhale'] = {-- Smoke particles after exhale
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.1,
                Offset = vector3(0.014, -0.003, 0.008),
                Rot = vector3(55.0, 0.0, 110.0),},
            ['Prop'] = {-- Smoke particles on prop 
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.03,
                Offset = vector3(-0.050, 0.0, 0.0),
                Rot = vector3(0.0, 0.0, 0.0),}
        },
        Anims = {-- New animations can be added to each category and are always selected randomly
            ['USE'] = {
                [1] = {anim = 'idle_a', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [2] = {anim = 'idle_b', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [3] = {anim = 'idle_c', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [4] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1300, 49, 1}},
            },
            ['MOUTH'] = {
                [1] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['EAR'] = {
                [1] = {anim = 'takecig_clip', dict = 'takecig@devcore', params = {1.0, -1.0, 1500, 49, 1}},
            },
            ['COUGH'] = {
                [1] = {anim = 'idle_cough', dict = 'timetable@gardener@smoking_joint', params = {1.0, -1.0, 2500, 49, 1}},
            },
            ['THROW'] = {
                [1] = {anim = 'putdown_low', dict = 'pickup_object', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['GIVE'] = {
                [1] = {anim = 'givetake1_a', dict = 'mp_common', params = {1.0, -1.0, 1800, 49, 1}},
            },
            ['TAKEOUT'] = {
                [1] = {anim = 'enter', dict = 'amb@world_human_smoking@male@male_a@enter', params = {1.0, -1.0, 1300, 49, 1}},
            },
        },
    },

    ['smoking_ogsmokingjoint'] = { -- ITEM NAME
        Type = 'NEED_LIGHTER', -- NEED_LIGHTER (joints, cigarette, cigars), BONG(bongs), VAPE(Must be topped up with liquid), VAPE-PUFF(It has an adjustable maximum amount of puffs)
        Prop = {Default = 'p_cs_joint_02', Light = 'p_cs_joint_01'}, -- Default is prop before lighting and Light is prop after lighting the cigarette (if you want to use only one prop, remove the Light prop and leave only the Default prop)
        Setting = {
            Size = 100,  --  TYPE : NEED_LIGHTER : set a value that decreases gradually after the item is lit, the larger the number you set, the longer the item can be smoked. 
            Remove = 0.3, Time = 3}, -- The size is subtracted by 0.3 every 3 seconds during smoking, depending on the setting (ONLY FOR: NEED_LIGHTER).
        AfterExhale = {
            ExhaleTime = {min = 1, max = 2}, -- Smoke exhalation time in sec
            RemoveAfterExhale = 5, -- The set value is taken after each exhalation of smoke from the size
            ExhaleEffect = {enable = true, intensity = 40, effect = false}, -- screen blackout effect after smoke exhalation and Animpostfx effect-- https://forge.plebmasters.de/animpostfx
            ShakeCam = {effect = 'VIBRATE_SHAKE', value = 2.0}, -- Shake cam effect after exhale smoke for disable, effect = false
            CoughChance = 2, -- false if you don't want to have any chance of coughing / 1 is a 100% chance, the higher the value you set, the lower the chance will be.
        Status = {
            ['ARMOR'] = 35, -- Adds armor after each puff of smoke
            ['STRESS'] = math.random(5, 10), -- Removes stress after smoke exhalation
            ['HIGH'] = 1, -- Adds a high after exhaling smoke
            }
        },
        Positions = {
            ['HAND'] = { -- Prop position in hand 
                BoneID = 64097,
                Offset = vector3(0.020, 0.02, -0.008),
                Rot = vector3(100.0, 0.0, 100.0),},
            ['MOUTH'] = { -- Prop position in mouth 
                BoneID = 47419,
                Offset = vector3(0.01, -0.01, 0.01),
                Rot = vector3(-2.0, 0.0, 96.0),},
            ['EAR'] = { -- Prop position behind the ear
                BoneID = 12844,
                Offset = vector3(0.05, -0.03, -0.08),
                Rot = vector3(80.0, 7.0, -118.0),},
        },
        Particles = {
            ['Exhale'] = {-- Smoke particles after exhale
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.1,
                Offset = vector3(0.014, -0.003, 0.008),
                Rot = vector3(55.0, 0.0, 110.0),},
            ['Prop'] = {-- Smoke particles on prop 
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.03,
                Offset = vector3(-0.050, 0.0, 0.0),
                Rot = vector3(0.0, 0.0, 0.0),}
        },
        Anims = {-- New animations can be added to each category and are always selected randomly
            ['USE'] = {
                [1] = {anim = 'idle_a', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [2] = {anim = 'idle_b', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [3] = {anim = 'idle_c', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [4] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1300, 49, 1}},
            },
            ['MOUTH'] = {
                [1] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['EAR'] = {
                [1] = {anim = 'takecig_clip', dict = 'takecig@devcore', params = {1.0, -1.0, 1500, 49, 1}},
            },
            ['COUGH'] = {
                [1] = {anim = 'idle_cough', dict = 'timetable@gardener@smoking_joint', params = {1.0, -1.0, 2500, 49, 1}},
            },
            ['THROW'] = {
                [1] = {anim = 'putdown_low', dict = 'pickup_object', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['GIVE'] = {
                [1] = {anim = 'givetake1_a', dict = 'mp_common', params = {1.0, -1.0, 1800, 49, 1}},
            },
            ['TAKEOUT'] = {
                [1] = {anim = 'enter', dict = 'amb@world_human_smoking@male@male_a@enter', params = {1.0, -1.0, 1300, 49, 1}},
            },
        },
    },

    ['smoking_fossilfueljoint'] = { -- ITEM NAME
        Type = 'NEED_LIGHTER', -- NEED_LIGHTER (joints, cigarette, cigars), BONG(bongs), VAPE(Must be topped up with liquid), VAPE-PUFF(It has an adjustable maximum amount of puffs)
        Prop = {Default = 'p_cs_joint_02', Light = 'p_cs_joint_01'}, -- Default is prop before lighting and Light is prop after lighting the cigarette (if you want to use only one prop, remove the Light prop and leave only the Default prop)
        Setting = {
            Size = 100,  --  TYPE : NEED_LIGHTER : set a value that decreases gradually after the item is lit, the larger the number you set, the longer the item can be smoked. 
            Remove = 0.3, Time = 3}, -- The size is subtracted by 0.3 every 3 seconds during smoking, depending on the setting (ONLY FOR: NEED_LIGHTER).
        AfterExhale = {
            ExhaleTime = {min = 1, max = 2}, -- Smoke exhalation time in sec
            RemoveAfterExhale = 5, -- The set value is taken after each exhalation of smoke from the size
            ExhaleEffect = {enable = true, intensity = 40, effect = false}, -- screen blackout effect after smoke exhalation and Animpostfx effect-- https://forge.plebmasters.de/animpostfx
            ShakeCam = {effect = 'VIBRATE_SHAKE', value = 2.0}, -- Shake cam effect after exhale smoke for disable, effect = false
            CoughChance = 2, -- false if you don't want to have any chance of coughing / 1 is a 100% chance, the higher the value you set, the lower the chance will be.
        Status = {
            ['ARMOR'] = 35, -- Adds armor after each puff of smoke
            ['STRESS'] = math.random(5, 10), -- Removes stress after smoke exhalation
            ['HIGH'] = 1, -- Adds a high after exhaling smoke
            }
        },
        Positions = {
            ['HAND'] = { -- Prop position in hand 
                BoneID = 64097,
                Offset = vector3(0.020, 0.02, -0.008),
                Rot = vector3(100.0, 0.0, 100.0),},
            ['MOUTH'] = { -- Prop position in mouth 
                BoneID = 47419,
                Offset = vector3(0.01, -0.01, 0.01),
                Rot = vector3(-2.0, 0.0, 96.0),},
            ['EAR'] = { -- Prop position behind the ear
                BoneID = 12844,
                Offset = vector3(0.05, -0.03, -0.08),
                Rot = vector3(80.0, 7.0, -118.0),},
        },
        Particles = {
            ['Exhale'] = {-- Smoke particles after exhale
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.1,
                Offset = vector3(0.014, -0.003, 0.008),
                Rot = vector3(55.0, 0.0, 110.0),},
            ['Prop'] = {-- Smoke particles on prop 
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.03,
                Offset = vector3(-0.050, 0.0, 0.0),
                Rot = vector3(0.0, 0.0, 0.0),}
        },
        Anims = {-- New animations can be added to each category and are always selected randomly
            ['USE'] = {
                [1] = {anim = 'idle_a', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [2] = {anim = 'idle_b', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [3] = {anim = 'idle_c', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [4] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1300, 49, 1}},
            },
            ['MOUTH'] = {
                [1] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['EAR'] = {
                [1] = {anim = 'takecig_clip', dict = 'takecig@devcore', params = {1.0, -1.0, 1500, 49, 1}},
            },
            ['COUGH'] = {
                [1] = {anim = 'idle_cough', dict = 'timetable@gardener@smoking_joint', params = {1.0, -1.0, 2500, 49, 1}},
            },
            ['THROW'] = {
                [1] = {anim = 'putdown_low', dict = 'pickup_object', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['GIVE'] = {
                [1] = {anim = 'givetake1_a', dict = 'mp_common', params = {1.0, -1.0, 1800, 49, 1}},
            },
            ['TAKEOUT'] = {
                [1] = {anim = 'enter', dict = 'amb@world_human_smoking@male@male_a@enter', params = {1.0, -1.0, 1300, 49, 1}},
            },
        },
    },

    ['smoking_afterlifejoint'] = { -- ITEM NAME
        Type = 'NEED_LIGHTER', -- NEED_LIGHTER (joints, cigarette, cigars), BONG(bongs), VAPE(Must be topped up with liquid), VAPE-PUFF(It has an adjustable maximum amount of puffs)
        Prop = {Default = 'p_cs_joint_02', Light = 'p_cs_joint_01'}, -- Default is prop before lighting and Light is prop after lighting the cigarette (if you want to use only one prop, remove the Light prop and leave only the Default prop)
        Setting = {
            Size = 100,  --  TYPE : NEED_LIGHTER : set a value that decreases gradually after the item is lit, the larger the number you set, the longer the item can be smoked. 
            Remove = 0.3, Time = 3}, -- The size is subtracted by 0.3 every 3 seconds during smoking, depending on the setting (ONLY FOR: NEED_LIGHTER).
        AfterExhale = {
            ExhaleTime = {min = 1, max = 2}, -- Smoke exhalation time in sec
            RemoveAfterExhale = 5, -- The set value is taken after each exhalation of smoke from the size
            ExhaleEffect = {enable = true, intensity = 40, effect = false}, -- screen blackout effect after smoke exhalation and Animpostfx effect-- https://forge.plebmasters.de/animpostfx
            ShakeCam = {effect = 'VIBRATE_SHAKE', value = 2.0}, -- Shake cam effect after exhale smoke for disable, effect = false
            CoughChance = 2, -- false if you don't want to have any chance of coughing / 1 is a 100% chance, the higher the value you set, the lower the chance will be.
        Status = {
            ['ARMOR'] = 35, -- Adds armor after each puff of smoke
            ['STRESS'] = math.random(5, 10), -- Removes stress after smoke exhalation
            ['HIGH'] = 1, -- Adds a high after exhaling smoke
            }
        },
        Positions = {
            ['HAND'] = { -- Prop position in hand 
                BoneID = 64097,
                Offset = vector3(0.020, 0.02, -0.008),
                Rot = vector3(100.0, 0.0, 100.0),},
            ['MOUTH'] = { -- Prop position in mouth 
                BoneID = 47419,
                Offset = vector3(0.01, -0.01, 0.01),
                Rot = vector3(-2.0, 0.0, 96.0),},
            ['EAR'] = { -- Prop position behind the ear
                BoneID = 12844,
                Offset = vector3(0.05, -0.03, -0.08),
                Rot = vector3(80.0, 7.0, -118.0),},
        },
        Particles = {
            ['Exhale'] = {-- Smoke particles after exhale
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.1,
                Offset = vector3(0.014, -0.003, 0.008),
                Rot = vector3(55.0, 0.0, 110.0),},
            ['Prop'] = {-- Smoke particles on prop 
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.03,
                Offset = vector3(-0.050, 0.0, 0.0),
                Rot = vector3(0.0, 0.0, 0.0),}
        },
        Anims = {-- New animations can be added to each category and are always selected randomly
            ['USE'] = {
                [1] = {anim = 'idle_a', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [2] = {anim = 'idle_b', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [3] = {anim = 'idle_c', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [4] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1300, 49, 1}},
            },
            ['MOUTH'] = {
                [1] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['EAR'] = {
                [1] = {anim = 'takecig_clip', dict = 'takecig@devcore', params = {1.0, -1.0, 1500, 49, 1}},
            },
            ['COUGH'] = {
                [1] = {anim = 'idle_cough', dict = 'timetable@gardener@smoking_joint', params = {1.0, -1.0, 2500, 49, 1}},
            },
            ['THROW'] = {
                [1] = {anim = 'putdown_low', dict = 'pickup_object', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['GIVE'] = {
                [1] = {anim = 'givetake1_a', dict = 'mp_common', params = {1.0, -1.0, 1800, 49, 1}},
            },
            ['TAKEOUT'] = {
                [1] = {anim = 'enter', dict = 'amb@world_human_smoking@male@male_a@enter', params = {1.0, -1.0, 1300, 49, 1}},
            },
        },
    },

    ['smoking_pineappleexpressjoint'] = { -- ITEM NAME
        Type = 'NEED_LIGHTER', -- NEED_LIGHTER (joints, cigarette, cigars), BONG(bongs), VAPE(Must be topped up with liquid), VAPE-PUFF(It has an adjustable maximum amount of puffs)
        Prop = {Default = 'p_cs_joint_02', Light = 'p_cs_joint_01'}, -- Default is prop before lighting and Light is prop after lighting the cigarette (if you want to use only one prop, remove the Light prop and leave only the Default prop)
        Setting = {
            Size = 100,  --  TYPE : NEED_LIGHTER : set a value that decreases gradually after the item is lit, the larger the number you set, the longer the item can be smoked. 
            Remove = 0.3, Time = 3}, -- The size is subtracted by 0.3 every 3 seconds during smoking, depending on the setting (ONLY FOR: NEED_LIGHTER).
        AfterExhale = {
            ExhaleTime = {min = 1, max = 2}, -- Smoke exhalation time in sec
            RemoveAfterExhale = 5, -- The set value is taken after each exhalation of smoke from the size
            ExhaleEffect = {enable = true, intensity = 40, effect = false}, -- screen blackout effect after smoke exhalation and Animpostfx effect-- https://forge.plebmasters.de/animpostfx
            ShakeCam = {effect = 'VIBRATE_SHAKE', value = 2.0}, -- Shake cam effect after exhale smoke for disable, effect = false
            CoughChance = 2, -- false if you don't want to have any chance of coughing / 1 is a 100% chance, the higher the value you set, the lower the chance will be.
        Status = {
            ['ARMOR'] = 35, -- Adds armor after each puff of smoke
            ['STRESS'] = math.random(5, 10), -- Removes stress after smoke exhalation
            ['HIGH'] = 1, -- Adds a high after exhaling smoke
            }
        },
        Positions = {
            ['HAND'] = { -- Prop position in hand 
                BoneID = 64097,
                Offset = vector3(0.020, 0.02, -0.008),
                Rot = vector3(100.0, 0.0, 100.0),},
            ['MOUTH'] = { -- Prop position in mouth 
                BoneID = 47419,
                Offset = vector3(0.01, -0.01, 0.01),
                Rot = vector3(-2.0, 0.0, 96.0),},
            ['EAR'] = { -- Prop position behind the ear
                BoneID = 12844,
                Offset = vector3(0.05, -0.03, -0.08),
                Rot = vector3(80.0, 7.0, -118.0),},
        },
        Particles = {
            ['Exhale'] = {-- Smoke particles after exhale
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.1,
                Offset = vector3(0.014, -0.003, 0.008),
                Rot = vector3(55.0, 0.0, 110.0),},
            ['Prop'] = {-- Smoke particles on prop 
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.03,
                Offset = vector3(-0.050, 0.0, 0.0),
                Rot = vector3(0.0, 0.0, 0.0),}
        },
        Anims = {-- New animations can be added to each category and are always selected randomly
            ['USE'] = {
                [1] = {anim = 'idle_a', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [2] = {anim = 'idle_b', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [3] = {anim = 'idle_c', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [4] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1300, 49, 1}},
            },
            ['MOUTH'] = {
                [1] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['EAR'] = {
                [1] = {anim = 'takecig_clip', dict = 'takecig@devcore', params = {1.0, -1.0, 1500, 49, 1}},
            },
            ['COUGH'] = {
                [1] = {anim = 'idle_cough', dict = 'timetable@gardener@smoking_joint', params = {1.0, -1.0, 2500, 49, 1}},
            },
            ['THROW'] = {
                [1] = {anim = 'putdown_low', dict = 'pickup_object', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['GIVE'] = {
                [1] = {anim = 'givetake1_a', dict = 'mp_common', params = {1.0, -1.0, 1800, 49, 1}},
            },
            ['TAKEOUT'] = {
                [1] = {anim = 'enter', dict = 'amb@world_human_smoking@male@male_a@enter', params = {1.0, -1.0, 1300, 49, 1}},
            },
        },
    },
    --------------------------- END COSMIC
    ['afghankush_joint'] = { -- ITEM NAME
        Type = 'NEED_LIGHTER', -- NEED_LIGHTER (joints, cigarette, cigars), BONG(bongs), VAPE(Must be topped up with liquid), VAPE-PUFF(It has an adjustable maximum amount of puffs)
        Prop = {Default = 'p_cs_joint_02', Light = 'p_cs_joint_01'}, -- Default is prop before lighting and Light is prop after lighting the cigarette (if you want to use only one prop, remove the Light prop and leave only the Default prop)
        Setting = {
            Size = 100,  --  TYPE : NEED_LIGHTER : set a value that decreases gradually after the item is lit, the larger the number you set, the longer the item can be smoked. 
            Remove = 0.3, Time = 3}, -- The size is subtracted by 0.3 every 3 seconds during smoking, depending on the setting (ONLY FOR: NEED_LIGHTER).
        AfterExhale = {
            ExhaleTime = {min = 1, max = 2}, -- Smoke exhalation time in sec
            RemoveAfterExhale = 5, -- The set value is taken after each exhalation of smoke from the size
            ExhaleEffect = {enable = true, intensity = 40, effect = false}, -- screen blackout effect after smoke exhalation and Animpostfx effect-- https://forge.plebmasters.de/animpostfx
            ShakeCam = {effect = 'VIBRATE_SHAKE', value = 2.0}, -- Shake cam effect after exhale smoke for disable, effect = false
            CoughChance = 2, -- false if you don't want to have any chance of coughing / 1 is a 100% chance, the higher the value you set, the lower the chance will be.
        Status = {
            ['ARMOR'] = 35, -- Adds armor after each puff of smoke
            ['STRESS'] = math.random(5, 10), -- Removes stress after smoke exhalation
            ['HIGH'] = 1, -- Adds a high after exhaling smoke
            }
        },
        Positions = {
            ['HAND'] = { -- Prop position in hand 
                BoneID = 64097,
                Offset = vector3(0.020, 0.02, -0.008),
                Rot = vector3(100.0, 0.0, 100.0),},
            ['MOUTH'] = { -- Prop position in mouth 
                BoneID = 47419,
                Offset = vector3(0.01, -0.01, 0.01),
                Rot = vector3(-2.0, 0.0, 96.0),},
            ['EAR'] = { -- Prop position behind the ear
                BoneID = 12844,
                Offset = vector3(0.05, -0.03, -0.08),
                Rot = vector3(80.0, 7.0, -118.0),},
        },
        Particles = {
            ['Exhale'] = {-- Smoke particles after exhale
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.1,
                Offset = vector3(0.014, -0.003, 0.008),
                Rot = vector3(55.0, 0.0, 110.0),},
            ['Prop'] = {-- Smoke particles on prop 
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.03,
                Offset = vector3(-0.050, 0.0, 0.0),
                Rot = vector3(0.0, 0.0, 0.0),}
        },
        Anims = {-- New animations can be added to each category and are always selected randomly
            ['USE'] = {
                [1] = {anim = 'idle_a', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [2] = {anim = 'idle_b', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [3] = {anim = 'idle_c', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [4] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1300, 49, 1}},
            },
            ['MOUTH'] = {
                [1] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['EAR'] = {
                [1] = {anim = 'takecig_clip', dict = 'takecig@devcore', params = {1.0, -1.0, 1500, 49, 1}},
            },
            ['COUGH'] = {
                [1] = {anim = 'idle_cough', dict = 'timetable@gardener@smoking_joint', params = {1.0, -1.0, 2500, 49, 1}},
            },
            ['THROW'] = {
                [1] = {anim = 'putdown_low', dict = 'pickup_object', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['GIVE'] = {
                [1] = {anim = 'givetake1_a', dict = 'mp_common', params = {1.0, -1.0, 1800, 49, 1}},
            },
            ['TAKEOUT'] = {
                [1] = {anim = 'enter', dict = 'amb@world_human_smoking@male@male_a@enter', params = {1.0, -1.0, 1300, 49, 1}},
            },
        },
    },

    ['bluedream_joint'] = { -- ITEM NAME
        Type = 'NEED_LIGHTER', -- NEED_LIGHTER (joints, cigarette, cigars), BONG(bongs), VAPE(Must be topped up with liquid), VAPE-PUFF(It has an adjustable maximum amount of puffs)
        Prop = {Default = 'p_cs_joint_02', Light = 'p_cs_joint_01'}, -- Default is prop before lighting and Light is prop after lighting the cigarette (if you want to use only one prop, remove the Light prop and leave only the Default prop)
        Setting = {
            Size = 100,  --  TYPE : NEED_LIGHTER : set a value that decreases gradually after the item is lit, the larger the number you set, the longer the item can be smoked. 
            Remove = 0.3, Time = 3}, -- The size is subtracted by 0.3 every 3 seconds during smoking, depending on the setting (ONLY FOR: NEED_LIGHTER).
        AfterExhale = {
            ExhaleTime = {min = 1, max = 2}, -- Smoke exhalation time in sec
            RemoveAfterExhale = 5, -- The set value is taken after each exhalation of smoke from the size
            ExhaleEffect = {enable = true, intensity = 40, effect = false}, -- screen blackout effect after smoke exhalation and Animpostfx effect-- https://forge.plebmasters.de/animpostfx
            ShakeCam = {effect = 'VIBRATE_SHAKE', value = 2.0}, -- Shake cam effect after exhale smoke for disable, effect = false
            CoughChance = 2, -- false if you don't want to have any chance of coughing / 1 is a 100% chance, the higher the value you set, the lower the chance will be.
        Status = {
            ['ARMOR'] = 35, -- Adds armor after each puff of smoke
            ['STRESS'] = math.random(5, 10), -- Removes stress after smoke exhalation
            ['HIGH'] = 1, -- Adds a high after exhaling smoke
            }
        },
        Positions = {
            ['HAND'] = { -- Prop position in hand 
                BoneID = 64097,
                Offset = vector3(0.020, 0.02, -0.008),
                Rot = vector3(100.0, 0.0, 100.0),},
            ['MOUTH'] = { -- Prop position in mouth 
                BoneID = 47419,
                Offset = vector3(0.01, -0.01, 0.01),
                Rot = vector3(-2.0, 0.0, 96.0),},
            ['EAR'] = { -- Prop position behind the ear
                BoneID = 12844,
                Offset = vector3(0.05, -0.03, -0.08),
                Rot = vector3(80.0, 7.0, -118.0),},
        },
        Particles = {
            ['Exhale'] = {-- Smoke particles after exhale
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.1,
                Offset = vector3(0.014, -0.003, 0.008),
                Rot = vector3(55.0, 0.0, 110.0),},
            ['Prop'] = {-- Smoke particles on prop 
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.03,
                Offset = vector3(-0.050, 0.0, 0.0),
                Rot = vector3(0.0, 0.0, 0.0),}
        },
        Anims = {-- New animations can be added to each category and are always selected randomly
            ['USE'] = {
                [1] = {anim = 'idle_a', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [2] = {anim = 'idle_b', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [3] = {anim = 'idle_c', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [4] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1300, 49, 1}},
            },
            ['MOUTH'] = {
                [1] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['EAR'] = {
                [1] = {anim = 'takecig_clip', dict = 'takecig@devcore', params = {1.0, -1.0, 1500, 49, 1}},
            },
            ['COUGH'] = {
                [1] = {anim = 'idle_cough', dict = 'timetable@gardener@smoking_joint', params = {1.0, -1.0, 2500, 49, 1}},
            },
            ['THROW'] = {
                [1] = {anim = 'putdown_low', dict = 'pickup_object', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['GIVE'] = {
                [1] = {anim = 'givetake1_a', dict = 'mp_common', params = {1.0, -1.0, 1800, 49, 1}},
            },
            ['TAKEOUT'] = {
                [1] = {anim = 'enter', dict = 'amb@world_human_smoking@male@male_a@enter', params = {1.0, -1.0, 1300, 49, 1}},
            },
        },
    },

    ['granddaddypurple_joint'] = { -- ITEM NAME
        Type = 'NEED_LIGHTER', -- NEED_LIGHTER (joints, cigarette, cigars), BONG(bongs), VAPE(Must be topped up with liquid), VAPE-PUFF(It has an adjustable maximum amount of puffs)
        Prop = {Default = 'p_cs_joint_02', Light = 'p_cs_joint_01'}, -- Default is prop before lighting and Light is prop after lighting the cigarette (if you want to use only one prop, remove the Light prop and leave only the Default prop)
        Setting = {
            Size = 100,  --  TYPE : NEED_LIGHTER : set a value that decreases gradually after the item is lit, the larger the number you set, the longer the item can be smoked. 
            Remove = 0.3, Time = 3}, -- The size is subtracted by 0.3 every 3 seconds during smoking, depending on the setting (ONLY FOR: NEED_LIGHTER).
        AfterExhale = {
            ExhaleTime = {min = 1, max = 2}, -- Smoke exhalation time in sec
            RemoveAfterExhale = 5, -- The set value is taken after each exhalation of smoke from the size
            ExhaleEffect = {enable = true, intensity = 40, effect = false}, -- screen blackout effect after smoke exhalation and Animpostfx effect-- https://forge.plebmasters.de/animpostfx
            ShakeCam = {effect = 'VIBRATE_SHAKE', value = 2.0}, -- Shake cam effect after exhale smoke for disable, effect = false
            CoughChance = 2, -- false if you don't want to have any chance of coughing / 1 is a 100% chance, the higher the value you set, the lower the chance will be.
        Status = {
            ['ARMOR'] = 35, -- Adds armor after each puff of smoke
            ['STRESS'] = math.random(5, 10), -- Removes stress after smoke exhalation
            ['HIGH'] = 1, -- Adds a high after exhaling smoke
            }
        },
        Positions = {
            ['HAND'] = { -- Prop position in hand 
                BoneID = 64097,
                Offset = vector3(0.020, 0.02, -0.008),
                Rot = vector3(100.0, 0.0, 100.0),},
            ['MOUTH'] = { -- Prop position in mouth 
                BoneID = 47419,
                Offset = vector3(0.01, -0.01, 0.01),
                Rot = vector3(-2.0, 0.0, 96.0),},
            ['EAR'] = { -- Prop position behind the ear
                BoneID = 12844,
                Offset = vector3(0.05, -0.03, -0.08),
                Rot = vector3(80.0, 7.0, -118.0),},
        },
        Particles = {
            ['Exhale'] = {-- Smoke particles after exhale
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.1,
                Offset = vector3(0.014, -0.003, 0.008),
                Rot = vector3(55.0, 0.0, 110.0),},
            ['Prop'] = {-- Smoke particles on prop 
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.03,
                Offset = vector3(-0.050, 0.0, 0.0),
                Rot = vector3(0.0, 0.0, 0.0),}
        },
        Anims = {-- New animations can be added to each category and are always selected randomly
            ['USE'] = {
                [1] = {anim = 'idle_a', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [2] = {anim = 'idle_b', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [3] = {anim = 'idle_c', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [4] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1300, 49, 1}},
            },
            ['MOUTH'] = {
                [1] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['EAR'] = {
                [1] = {anim = 'takecig_clip', dict = 'takecig@devcore', params = {1.0, -1.0, 1500, 49, 1}},
            },
            ['COUGH'] = {
                [1] = {anim = 'idle_cough', dict = 'timetable@gardener@smoking_joint', params = {1.0, -1.0, 2500, 49, 1}},
            },
            ['THROW'] = {
                [1] = {anim = 'putdown_low', dict = 'pickup_object', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['GIVE'] = {
                [1] = {anim = 'givetake1_a', dict = 'mp_common', params = {1.0, -1.0, 1800, 49, 1}},
            },
            ['TAKEOUT'] = {
                [1] = {anim = 'enter', dict = 'amb@world_human_smoking@male@male_a@enter', params = {1.0, -1.0, 1300, 49, 1}},
            },
        },
    },

    ['greencrack_joint'] = { -- ITEM NAME
        Type = 'NEED_LIGHTER', -- NEED_LIGHTER (joints, cigarette, cigars), BONG(bongs), VAPE(Must be topped up with liquid), VAPE-PUFF(It has an adjustable maximum amount of puffs)
        Prop = {Default = 'p_cs_joint_02', Light = 'p_cs_joint_01'}, -- Default is prop before lighting and Light is prop after lighting the cigarette (if you want to use only one prop, remove the Light prop and leave only the Default prop)
        Setting = {
            Size = 100,  --  TYPE : NEED_LIGHTER : set a value that decreases gradually after the item is lit, the larger the number you set, the longer the item can be smoked. 
            Remove = 0.3, Time = 3}, -- The size is subtracted by 0.3 every 3 seconds during smoking, depending on the setting (ONLY FOR: NEED_LIGHTER).
        AfterExhale = {
            ExhaleTime = {min = 1, max = 2}, -- Smoke exhalation time in sec
            RemoveAfterExhale = 5, -- The set value is taken after each exhalation of smoke from the size
            ExhaleEffect = {enable = true, intensity = 40, effect = false}, -- screen blackout effect after smoke exhalation and Animpostfx effect-- https://forge.plebmasters.de/animpostfx
            ShakeCam = {effect = 'VIBRATE_SHAKE', value = 2.0}, -- Shake cam effect after exhale smoke for disable, effect = false
            CoughChance = 2, -- false if you don't want to have any chance of coughing / 1 is a 100% chance, the higher the value you set, the lower the chance will be.
        Status = {
            ['ARMOR'] = 35, -- Adds armor after each puff of smoke
            ['STRESS'] = math.random(5, 10), -- Removes stress after smoke exhalation
            ['HIGH'] = 1, -- Adds a high after exhaling smoke
            }
        },
        Positions = {
            ['HAND'] = { -- Prop position in hand 
                BoneID = 64097,
                Offset = vector3(0.020, 0.02, -0.008),
                Rot = vector3(100.0, 0.0, 100.0),},
            ['MOUTH'] = { -- Prop position in mouth 
                BoneID = 47419,
                Offset = vector3(0.01, -0.01, 0.01),
                Rot = vector3(-2.0, 0.0, 96.0),},
            ['EAR'] = { -- Prop position behind the ear
                BoneID = 12844,
                Offset = vector3(0.05, -0.03, -0.08),
                Rot = vector3(80.0, 7.0, -118.0),},
        },
        Particles = {
            ['Exhale'] = {-- Smoke particles after exhale
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.1,
                Offset = vector3(0.014, -0.003, 0.008),
                Rot = vector3(55.0, 0.0, 110.0),},
            ['Prop'] = {-- Smoke particles on prop 
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.03,
                Offset = vector3(-0.050, 0.0, 0.0),
                Rot = vector3(0.0, 0.0, 0.0),}
        },
        Anims = {-- New animations can be added to each category and are always selected randomly
            ['USE'] = {
                [1] = {anim = 'idle_a', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [2] = {anim = 'idle_b', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [3] = {anim = 'idle_c', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [4] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1300, 49, 1}},
            },
            ['MOUTH'] = {
                [1] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['EAR'] = {
                [1] = {anim = 'takecig_clip', dict = 'takecig@devcore', params = {1.0, -1.0, 1500, 49, 1}},
            },
            ['COUGH'] = {
                [1] = {anim = 'idle_cough', dict = 'timetable@gardener@smoking_joint', params = {1.0, -1.0, 2500, 49, 1}},
            },
            ['THROW'] = {
                [1] = {anim = 'putdown_low', dict = 'pickup_object', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['GIVE'] = {
                [1] = {anim = 'givetake1_a', dict = 'mp_common', params = {1.0, -1.0, 1800, 49, 1}},
            },
            ['TAKEOUT'] = {
                [1] = {anim = 'enter', dict = 'amb@world_human_smoking@male@male_a@enter', params = {1.0, -1.0, 1300, 49, 1}},
            },
        },
    },

    ['jackherrer_joint'] = { -- ITEM NAME
        Type = 'NEED_LIGHTER', -- NEED_LIGHTER (joints, cigarette, cigars), BONG(bongs), VAPE(Must be topped up with liquid), VAPE-PUFF(It has an adjustable maximum amount of puffs)
        Prop = {Default = 'p_cs_joint_02', Light = 'p_cs_joint_01'}, -- Default is prop before lighting and Light is prop after lighting the cigarette (if you want to use only one prop, remove the Light prop and leave only the Default prop)
        Setting = {
            Size = 100,  --  TYPE : NEED_LIGHTER : set a value that decreases gradually after the item is lit, the larger the number you set, the longer the item can be smoked. 
            Remove = 0.3, Time = 3}, -- The size is subtracted by 0.3 every 3 seconds during smoking, depending on the setting (ONLY FOR: NEED_LIGHTER).
        AfterExhale = {
            ExhaleTime = {min = 1, max = 2}, -- Smoke exhalation time in sec
            RemoveAfterExhale = 5, -- The set value is taken after each exhalation of smoke from the size
            ExhaleEffect = {enable = true, intensity = 40, effect = false}, -- screen blackout effect after smoke exhalation and Animpostfx effect-- https://forge.plebmasters.de/animpostfx
            ShakeCam = {effect = 'VIBRATE_SHAKE', value = 2.0}, -- Shake cam effect after exhale smoke for disable, effect = false
            CoughChance = 2, -- false if you don't want to have any chance of coughing / 1 is a 100% chance, the higher the value you set, the lower the chance will be.
        Status = {
            ['ARMOR'] = 35, -- Adds armor after each puff of smoke
            ['STRESS'] = math.random(5, 10), -- Removes stress after smoke exhalation
            ['HIGH'] = 1, -- Adds a high after exhaling smoke
            }
        },
        Positions = {
            ['HAND'] = { -- Prop position in hand 
                BoneID = 64097,
                Offset = vector3(0.020, 0.02, -0.008),
                Rot = vector3(100.0, 0.0, 100.0),},
            ['MOUTH'] = { -- Prop position in mouth 
                BoneID = 47419,
                Offset = vector3(0.01, -0.01, 0.01),
                Rot = vector3(-2.0, 0.0, 96.0),},
            ['EAR'] = { -- Prop position behind the ear
                BoneID = 12844,
                Offset = vector3(0.05, -0.03, -0.08),
                Rot = vector3(80.0, 7.0, -118.0),},
        },
        Particles = {
            ['Exhale'] = {-- Smoke particles after exhale
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.1,
                Offset = vector3(0.014, -0.003, 0.008),
                Rot = vector3(55.0, 0.0, 110.0),},
            ['Prop'] = {-- Smoke particles on prop 
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.03,
                Offset = vector3(-0.050, 0.0, 0.0),
                Rot = vector3(0.0, 0.0, 0.0),}
        },
        Anims = {-- New animations can be added to each category and are always selected randomly
            ['USE'] = {
                [1] = {anim = 'idle_a', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [2] = {anim = 'idle_b', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [3] = {anim = 'idle_c', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [4] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1300, 49, 1}},
            },
            ['MOUTH'] = {
                [1] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['EAR'] = {
                [1] = {anim = 'takecig_clip', dict = 'takecig@devcore', params = {1.0, -1.0, 1500, 49, 1}},
            },
            ['COUGH'] = {
                [1] = {anim = 'idle_cough', dict = 'timetable@gardener@smoking_joint', params = {1.0, -1.0, 2500, 49, 1}},
            },
            ['THROW'] = {
                [1] = {anim = 'putdown_low', dict = 'pickup_object', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['GIVE'] = {
                [1] = {anim = 'givetake1_a', dict = 'mp_common', params = {1.0, -1.0, 1800, 49, 1}},
            },
            ['TAKEOUT'] = {
                [1] = {anim = 'enter', dict = 'amb@world_human_smoking@male@male_a@enter', params = {1.0, -1.0, 1300, 49, 1}},
            },
        },
    },

    ['sourdiesel_joint'] = { -- ITEM NAME
        Type = 'NEED_LIGHTER', -- NEED_LIGHTER (joints, cigarette, cigars), BONG(bongs), VAPE(Must be topped up with liquid), VAPE-PUFF(It has an adjustable maximum amount of puffs)
        Prop = {Default = 'p_cs_joint_02', Light = 'p_cs_joint_01'}, -- Default is prop before lighting and Light is prop after lighting the cigarette (if you want to use only one prop, remove the Light prop and leave only the Default prop)
        Setting = {
            Size = 100,  --  TYPE : NEED_LIGHTER : set a value that decreases gradually after the item is lit, the larger the number you set, the longer the item can be smoked. 
            Remove = 0.3, Time = 3}, -- The size is subtracted by 0.3 every 3 seconds during smoking, depending on the setting (ONLY FOR: NEED_LIGHTER).
        AfterExhale = {
            ExhaleTime = {min = 1, max = 2}, -- Smoke exhalation time in sec
            RemoveAfterExhale = 5, -- The set value is taken after each exhalation of smoke from the size
            ExhaleEffect = {enable = true, intensity = 40, effect = false}, -- screen blackout effect after smoke exhalation and Animpostfx effect-- https://forge.plebmasters.de/animpostfx
            ShakeCam = {effect = 'VIBRATE_SHAKE', value = 2.0}, -- Shake cam effect after exhale smoke for disable, effect = false
            CoughChance = 2, -- false if you don't want to have any chance of coughing / 1 is a 100% chance, the higher the value you set, the lower the chance will be.
        Status = {
            ['ARMOR'] = 35, -- Adds armor after each puff of smoke
            ['STRESS'] = math.random(5, 10), -- Removes stress after smoke exhalation
            ['HIGH'] = 1, -- Adds a high after exhaling smoke
            }
        },
        Positions = {
            ['HAND'] = { -- Prop position in hand 
                BoneID = 64097,
                Offset = vector3(0.020, 0.02, -0.008),
                Rot = vector3(100.0, 0.0, 100.0),},
            ['MOUTH'] = { -- Prop position in mouth 
                BoneID = 47419,
                Offset = vector3(0.01, -0.01, 0.01),
                Rot = vector3(-2.0, 0.0, 96.0),},
            ['EAR'] = { -- Prop position behind the ear
                BoneID = 12844,
                Offset = vector3(0.05, -0.03, -0.08),
                Rot = vector3(80.0, 7.0, -118.0),},
        },
        Particles = {
            ['Exhale'] = {-- Smoke particles after exhale
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.1,
                Offset = vector3(0.014, -0.003, 0.008),
                Rot = vector3(55.0, 0.0, 110.0),},
            ['Prop'] = {-- Smoke particles on prop 
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.03,
                Offset = vector3(-0.050, 0.0, 0.0),
                Rot = vector3(0.0, 0.0, 0.0),}
        },
        Anims = {-- New animations can be added to each category and are always selected randomly
            ['USE'] = {
                [1] = {anim = 'idle_a', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [2] = {anim = 'idle_b', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [3] = {anim = 'idle_c', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [4] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1300, 49, 1}},
            },
            ['MOUTH'] = {
                [1] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['EAR'] = {
                [1] = {anim = 'takecig_clip', dict = 'takecig@devcore', params = {1.0, -1.0, 1500, 49, 1}},
            },
            ['COUGH'] = {
                [1] = {anim = 'idle_cough', dict = 'timetable@gardener@smoking_joint', params = {1.0, -1.0, 2500, 49, 1}},
            },
            ['THROW'] = {
                [1] = {anim = 'putdown_low', dict = 'pickup_object', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['GIVE'] = {
                [1] = {anim = 'givetake1_a', dict = 'mp_common', params = {1.0, -1.0, 1800, 49, 1}},
            },
            ['TAKEOUT'] = {
                [1] = {anim = 'enter', dict = 'amb@world_human_smoking@male@male_a@enter', params = {1.0, -1.0, 1300, 49, 1}},
            },
        },
    },

    ['weddingcake_joint'] = { -- ITEM NAME
        Type = 'NEED_LIGHTER', -- NEED_LIGHTER (joints, cigarette, cigars), BONG(bongs), VAPE(Must be topped up with liquid), VAPE-PUFF(It has an adjustable maximum amount of puffs)
        Prop = {Default = 'p_cs_joint_02', Light = 'p_cs_joint_01'}, -- Default is prop before lighting and Light is prop after lighting the cigarette (if you want to use only one prop, remove the Light prop and leave only the Default prop)
        Setting = {
            Size = 100,  --  TYPE : NEED_LIGHTER : set a value that decreases gradually after the item is lit, the larger the number you set, the longer the item can be smoked. 
            Remove = 0.3, Time = 3}, -- The size is subtracted by 0.3 every 3 seconds during smoking, depending on the setting (ONLY FOR: NEED_LIGHTER).
        AfterExhale = {
            ExhaleTime = {min = 1, max = 2}, -- Smoke exhalation time in sec
            RemoveAfterExhale = 5, -- The set value is taken after each exhalation of smoke from the size
            ExhaleEffect = {enable = true, intensity = 40, effect = false}, -- screen blackout effect after smoke exhalation and Animpostfx effect-- https://forge.plebmasters.de/animpostfx
            ShakeCam = {effect = 'VIBRATE_SHAKE', value = 2.0}, -- Shake cam effect after exhale smoke for disable, effect = false
            CoughChance = 2, -- false if you don't want to have any chance of coughing / 1 is a 100% chance, the higher the value you set, the lower the chance will be.
        Status = {
            ['ARMOR'] = 35, -- Adds armor after each puff of smoke
            ['STRESS'] = math.random(5, 10), -- Removes stress after smoke exhalation
            ['HIGH'] = 1, -- Adds a high after exhaling smoke
            }
        },
        Positions = {
            ['HAND'] = { -- Prop position in hand 
                BoneID = 64097,
                Offset = vector3(0.020, 0.02, -0.008),
                Rot = vector3(100.0, 0.0, 100.0),},
            ['MOUTH'] = { -- Prop position in mouth 
                BoneID = 47419,
                Offset = vector3(0.01, -0.01, 0.01),
                Rot = vector3(-2.0, 0.0, 96.0),},
            ['EAR'] = { -- Prop position behind the ear
                BoneID = 12844,
                Offset = vector3(0.05, -0.03, -0.08),
                Rot = vector3(80.0, 7.0, -118.0),},
        },
        Particles = {
            ['Exhale'] = {-- Smoke particles after exhale
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.1,
                Offset = vector3(0.014, -0.003, 0.008),
                Rot = vector3(55.0, 0.0, 110.0),},
            ['Prop'] = {-- Smoke particles on prop 
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.03,
                Offset = vector3(-0.050, 0.0, 0.0),
                Rot = vector3(0.0, 0.0, 0.0),}
        },
        Anims = {-- New animations can be added to each category and are always selected randomly
            ['USE'] = {
                [1] = {anim = 'idle_a', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [2] = {anim = 'idle_b', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [3] = {anim = 'idle_c', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [4] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1300, 49, 1}},
            },
            ['MOUTH'] = {
                [1] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['EAR'] = {
                [1] = {anim = 'takecig_clip', dict = 'takecig@devcore', params = {1.0, -1.0, 1500, 49, 1}},
            },
            ['COUGH'] = {
                [1] = {anim = 'idle_cough', dict = 'timetable@gardener@smoking_joint', params = {1.0, -1.0, 2500, 49, 1}},
            },
            ['THROW'] = {
                [1] = {anim = 'putdown_low', dict = 'pickup_object', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['GIVE'] = {
                [1] = {anim = 'givetake1_a', dict = 'mp_common', params = {1.0, -1.0, 1800, 49, 1}},
            },
            ['TAKEOUT'] = {
                [1] = {anim = 'enter', dict = 'amb@world_human_smoking@male@male_a@enter', params = {1.0, -1.0, 1300, 49, 1}},
            },
        },
    },

    ['ppp_joint'] = { -- ITEM NAME
        Type = 'NEED_LIGHTER', -- NEED_LIGHTER (joints, cigarette, cigars), BONG(bongs), VAPE(Must be topped up with liquid), VAPE-PUFF(It has an adjustable maximum amount of puffs)
        Prop = {Default = 'p_cs_joint_02', Light = 'p_cs_joint_01'}, -- Default is prop before lighting and Light is prop after lighting the cigarette (if you want to use only one prop, remove the Light prop and leave only the Default prop)
        Setting = {
            Size = 100,  --  TYPE : NEED_LIGHTER : set a value that decreases gradually after the item is lit, the larger the number you set, the longer the item can be smoked. 
            Remove = 0.3, Time = 3}, -- The size is subtracted by 0.3 every 3 seconds during smoking, depending on the setting (ONLY FOR: NEED_LIGHTER).
        AfterExhale = {
            ExhaleTime = {min = 1, max = 2}, -- Smoke exhalation time in sec
            RemoveAfterExhale = 5, -- The set value is taken after each exhalation of smoke from the size
            ExhaleEffect = {enable = true, intensity = 40, effect = false}, -- screen blackout effect after smoke exhalation and Animpostfx effect-- https://forge.plebmasters.de/animpostfx
            ShakeCam = {effect = 'VIBRATE_SHAKE', value = 2.0}, -- Shake cam effect after exhale smoke for disable, effect = false
            CoughChance = 2, -- false if you don't want to have any chance of coughing / 1 is a 100% chance, the higher the value you set, the lower the chance will be.
        Status = {
            ['ARMOR'] = 35, -- Adds armor after each puff of smoke
            ['STRESS'] = math.random(5, 10), -- Removes stress after smoke exhalation
            ['HIGH'] = 1, -- Adds a high after exhaling smoke
            }
        },
        Positions = {
            ['HAND'] = { -- Prop position in hand 
                BoneID = 64097,
                Offset = vector3(0.020, 0.02, -0.008),
                Rot = vector3(100.0, 0.0, 100.0),},
            ['MOUTH'] = { -- Prop position in mouth 
                BoneID = 47419,
                Offset = vector3(0.01, -0.01, 0.01),
                Rot = vector3(-2.0, 0.0, 96.0),},
            ['EAR'] = { -- Prop position behind the ear
                BoneID = 12844,
                Offset = vector3(0.05, -0.03, -0.08),
                Rot = vector3(80.0, 7.0, -118.0),},
        },
        Particles = {
            ['Exhale'] = {-- Smoke particles after exhale
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.1,
                Offset = vector3(0.014, -0.003, 0.008),
                Rot = vector3(55.0, 0.0, 110.0),},
            ['Prop'] = {-- Smoke particles on prop 
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.03,
                Offset = vector3(-0.050, 0.0, 0.0),
                Rot = vector3(0.0, 0.0, 0.0),}
        },
        Anims = {-- New animations can be added to each category and are always selected randomly
            ['USE'] = {
                [1] = {anim = 'idle_a', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [2] = {anim = 'idle_b', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [3] = {anim = 'idle_c', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [4] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1300, 49, 1}},
            },
            ['MOUTH'] = {
                [1] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['EAR'] = {
                [1] = {anim = 'takecig_clip', dict = 'takecig@devcore', params = {1.0, -1.0, 1500, 49, 1}},
            },
            ['COUGH'] = {
                [1] = {anim = 'idle_cough', dict = 'timetable@gardener@smoking_joint', params = {1.0, -1.0, 2500, 49, 1}},
            },
            ['THROW'] = {
                [1] = {anim = 'putdown_low', dict = 'pickup_object', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['GIVE'] = {
                [1] = {anim = 'givetake1_a', dict = 'mp_common', params = {1.0, -1.0, 1800, 49, 1}},
            },
            ['TAKEOUT'] = {
                [1] = {anim = 'enter', dict = 'amb@world_human_smoking@male@male_a@enter', params = {1.0, -1.0, 1300, 49, 1}},
            },
        },
    },

    ['blazeit_joint'] = { -- ITEM NAME
        Type = 'NEED_LIGHTER', -- NEED_LIGHTER (joints, cigarette, cigars), BONG(bongs), VAPE(Must be topped up with liquid), VAPE-PUFF(It has an adjustable maximum amount of puffs)
        Prop = {Default = 'p_cs_joint_02', Light = 'p_cs_joint_01'}, -- Default is prop before lighting and Light is prop after lighting the cigarette (if you want to use only one prop, remove the Light prop and leave only the Default prop)
        Setting = {
            Size = 100,  --  TYPE : NEED_LIGHTER : set a value that decreases gradually after the item is lit, the larger the number you set, the longer the item can be smoked. 
            Remove = 0.3, Time = 3}, -- The size is subtracted by 0.3 every 3 seconds during smoking, depending on the setting (ONLY FOR: NEED_LIGHTER).
        AfterExhale = {
            ExhaleTime = {min = 1, max = 2}, -- Smoke exhalation time in sec
            RemoveAfterExhale = 5, -- The set value is taken after each exhalation of smoke from the size
            ExhaleEffect = {enable = true, intensity = 40, effect = false}, -- screen blackout effect after smoke exhalation and Animpostfx effect-- https://forge.plebmasters.de/animpostfx
            ShakeCam = {effect = 'VIBRATE_SHAKE', value = 2.0}, -- Shake cam effect after exhale smoke for disable, effect = false
            CoughChance = 2, -- false if you don't want to have any chance of coughing / 1 is a 100% chance, the higher the value you set, the lower the chance will be.
        Status = {
            ['ARMOR'] = 35, -- Adds armor after each puff of smoke
            ['STRESS'] = math.random(5, 10), -- Removes stress after smoke exhalation
            ['HIGH'] = 1, -- Adds a high after exhaling smoke
            }
        },
        Positions = {
            ['HAND'] = { -- Prop position in hand 
                BoneID = 64097,
                Offset = vector3(0.020, 0.02, -0.008),
                Rot = vector3(100.0, 0.0, 100.0),},
            ['MOUTH'] = { -- Prop position in mouth 
                BoneID = 47419,
                Offset = vector3(0.01, -0.01, 0.01),
                Rot = vector3(-2.0, 0.0, 96.0),},
            ['EAR'] = { -- Prop position behind the ear
                BoneID = 12844,
                Offset = vector3(0.05, -0.03, -0.08),
                Rot = vector3(80.0, 7.0, -118.0),},
        },
        Particles = {
            ['Exhale'] = {-- Smoke particles after exhale
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.1,
                Offset = vector3(0.014, -0.003, 0.008),
                Rot = vector3(55.0, 0.0, 110.0),},
            ['Prop'] = {-- Smoke particles on prop 
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.03,
                Offset = vector3(-0.050, 0.0, 0.0),
                Rot = vector3(0.0, 0.0, 0.0),}
        },
        Anims = {-- New animations can be added to each category and are always selected randomly
            ['USE'] = {
                [1] = {anim = 'idle_a', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [2] = {anim = 'idle_b', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [3] = {anim = 'idle_c', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [4] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1300, 49, 1}},
            },
            ['MOUTH'] = {
                [1] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['EAR'] = {
                [1] = {anim = 'takecig_clip', dict = 'takecig@devcore', params = {1.0, -1.0, 1500, 49, 1}},
            },
            ['COUGH'] = {
                [1] = {anim = 'idle_cough', dict = 'timetable@gardener@smoking_joint', params = {1.0, -1.0, 2500, 49, 1}},
            },
            ['THROW'] = {
                [1] = {anim = 'putdown_low', dict = 'pickup_object', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['GIVE'] = {
                [1] = {anim = 'givetake1_a', dict = 'mp_common', params = {1.0, -1.0, 1800, 49, 1}},
            },
            ['TAKEOUT'] = {
                [1] = {anim = 'enter', dict = 'amb@world_human_smoking@male@male_a@enter', params = {1.0, -1.0, 1300, 49, 1}},
            },
        },
    },
-- white widow

    ['amethyst_aura_joint'] = { -- ITEM NAME
        Type = 'NEED_LIGHTER', -- NEED_LIGHTER (joints, cigarette, cigars), BONG(bongs), VAPE(Must be topped up with liquid), VAPE-PUFF(It has an adjustable maximum amount of puffs)
        Prop = {Default = 'p_cs_joint_02', Light = 'p_cs_joint_01'}, -- Default is prop before lighting and Light is prop after lighting the cigarette (if you want to use only one prop, remove the Light prop and leave only the Default prop)
        Setting = {
            Size = 100,  --  TYPE : NEED_LIGHTER : set a value that decreases gradually after the item is lit, the larger the number you set, the longer the item can be smoked. 
            Remove = 0.3, Time = 3}, -- The size is subtracted by 0.3 every 3 seconds during smoking, depending on the setting (ONLY FOR: NEED_LIGHTER).
        AfterExhale = {
            ExhaleTime = {min = 1, max = 2}, -- Smoke exhalation time in sec
            RemoveAfterExhale = 5, -- The set value is taken after each exhalation of smoke from the size
            ExhaleEffect = {enable = true, intensity = 40, effect = false}, -- screen blackout effect after smoke exhalation and Animpostfx effect-- https://forge.plebmasters.de/animpostfx
            ShakeCam = {effect = 'VIBRATE_SHAKE', value = 2.0}, -- Shake cam effect after exhale smoke for disable, effect = false
            CoughChance = 2, -- false if you don't want to have any chance of coughing / 1 is a 100% chance, the higher the value you set, the lower the chance will be.
        Status = {
            ['ARMOR'] = 35, -- Adds armor after each puff of smoke
            ['STRESS'] = math.random(5, 10), -- Removes stress after smoke exhalation
            ['HIGH'] = 1, -- Adds a high after exhaling smoke
            }
        },
        Positions = {
            ['HAND'] = { -- Prop position in hand 
                BoneID = 64097,
                Offset = vector3(0.020, 0.02, -0.008),
                Rot = vector3(100.0, 0.0, 100.0),},
            ['MOUTH'] = { -- Prop position in mouth 
                BoneID = 47419,
                Offset = vector3(0.01, -0.01, 0.01),
                Rot = vector3(-2.0, 0.0, 96.0),},
            ['EAR'] = { -- Prop position behind the ear
                BoneID = 12844,
                Offset = vector3(0.05, -0.03, -0.08),
                Rot = vector3(80.0, 7.0, -118.0),},
        },
        Particles = {
            ['Exhale'] = {-- Smoke particles after exhale
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.1,
                Offset = vector3(0.014, -0.003, 0.008),
                Rot = vector3(55.0, 0.0, 110.0),},
            ['Prop'] = {-- Smoke particles on prop 
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.03,
                Offset = vector3(-0.050, 0.0, 0.0),
                Rot = vector3(0.0, 0.0, 0.0),}
        },
        Anims = {-- New animations can be added to each category and are always selected randomly
            ['USE'] = {
                [1] = {anim = 'idle_a', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [2] = {anim = 'idle_b', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [3] = {anim = 'idle_c', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [4] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1300, 49, 1}},
            },
            ['MOUTH'] = {
                [1] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['EAR'] = {
                [1] = {anim = 'takecig_clip', dict = 'takecig@devcore', params = {1.0, -1.0, 1500, 49, 1}},
            },
            ['COUGH'] = {
                [1] = {anim = 'idle_cough', dict = 'timetable@gardener@smoking_joint', params = {1.0, -1.0, 2500, 49, 1}},
            },
            ['THROW'] = {
                [1] = {anim = 'putdown_low', dict = 'pickup_object', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['GIVE'] = {
                [1] = {anim = 'givetake1_a', dict = 'mp_common', params = {1.0, -1.0, 1800, 49, 1}},
            },
            ['TAKEOUT'] = {
                [1] = {anim = 'enter', dict = 'amb@world_human_smoking@male@male_a@enter', params = {1.0, -1.0, 1300, 49, 1}},
            },
        },
    },

    ['babinski_kush_joint'] = { -- ITEM NAME
        Type = 'NEED_LIGHTER', -- NEED_LIGHTER (joints, cigarette, cigars), BONG(bongs), VAPE(Must be topped up with liquid), VAPE-PUFF(It has an adjustable maximum amount of puffs)
        Prop = {Default = 'p_cs_joint_02', Light = 'p_cs_joint_01'}, -- Default is prop before lighting and Light is prop after lighting the cigarette (if you want to use only one prop, remove the Light prop and leave only the Default prop)
        Setting = {
            Size = 100,  --  TYPE : NEED_LIGHTER : set a value that decreases gradually after the item is lit, the larger the number you set, the longer the item can be smoked. 
            Remove = 0.3, Time = 3}, -- The size is subtracted by 0.3 every 3 seconds during smoking, depending on the setting (ONLY FOR: NEED_LIGHTER).
        AfterExhale = {
            ExhaleTime = {min = 1, max = 2}, -- Smoke exhalation time in sec
            RemoveAfterExhale = 5, -- The set value is taken after each exhalation of smoke from the size
            ExhaleEffect = {enable = true, intensity = 40, effect = false}, -- screen blackout effect after smoke exhalation and Animpostfx effect-- https://forge.plebmasters.de/animpostfx
            ShakeCam = {effect = 'VIBRATE_SHAKE', value = 2.0}, -- Shake cam effect after exhale smoke for disable, effect = false
            CoughChance = 2, -- false if you don't want to have any chance of coughing / 1 is a 100% chance, the higher the value you set, the lower the chance will be.
        Status = {
            ['ARMOR'] = 35, -- Adds armor after each puff of smoke
            ['STRESS'] = math.random(5, 10), -- Removes stress after smoke exhalation
            ['HIGH'] = 1, -- Adds a high after exhaling smoke
            }
        },
        Positions = {
            ['HAND'] = { -- Prop position in hand 
                BoneID = 64097,
                Offset = vector3(0.020, 0.02, -0.008),
                Rot = vector3(100.0, 0.0, 100.0),},
            ['MOUTH'] = { -- Prop position in mouth 
                BoneID = 47419,
                Offset = vector3(0.01, -0.01, 0.01),
                Rot = vector3(-2.0, 0.0, 96.0),},
            ['EAR'] = { -- Prop position behind the ear
                BoneID = 12844,
                Offset = vector3(0.05, -0.03, -0.08),
                Rot = vector3(80.0, 7.0, -118.0),},
        },
        Particles = {
            ['Exhale'] = {-- Smoke particles after exhale
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.1,
                Offset = vector3(0.014, -0.003, 0.008),
                Rot = vector3(55.0, 0.0, 110.0),},
            ['Prop'] = {-- Smoke particles on prop 
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.03,
                Offset = vector3(-0.050, 0.0, 0.0),
                Rot = vector3(0.0, 0.0, 0.0),}
        },
        Anims = {-- New animations can be added to each category and are always selected randomly
            ['USE'] = {
                [1] = {anim = 'idle_a', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [2] = {anim = 'idle_b', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [3] = {anim = 'idle_c', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [4] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1300, 49, 1}},
            },
            ['MOUTH'] = {
                [1] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['EAR'] = {
                [1] = {anim = 'takecig_clip', dict = 'takecig@devcore', params = {1.0, -1.0, 1500, 49, 1}},
            },
            ['COUGH'] = {
                [1] = {anim = 'idle_cough', dict = 'timetable@gardener@smoking_joint', params = {1.0, -1.0, 2500, 49, 1}},
            },
            ['THROW'] = {
                [1] = {anim = 'putdown_low', dict = 'pickup_object', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['GIVE'] = {
                [1] = {anim = 'givetake1_a', dict = 'mp_common', params = {1.0, -1.0, 1800, 49, 1}},
            },
            ['TAKEOUT'] = {
                [1] = {anim = 'enter', dict = 'amb@world_human_smoking@male@male_a@enter', params = {1.0, -1.0, 1300, 49, 1}},
            },
        },
    },

    ['get_sharked_joint'] = { -- ITEM NAME
        Type = 'NEED_LIGHTER', -- NEED_LIGHTER (joints, cigarette, cigars), BONG(bongs), VAPE(Must be topped up with liquid), VAPE-PUFF(It has an adjustable maximum amount of puffs)
        Prop = {Default = 'p_cs_joint_02', Light = 'p_cs_joint_01'}, -- Default is prop before lighting and Light is prop after lighting the cigarette (if you want to use only one prop, remove the Light prop and leave only the Default prop)
        Setting = {
            Size = 100,  --  TYPE : NEED_LIGHTER : set a value that decreases gradually after the item is lit, the larger the number you set, the longer the item can be smoked. 
            Remove = 0.3, Time = 3}, -- The size is subtracted by 0.3 every 3 seconds during smoking, depending on the setting (ONLY FOR: NEED_LIGHTER).
        AfterExhale = {
            ExhaleTime = {min = 1, max = 2}, -- Smoke exhalation time in sec
            RemoveAfterExhale = 5, -- The set value is taken after each exhalation of smoke from the size
            ExhaleEffect = {enable = true, intensity = 40, effect = false}, -- screen blackout effect after smoke exhalation and Animpostfx effect-- https://forge.plebmasters.de/animpostfx
            ShakeCam = {effect = 'VIBRATE_SHAKE', value = 2.0}, -- Shake cam effect after exhale smoke for disable, effect = false
            CoughChance = 2, -- false if you don't want to have any chance of coughing / 1 is a 100% chance, the higher the value you set, the lower the chance will be.
        Status = {
            ['ARMOR'] = 35, -- Adds armor after each puff of smoke
            ['STRESS'] = math.random(5, 10), -- Removes stress after smoke exhalation
            ['HIGH'] = 1, -- Adds a high after exhaling smoke
            }
        },
        Positions = {
            ['HAND'] = { -- Prop position in hand 
                BoneID = 64097,
                Offset = vector3(0.020, 0.02, -0.008),
                Rot = vector3(100.0, 0.0, 100.0),},
            ['MOUTH'] = { -- Prop position in mouth 
                BoneID = 47419,
                Offset = vector3(0.01, -0.01, 0.01),
                Rot = vector3(-2.0, 0.0, 96.0),},
            ['EAR'] = { -- Prop position behind the ear
                BoneID = 12844,
                Offset = vector3(0.05, -0.03, -0.08),
                Rot = vector3(80.0, 7.0, -118.0),},
        },
        Particles = {
            ['Exhale'] = {-- Smoke particles after exhale
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.1,
                Offset = vector3(0.014, -0.003, 0.008),
                Rot = vector3(55.0, 0.0, 110.0),},
            ['Prop'] = {-- Smoke particles on prop 
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.03,
                Offset = vector3(-0.050, 0.0, 0.0),
                Rot = vector3(0.0, 0.0, 0.0),}
        },
        Anims = {-- New animations can be added to each category and are always selected randomly
            ['USE'] = {
                [1] = {anim = 'idle_a', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [2] = {anim = 'idle_b', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [3] = {anim = 'idle_c', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [4] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1300, 49, 1}},
            },
            ['MOUTH'] = {
                [1] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['EAR'] = {
                [1] = {anim = 'takecig_clip', dict = 'takecig@devcore', params = {1.0, -1.0, 1500, 49, 1}},
            },
            ['COUGH'] = {
                [1] = {anim = 'idle_cough', dict = 'timetable@gardener@smoking_joint', params = {1.0, -1.0, 2500, 49, 1}},
            },
            ['THROW'] = {
                [1] = {anim = 'putdown_low', dict = 'pickup_object', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['GIVE'] = {
                [1] = {anim = 'givetake1_a', dict = 'mp_common', params = {1.0, -1.0, 1800, 49, 1}},
            },
            ['TAKEOUT'] = {
                [1] = {anim = 'enter', dict = 'amb@world_human_smoking@male@male_a@enter', params = {1.0, -1.0, 1300, 49, 1}},
            },
        },
    },

    ['lex_lotto_joint'] = { -- ITEM NAME
        Type = 'NEED_LIGHTER', -- NEED_LIGHTER (joints, cigarette, cigars), BONG(bongs), VAPE(Must be topped up with liquid), VAPE-PUFF(It has an adjustable maximum amount of puffs)
        Prop = {Default = 'p_cs_joint_02', Light = 'p_cs_joint_01'}, -- Default is prop before lighting and Light is prop after lighting the cigarette (if you want to use only one prop, remove the Light prop and leave only the Default prop)
        Setting = {
            Size = 100,  --  TYPE : NEED_LIGHTER : set a value that decreases gradually after the item is lit, the larger the number you set, the longer the item can be smoked. 
            Remove = 0.3, Time = 3}, -- The size is subtracted by 0.3 every 3 seconds during smoking, depending on the setting (ONLY FOR: NEED_LIGHTER).
        AfterExhale = {
            ExhaleTime = {min = 1, max = 2}, -- Smoke exhalation time in sec
            RemoveAfterExhale = 5, -- The set value is taken after each exhalation of smoke from the size
            ExhaleEffect = {enable = true, intensity = 40, effect = false}, -- screen blackout effect after smoke exhalation and Animpostfx effect-- https://forge.plebmasters.de/animpostfx
            ShakeCam = {effect = 'VIBRATE_SHAKE', value = 2.0}, -- Shake cam effect after exhale smoke for disable, effect = false
            CoughChance = 2, -- false if you don't want to have any chance of coughing / 1 is a 100% chance, the higher the value you set, the lower the chance will be.
        Status = {
            ['ARMOR'] = 35, -- Adds armor after each puff of smoke
            ['STRESS'] = math.random(5, 10), -- Removes stress after smoke exhalation
            ['HIGH'] = 1, -- Adds a high after exhaling smoke
            }
        },
        Positions = {
            ['HAND'] = { -- Prop position in hand 
                BoneID = 64097,
                Offset = vector3(0.020, 0.02, -0.008),
                Rot = vector3(100.0, 0.0, 100.0),},
            ['MOUTH'] = { -- Prop position in mouth 
                BoneID = 47419,
                Offset = vector3(0.01, -0.01, 0.01),
                Rot = vector3(-2.0, 0.0, 96.0),},
            ['EAR'] = { -- Prop position behind the ear
                BoneID = 12844,
                Offset = vector3(0.05, -0.03, -0.08),
                Rot = vector3(80.0, 7.0, -118.0),},
        },
        Particles = {
            ['Exhale'] = {-- Smoke particles after exhale
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.1,
                Offset = vector3(0.014, -0.003, 0.008),
                Rot = vector3(55.0, 0.0, 110.0),},
            ['Prop'] = {-- Smoke particles on prop 
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.03,
                Offset = vector3(-0.050, 0.0, 0.0),
                Rot = vector3(0.0, 0.0, 0.0),}
        },
        Anims = {-- New animations can be added to each category and are always selected randomly
            ['USE'] = {
                [1] = {anim = 'idle_a', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [2] = {anim = 'idle_b', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [3] = {anim = 'idle_c', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [4] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1300, 49, 1}},
            },
            ['MOUTH'] = {
                [1] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['EAR'] = {
                [1] = {anim = 'takecig_clip', dict = 'takecig@devcore', params = {1.0, -1.0, 1500, 49, 1}},
            },
            ['COUGH'] = {
                [1] = {anim = 'idle_cough', dict = 'timetable@gardener@smoking_joint', params = {1.0, -1.0, 2500, 49, 1}},
            },
            ['THROW'] = {
                [1] = {anim = 'putdown_low', dict = 'pickup_object', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['GIVE'] = {
                [1] = {anim = 'givetake1_a', dict = 'mp_common', params = {1.0, -1.0, 1800, 49, 1}},
            },
            ['TAKEOUT'] = {
                [1] = {anim = 'enter', dict = 'amb@world_human_smoking@male@male_a@enter', params = {1.0, -1.0, 1300, 49, 1}},
            },
        },
    },

    ['peach_zkittles_joint'] = { -- ITEM NAME
        Type = 'NEED_LIGHTER', -- NEED_LIGHTER (joints, cigarette, cigars), BONG(bongs), VAPE(Must be topped up with liquid), VAPE-PUFF(It has an adjustable maximum amount of puffs)
        Prop = {Default = 'p_cs_joint_02', Light = 'p_cs_joint_01'}, -- Default is prop before lighting and Light is prop after lighting the cigarette (if you want to use only one prop, remove the Light prop and leave only the Default prop)
        Setting = {
            Size = 100,  --  TYPE : NEED_LIGHTER : set a value that decreases gradually after the item is lit, the larger the number you set, the longer the item can be smoked. 
            Remove = 0.3, Time = 3}, -- The size is subtracted by 0.3 every 3 seconds during smoking, depending on the setting (ONLY FOR: NEED_LIGHTER).
        AfterExhale = {
            ExhaleTime = {min = 1, max = 2}, -- Smoke exhalation time in sec
            RemoveAfterExhale = 5, -- The set value is taken after each exhalation of smoke from the size
            ExhaleEffect = {enable = true, intensity = 40, effect = false}, -- screen blackout effect after smoke exhalation and Animpostfx effect-- https://forge.plebmasters.de/animpostfx
            ShakeCam = {effect = 'VIBRATE_SHAKE', value = 2.0}, -- Shake cam effect after exhale smoke for disable, effect = false
            CoughChance = 2, -- false if you don't want to have any chance of coughing / 1 is a 100% chance, the higher the value you set, the lower the chance will be.
        Status = {
            ['ARMOR'] = 35, -- Adds armor after each puff of smoke
            ['STRESS'] = math.random(5, 10), -- Removes stress after smoke exhalation
            ['HIGH'] = 1, -- Adds a high after exhaling smoke
            }
        },
        Positions = {
            ['HAND'] = { -- Prop position in hand 
                BoneID = 64097,
                Offset = vector3(0.020, 0.02, -0.008),
                Rot = vector3(100.0, 0.0, 100.0),},
            ['MOUTH'] = { -- Prop position in mouth 
                BoneID = 47419,
                Offset = vector3(0.01, -0.01, 0.01),
                Rot = vector3(-2.0, 0.0, 96.0),},
            ['EAR'] = { -- Prop position behind the ear
                BoneID = 12844,
                Offset = vector3(0.05, -0.03, -0.08),
                Rot = vector3(80.0, 7.0, -118.0),},
        },
        Particles = {
            ['Exhale'] = {-- Smoke particles after exhale
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.1,
                Offset = vector3(0.014, -0.003, 0.008),
                Rot = vector3(55.0, 0.0, 110.0),},
            ['Prop'] = {-- Smoke particles on prop 
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.03,
                Offset = vector3(-0.050, 0.0, 0.0),
                Rot = vector3(0.0, 0.0, 0.0),}
        },
        Anims = {-- New animations can be added to each category and are always selected randomly
            ['USE'] = {
                [1] = {anim = 'idle_a', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [2] = {anim = 'idle_b', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [3] = {anim = 'idle_c', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [4] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1300, 49, 1}},
            },
            ['MOUTH'] = {
                [1] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['EAR'] = {
                [1] = {anim = 'takecig_clip', dict = 'takecig@devcore', params = {1.0, -1.0, 1500, 49, 1}},
            },
            ['COUGH'] = {
                [1] = {anim = 'idle_cough', dict = 'timetable@gardener@smoking_joint', params = {1.0, -1.0, 2500, 49, 1}},
            },
            ['THROW'] = {
                [1] = {anim = 'putdown_low', dict = 'pickup_object', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['GIVE'] = {
                [1] = {anim = 'givetake1_a', dict = 'mp_common', params = {1.0, -1.0, 1800, 49, 1}},
            },
            ['TAKEOUT'] = {
                [1] = {anim = 'enter', dict = 'amb@world_human_smoking@male@male_a@enter', params = {1.0, -1.0, 1300, 49, 1}},
            },
        },
    },

    ['pretty_kitty_joint'] = { -- ITEM NAME
        Type = 'NEED_LIGHTER', -- NEED_LIGHTER (joints, cigarette, cigars), BONG(bongs), VAPE(Must be topped up with liquid), VAPE-PUFF(It has an adjustable maximum amount of puffs)
        Prop = {Default = 'p_cs_joint_02', Light = 'p_cs_joint_01'}, -- Default is prop before lighting and Light is prop after lighting the cigarette (if you want to use only one prop, remove the Light prop and leave only the Default prop)
        Setting = {
            Size = 100,  --  TYPE : NEED_LIGHTER : set a value that decreases gradually after the item is lit, the larger the number you set, the longer the item can be smoked. 
            Remove = 0.3, Time = 3}, -- The size is subtracted by 0.3 every 3 seconds during smoking, depending on the setting (ONLY FOR: NEED_LIGHTER).
        AfterExhale = {
            ExhaleTime = {min = 1, max = 2}, -- Smoke exhalation time in sec
            RemoveAfterExhale = 5, -- The set value is taken after each exhalation of smoke from the size
            ExhaleEffect = {enable = true, intensity = 40, effect = false}, -- screen blackout effect after smoke exhalation and Animpostfx effect-- https://forge.plebmasters.de/animpostfx
            ShakeCam = {effect = 'VIBRATE_SHAKE', value = 2.0}, -- Shake cam effect after exhale smoke for disable, effect = false
            CoughChance = 2, -- false if you don't want to have any chance of coughing / 1 is a 100% chance, the higher the value you set, the lower the chance will be.
        Status = {
            ['ARMOR'] = 35, -- Adds armor after each puff of smoke
            ['STRESS'] = math.random(5, 10), -- Removes stress after smoke exhalation
            ['HIGH'] = 1, -- Adds a high after exhaling smoke
            }
        },
        Positions = {
            ['HAND'] = { -- Prop position in hand 
                BoneID = 64097,
                Offset = vector3(0.020, 0.02, -0.008),
                Rot = vector3(100.0, 0.0, 100.0),},
            ['MOUTH'] = { -- Prop position in mouth 
                BoneID = 47419,
                Offset = vector3(0.01, -0.01, 0.01),
                Rot = vector3(-2.0, 0.0, 96.0),},
            ['EAR'] = { -- Prop position behind the ear
                BoneID = 12844,
                Offset = vector3(0.05, -0.03, -0.08),
                Rot = vector3(80.0, 7.0, -118.0),},
        },
        Particles = {
            ['Exhale'] = {-- Smoke particles after exhale
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.1,
                Offset = vector3(0.014, -0.003, 0.008),
                Rot = vector3(55.0, 0.0, 110.0),},
            ['Prop'] = {-- Smoke particles on prop 
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.03,
                Offset = vector3(-0.050, 0.0, 0.0),
                Rot = vector3(0.0, 0.0, 0.0),}
        },
        Anims = {-- New animations can be added to each category and are always selected randomly
            ['USE'] = {
                [1] = {anim = 'idle_a', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [2] = {anim = 'idle_b', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [3] = {anim = 'idle_c', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [4] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1300, 49, 1}},
            },
            ['MOUTH'] = {
                [1] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['EAR'] = {
                [1] = {anim = 'takecig_clip', dict = 'takecig@devcore', params = {1.0, -1.0, 1500, 49, 1}},
            },
            ['COUGH'] = {
                [1] = {anim = 'idle_cough', dict = 'timetable@gardener@smoking_joint', params = {1.0, -1.0, 2500, 49, 1}},
            },
            ['THROW'] = {
                [1] = {anim = 'putdown_low', dict = 'pickup_object', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['GIVE'] = {
                [1] = {anim = 'givetake1_a', dict = 'mp_common', params = {1.0, -1.0, 1800, 49, 1}},
            },
            ['TAKEOUT'] = {
                [1] = {anim = 'enter', dict = 'amb@world_human_smoking@male@male_a@enter', params = {1.0, -1.0, 1300, 49, 1}},
            },
        },
    },

    ['sheilas_dream_joint'] = { -- ITEM NAME
        Type = 'NEED_LIGHTER', -- NEED_LIGHTER (joints, cigarette, cigars), BONG(bongs), VAPE(Must be topped up with liquid), VAPE-PUFF(It has an adjustable maximum amount of puffs)
        Prop = {Default = 'p_cs_joint_02', Light = 'p_cs_joint_01'}, -- Default is prop before lighting and Light is prop after lighting the cigarette (if you want to use only one prop, remove the Light prop and leave only the Default prop)
        Setting = {
            Size = 100,  --  TYPE : NEED_LIGHTER : set a value that decreases gradually after the item is lit, the larger the number you set, the longer the item can be smoked. 
            Remove = 0.3, Time = 3}, -- The size is subtracted by 0.3 every 3 seconds during smoking, depending on the setting (ONLY FOR: NEED_LIGHTER).
        AfterExhale = {
            ExhaleTime = {min = 1, max = 2}, -- Smoke exhalation time in sec
            RemoveAfterExhale = 5, -- The set value is taken after each exhalation of smoke from the size
            ExhaleEffect = {enable = true, intensity = 40, effect = false}, -- screen blackout effect after smoke exhalation and Animpostfx effect-- https://forge.plebmasters.de/animpostfx
            ShakeCam = {effect = 'VIBRATE_SHAKE', value = 2.0}, -- Shake cam effect after exhale smoke for disable, effect = false
            CoughChance = 2, -- false if you don't want to have any chance of coughing / 1 is a 100% chance, the higher the value you set, the lower the chance will be.
        Status = {
            ['ARMOR'] = 35, -- Adds armor after each puff of smoke
            ['STRESS'] = math.random(5, 10), -- Removes stress after smoke exhalation
            ['HIGH'] = 1, -- Adds a high after exhaling smoke
            }
        },
        Positions = {
            ['HAND'] = { -- Prop position in hand 
                BoneID = 64097,
                Offset = vector3(0.020, 0.02, -0.008),
                Rot = vector3(100.0, 0.0, 100.0),},
            ['MOUTH'] = { -- Prop position in mouth 
                BoneID = 47419,
                Offset = vector3(0.01, -0.01, 0.01),
                Rot = vector3(-2.0, 0.0, 96.0),},
            ['EAR'] = { -- Prop position behind the ear
                BoneID = 12844,
                Offset = vector3(0.05, -0.03, -0.08),
                Rot = vector3(80.0, 7.0, -118.0),},
        },
        Particles = {
            ['Exhale'] = {-- Smoke particles after exhale
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.1,
                Offset = vector3(0.014, -0.003, 0.008),
                Rot = vector3(55.0, 0.0, 110.0),},
            ['Prop'] = {-- Smoke particles on prop 
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.03,
                Offset = vector3(-0.050, 0.0, 0.0),
                Rot = vector3(0.0, 0.0, 0.0),}
        },
        Anims = {-- New animations can be added to each category and are always selected randomly
            ['USE'] = {
                [1] = {anim = 'idle_a', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [2] = {anim = 'idle_b', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [3] = {anim = 'idle_c', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [4] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1300, 49, 1}},
            },
            ['MOUTH'] = {
                [1] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['EAR'] = {
                [1] = {anim = 'takecig_clip', dict = 'takecig@devcore', params = {1.0, -1.0, 1500, 49, 1}},
            },
            ['COUGH'] = {
                [1] = {anim = 'idle_cough', dict = 'timetable@gardener@smoking_joint', params = {1.0, -1.0, 2500, 49, 1}},
            },
            ['THROW'] = {
                [1] = {anim = 'putdown_low', dict = 'pickup_object', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['GIVE'] = {
                [1] = {anim = 'givetake1_a', dict = 'mp_common', params = {1.0, -1.0, 1800, 49, 1}},
            },
            ['TAKEOUT'] = {
                [1] = {anim = 'enter', dict = 'amb@world_human_smoking@male@male_a@enter', params = {1.0, -1.0, 1300, 49, 1}},
            },
        },
    },

    ['tazmanian_joint'] = { -- ITEM NAME
        Type = 'NEED_LIGHTER', -- NEED_LIGHTER (joints, cigarette, cigars), BONG(bongs), VAPE(Must be topped up with liquid), VAPE-PUFF(It has an adjustable maximum amount of puffs)
        Prop = {Default = 'p_cs_joint_02', Light = 'p_cs_joint_01'}, -- Default is prop before lighting and Light is prop after lighting the cigarette (if you want to use only one prop, remove the Light prop and leave only the Default prop)
        Setting = {
            Size = 100,  --  TYPE : NEED_LIGHTER : set a value that decreases gradually after the item is lit, the larger the number you set, the longer the item can be smoked. 
            Remove = 0.3, Time = 3}, -- The size is subtracted by 0.3 every 3 seconds during smoking, depending on the setting (ONLY FOR: NEED_LIGHTER).
        AfterExhale = {
            ExhaleTime = {min = 1, max = 2}, -- Smoke exhalation time in sec
            RemoveAfterExhale = 5, -- The set value is taken after each exhalation of smoke from the size
            ExhaleEffect = {enable = true, intensity = 40, effect = false}, -- screen blackout effect after smoke exhalation and Animpostfx effect-- https://forge.plebmasters.de/animpostfx
            ShakeCam = {effect = 'VIBRATE_SHAKE', value = 2.0}, -- Shake cam effect after exhale smoke for disable, effect = false
            CoughChance = 2, -- false if you don't want to have any chance of coughing / 1 is a 100% chance, the higher the value you set, the lower the chance will be.
        Status = {
            ['ARMOR'] = 35, -- Adds armor after each puff of smoke
            ['STRESS'] = math.random(5, 10), -- Removes stress after smoke exhalation
            ['HIGH'] = 1, -- Adds a high after exhaling smoke
            }
        },
        Positions = {
            ['HAND'] = { -- Prop position in hand 
                BoneID = 64097,
                Offset = vector3(0.020, 0.02, -0.008),
                Rot = vector3(100.0, 0.0, 100.0),},
            ['MOUTH'] = { -- Prop position in mouth 
                BoneID = 47419,
                Offset = vector3(0.01, -0.01, 0.01),
                Rot = vector3(-2.0, 0.0, 96.0),},
            ['EAR'] = { -- Prop position behind the ear
                BoneID = 12844,
                Offset = vector3(0.05, -0.03, -0.08),
                Rot = vector3(80.0, 7.0, -118.0),},
        },
        Particles = {
            ['Exhale'] = {-- Smoke particles after exhale
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.1,
                Offset = vector3(0.014, -0.003, 0.008),
                Rot = vector3(55.0, 0.0, 110.0),},
            ['Prop'] = {-- Smoke particles on prop 
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.03,
                Offset = vector3(-0.050, 0.0, 0.0),
                Rot = vector3(0.0, 0.0, 0.0),}
        },
        Anims = {-- New animations can be added to each category and are always selected randomly
            ['USE'] = {
                [1] = {anim = 'idle_a', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [2] = {anim = 'idle_b', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [3] = {anim = 'idle_c', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [4] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1300, 49, 1}},
            },
            ['MOUTH'] = {
                [1] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['EAR'] = {
                [1] = {anim = 'takecig_clip', dict = 'takecig@devcore', params = {1.0, -1.0, 1500, 49, 1}},
            },
            ['COUGH'] = {
                [1] = {anim = 'idle_cough', dict = 'timetable@gardener@smoking_joint', params = {1.0, -1.0, 2500, 49, 1}},
            },
            ['THROW'] = {
                [1] = {anim = 'putdown_low', dict = 'pickup_object', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['GIVE'] = {
                [1] = {anim = 'givetake1_a', dict = 'mp_common', params = {1.0, -1.0, 1800, 49, 1}},
            },
            ['TAKEOUT'] = {
                [1] = {anim = 'enter', dict = 'amb@world_human_smoking@male@male_a@enter', params = {1.0, -1.0, 1300, 49, 1}},
            },
        },
    },
    -- Puff Puff Pass

    ['weed_skunk_cbd_joint'] = { -- ITEM NAME
        Type = 'NEED_LIGHTER', -- NEED_LIGHTER (joints, cigarette, cigars), BONG(bongs), VAPE(Must be topped up with liquid), VAPE-PUFF(It has an adjustable maximum amount of puffs)
        Prop = {Default = 'p_cs_joint_02', Light = 'p_cs_joint_01'}, -- Default is prop before lighting and Light is prop after lighting the cigarette (if you want to use only one prop, remove the Light prop and leave only the Default prop)
        Setting = {
            Size = 100,  --  TYPE : NEED_LIGHTER : set a value that decreases gradually after the item is lit, the larger the number you set, the longer the item can be smoked. 
            Remove = 0.3, Time = 3}, -- The size is subtracted by 0.3 every 3 seconds during smoking, depending on the setting (ONLY FOR: NEED_LIGHTER).
        AfterExhale = {
            ExhaleTime = {min = 1, max = 2}, -- Smoke exhalation time in sec
            RemoveAfterExhale = 5, -- The set value is taken after each exhalation of smoke from the size
            ExhaleEffect = {enable = true, intensity = 40, effect = false}, -- screen blackout effect after smoke exhalation and Animpostfx effect-- https://forge.plebmasters.de/animpostfx
            ShakeCam = {effect = 'VIBRATE_SHAKE', value = 2.0}, -- Shake cam effect after exhale smoke for disable, effect = false
            CoughChance = 2, -- false if you don't want to have any chance of coughing / 1 is a 100% chance, the higher the value you set, the lower the chance will be.
        Status = {
            ['ARMOR'] = 35, -- Adds armor after each puff of smoke
            ['STRESS'] = math.random(5, 10), -- Removes stress after smoke exhalation
            ['HIGH'] = 1, -- Adds a high after exhaling smoke
            }
        },
        Positions = {
            ['HAND'] = { -- Prop position in hand 
                BoneID = 64097,
                Offset = vector3(0.020, 0.02, -0.008),
                Rot = vector3(100.0, 0.0, 100.0),},
            ['MOUTH'] = { -- Prop position in mouth 
                BoneID = 47419,
                Offset = vector3(0.01, -0.01, 0.01),
                Rot = vector3(-2.0, 0.0, 96.0),},
            ['EAR'] = { -- Prop position behind the ear
                BoneID = 12844,
                Offset = vector3(0.05, -0.03, -0.08),
                Rot = vector3(80.0, 7.0, -118.0),},
        },
        Particles = {
            ['Exhale'] = {-- Smoke particles after exhale
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.1,
                Offset = vector3(0.014, -0.003, 0.008),
                Rot = vector3(55.0, 0.0, 110.0),},
            ['Prop'] = {-- Smoke particles on prop 
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.03,
                Offset = vector3(-0.050, 0.0, 0.0),
                Rot = vector3(0.0, 0.0, 0.0),}
        },
        Anims = {-- New animations can be added to each category and are always selected randomly
            ['USE'] = {
                [1] = {anim = 'idle_a', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [2] = {anim = 'idle_b', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [3] = {anim = 'idle_c', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [4] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1300, 49, 1}},
            },
            ['MOUTH'] = {
                [1] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['EAR'] = {
                [1] = {anim = 'takecig_clip', dict = 'takecig@devcore', params = {1.0, -1.0, 1500, 49, 1}},
            },
            ['COUGH'] = {
                [1] = {anim = 'idle_cough', dict = 'timetable@gardener@smoking_joint', params = {1.0, -1.0, 2500, 49, 1}},
            },
            ['THROW'] = {
                [1] = {anim = 'putdown_low', dict = 'pickup_object', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['GIVE'] = {
                [1] = {anim = 'givetake1_a', dict = 'mp_common', params = {1.0, -1.0, 1800, 49, 1}},
            },
            ['TAKEOUT'] = {
                [1] = {anim = 'enter', dict = 'amb@world_human_smoking@male@male_a@enter', params = {1.0, -1.0, 1300, 49, 1}},
            },
        },
    },

    ['weed_og-kush_cbd_joint'] = { -- ITEM NAME
        Type = 'NEED_LIGHTER', -- NEED_LIGHTER (joints, cigarette, cigars), BONG(bongs), VAPE(Must be topped up with liquid), VAPE-PUFF(It has an adjustable maximum amount of puffs)
        Prop = {Default = 'p_cs_joint_02', Light = 'p_cs_joint_01'}, -- Default is prop before lighting and Light is prop after lighting the cigarette (if you want to use only one prop, remove the Light prop and leave only the Default prop)
        Setting = {
            Size = 100,  --  TYPE : NEED_LIGHTER : set a value that decreases gradually after the item is lit, the larger the number you set, the longer the item can be smoked. 
            Remove = 0.3, Time = 3}, -- The size is subtracted by 0.3 every 3 seconds during smoking, depending on the setting (ONLY FOR: NEED_LIGHTER).
        AfterExhale = {
            ExhaleTime = {min = 1, max = 2}, -- Smoke exhalation time in sec
            RemoveAfterExhale = 5, -- The set value is taken after each exhalation of smoke from the size
            ExhaleEffect = {enable = true, intensity = 40, effect = false}, -- screen blackout effect after smoke exhalation and Animpostfx effect-- https://forge.plebmasters.de/animpostfx
            ShakeCam = {effect = 'VIBRATE_SHAKE', value = 2.0}, -- Shake cam effect after exhale smoke for disable, effect = false
            CoughChance = 2, -- false if you don't want to have any chance of coughing / 1 is a 100% chance, the higher the value you set, the lower the chance will be.
        Status = {
            ['ARMOR'] = 35, -- Adds armor after each puff of smoke
            ['STRESS'] = math.random(5, 10), -- Removes stress after smoke exhalation
            ['HIGH'] = 1, -- Adds a high after exhaling smoke
            }
        },
        Positions = {
            ['HAND'] = { -- Prop position in hand 
                BoneID = 64097,
                Offset = vector3(0.020, 0.02, -0.008),
                Rot = vector3(100.0, 0.0, 100.0),},
            ['MOUTH'] = { -- Prop position in mouth 
                BoneID = 47419,
                Offset = vector3(0.01, -0.01, 0.01),
                Rot = vector3(-2.0, 0.0, 96.0),},
            ['EAR'] = { -- Prop position behind the ear
                BoneID = 12844,
                Offset = vector3(0.05, -0.03, -0.08),
                Rot = vector3(80.0, 7.0, -118.0),},
        },
        Particles = {
            ['Exhale'] = {-- Smoke particles after exhale
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.1,
                Offset = vector3(0.014, -0.003, 0.008),
                Rot = vector3(55.0, 0.0, 110.0),},
            ['Prop'] = {-- Smoke particles on prop 
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.03,
                Offset = vector3(-0.050, 0.0, 0.0),
                Rot = vector3(0.0, 0.0, 0.0),}
        },
        Anims = {-- New animations can be added to each category and are always selected randomly
            ['USE'] = {
                [1] = {anim = 'idle_a', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [2] = {anim = 'idle_b', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [3] = {anim = 'idle_c', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [4] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1300, 49, 1}},
            },
            ['MOUTH'] = {
                [1] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['EAR'] = {
                [1] = {anim = 'takecig_clip', dict = 'takecig@devcore', params = {1.0, -1.0, 1500, 49, 1}},
            },
            ['COUGH'] = {
                [1] = {anim = 'idle_cough', dict = 'timetable@gardener@smoking_joint', params = {1.0, -1.0, 2500, 49, 1}},
            },
            ['THROW'] = {
                [1] = {anim = 'putdown_low', dict = 'pickup_object', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['GIVE'] = {
                [1] = {anim = 'givetake1_a', dict = 'mp_common', params = {1.0, -1.0, 1800, 49, 1}},
            },
            ['TAKEOUT'] = {
                [1] = {anim = 'enter', dict = 'amb@world_human_smoking@male@male_a@enter', params = {1.0, -1.0, 1300, 49, 1}},
            },
        },
    },

    ['weed_white-widow_cbd_joint'] = { -- ITEM NAME
        Type = 'NEED_LIGHTER', -- NEED_LIGHTER (joints, cigarette, cigars), BONG(bongs), VAPE(Must be topped up with liquid), VAPE-PUFF(It has an adjustable maximum amount of puffs)
        Prop = {Default = 'p_cs_joint_02', Light = 'p_cs_joint_01'}, -- Default is prop before lighting and Light is prop after lighting the cigarette (if you want to use only one prop, remove the Light prop and leave only the Default prop)
        Setting = {
            Size = 100,  --  TYPE : NEED_LIGHTER : set a value that decreases gradually after the item is lit, the larger the number you set, the longer the item can be smoked. 
            Remove = 0.3, Time = 3}, -- The size is subtracted by 0.3 every 3 seconds during smoking, depending on the setting (ONLY FOR: NEED_LIGHTER).
        AfterExhale = {
            ExhaleTime = {min = 1, max = 2}, -- Smoke exhalation time in sec
            RemoveAfterExhale = 5, -- The set value is taken after each exhalation of smoke from the size
            ExhaleEffect = {enable = true, intensity = 40, effect = false}, -- screen blackout effect after smoke exhalation and Animpostfx effect-- https://forge.plebmasters.de/animpostfx
            ShakeCam = {effect = 'VIBRATE_SHAKE', value = 2.0}, -- Shake cam effect after exhale smoke for disable, effect = false
            CoughChance = 2, -- false if you don't want to have any chance of coughing / 1 is a 100% chance, the higher the value you set, the lower the chance will be.
        Status = {
            ['ARMOR'] = 35, -- Adds armor after each puff of smoke
            ['STRESS'] = math.random(5, 10), -- Removes stress after smoke exhalation
            ['HIGH'] = 1, -- Adds a high after exhaling smoke
            }
        },
        Positions = {
            ['HAND'] = { -- Prop position in hand 
                BoneID = 64097,
                Offset = vector3(0.020, 0.02, -0.008),
                Rot = vector3(100.0, 0.0, 100.0),},
            ['MOUTH'] = { -- Prop position in mouth 
                BoneID = 47419,
                Offset = vector3(0.01, -0.01, 0.01),
                Rot = vector3(-2.0, 0.0, 96.0),},
            ['EAR'] = { -- Prop position behind the ear
                BoneID = 12844,
                Offset = vector3(0.05, -0.03, -0.08),
                Rot = vector3(80.0, 7.0, -118.0),},
        },
        Particles = {
            ['Exhale'] = {-- Smoke particles after exhale
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.1,
                Offset = vector3(0.014, -0.003, 0.008),
                Rot = vector3(55.0, 0.0, 110.0),},
            ['Prop'] = {-- Smoke particles on prop 
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.03,
                Offset = vector3(-0.050, 0.0, 0.0),
                Rot = vector3(0.0, 0.0, 0.0),}
        },
        Anims = {-- New animations can be added to each category and are always selected randomly
            ['USE'] = {
                [1] = {anim = 'idle_a', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [2] = {anim = 'idle_b', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [3] = {anim = 'idle_c', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [4] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1300, 49, 1}},
            },
            ['MOUTH'] = {
                [1] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['EAR'] = {
                [1] = {anim = 'takecig_clip', dict = 'takecig@devcore', params = {1.0, -1.0, 1500, 49, 1}},
            },
            ['COUGH'] = {
                [1] = {anim = 'idle_cough', dict = 'timetable@gardener@smoking_joint', params = {1.0, -1.0, 2500, 49, 1}},
            },
            ['THROW'] = {
                [1] = {anim = 'putdown_low', dict = 'pickup_object', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['GIVE'] = {
                [1] = {anim = 'givetake1_a', dict = 'mp_common', params = {1.0, -1.0, 1800, 49, 1}},
            },
            ['TAKEOUT'] = {
                [1] = {anim = 'enter', dict = 'amb@world_human_smoking@male@male_a@enter', params = {1.0, -1.0, 1300, 49, 1}},
            },
        },
    },

    ['weed_ak47_cbd_joint'] = { -- ITEM NAME
        Type = 'NEED_LIGHTER', -- NEED_LIGHTER (joints, cigarette, cigars), BONG(bongs), VAPE(Must be topped up with liquid), VAPE-PUFF(It has an adjustable maximum amount of puffs)
        Prop = {Default = 'p_cs_joint_02', Light = 'p_cs_joint_01'}, -- Default is prop before lighting and Light is prop after lighting the cigarette (if you want to use only one prop, remove the Light prop and leave only the Default prop)
        Setting = {
            Size = 100,  --  TYPE : NEED_LIGHTER : set a value that decreases gradually after the item is lit, the larger the number you set, the longer the item can be smoked. 
            Remove = 0.3, Time = 3}, -- The size is subtracted by 0.3 every 3 seconds during smoking, depending on the setting (ONLY FOR: NEED_LIGHTER).
        AfterExhale = {
            ExhaleTime = {min = 1, max = 2}, -- Smoke exhalation time in sec
            RemoveAfterExhale = 5, -- The set value is taken after each exhalation of smoke from the size
            ExhaleEffect = {enable = true, intensity = 40, effect = false}, -- screen blackout effect after smoke exhalation and Animpostfx effect-- https://forge.plebmasters.de/animpostfx
            ShakeCam = {effect = 'VIBRATE_SHAKE', value = 2.0}, -- Shake cam effect after exhale smoke for disable, effect = false
            CoughChance = 2, -- false if you don't want to have any chance of coughing / 1 is a 100% chance, the higher the value you set, the lower the chance will be.
        Status = {
            ['ARMOR'] = 35, -- Adds armor after each puff of smoke
            ['STRESS'] = math.random(5, 10), -- Removes stress after smoke exhalation
            ['HIGH'] = 1, -- Adds a high after exhaling smoke
            }
        },
        Positions = {
            ['HAND'] = { -- Prop position in hand 
                BoneID = 64097,
                Offset = vector3(0.020, 0.02, -0.008),
                Rot = vector3(100.0, 0.0, 100.0),},
            ['MOUTH'] = { -- Prop position in mouth 
                BoneID = 47419,
                Offset = vector3(0.01, -0.01, 0.01),
                Rot = vector3(-2.0, 0.0, 96.0),},
            ['EAR'] = { -- Prop position behind the ear
                BoneID = 12844,
                Offset = vector3(0.05, -0.03, -0.08),
                Rot = vector3(80.0, 7.0, -118.0),},
        },
        Particles = {
            ['Exhale'] = {-- Smoke particles after exhale
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.1,
                Offset = vector3(0.014, -0.003, 0.008),
                Rot = vector3(55.0, 0.0, 110.0),},
            ['Prop'] = {-- Smoke particles on prop 
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.03,
                Offset = vector3(-0.050, 0.0, 0.0),
                Rot = vector3(0.0, 0.0, 0.0),}
        },
        Anims = {-- New animations can be added to each category and are always selected randomly
            ['USE'] = {
                [1] = {anim = 'idle_a', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [2] = {anim = 'idle_b', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [3] = {anim = 'idle_c', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [4] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1300, 49, 1}},
            },
            ['MOUTH'] = {
                [1] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['EAR'] = {
                [1] = {anim = 'takecig_clip', dict = 'takecig@devcore', params = {1.0, -1.0, 1500, 49, 1}},
            },
            ['COUGH'] = {
                [1] = {anim = 'idle_cough', dict = 'timetable@gardener@smoking_joint', params = {1.0, -1.0, 2500, 49, 1}},
            },
            ['THROW'] = {
                [1] = {anim = 'putdown_low', dict = 'pickup_object', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['GIVE'] = {
                [1] = {anim = 'givetake1_a', dict = 'mp_common', params = {1.0, -1.0, 1800, 49, 1}},
            },
            ['TAKEOUT'] = {
                [1] = {anim = 'enter', dict = 'amb@world_human_smoking@male@male_a@enter', params = {1.0, -1.0, 1300, 49, 1}},
            },
        },
    },

    ['weed_amnesia_cbd_joint'] = { -- ITEM NAME
        Type = 'NEED_LIGHTER', -- NEED_LIGHTER (joints, cigarette, cigars), BONG(bongs), VAPE(Must be topped up with liquid), VAPE-PUFF(It has an adjustable maximum amount of puffs)
        Prop = {Default = 'p_cs_joint_02', Light = 'p_cs_joint_01'}, -- Default is prop before lighting and Light is prop after lighting the cigarette (if you want to use only one prop, remove the Light prop and leave only the Default prop)
        Setting = {
            Size = 100,  --  TYPE : NEED_LIGHTER : set a value that decreases gradually after the item is lit, the larger the number you set, the longer the item can be smoked. 
            Remove = 0.3, Time = 3}, -- The size is subtracted by 0.3 every 3 seconds during smoking, depending on the setting (ONLY FOR: NEED_LIGHTER).
        AfterExhale = {
            ExhaleTime = {min = 1, max = 2}, -- Smoke exhalation time in sec
            RemoveAfterExhale = 5, -- The set value is taken after each exhalation of smoke from the size
            ExhaleEffect = {enable = true, intensity = 40, effect = false}, -- screen blackout effect after smoke exhalation and Animpostfx effect-- https://forge.plebmasters.de/animpostfx
            ShakeCam = {effect = 'VIBRATE_SHAKE', value = 2.0}, -- Shake cam effect after exhale smoke for disable, effect = false
            CoughChance = 2, -- false if you don't want to have any chance of coughing / 1 is a 100% chance, the higher the value you set, the lower the chance will be.
        Status = {
            ['ARMOR'] = 35, -- Adds armor after each puff of smoke
            ['STRESS'] = math.random(5, 10), -- Removes stress after smoke exhalation
            ['HIGH'] = 1, -- Adds a high after exhaling smoke
            }
        },
        Positions = {
            ['HAND'] = { -- Prop position in hand 
                BoneID = 64097,
                Offset = vector3(0.020, 0.02, -0.008),
                Rot = vector3(100.0, 0.0, 100.0),},
            ['MOUTH'] = { -- Prop position in mouth 
                BoneID = 47419,
                Offset = vector3(0.01, -0.01, 0.01),
                Rot = vector3(-2.0, 0.0, 96.0),},
            ['EAR'] = { -- Prop position behind the ear
                BoneID = 12844,
                Offset = vector3(0.05, -0.03, -0.08),
                Rot = vector3(80.0, 7.0, -118.0),},
        },
        Particles = {
            ['Exhale'] = {-- Smoke particles after exhale
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.1,
                Offset = vector3(0.014, -0.003, 0.008),
                Rot = vector3(55.0, 0.0, 110.0),},
            ['Prop'] = {-- Smoke particles on prop 
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.03,
                Offset = vector3(-0.050, 0.0, 0.0),
                Rot = vector3(0.0, 0.0, 0.0),}
        },
        Anims = {-- New animations can be added to each category and are always selected randomly
            ['USE'] = {
                [1] = {anim = 'idle_a', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [2] = {anim = 'idle_b', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [3] = {anim = 'idle_c', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [4] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1300, 49, 1}},
            },
            ['MOUTH'] = {
                [1] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['EAR'] = {
                [1] = {anim = 'takecig_clip', dict = 'takecig@devcore', params = {1.0, -1.0, 1500, 49, 1}},
            },
            ['COUGH'] = {
                [1] = {anim = 'idle_cough', dict = 'timetable@gardener@smoking_joint', params = {1.0, -1.0, 2500, 49, 1}},
            },
            ['THROW'] = {
                [1] = {anim = 'putdown_low', dict = 'pickup_object', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['GIVE'] = {
                [1] = {anim = 'givetake1_a', dict = 'mp_common', params = {1.0, -1.0, 1800, 49, 1}},
            },
            ['TAKEOUT'] = {
                [1] = {anim = 'enter', dict = 'amb@world_human_smoking@male@male_a@enter', params = {1.0, -1.0, 1300, 49, 1}},
            },
        },
    },

    ['weed_purple-haze_cbd_joint'] = { -- ITEM NAME
        Type = 'NEED_LIGHTER', -- NEED_LIGHTER (joints, cigarette, cigars), BONG(bongs), VAPE(Must be topped up with liquid), VAPE-PUFF(It has an adjustable maximum amount of puffs)
        Prop = {Default = 'p_cs_joint_02', Light = 'p_cs_joint_01'}, -- Default is prop before lighting and Light is prop after lighting the cigarette (if you want to use only one prop, remove the Light prop and leave only the Default prop)
        Setting = {
            Size = 100,  --  TYPE : NEED_LIGHTER : set a value that decreases gradually after the item is lit, the larger the number you set, the longer the item can be smoked. 
            Remove = 0.3, Time = 3}, -- The size is subtracted by 0.3 every 3 seconds during smoking, depending on the setting (ONLY FOR: NEED_LIGHTER).
        AfterExhale = {
            ExhaleTime = {min = 1, max = 2}, -- Smoke exhalation time in sec
            RemoveAfterExhale = 5, -- The set value is taken after each exhalation of smoke from the size
            ExhaleEffect = {enable = true, intensity = 40, effect = false}, -- screen blackout effect after smoke exhalation and Animpostfx effect-- https://forge.plebmasters.de/animpostfx
            ShakeCam = {effect = 'VIBRATE_SHAKE', value = 2.0}, -- Shake cam effect after exhale smoke for disable, effect = false
            CoughChance = 2, -- false if you don't want to have any chance of coughing / 1 is a 100% chance, the higher the value you set, the lower the chance will be.
        Status = {
            ['ARMOR'] = 35, -- Adds armor after each puff of smoke
            ['STRESS'] = math.random(5, 10), -- Removes stress after smoke exhalation
            ['HIGH'] = 1, -- Adds a high after exhaling smoke
            }
        },
        Positions = {
            ['HAND'] = { -- Prop position in hand 
                BoneID = 64097,
                Offset = vector3(0.020, 0.02, -0.008),
                Rot = vector3(100.0, 0.0, 100.0),},
            ['MOUTH'] = { -- Prop position in mouth 
                BoneID = 47419,
                Offset = vector3(0.01, -0.01, 0.01),
                Rot = vector3(-2.0, 0.0, 96.0),},
            ['EAR'] = { -- Prop position behind the ear
                BoneID = 12844,
                Offset = vector3(0.05, -0.03, -0.08),
                Rot = vector3(80.0, 7.0, -118.0),},
        },
        Particles = {
            ['Exhale'] = {-- Smoke particles after exhale
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.1,
                Offset = vector3(0.014, -0.003, 0.008),
                Rot = vector3(55.0, 0.0, 110.0),},
            ['Prop'] = {-- Smoke particles on prop 
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.03,
                Offset = vector3(-0.050, 0.0, 0.0),
                Rot = vector3(0.0, 0.0, 0.0),}
        },
        Anims = {-- New animations can be added to each category and are always selected randomly
            ['USE'] = {
                [1] = {anim = 'idle_a', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [2] = {anim = 'idle_b', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [3] = {anim = 'idle_c', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [4] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1300, 49, 1}},
            },
            ['MOUTH'] = {
                [1] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['EAR'] = {
                [1] = {anim = 'takecig_clip', dict = 'takecig@devcore', params = {1.0, -1.0, 1500, 49, 1}},
            },
            ['COUGH'] = {
                [1] = {anim = 'idle_cough', dict = 'timetable@gardener@smoking_joint', params = {1.0, -1.0, 2500, 49, 1}},
            },
            ['THROW'] = {
                [1] = {anim = 'putdown_low', dict = 'pickup_object', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['GIVE'] = {
                [1] = {anim = 'givetake1_a', dict = 'mp_common', params = {1.0, -1.0, 1800, 49, 1}},
            },
            ['TAKEOUT'] = {
                [1] = {anim = 'enter', dict = 'amb@world_human_smoking@male@male_a@enter', params = {1.0, -1.0, 1300, 49, 1}},
            },
        },
    },

    ['weed_gelato_cbd_joint'] = { -- ITEM NAME
        Type = 'NEED_LIGHTER', -- NEED_LIGHTER (joints, cigarette, cigars), BONG(bongs), VAPE(Must be topped up with liquid), VAPE-PUFF(It has an adjustable maximum amount of puffs)
        Prop = {Default = 'p_cs_joint_02', Light = 'p_cs_joint_01'}, -- Default is prop before lighting and Light is prop after lighting the cigarette (if you want to use only one prop, remove the Light prop and leave only the Default prop)
        Setting = {
            Size = 100,  --  TYPE : NEED_LIGHTER : set a value that decreases gradually after the item is lit, the larger the number you set, the longer the item can be smoked. 
            Remove = 0.3, Time = 3}, -- The size is subtracted by 0.3 every 3 seconds during smoking, depending on the setting (ONLY FOR: NEED_LIGHTER).
        AfterExhale = {
            ExhaleTime = {min = 1, max = 2}, -- Smoke exhalation time in sec
            RemoveAfterExhale = 5, -- The set value is taken after each exhalation of smoke from the size
            ExhaleEffect = {enable = true, intensity = 40, effect = false}, -- screen blackout effect after smoke exhalation and Animpostfx effect-- https://forge.plebmasters.de/animpostfx
            ShakeCam = {effect = 'VIBRATE_SHAKE', value = 2.0}, -- Shake cam effect after exhale smoke for disable, effect = false
            CoughChance = 2, -- false if you don't want to have any chance of coughing / 1 is a 100% chance, the higher the value you set, the lower the chance will be.
        Status = {
            ['ARMOR'] = 35, -- Adds armor after each puff of smoke
            ['STRESS'] = math.random(5, 10), -- Removes stress after smoke exhalation
            ['HIGH'] = 1, -- Adds a high after exhaling smoke
            }
        },
        Positions = {
            ['HAND'] = { -- Prop position in hand 
                BoneID = 64097,
                Offset = vector3(0.020, 0.02, -0.008),
                Rot = vector3(100.0, 0.0, 100.0),},
            ['MOUTH'] = { -- Prop position in mouth 
                BoneID = 47419,
                Offset = vector3(0.01, -0.01, 0.01),
                Rot = vector3(-2.0, 0.0, 96.0),},
            ['EAR'] = { -- Prop position behind the ear
                BoneID = 12844,
                Offset = vector3(0.05, -0.03, -0.08),
                Rot = vector3(80.0, 7.0, -118.0),},
        },
        Particles = {
            ['Exhale'] = {-- Smoke particles after exhale
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.1,
                Offset = vector3(0.014, -0.003, 0.008),
                Rot = vector3(55.0, 0.0, 110.0),},
            ['Prop'] = {-- Smoke particles on prop 
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.03,
                Offset = vector3(-0.050, 0.0, 0.0),
                Rot = vector3(0.0, 0.0, 0.0),}
        },
        Anims = {-- New animations can be added to each category and are always selected randomly
            ['USE'] = {
                [1] = {anim = 'idle_a', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [2] = {anim = 'idle_b', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [3] = {anim = 'idle_c', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [4] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1300, 49, 1}},
            },
            ['MOUTH'] = {
                [1] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['EAR'] = {
                [1] = {anim = 'takecig_clip', dict = 'takecig@devcore', params = {1.0, -1.0, 1500, 49, 1}},
            },
            ['COUGH'] = {
                [1] = {anim = 'idle_cough', dict = 'timetable@gardener@smoking_joint', params = {1.0, -1.0, 2500, 49, 1}},
            },
            ['THROW'] = {
                [1] = {anim = 'putdown_low', dict = 'pickup_object', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['GIVE'] = {
                [1] = {anim = 'givetake1_a', dict = 'mp_common', params = {1.0, -1.0, 1800, 49, 1}},
            },
            ['TAKEOUT'] = {
                [1] = {anim = 'enter', dict = 'amb@world_human_smoking@male@male_a@enter', params = {1.0, -1.0, 1300, 49, 1}},
            },
        },
    },

    ['weed_zkittlez_cbd_joint'] = { -- ITEM NAME
        Type = 'NEED_LIGHTER', -- NEED_LIGHTER (joints, cigarette, cigars), BONG(bongs), VAPE(Must be topped up with liquid), VAPE-PUFF(It has an adjustable maximum amount of puffs)
        Prop = {Default = 'p_cs_joint_02', Light = 'p_cs_joint_01'}, -- Default is prop before lighting and Light is prop after lighting the cigarette (if you want to use only one prop, remove the Light prop and leave only the Default prop)
        Setting = {
            Size = 100,  --  TYPE : NEED_LIGHTER : set a value that decreases gradually after the item is lit, the larger the number you set, the longer the item can be smoked. 
            Remove = 0.3, Time = 3}, -- The size is subtracted by 0.3 every 3 seconds during smoking, depending on the setting (ONLY FOR: NEED_LIGHTER).
        AfterExhale = {
            ExhaleTime = {min = 1, max = 2}, -- Smoke exhalation time in sec
            RemoveAfterExhale = 5, -- The set value is taken after each exhalation of smoke from the size
            ExhaleEffect = {enable = true, intensity = 40, effect = false}, -- screen blackout effect after smoke exhalation and Animpostfx effect-- https://forge.plebmasters.de/animpostfx
            ShakeCam = {effect = 'VIBRATE_SHAKE', value = 2.0}, -- Shake cam effect after exhale smoke for disable, effect = false
            CoughChance = 2, -- false if you don't want to have any chance of coughing / 1 is a 100% chance, the higher the value you set, the lower the chance will be.
        Status = {
            ['ARMOR'] = 35, -- Adds armor after each puff of smoke
            ['STRESS'] = math.random(5, 10), -- Removes stress after smoke exhalation
            ['HIGH'] = 1, -- Adds a high after exhaling smoke
            }
        },
        Positions = {
            ['HAND'] = { -- Prop position in hand 
                BoneID = 64097,
                Offset = vector3(0.020, 0.02, -0.008),
                Rot = vector3(100.0, 0.0, 100.0),},
            ['MOUTH'] = { -- Prop position in mouth 
                BoneID = 47419,
                Offset = vector3(0.01, -0.01, 0.01),
                Rot = vector3(-2.0, 0.0, 96.0),},
            ['EAR'] = { -- Prop position behind the ear
                BoneID = 12844,
                Offset = vector3(0.05, -0.03, -0.08),
                Rot = vector3(80.0, 7.0, -118.0),},
        },
        Particles = {
            ['Exhale'] = {-- Smoke particles after exhale
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.1,
                Offset = vector3(0.014, -0.003, 0.008),
                Rot = vector3(55.0, 0.0, 110.0),},
            ['Prop'] = {-- Smoke particles on prop 
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.03,
                Offset = vector3(-0.050, 0.0, 0.0),
                Rot = vector3(0.0, 0.0, 0.0),}
        },
        Anims = {-- New animations can be added to each category and are always selected randomly
            ['USE'] = {
                [1] = {anim = 'idle_a', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [2] = {anim = 'idle_b', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [3] = {anim = 'idle_c', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [4] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1300, 49, 1}},
            },
            ['MOUTH'] = {
                [1] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['EAR'] = {
                [1] = {anim = 'takecig_clip', dict = 'takecig@devcore', params = {1.0, -1.0, 1500, 49, 1}},
            },
            ['COUGH'] = {
                [1] = {anim = 'idle_cough', dict = 'timetable@gardener@smoking_joint', params = {1.0, -1.0, 2500, 49, 1}},
            },
            ['THROW'] = {
                [1] = {anim = 'putdown_low', dict = 'pickup_object', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['GIVE'] = {
                [1] = {anim = 'givetake1_a', dict = 'mp_common', params = {1.0, -1.0, 1800, 49, 1}},
            },
            ['TAKEOUT'] = {
                [1] = {anim = 'enter', dict = 'amb@world_human_smoking@male@male_a@enter', params = {1.0, -1.0, 1300, 49, 1}},
            },
        },
    },

    -- Hoxbox

    ['bubba_kush_joint'] = { -- ITEM NAME
        Type = 'NEED_LIGHTER', -- NEED_LIGHTER (joints, cigarette, cigars), BONG(bongs), VAPE(Must be topped up with liquid), VAPE-PUFF(It has an adjustable maximum amount of puffs)
        Prop = {Default = 'p_cs_joint_02', Light = 'p_cs_joint_01'}, -- Default is prop before lighting and Light is prop after lighting the cigarette (if you want to use only one prop, remove the Light prop and leave only the Default prop)
        Setting = {
            Size = 100,  --  TYPE : NEED_LIGHTER : set a value that decreases gradually after the item is lit, the larger the number you set, the longer the item can be smoked. 
            Remove = 0.3, Time = 3}, -- The size is subtracted by 0.3 every 3 seconds during smoking, depending on the setting (ONLY FOR: NEED_LIGHTER).
        AfterExhale = {
            ExhaleTime = {min = 1, max = 2}, -- Smoke exhalation time in sec
            RemoveAfterExhale = 5, -- The set value is taken after each exhalation of smoke from the size
            ExhaleEffect = {enable = true, intensity = 40, effect = false}, -- screen blackout effect after smoke exhalation and Animpostfx effect-- https://forge.plebmasters.de/animpostfx
            ShakeCam = {effect = 'VIBRATE_SHAKE', value = 2.0}, -- Shake cam effect after exhale smoke for disable, effect = false
            CoughChance = 2, -- false if you don't want to have any chance of coughing / 1 is a 100% chance, the higher the value you set, the lower the chance will be.
        Status = {
            ['ARMOR'] = 35, -- Adds armor after each puff of smoke
            ['STRESS'] = math.random(5, 10), -- Removes stress after smoke exhalation
            ['HIGH'] = 1, -- Adds a high after exhaling smoke
            }
        },
        Positions = {
            ['HAND'] = { -- Prop position in hand 
                BoneID = 64097,
                Offset = vector3(0.020, 0.02, -0.008),
                Rot = vector3(100.0, 0.0, 100.0),},
            ['MOUTH'] = { -- Prop position in mouth 
                BoneID = 47419,
                Offset = vector3(0.01, -0.01, 0.01),
                Rot = vector3(-2.0, 0.0, 96.0),},
            ['EAR'] = { -- Prop position behind the ear
                BoneID = 12844,
                Offset = vector3(0.05, -0.03, -0.08),
                Rot = vector3(80.0, 7.0, -118.0),},
        },
        Particles = {
            ['Exhale'] = {-- Smoke particles after exhale
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.1,
                Offset = vector3(0.014, -0.003, 0.008),
                Rot = vector3(55.0, 0.0, 110.0),},
            ['Prop'] = {-- Smoke particles on prop 
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.03,
                Offset = vector3(-0.050, 0.0, 0.0),
                Rot = vector3(0.0, 0.0, 0.0),}
        },
        Anims = {-- New animations can be added to each category and are always selected randomly
            ['USE'] = {
                [1] = {anim = 'idle_a', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [2] = {anim = 'idle_b', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [3] = {anim = 'idle_c', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [4] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1300, 49, 1}},
            },
            ['MOUTH'] = {
                [1] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['EAR'] = {
                [1] = {anim = 'takecig_clip', dict = 'takecig@devcore', params = {1.0, -1.0, 1500, 49, 1}},
            },
            ['COUGH'] = {
                [1] = {anim = 'idle_cough', dict = 'timetable@gardener@smoking_joint', params = {1.0, -1.0, 2500, 49, 1}},
            },
            ['THROW'] = {
                [1] = {anim = 'putdown_low', dict = 'pickup_object', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['GIVE'] = {
                [1] = {anim = 'givetake1_a', dict = 'mp_common', params = {1.0, -1.0, 1800, 49, 1}},
            },
            ['TAKEOUT'] = {
                [1] = {anim = 'enter', dict = 'amb@world_human_smoking@male@male_a@enter', params = {1.0, -1.0, 1300, 49, 1}},
            },
        },
    },

    ['leila_delight_joint'] = { -- ITEM NAME
        Type = 'NEED_LIGHTER', -- NEED_LIGHTER (joints, cigarette, cigars), BONG(bongs), VAPE(Must be topped up with liquid), VAPE-PUFF(It has an adjustable maximum amount of puffs)
        Prop = {Default = 'p_cs_joint_02', Light = 'p_cs_joint_01'}, -- Default is prop before lighting and Light is prop after lighting the cigarette (if you want to use only one prop, remove the Light prop and leave only the Default prop)
        Setting = {
            Size = 100,  --  TYPE : NEED_LIGHTER : set a value that decreases gradually after the item is lit, the larger the number you set, the longer the item can be smoked. 
            Remove = 0.3, Time = 3}, -- The size is subtracted by 0.3 every 3 seconds during smoking, depending on the setting (ONLY FOR: NEED_LIGHTER).
        AfterExhale = {
            ExhaleTime = {min = 1, max = 2}, -- Smoke exhalation time in sec
            RemoveAfterExhale = 5, -- The set value is taken after each exhalation of smoke from the size
            ExhaleEffect = {enable = true, intensity = 40, effect = false}, -- screen blackout effect after smoke exhalation and Animpostfx effect-- https://forge.plebmasters.de/animpostfx
            ShakeCam = {effect = 'VIBRATE_SHAKE', value = 2.0}, -- Shake cam effect after exhale smoke for disable, effect = false
            CoughChance = 2, -- false if you don't want to have any chance of coughing / 1 is a 100% chance, the higher the value you set, the lower the chance will be.
        Status = {
            ['ARMOR'] = 35, -- Adds armor after each puff of smoke
            ['STRESS'] = math.random(5, 10), -- Removes stress after smoke exhalation
            ['HIGH'] = 1, -- Adds a high after exhaling smoke
            }
        },
        Positions = {
            ['HAND'] = { -- Prop position in hand 
                BoneID = 64097,
                Offset = vector3(0.020, 0.02, -0.008),
                Rot = vector3(100.0, 0.0, 100.0),},
            ['MOUTH'] = { -- Prop position in mouth 
                BoneID = 47419,
                Offset = vector3(0.01, -0.01, 0.01),
                Rot = vector3(-2.0, 0.0, 96.0),},
            ['EAR'] = { -- Prop position behind the ear
                BoneID = 12844,
                Offset = vector3(0.05, -0.03, -0.08),
                Rot = vector3(80.0, 7.0, -118.0),},
        },
        Particles = {
            ['Exhale'] = {-- Smoke particles after exhale
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.1,
                Offset = vector3(0.014, -0.003, 0.008),
                Rot = vector3(55.0, 0.0, 110.0),},
            ['Prop'] = {-- Smoke particles on prop 
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.03,
                Offset = vector3(-0.050, 0.0, 0.0),
                Rot = vector3(0.0, 0.0, 0.0),}
        },
        Anims = {-- New animations can be added to each category and are always selected randomly
            ['USE'] = {
                [1] = {anim = 'idle_a', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [2] = {anim = 'idle_b', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [3] = {anim = 'idle_c', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [4] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1300, 49, 1}},
            },
            ['MOUTH'] = {
                [1] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['EAR'] = {
                [1] = {anim = 'takecig_clip', dict = 'takecig@devcore', params = {1.0, -1.0, 1500, 49, 1}},
            },
            ['COUGH'] = {
                [1] = {anim = 'idle_cough', dict = 'timetable@gardener@smoking_joint', params = {1.0, -1.0, 2500, 49, 1}},
            },
            ['THROW'] = {
                [1] = {anim = 'putdown_low', dict = 'pickup_object', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['GIVE'] = {
                [1] = {anim = 'givetake1_a', dict = 'mp_common', params = {1.0, -1.0, 1800, 49, 1}},
            },
            ['TAKEOUT'] = {
                [1] = {anim = 'enter', dict = 'amb@world_human_smoking@male@male_a@enter', params = {1.0, -1.0, 1300, 49, 1}},
            },
        },
    },

    ['pineapple_express_joint'] = { -- ITEM NAME
        Type = 'NEED_LIGHTER', -- NEED_LIGHTER (joints, cigarette, cigars), BONG(bongs), VAPE(Must be topped up with liquid), VAPE-PUFF(It has an adjustable maximum amount of puffs)
        Prop = {Default = 'p_cs_joint_02', Light = 'p_cs_joint_01'}, -- Default is prop before lighting and Light is prop after lighting the cigarette (if you want to use only one prop, remove the Light prop and leave only the Default prop)
        Setting = {
            Size = 100,  --  TYPE : NEED_LIGHTER : set a value that decreases gradually after the item is lit, the larger the number you set, the longer the item can be smoked. 
            Remove = 0.3, Time = 3}, -- The size is subtracted by 0.3 every 3 seconds during smoking, depending on the setting (ONLY FOR: NEED_LIGHTER).
        AfterExhale = {
            ExhaleTime = {min = 1, max = 2}, -- Smoke exhalation time in sec
            RemoveAfterExhale = 5, -- The set value is taken after each exhalation of smoke from the size
            ExhaleEffect = {enable = true, intensity = 40, effect = false}, -- screen blackout effect after smoke exhalation and Animpostfx effect-- https://forge.plebmasters.de/animpostfx
            ShakeCam = {effect = 'VIBRATE_SHAKE', value = 2.0}, -- Shake cam effect after exhale smoke for disable, effect = false
            CoughChance = 2, -- false if you don't want to have any chance of coughing / 1 is a 100% chance, the higher the value you set, the lower the chance will be.
        Status = {
            ['ARMOR'] = 35, -- Adds armor after each puff of smoke
            ['STRESS'] = math.random(5, 10), -- Removes stress after smoke exhalation
            ['HIGH'] = 1, -- Adds a high after exhaling smoke
            }
        },
        Positions = {
            ['HAND'] = { -- Prop position in hand 
                BoneID = 64097,
                Offset = vector3(0.020, 0.02, -0.008),
                Rot = vector3(100.0, 0.0, 100.0),},
            ['MOUTH'] = { -- Prop position in mouth 
                BoneID = 47419,
                Offset = vector3(0.01, -0.01, 0.01),
                Rot = vector3(-2.0, 0.0, 96.0),},
            ['EAR'] = { -- Prop position behind the ear
                BoneID = 12844,
                Offset = vector3(0.05, -0.03, -0.08),
                Rot = vector3(80.0, 7.0, -118.0),},
        },
        Particles = {
            ['Exhale'] = {-- Smoke particles after exhale
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.1,
                Offset = vector3(0.014, -0.003, 0.008),
                Rot = vector3(55.0, 0.0, 110.0),},
            ['Prop'] = {-- Smoke particles on prop 
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.03,
                Offset = vector3(-0.050, 0.0, 0.0),
                Rot = vector3(0.0, 0.0, 0.0),}
        },
        Anims = {-- New animations can be added to each category and are always selected randomly
            ['USE'] = {
                [1] = {anim = 'idle_a', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [2] = {anim = 'idle_b', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [3] = {anim = 'idle_c', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [4] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1300, 49, 1}},
            },
            ['MOUTH'] = {
                [1] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['EAR'] = {
                [1] = {anim = 'takecig_clip', dict = 'takecig@devcore', params = {1.0, -1.0, 1500, 49, 1}},
            },
            ['COUGH'] = {
                [1] = {anim = 'idle_cough', dict = 'timetable@gardener@smoking_joint', params = {1.0, -1.0, 2500, 49, 1}},
            },
            ['THROW'] = {
                [1] = {anim = 'putdown_low', dict = 'pickup_object', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['GIVE'] = {
                [1] = {anim = 'givetake1_a', dict = 'mp_common', params = {1.0, -1.0, 1800, 49, 1}},
            },
            ['TAKEOUT'] = {
                [1] = {anim = 'enter', dict = 'amb@world_human_smoking@male@male_a@enter', params = {1.0, -1.0, 1300, 49, 1}},
            },
        },
    },

    ['northern_lights_joint'] = { -- ITEM NAME
        Type = 'NEED_LIGHTER', -- NEED_LIGHTER (joints, cigarette, cigars), BONG(bongs), VAPE(Must be topped up with liquid), VAPE-PUFF(It has an adjustable maximum amount of puffs)
        Prop = {Default = 'p_cs_joint_02', Light = 'p_cs_joint_01'}, -- Default is prop before lighting and Light is prop after lighting the cigarette (if you want to use only one prop, remove the Light prop and leave only the Default prop)
        Setting = {
            Size = 100,  --  TYPE : NEED_LIGHTER : set a value that decreases gradually after the item is lit, the larger the number you set, the longer the item can be smoked. 
            Remove = 0.3, Time = 3}, -- The size is subtracted by 0.3 every 3 seconds during smoking, depending on the setting (ONLY FOR: NEED_LIGHTER).
        AfterExhale = {
            ExhaleTime = {min = 1, max = 2}, -- Smoke exhalation time in sec
            RemoveAfterExhale = 5, -- The set value is taken after each exhalation of smoke from the size
            ExhaleEffect = {enable = true, intensity = 40, effect = false}, -- screen blackout effect after smoke exhalation and Animpostfx effect-- https://forge.plebmasters.de/animpostfx
            ShakeCam = {effect = 'VIBRATE_SHAKE', value = 2.0}, -- Shake cam effect after exhale smoke for disable, effect = false
            CoughChance = 2, -- false if you don't want to have any chance of coughing / 1 is a 100% chance, the higher the value you set, the lower the chance will be.
        Status = {
            ['ARMOR'] = 35, -- Adds armor after each puff of smoke
            ['STRESS'] = math.random(5, 10), -- Removes stress after smoke exhalation
            ['HIGH'] = 1, -- Adds a high after exhaling smoke
            }
        },
        Positions = {
            ['HAND'] = { -- Prop position in hand 
                BoneID = 64097,
                Offset = vector3(0.020, 0.02, -0.008),
                Rot = vector3(100.0, 0.0, 100.0),},
            ['MOUTH'] = { -- Prop position in mouth 
                BoneID = 47419,
                Offset = vector3(0.01, -0.01, 0.01),
                Rot = vector3(-2.0, 0.0, 96.0),},
            ['EAR'] = { -- Prop position behind the ear
                BoneID = 12844,
                Offset = vector3(0.05, -0.03, -0.08),
                Rot = vector3(80.0, 7.0, -118.0),},
        },
        Particles = {
            ['Exhale'] = {-- Smoke particles after exhale
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.1,
                Offset = vector3(0.014, -0.003, 0.008),
                Rot = vector3(55.0, 0.0, 110.0),},
            ['Prop'] = {-- Smoke particles on prop 
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.03,
                Offset = vector3(-0.050, 0.0, 0.0),
                Rot = vector3(0.0, 0.0, 0.0),}
        },
        Anims = {-- New animations can be added to each category and are always selected randomly
            ['USE'] = {
                [1] = {anim = 'idle_a', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [2] = {anim = 'idle_b', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [3] = {anim = 'idle_c', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [4] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1300, 49, 1}},
            },
            ['MOUTH'] = {
                [1] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['EAR'] = {
                [1] = {anim = 'takecig_clip', dict = 'takecig@devcore', params = {1.0, -1.0, 1500, 49, 1}},
            },
            ['COUGH'] = {
                [1] = {anim = 'idle_cough', dict = 'timetable@gardener@smoking_joint', params = {1.0, -1.0, 2500, 49, 1}},
            },
            ['THROW'] = {
                [1] = {anim = 'putdown_low', dict = 'pickup_object', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['GIVE'] = {
                [1] = {anim = 'givetake1_a', dict = 'mp_common', params = {1.0, -1.0, 1800, 49, 1}},
            },
            ['TAKEOUT'] = {
                [1] = {anim = 'enter', dict = 'amb@world_human_smoking@male@male_a@enter', params = {1.0, -1.0, 1300, 49, 1}},
            },
        },
    },

    ['maui_waui_joint'] = { -- ITEM NAME
        Type = 'NEED_LIGHTER', -- NEED_LIGHTER (joints, cigarette, cigars), BONG(bongs), VAPE(Must be topped up with liquid), VAPE-PUFF(It has an adjustable maximum amount of puffs)
        Prop = {Default = 'p_cs_joint_02', Light = 'p_cs_joint_01'}, -- Default is prop before lighting and Light is prop after lighting the cigarette (if you want to use only one prop, remove the Light prop and leave only the Default prop)
        Setting = {
            Size = 100,  --  TYPE : NEED_LIGHTER : set a value that decreases gradually after the item is lit, the larger the number you set, the longer the item can be smoked. 
            Remove = 0.3, Time = 3}, -- The size is subtracted by 0.3 every 3 seconds during smoking, depending on the setting (ONLY FOR: NEED_LIGHTER).
        AfterExhale = {
            ExhaleTime = {min = 1, max = 2}, -- Smoke exhalation time in sec
            RemoveAfterExhale = 5, -- The set value is taken after each exhalation of smoke from the size
            ExhaleEffect = {enable = true, intensity = 40, effect = false}, -- screen blackout effect after smoke exhalation and Animpostfx effect-- https://forge.plebmasters.de/animpostfx
            ShakeCam = {effect = 'VIBRATE_SHAKE', value = 2.0}, -- Shake cam effect after exhale smoke for disable, effect = false
            CoughChance = 2, -- false if you don't want to have any chance of coughing / 1 is a 100% chance, the higher the value you set, the lower the chance will be.
        Status = {
            ['ARMOR'] = 35, -- Adds armor after each puff of smoke
            ['STRESS'] = math.random(5, 10), -- Removes stress after smoke exhalation
            ['HIGH'] = 1, -- Adds a high after exhaling smoke
            }
        },
        Positions = {
            ['HAND'] = { -- Prop position in hand 
                BoneID = 64097,
                Offset = vector3(0.020, 0.02, -0.008),
                Rot = vector3(100.0, 0.0, 100.0),},
            ['MOUTH'] = { -- Prop position in mouth 
                BoneID = 47419,
                Offset = vector3(0.01, -0.01, 0.01),
                Rot = vector3(-2.0, 0.0, 96.0),},
            ['EAR'] = { -- Prop position behind the ear
                BoneID = 12844,
                Offset = vector3(0.05, -0.03, -0.08),
                Rot = vector3(80.0, 7.0, -118.0),},
        },
        Particles = {
            ['Exhale'] = {-- Smoke particles after exhale
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.1,
                Offset = vector3(0.014, -0.003, 0.008),
                Rot = vector3(55.0, 0.0, 110.0),},
            ['Prop'] = {-- Smoke particles on prop 
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.03,
                Offset = vector3(-0.050, 0.0, 0.0),
                Rot = vector3(0.0, 0.0, 0.0),}
        },
        Anims = {-- New animations can be added to each category and are always selected randomly
            ['USE'] = {
                [1] = {anim = 'idle_a', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [2] = {anim = 'idle_b', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [3] = {anim = 'idle_c', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [4] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1300, 49, 1}},
            },
            ['MOUTH'] = {
                [1] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['EAR'] = {
                [1] = {anim = 'takecig_clip', dict = 'takecig@devcore', params = {1.0, -1.0, 1500, 49, 1}},
            },
            ['COUGH'] = {
                [1] = {anim = 'idle_cough', dict = 'timetable@gardener@smoking_joint', params = {1.0, -1.0, 2500, 49, 1}},
            },
            ['THROW'] = {
                [1] = {anim = 'putdown_low', dict = 'pickup_object', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['GIVE'] = {
                [1] = {anim = 'givetake1_a', dict = 'mp_common', params = {1.0, -1.0, 1800, 49, 1}},
            },
            ['TAKEOUT'] = {
                [1] = {anim = 'enter', dict = 'amb@world_human_smoking@male@male_a@enter', params = {1.0, -1.0, 1300, 49, 1}},
            },
        },
    },

    ['coop_goop_joint'] = { -- ITEM NAME
        Type = 'NEED_LIGHTER', -- NEED_LIGHTER (joints, cigarette, cigars), BONG(bongs), VAPE(Must be topped up with liquid), VAPE-PUFF(It has an adjustable maximum amount of puffs)
        Prop = {Default = 'p_cs_joint_02', Light = 'p_cs_joint_01'}, -- Default is prop before lighting and Light is prop after lighting the cigarette (if you want to use only one prop, remove the Light prop and leave only the Default prop)
        Setting = {
            Size = 100,  --  TYPE : NEED_LIGHTER : set a value that decreases gradually after the item is lit, the larger the number you set, the longer the item can be smoked. 
            Remove = 0.3, Time = 3}, -- The size is subtracted by 0.3 every 3 seconds during smoking, depending on the setting (ONLY FOR: NEED_LIGHTER).
        AfterExhale = {
            ExhaleTime = {min = 1, max = 2}, -- Smoke exhalation time in sec
            RemoveAfterExhale = 5, -- The set value is taken after each exhalation of smoke from the size
            ExhaleEffect = {enable = true, intensity = 40, effect = false}, -- screen blackout effect after smoke exhalation and Animpostfx effect-- https://forge.plebmasters.de/animpostfx
            ShakeCam = {effect = 'VIBRATE_SHAKE', value = 2.0}, -- Shake cam effect after exhale smoke for disable, effect = false
            CoughChance = 2, -- false if you don't want to have any chance of coughing / 1 is a 100% chance, the higher the value you set, the lower the chance will be.
        Status = {
            ['ARMOR'] = 35, -- Adds armor after each puff of smoke
            ['STRESS'] = math.random(5, 10), -- Removes stress after smoke exhalation
            ['HIGH'] = 1, -- Adds a high after exhaling smoke
            }
        },
        Positions = {
            ['HAND'] = { -- Prop position in hand 
                BoneID = 64097,
                Offset = vector3(0.020, 0.02, -0.008),
                Rot = vector3(100.0, 0.0, 100.0),},
            ['MOUTH'] = { -- Prop position in mouth 
                BoneID = 47419,
                Offset = vector3(0.01, -0.01, 0.01),
                Rot = vector3(-2.0, 0.0, 96.0),},
            ['EAR'] = { -- Prop position behind the ear
                BoneID = 12844,
                Offset = vector3(0.05, -0.03, -0.08),
                Rot = vector3(80.0, 7.0, -118.0),},
        },
        Particles = {
            ['Exhale'] = {-- Smoke particles after exhale
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.1,
                Offset = vector3(0.014, -0.003, 0.008),
                Rot = vector3(55.0, 0.0, 110.0),},
            ['Prop'] = {-- Smoke particles on prop 
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.03,
                Offset = vector3(-0.050, 0.0, 0.0),
                Rot = vector3(0.0, 0.0, 0.0),}
        },
        Anims = {-- New animations can be added to each category and are always selected randomly
            ['USE'] = {
                [1] = {anim = 'idle_a', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [2] = {anim = 'idle_b', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [3] = {anim = 'idle_c', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [4] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1300, 49, 1}},
            },
            ['MOUTH'] = {
                [1] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['EAR'] = {
                [1] = {anim = 'takecig_clip', dict = 'takecig@devcore', params = {1.0, -1.0, 1500, 49, 1}},
            },
            ['COUGH'] = {
                [1] = {anim = 'idle_cough', dict = 'timetable@gardener@smoking_joint', params = {1.0, -1.0, 2500, 49, 1}},
            },
            ['THROW'] = {
                [1] = {anim = 'putdown_low', dict = 'pickup_object', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['GIVE'] = {
                [1] = {anim = 'givetake1_a', dict = 'mp_common', params = {1.0, -1.0, 1800, 49, 1}},
            },
            ['TAKEOUT'] = {
                [1] = {anim = 'enter', dict = 'amb@world_human_smoking@male@male_a@enter', params = {1.0, -1.0, 1300, 49, 1}},
            },
        },
    },

    ['sienna_haze_joint'] = { -- ITEM NAME
        Type = 'NEED_LIGHTER', -- NEED_LIGHTER (joints, cigarette, cigars), BONG(bongs), VAPE(Must be topped up with liquid), VAPE-PUFF(It has an adjustable maximum amount of puffs)
        Prop = {Default = 'p_cs_joint_02', Light = 'p_cs_joint_01'}, -- Default is prop before lighting and Light is prop after lighting the cigarette (if you want to use only one prop, remove the Light prop and leave only the Default prop)
        Setting = {
            Size = 100,  --  TYPE : NEED_LIGHTER : set a value that decreases gradually after the item is lit, the larger the number you set, the longer the item can be smoked. 
            Remove = 0.3, Time = 3}, -- The size is subtracted by 0.3 every 3 seconds during smoking, depending on the setting (ONLY FOR: NEED_LIGHTER).
        AfterExhale = {
            ExhaleTime = {min = 1, max = 2}, -- Smoke exhalation time in sec
            RemoveAfterExhale = 5, -- The set value is taken after each exhalation of smoke from the size
            ExhaleEffect = {enable = true, intensity = 40, effect = false}, -- screen blackout effect after smoke exhalation and Animpostfx effect-- https://forge.plebmasters.de/animpostfx
            ShakeCam = {effect = 'VIBRATE_SHAKE', value = 2.0}, -- Shake cam effect after exhale smoke for disable, effect = false
            CoughChance = 2, -- false if you don't want to have any chance of coughing / 1 is a 100% chance, the higher the value you set, the lower the chance will be.
        Status = {
            ['ARMOR'] = 35, -- Adds armor after each puff of smoke
            ['STRESS'] = math.random(5, 10), -- Removes stress after smoke exhalation
            ['HIGH'] = 1, -- Adds a high after exhaling smoke
            }
        },
        Positions = {
            ['HAND'] = { -- Prop position in hand 
                BoneID = 64097,
                Offset = vector3(0.020, 0.02, -0.008),
                Rot = vector3(100.0, 0.0, 100.0),},
            ['MOUTH'] = { -- Prop position in mouth 
                BoneID = 47419,
                Offset = vector3(0.01, -0.01, 0.01),
                Rot = vector3(-2.0, 0.0, 96.0),},
            ['EAR'] = { -- Prop position behind the ear
                BoneID = 12844,
                Offset = vector3(0.05, -0.03, -0.08),
                Rot = vector3(80.0, 7.0, -118.0),},
        },
        Particles = {
            ['Exhale'] = {-- Smoke particles after exhale
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.1,
                Offset = vector3(0.014, -0.003, 0.008),
                Rot = vector3(55.0, 0.0, 110.0),},
            ['Prop'] = {-- Smoke particles on prop 
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.03,
                Offset = vector3(-0.050, 0.0, 0.0),
                Rot = vector3(0.0, 0.0, 0.0),}
        },
        Anims = {-- New animations can be added to each category and are always selected randomly
            ['USE'] = {
                [1] = {anim = 'idle_a', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [2] = {anim = 'idle_b', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [3] = {anim = 'idle_c', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [4] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1300, 49, 1}},
            },
            ['MOUTH'] = {
                [1] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['EAR'] = {
                [1] = {anim = 'takecig_clip', dict = 'takecig@devcore', params = {1.0, -1.0, 1500, 49, 1}},
            },
            ['COUGH'] = {
                [1] = {anim = 'idle_cough', dict = 'timetable@gardener@smoking_joint', params = {1.0, -1.0, 2500, 49, 1}},
            },
            ['THROW'] = {
                [1] = {anim = 'putdown_low', dict = 'pickup_object', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['GIVE'] = {
                [1] = {anim = 'givetake1_a', dict = 'mp_common', params = {1.0, -1.0, 1800, 49, 1}},
            },
            ['TAKEOUT'] = {
                [1] = {anim = 'enter', dict = 'amb@world_human_smoking@male@male_a@enter', params = {1.0, -1.0, 1300, 49, 1}},
            },
        },
    },

    ['frankistank_joint'] = { -- ITEM NAME
        Type = 'NEED_LIGHTER', -- NEED_LIGHTER (joints, cigarette, cigars), BONG(bongs), VAPE(Must be topped up with liquid), VAPE-PUFF(It has an adjustable maximum amount of puffs)
        Prop = {Default = 'p_cs_joint_02', Light = 'p_cs_joint_01'}, -- Default is prop before lighting and Light is prop after lighting the cigarette (if you want to use only one prop, remove the Light prop and leave only the Default prop)
        Setting = {
            Size = 100,  --  TYPE : NEED_LIGHTER : set a value that decreases gradually after the item is lit, the larger the number you set, the longer the item can be smoked. 
            Remove = 0.3, Time = 3}, -- The size is subtracted by 0.3 every 3 seconds during smoking, depending on the setting (ONLY FOR: NEED_LIGHTER).
        AfterExhale = {
            ExhaleTime = {min = 1, max = 2}, -- Smoke exhalation time in sec
            RemoveAfterExhale = 5, -- The set value is taken after each exhalation of smoke from the size
            ExhaleEffect = {enable = true, intensity = 40, effect = false}, -- screen blackout effect after smoke exhalation and Animpostfx effect-- https://forge.plebmasters.de/animpostfx
            ShakeCam = {effect = 'VIBRATE_SHAKE', value = 2.0}, -- Shake cam effect after exhale smoke for disable, effect = false
            CoughChance = 2, -- false if you don't want to have any chance of coughing / 1 is a 100% chance, the higher the value you set, the lower the chance will be.
        Status = {
            ['ARMOR'] = 35, -- Adds armor after each puff of smoke
            ['STRESS'] = math.random(5, 10), -- Removes stress after smoke exhalation
            ['HIGH'] = 1, -- Adds a high after exhaling smoke
            }
        },
        Positions = {
            ['HAND'] = { -- Prop position in hand 
                BoneID = 64097,
                Offset = vector3(0.020, 0.02, -0.008),
                Rot = vector3(100.0, 0.0, 100.0),},
            ['MOUTH'] = { -- Prop position in mouth 
                BoneID = 47419,
                Offset = vector3(0.01, -0.01, 0.01),
                Rot = vector3(-2.0, 0.0, 96.0),},
            ['EAR'] = { -- Prop position behind the ear
                BoneID = 12844,
                Offset = vector3(0.05, -0.03, -0.08),
                Rot = vector3(80.0, 7.0, -118.0),},
        },
        Particles = {
            ['Exhale'] = {-- Smoke particles after exhale
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.1,
                Offset = vector3(0.014, -0.003, 0.008),
                Rot = vector3(55.0, 0.0, 110.0),},
            ['Prop'] = {-- Smoke particles on prop 
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.03,
                Offset = vector3(-0.050, 0.0, 0.0),
                Rot = vector3(0.0, 0.0, 0.0),}
        },
        Anims = {-- New animations can be added to each category and are always selected randomly
            ['USE'] = {
                [1] = {anim = 'idle_a', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [2] = {anim = 'idle_b', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [3] = {anim = 'idle_c', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [4] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1300, 49, 1}},
            },
            ['MOUTH'] = {
                [1] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['EAR'] = {
                [1] = {anim = 'takecig_clip', dict = 'takecig@devcore', params = {1.0, -1.0, 1500, 49, 1}},
            },
            ['COUGH'] = {
                [1] = {anim = 'idle_cough', dict = 'timetable@gardener@smoking_joint', params = {1.0, -1.0, 2500, 49, 1}},
            },
            ['THROW'] = {
                [1] = {anim = 'putdown_low', dict = 'pickup_object', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['GIVE'] = {
                [1] = {anim = 'givetake1_a', dict = 'mp_common', params = {1.0, -1.0, 1800, 49, 1}},
            },
            ['TAKEOUT'] = {
                [1] = {anim = 'enter', dict = 'amb@world_human_smoking@male@male_a@enter', params = {1.0, -1.0, 1300, 49, 1}},
            },
        },
    },

    ----- cloud9
    ['joint_pink'] = {
        Type = 'NEED_LIGHTER', -- NEED_LIGHTER (joints, cigarette, cigars), BONG(bongs), VAPE(Must be topped up with liquid), VAPE-PUFF(It has an adjustable maximum amount of puffs)
        Prop = {Default = 'prop_cs_ciggy_01', Light = 'prop_cs_ciggy_01b'}, -- Default is prop before lighting and Light is prop after lighting the cigarette (if you want to use only one prop, remove the Light prop and leave only the Default prop)
        Setting = {
            Size = 100, -- The size for the NEED_LIGHTER type sets a value that decreases gradually after the item is lit, the larger the number you set, the longer the item can be smoked.
            Remove = 0.3, Time = 3}, -- The size is subtracted by 0.3 every 3 seconds during smoking, depending on the setting (ONLY FOR: NEED_LIGHTER).
        AfterExhale = {
            ExhaleTime = {min = 1, max = 1}, -- Smoke exhalation time
            RemoveAfterExhale = 5, -- The set value is taken after each exhalation of smoke from the size 
            ExhaleEffect = {enable = true, intensity = 60, effect = false}, -- screen blackout effect after smoke exhalation and Animpostfx effect-- https://forge.plebmasters.de/animpostfx
            ShakeCam = {effect = 'VIBRATE_SHAKE', value = 0.5}, -- Shake cam effect after exhale smoke for disable, effect = false
            CoughChance = false, -- false if you don't want to have any chance of coughing / 1 is a 100% chance, the higher the value you set, the lower the chance will be.
        Status = {
            ['ARMOR'] = 35, -- Adds armor after each puff of smoke
            ['STRESS'] = math.random(5, 10), -- Removes stress after smoke exhalation
            ['HIGH'] = 1, -- Adds a high after exhaling smoke
            }
        },
        Positions = {
            ['HAND'] = { -- Prop position in hand
                BoneID = 64097,
                Offset = vector3(0.020, 0.02, -0.008),
                Rot = vector3(100.0, 0.0, 100.0),},
            ['MOUTH'] = { -- Prop position in mouth 
                BoneID = 47419,
                Offset = vector3(0.014, -0.003, 0.008),
                Rot = vector3(55.0, 0.0, 110.0),},
            ['EAR'] = { -- Prop position behind the ear
                BoneID = 12844,
                Offset = vector3(0.05, -0.03, -0.08),
                Rot = vector3(80.0, 7.0, -118.0),},
        },
        Particles = {
            ['Exhale'] = {-- Smoke particles after exhale
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.1,
                Offset = vector3(0.014, -0.003, 0.008),
                Rot = vector3(55.0, 0.0, 110.0),},
            ['Prop'] = {-- Smoke particles on prop 
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.02,
                Offset = vector3(-0.050, 0.0, 0.0),
                Rot = vector3(0.0, 0.0, 0.0),}
        },
        Anims = { -- New animations can be added to each category and are always selected randomly
            ['USE'] = {
                [1] = {anim = 'idle_a', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [2] = {anim = 'idle_b', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [3] = {anim = 'idle_c', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [4] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1600, 49, 1}},
            },
            ['MOUTH'] = {
                [1] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['EAR'] = {
                [1] = {anim = 'takecig_clip', dict = 'takecig@devcore', params = {1.0, -1.0, 1500, 49, 1}},
            },
            ['COUGH'] = {
                [1] = {anim = 'idle_cough', dict = 'timetable@gardener@smoking_joint', params = {1.0, -1.0, 2500, 49, 1}},
            },
            ['THROW'] = {
                [1] = {anim = 'putdown_low', dict = 'pickup_object', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['GIVE'] = {
                [1] = {anim = 'givetake1_a', dict = 'mp_common', params = {1.0, -1.0, 1800, 49, 1}},
            },
            ['TAKEOUT'] = {
                [1] = {anim = 'enter', dict = 'amb@world_human_smoking@male@male_a@enter', params = {1.0, -1.0, 1300, 49, 1}},
            },
        },
    },
    ['joint_black'] = {
        Type = 'NEED_LIGHTER', -- NEED_LIGHTER (joints, cigarette, cigars), BONG(bongs), VAPE(Must be topped up with liquid), VAPE-PUFF(It has an adjustable maximum amount of puffs)
        Prop = {Default = 'prop_cs_ciggy_01', Light = 'prop_cs_ciggy_01b'}, -- Default is prop before lighting and Light is prop after lighting the cigarette (if you want to use only one prop, remove the Light prop and leave only the Default prop)
        Setting = {
            Size = 100, -- The size for the NEED_LIGHTER type sets a value that decreases gradually after the item is lit, the larger the number you set, the longer the item can be smoked.
            Remove = 0.3, Time = 3}, -- The size is subtracted by 0.3 every 3 seconds during smoking, depending on the setting (ONLY FOR: NEED_LIGHTER).
        AfterExhale = {
            ExhaleTime = {min = 1, max = 1}, -- Smoke exhalation time
            RemoveAfterExhale = 5, -- The set value is taken after each exhalation of smoke from the size 
            ExhaleEffect = {enable = true, intensity = 60, effect = false}, -- screen blackout effect after smoke exhalation and Animpostfx effect-- https://forge.plebmasters.de/animpostfx
            ShakeCam = {effect = 'VIBRATE_SHAKE', value = 0.5}, -- Shake cam effect after exhale smoke for disable, effect = false
            CoughChance = false, -- false if you don't want to have any chance of coughing / 1 is a 100% chance, the higher the value you set, the lower the chance will be.
        Status = {
            ['ARMOR'] = 35, -- Adds armor after each puff of smoke
            ['STRESS'] = math.random(5, 10), -- Removes stress after smoke exhalation
            ['HIGH'] = 1, -- Adds a high after exhaling smoke
            }
        },
        Positions = {
            ['HAND'] = { -- Prop position in hand
                BoneID = 64097,
                Offset = vector3(0.020, 0.02, -0.008),
                Rot = vector3(100.0, 0.0, 100.0),},
            ['MOUTH'] = { -- Prop position in mouth 
                BoneID = 47419,
                Offset = vector3(0.014, -0.003, 0.008),
                Rot = vector3(55.0, 0.0, 110.0),},
            ['EAR'] = { -- Prop position behind the ear
                BoneID = 12844,
                Offset = vector3(0.05, -0.03, -0.08),
                Rot = vector3(80.0, 7.0, -118.0),},
        },
        Particles = {
            ['Exhale'] = {-- Smoke particles after exhale
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.1,
                Offset = vector3(0.014, -0.003, 0.008),
                Rot = vector3(55.0, 0.0, 110.0),},
            ['Prop'] = {-- Smoke particles on prop 
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.02,
                Offset = vector3(-0.050, 0.0, 0.0),
                Rot = vector3(0.0, 0.0, 0.0),}
        },
        Anims = { -- New animations can be added to each category and are always selected randomly
            ['USE'] = {
                [1] = {anim = 'idle_a', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [2] = {anim = 'idle_b', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [3] = {anim = 'idle_c', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [4] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1600, 49, 1}},
            },
            ['MOUTH'] = {
                [1] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['EAR'] = {
                [1] = {anim = 'takecig_clip', dict = 'takecig@devcore', params = {1.0, -1.0, 1500, 49, 1}},
            },
            ['COUGH'] = {
                [1] = {anim = 'idle_cough', dict = 'timetable@gardener@smoking_joint', params = {1.0, -1.0, 2500, 49, 1}},
            },
            ['THROW'] = {
                [1] = {anim = 'putdown_low', dict = 'pickup_object', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['GIVE'] = {
                [1] = {anim = 'givetake1_a', dict = 'mp_common', params = {1.0, -1.0, 1800, 49, 1}},
            },
            ['TAKEOUT'] = {
                [1] = {anim = 'enter', dict = 'amb@world_human_smoking@male@male_a@enter', params = {1.0, -1.0, 1300, 49, 1}},
            },
        },
    },
    ['joint_lblue'] = {
        Type = 'NEED_LIGHTER', -- NEED_LIGHTER (joints, cigarette, cigars), BONG(bongs), VAPE(Must be topped up with liquid), VAPE-PUFF(It has an adjustable maximum amount of puffs)
        Prop = {Default = 'prop_cs_ciggy_01', Light = 'prop_cs_ciggy_01b'}, -- Default is prop before lighting and Light is prop after lighting the cigarette (if you want to use only one prop, remove the Light prop and leave only the Default prop)
        Setting = {
            Size = 100, -- The size for the NEED_LIGHTER type sets a value that decreases gradually after the item is lit, the larger the number you set, the longer the item can be smoked.
            Remove = 0.3, Time = 3}, -- The size is subtracted by 0.3 every 3 seconds during smoking, depending on the setting (ONLY FOR: NEED_LIGHTER).
        AfterExhale = {
            ExhaleTime = {min = 1, max = 1}, -- Smoke exhalation time
            RemoveAfterExhale = 5, -- The set value is taken after each exhalation of smoke from the size 
            ExhaleEffect = {enable = true, intensity = 60, effect = false}, -- screen blackout effect after smoke exhalation and Animpostfx effect-- https://forge.plebmasters.de/animpostfx
            ShakeCam = {effect = 'VIBRATE_SHAKE', value = 0.5}, -- Shake cam effect after exhale smoke for disable, effect = false
            CoughChance = false, -- false if you don't want to have any chance of coughing / 1 is a 100% chance, the higher the value you set, the lower the chance will be.
        Status = {
            ['ARMOR'] = 35, -- Adds armor after each puff of smoke
            ['STRESS'] = math.random(5, 10), -- Removes stress after smoke exhalation
            ['HIGH'] = 1, -- Adds a high after exhaling smoke
            }
        },
        Positions = {
            ['HAND'] = { -- Prop position in hand
                BoneID = 64097,
                Offset = vector3(0.020, 0.02, -0.008),
                Rot = vector3(100.0, 0.0, 100.0),},
            ['MOUTH'] = { -- Prop position in mouth 
                BoneID = 47419,
                Offset = vector3(0.014, -0.003, 0.008),
                Rot = vector3(55.0, 0.0, 110.0),},
            ['EAR'] = { -- Prop position behind the ear
                BoneID = 12844,
                Offset = vector3(0.05, -0.03, -0.08),
                Rot = vector3(80.0, 7.0, -118.0),},
        },
        Particles = {
            ['Exhale'] = {-- Smoke particles after exhale
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.1,
                Offset = vector3(0.014, -0.003, 0.008),
                Rot = vector3(55.0, 0.0, 110.0),},
            ['Prop'] = {-- Smoke particles on prop 
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.02,
                Offset = vector3(-0.050, 0.0, 0.0),
                Rot = vector3(0.0, 0.0, 0.0),}
        },
        Anims = { -- New animations can be added to each category and are always selected randomly
            ['USE'] = {
                [1] = {anim = 'idle_a', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [2] = {anim = 'idle_b', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [3] = {anim = 'idle_c', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [4] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1600, 49, 1}},
            },
            ['MOUTH'] = {
                [1] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['EAR'] = {
                [1] = {anim = 'takecig_clip', dict = 'takecig@devcore', params = {1.0, -1.0, 1500, 49, 1}},
            },
            ['COUGH'] = {
                [1] = {anim = 'idle_cough', dict = 'timetable@gardener@smoking_joint', params = {1.0, -1.0, 2500, 49, 1}},
            },
            ['THROW'] = {
                [1] = {anim = 'putdown_low', dict = 'pickup_object', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['GIVE'] = {
                [1] = {anim = 'givetake1_a', dict = 'mp_common', params = {1.0, -1.0, 1800, 49, 1}},
            },
            ['TAKEOUT'] = {
                [1] = {anim = 'enter', dict = 'amb@world_human_smoking@male@male_a@enter', params = {1.0, -1.0, 1300, 49, 1}},
            },
        },
    },
    ['joint_red'] = {
        Type = 'NEED_LIGHTER', -- NEED_LIGHTER (joints, cigarette, cigars), BONG(bongs), VAPE(Must be topped up with liquid), VAPE-PUFF(It has an adjustable maximum amount of puffs)
        Prop = {Default = 'prop_cs_ciggy_01', Light = 'prop_cs_ciggy_01b'}, -- Default is prop before lighting and Light is prop after lighting the cigarette (if you want to use only one prop, remove the Light prop and leave only the Default prop)
        Setting = {
            Size = 100, -- The size for the NEED_LIGHTER type sets a value that decreases gradually after the item is lit, the larger the number you set, the longer the item can be smoked.
            Remove = 0.3, Time = 3}, -- The size is subtracted by 0.3 every 3 seconds during smoking, depending on the setting (ONLY FOR: NEED_LIGHTER).
        AfterExhale = {
            ExhaleTime = {min = 1, max = 1}, -- Smoke exhalation time
            RemoveAfterExhale = 5, -- The set value is taken after each exhalation of smoke from the size 
            ExhaleEffect = {enable = true, intensity = 60, effect = false}, -- screen blackout effect after smoke exhalation and Animpostfx effect-- https://forge.plebmasters.de/animpostfx
            ShakeCam = {effect = 'VIBRATE_SHAKE', value = 0.5}, -- Shake cam effect after exhale smoke for disable, effect = false
            CoughChance = false, -- false if you don't want to have any chance of coughing / 1 is a 100% chance, the higher the value you set, the lower the chance will be.
        Status = {
            ['ARMOR'] = 35, -- Adds armor after each puff of smoke
            ['STRESS'] = math.random(5, 10), -- Removes stress after smoke exhalation
            ['HIGH'] = 1, -- Adds a high after exhaling smoke
            }
        },
        Positions = {
            ['HAND'] = { -- Prop position in hand
                BoneID = 64097,
                Offset = vector3(0.020, 0.02, -0.008),
                Rot = vector3(100.0, 0.0, 100.0),},
            ['MOUTH'] = { -- Prop position in mouth 
                BoneID = 47419,
                Offset = vector3(0.014, -0.003, 0.008),
                Rot = vector3(55.0, 0.0, 110.0),},
            ['EAR'] = { -- Prop position behind the ear
                BoneID = 12844,
                Offset = vector3(0.05, -0.03, -0.08),
                Rot = vector3(80.0, 7.0, -118.0),},
        },
        Particles = {
            ['Exhale'] = {-- Smoke particles after exhale
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.1,
                Offset = vector3(0.014, -0.003, 0.008),
                Rot = vector3(55.0, 0.0, 110.0),},
            ['Prop'] = {-- Smoke particles on prop 
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.02,
                Offset = vector3(-0.050, 0.0, 0.0),
                Rot = vector3(0.0, 0.0, 0.0),}
        },
        Anims = { -- New animations can be added to each category and are always selected randomly
            ['USE'] = {
                [1] = {anim = 'idle_a', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [2] = {anim = 'idle_b', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [3] = {anim = 'idle_c', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [4] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1600, 49, 1}},
            },
            ['MOUTH'] = {
                [1] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['EAR'] = {
                [1] = {anim = 'takecig_clip', dict = 'takecig@devcore', params = {1.0, -1.0, 1500, 49, 1}},
            },
            ['COUGH'] = {
                [1] = {anim = 'idle_cough', dict = 'timetable@gardener@smoking_joint', params = {1.0, -1.0, 2500, 49, 1}},
            },
            ['THROW'] = {
                [1] = {anim = 'putdown_low', dict = 'pickup_object', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['GIVE'] = {
                [1] = {anim = 'givetake1_a', dict = 'mp_common', params = {1.0, -1.0, 1800, 49, 1}},
            },
            ['TAKEOUT'] = {
                [1] = {anim = 'enter', dict = 'amb@world_human_smoking@male@male_a@enter', params = {1.0, -1.0, 1300, 49, 1}},
            },
        },
    },
    ['joint_purple'] = {
        Type = 'NEED_LIGHTER', -- NEED_LIGHTER (joints, cigarette, cigars), BONG(bongs), VAPE(Must be topped up with liquid), VAPE-PUFF(It has an adjustable maximum amount of puffs)
        Prop = {Default = 'prop_cs_ciggy_01', Light = 'prop_cs_ciggy_01b'}, -- Default is prop before lighting and Light is prop after lighting the cigarette (if you want to use only one prop, remove the Light prop and leave only the Default prop)
        Setting = {
            Size = 100, -- The size for the NEED_LIGHTER type sets a value that decreases gradually after the item is lit, the larger the number you set, the longer the item can be smoked.
            Remove = 0.3, Time = 3}, -- The size is subtracted by 0.3 every 3 seconds during smoking, depending on the setting (ONLY FOR: NEED_LIGHTER).
        AfterExhale = {
            ExhaleTime = {min = 1, max = 1}, -- Smoke exhalation time
            RemoveAfterExhale = 5, -- The set value is taken after each exhalation of smoke from the size 
            ExhaleEffect = {enable = true, intensity = 60, effect = false}, -- screen blackout effect after smoke exhalation and Animpostfx effect-- https://forge.plebmasters.de/animpostfx
            ShakeCam = {effect = 'VIBRATE_SHAKE', value = 0.5}, -- Shake cam effect after exhale smoke for disable, effect = false
            CoughChance = false, -- false if you don't want to have any chance of coughing / 1 is a 100% chance, the higher the value you set, the lower the chance will be.
        Status = {
            ['ARMOR'] = 35, -- Adds armor after each puff of smoke
            ['STRESS'] = math.random(5, 10), -- Removes stress after smoke exhalation
            ['HIGH'] = 1, -- Adds a high after exhaling smoke
            }
        },
        Positions = {
            ['HAND'] = { -- Prop position in hand
                BoneID = 64097,
                Offset = vector3(0.020, 0.02, -0.008),
                Rot = vector3(100.0, 0.0, 100.0),},
            ['MOUTH'] = { -- Prop position in mouth 
                BoneID = 47419,
                Offset = vector3(0.014, -0.003, 0.008),
                Rot = vector3(55.0, 0.0, 110.0),},
            ['EAR'] = { -- Prop position behind the ear
                BoneID = 12844,
                Offset = vector3(0.05, -0.03, -0.08),
                Rot = vector3(80.0, 7.0, -118.0),},
        },
        Particles = {
            ['Exhale'] = {-- Smoke particles after exhale
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.1,
                Offset = vector3(0.014, -0.003, 0.008),
                Rot = vector3(55.0, 0.0, 110.0),},
            ['Prop'] = {-- Smoke particles on prop 
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.02,
                Offset = vector3(-0.050, 0.0, 0.0),
                Rot = vector3(0.0, 0.0, 0.0),}
        },
        Anims = { -- New animations can be added to each category and are always selected randomly
            ['USE'] = {
                [1] = {anim = 'idle_a', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [2] = {anim = 'idle_b', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [3] = {anim = 'idle_c', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [4] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1600, 49, 1}},
            },
            ['MOUTH'] = {
                [1] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['EAR'] = {
                [1] = {anim = 'takecig_clip', dict = 'takecig@devcore', params = {1.0, -1.0, 1500, 49, 1}},
            },
            ['COUGH'] = {
                [1] = {anim = 'idle_cough', dict = 'timetable@gardener@smoking_joint', params = {1.0, -1.0, 2500, 49, 1}},
            },
            ['THROW'] = {
                [1] = {anim = 'putdown_low', dict = 'pickup_object', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['GIVE'] = {
                [1] = {anim = 'givetake1_a', dict = 'mp_common', params = {1.0, -1.0, 1800, 49, 1}},
            },
            ['TAKEOUT'] = {
                [1] = {anim = 'enter', dict = 'amb@world_human_smoking@male@male_a@enter', params = {1.0, -1.0, 1300, 49, 1}},
            },
        },
    },

    ---- High note
    ['saltyryan'] = {
        Type = 'NEED_LIGHTER', -- NEED_LIGHTER (joints, cigarette, cigars), BONG(bongs), VAPE(Must be topped up with liquid), VAPE-PUFF(It has an adjustable maximum amount of puffs)
        Prop = {Default = 'prop_cs_ciggy_01', Light = 'prop_cs_ciggy_01b'}, -- Default is prop before lighting and Light is prop after lighting the cigarette (if you want to use only one prop, remove the Light prop and leave only the Default prop)
        Setting = {
            Size = 100, -- The size for the NEED_LIGHTER type sets a value that decreases gradually after the item is lit, the larger the number you set, the longer the item can be smoked.
            Remove = 0.3, Time = 3}, -- The size is subtracted by 0.3 every 3 seconds during smoking, depending on the setting (ONLY FOR: NEED_LIGHTER).
        AfterExhale = {
            ExhaleTime = {min = 1, max = 1}, -- Smoke exhalation time
            RemoveAfterExhale = 5, -- The set value is taken after each exhalation of smoke from the size 
            ExhaleEffect = {enable = true, intensity = 60, effect = false}, -- screen blackout effect after smoke exhalation and Animpostfx effect-- https://forge.plebmasters.de/animpostfx
            ShakeCam = {effect = 'VIBRATE_SHAKE', value = 0.5}, -- Shake cam effect after exhale smoke for disable, effect = false
            CoughChance = false, -- false if you don't want to have any chance of coughing / 1 is a 100% chance, the higher the value you set, the lower the chance will be.
        Status = {
            ['ARMOR'] = 35, -- Adds armor after each puff of smoke
            ['STRESS'] = math.random(5, 10), -- Removes stress after smoke exhalation
            ['HIGH'] = 1, -- Adds a high after exhaling smoke
            }
        },
        Positions = {
            ['HAND'] = { -- Prop position in hand
                BoneID = 64097,
                Offset = vector3(0.020, 0.02, -0.008),
                Rot = vector3(100.0, 0.0, 100.0),},
            ['MOUTH'] = { -- Prop position in mouth 
                BoneID = 47419,
                Offset = vector3(0.014, -0.003, 0.008),
                Rot = vector3(55.0, 0.0, 110.0),},
            ['EAR'] = { -- Prop position behind the ear
                BoneID = 12844,
                Offset = vector3(0.05, -0.03, -0.08),
                Rot = vector3(80.0, 7.0, -118.0),},
        },
        Particles = {
            ['Exhale'] = {-- Smoke particles after exhale
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.1,
                Offset = vector3(0.014, -0.003, 0.008),
                Rot = vector3(55.0, 0.0, 110.0),},
            ['Prop'] = {-- Smoke particles on prop 
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.02,
                Offset = vector3(-0.050, 0.0, 0.0),
                Rot = vector3(0.0, 0.0, 0.0),}
        },
        Anims = { -- New animations can be added to each category and are always selected randomly
            ['USE'] = {
                [1] = {anim = 'idle_a', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [2] = {anim = 'idle_b', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [3] = {anim = 'idle_c', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [4] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1600, 49, 1}},
            },
            ['MOUTH'] = {
                [1] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['EAR'] = {
                [1] = {anim = 'takecig_clip', dict = 'takecig@devcore', params = {1.0, -1.0, 1500, 49, 1}},
            },
            ['COUGH'] = {
                [1] = {anim = 'idle_cough', dict = 'timetable@gardener@smoking_joint', params = {1.0, -1.0, 2500, 49, 1}},
            },
            ['THROW'] = {
                [1] = {anim = 'putdown_low', dict = 'pickup_object', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['GIVE'] = {
                [1] = {anim = 'givetake1_a', dict = 'mp_common', params = {1.0, -1.0, 1800, 49, 1}},
            },
            ['TAKEOUT'] = {
                [1] = {anim = 'enter', dict = 'amb@world_human_smoking@male@male_a@enter', params = {1.0, -1.0, 1300, 49, 1}},
            },
        },
    },
    ['icedriley'] = {
        Type = 'NEED_LIGHTER', -- NEED_LIGHTER (joints, cigarette, cigars), BONG(bongs), VAPE(Must be topped up with liquid), VAPE-PUFF(It has an adjustable maximum amount of puffs)
        Prop = {Default = 'prop_cs_ciggy_01', Light = 'prop_cs_ciggy_01b'}, -- Default is prop before lighting and Light is prop after lighting the cigarette (if you want to use only one prop, remove the Light prop and leave only the Default prop)
        Setting = {
            Size = 100, -- The size for the NEED_LIGHTER type sets a value that decreases gradually after the item is lit, the larger the number you set, the longer the item can be smoked.
            Remove = 0.3, Time = 3}, -- The size is subtracted by 0.3 every 3 seconds during smoking, depending on the setting (ONLY FOR: NEED_LIGHTER).
        AfterExhale = {
            ExhaleTime = {min = 1, max = 1}, -- Smoke exhalation time
            RemoveAfterExhale = 5, -- The set value is taken after each exhalation of smoke from the size 
            ExhaleEffect = {enable = true, intensity = 60, effect = false}, -- screen blackout effect after smoke exhalation and Animpostfx effect-- https://forge.plebmasters.de/animpostfx
            ShakeCam = {effect = 'VIBRATE_SHAKE', value = 0.5}, -- Shake cam effect after exhale smoke for disable, effect = false
            CoughChance = false, -- false if you don't want to have any chance of coughing / 1 is a 100% chance, the higher the value you set, the lower the chance will be.
        Status = {
            ['ARMOR'] = 35, -- Adds armor after each puff of smoke
            ['STRESS'] = math.random(5, 10), -- Removes stress after smoke exhalation
            ['HIGH'] = 1, -- Adds a high after exhaling smoke
            }
        },
        Positions = {
            ['HAND'] = { -- Prop position in hand
                BoneID = 64097,
                Offset = vector3(0.020, 0.02, -0.008),
                Rot = vector3(100.0, 0.0, 100.0),},
            ['MOUTH'] = { -- Prop position in mouth 
                BoneID = 47419,
                Offset = vector3(0.014, -0.003, 0.008),
                Rot = vector3(55.0, 0.0, 110.0),},
            ['EAR'] = { -- Prop position behind the ear
                BoneID = 12844,
                Offset = vector3(0.05, -0.03, -0.08),
                Rot = vector3(80.0, 7.0, -118.0),},
        },
        Particles = {
            ['Exhale'] = {-- Smoke particles after exhale
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.1,
                Offset = vector3(0.014, -0.003, 0.008),
                Rot = vector3(55.0, 0.0, 110.0),},
            ['Prop'] = {-- Smoke particles on prop 
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.02,
                Offset = vector3(-0.050, 0.0, 0.0),
                Rot = vector3(0.0, 0.0, 0.0),}
        },
        Anims = { -- New animations can be added to each category and are always selected randomly
            ['USE'] = {
                [1] = {anim = 'idle_a', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [2] = {anim = 'idle_b', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [3] = {anim = 'idle_c', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [4] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1600, 49, 1}},
            },
            ['MOUTH'] = {
                [1] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['EAR'] = {
                [1] = {anim = 'takecig_clip', dict = 'takecig@devcore', params = {1.0, -1.0, 1500, 49, 1}},
            },
            ['COUGH'] = {
                [1] = {anim = 'idle_cough', dict = 'timetable@gardener@smoking_joint', params = {1.0, -1.0, 2500, 49, 1}},
            },
            ['THROW'] = {
                [1] = {anim = 'putdown_low', dict = 'pickup_object', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['GIVE'] = {
                [1] = {anim = 'givetake1_a', dict = 'mp_common', params = {1.0, -1.0, 1800, 49, 1}},
            },
            ['TAKEOUT'] = {
                [1] = {anim = 'enter', dict = 'amb@world_human_smoking@male@male_a@enter', params = {1.0, -1.0, 1300, 49, 1}},
            },
        },
    },
    ['orangeweedsicle'] = {
        Type = 'NEED_LIGHTER', -- NEED_LIGHTER (joints, cigarette, cigars), BONG(bongs), VAPE(Must be topped up with liquid), VAPE-PUFF(It has an adjustable maximum amount of puffs)
        Prop = {Default = 'prop_cs_ciggy_01', Light = 'prop_cs_ciggy_01b'}, -- Default is prop before lighting and Light is prop after lighting the cigarette (if you want to use only one prop, remove the Light prop and leave only the Default prop)
        Setting = {
            Size = 100, -- The size for the NEED_LIGHTER type sets a value that decreases gradually after the item is lit, the larger the number you set, the longer the item can be smoked.
            Remove = 0.3, Time = 3}, -- The size is subtracted by 0.3 every 3 seconds during smoking, depending on the setting (ONLY FOR: NEED_LIGHTER).
        AfterExhale = {
            ExhaleTime = {min = 1, max = 1}, -- Smoke exhalation time
            RemoveAfterExhale = 5, -- The set value is taken after each exhalation of smoke from the size 
            ExhaleEffect = {enable = true, intensity = 60, effect = false}, -- screen blackout effect after smoke exhalation and Animpostfx effect-- https://forge.plebmasters.de/animpostfx
            ShakeCam = {effect = 'VIBRATE_SHAKE', value = 0.5}, -- Shake cam effect after exhale smoke for disable, effect = false
            CoughChance = false, -- false if you don't want to have any chance of coughing / 1 is a 100% chance, the higher the value you set, the lower the chance will be.
        Status = {
            ['ARMOR'] = 35, -- Adds armor after each puff of smoke
            ['STRESS'] = math.random(5, 10), -- Removes stress after smoke exhalation
            ['HIGH'] = 1, -- Adds a high after exhaling smoke
            }
        },
        Positions = {
            ['HAND'] = { -- Prop position in hand
                BoneID = 64097,
                Offset = vector3(0.020, 0.02, -0.008),
                Rot = vector3(100.0, 0.0, 100.0),},
            ['MOUTH'] = { -- Prop position in mouth 
                BoneID = 47419,
                Offset = vector3(0.014, -0.003, 0.008),
                Rot = vector3(55.0, 0.0, 110.0),},
            ['EAR'] = { -- Prop position behind the ear
                BoneID = 12844,
                Offset = vector3(0.05, -0.03, -0.08),
                Rot = vector3(80.0, 7.0, -118.0),},
        },
        Particles = {
            ['Exhale'] = {-- Smoke particles after exhale
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.1,
                Offset = vector3(0.014, -0.003, 0.008),
                Rot = vector3(55.0, 0.0, 110.0),},
            ['Prop'] = {-- Smoke particles on prop 
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.02,
                Offset = vector3(-0.050, 0.0, 0.0),
                Rot = vector3(0.0, 0.0, 0.0),}
        },
        Anims = { -- New animations can be added to each category and are always selected randomly
            ['USE'] = {
                [1] = {anim = 'idle_a', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [2] = {anim = 'idle_b', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [3] = {anim = 'idle_c', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [4] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1600, 49, 1}},
            },
            ['MOUTH'] = {
                [1] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['EAR'] = {
                [1] = {anim = 'takecig_clip', dict = 'takecig@devcore', params = {1.0, -1.0, 1500, 49, 1}},
            },
            ['COUGH'] = {
                [1] = {anim = 'idle_cough', dict = 'timetable@gardener@smoking_joint', params = {1.0, -1.0, 2500, 49, 1}},
            },
            ['THROW'] = {
                [1] = {anim = 'putdown_low', dict = 'pickup_object', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['GIVE'] = {
                [1] = {anim = 'givetake1_a', dict = 'mp_common', params = {1.0, -1.0, 1800, 49, 1}},
            },
            ['TAKEOUT'] = {
                [1] = {anim = 'enter', dict = 'amb@world_human_smoking@male@male_a@enter', params = {1.0, -1.0, 1300, 49, 1}},
            },
        },
    },
    ['smorelatte'] = {
        Type = 'NEED_LIGHTER', -- NEED_LIGHTER (joints, cigarette, cigars), BONG(bongs), VAPE(Must be topped up with liquid), VAPE-PUFF(It has an adjustable maximum amount of puffs)
        Prop = {Default = 'prop_cs_ciggy_01', Light = 'prop_cs_ciggy_01b'}, -- Default is prop before lighting and Light is prop after lighting the cigarette (if you want to use only one prop, remove the Light prop and leave only the Default prop)
        Setting = {
            Size = 100, -- The size for the NEED_LIGHTER type sets a value that decreases gradually after the item is lit, the larger the number you set, the longer the item can be smoked.
            Remove = 0.3, Time = 3}, -- The size is subtracted by 0.3 every 3 seconds during smoking, depending on the setting (ONLY FOR: NEED_LIGHTER).
        AfterExhale = {
            ExhaleTime = {min = 1, max = 1}, -- Smoke exhalation time
            RemoveAfterExhale = 5, -- The set value is taken after each exhalation of smoke from the size 
            ExhaleEffect = {enable = true, intensity = 60, effect = false}, -- screen blackout effect after smoke exhalation and Animpostfx effect-- https://forge.plebmasters.de/animpostfx
            ShakeCam = {effect = 'VIBRATE_SHAKE', value = 0.5}, -- Shake cam effect after exhale smoke for disable, effect = false
            CoughChance = false, -- false if you don't want to have any chance of coughing / 1 is a 100% chance, the higher the value you set, the lower the chance will be.
        Status = {
            ['ARMOR'] = 35, -- Adds armor after each puff of smoke
            ['STRESS'] = math.random(5, 10), -- Removes stress after smoke exhalation
            ['HIGH'] = 1, -- Adds a high after exhaling smoke
            }
        },
        Positions = {
            ['HAND'] = { -- Prop position in hand
                BoneID = 64097,
                Offset = vector3(0.020, 0.02, -0.008),
                Rot = vector3(100.0, 0.0, 100.0),},
            ['MOUTH'] = { -- Prop position in mouth 
                BoneID = 47419,
                Offset = vector3(0.014, -0.003, 0.008),
                Rot = vector3(55.0, 0.0, 110.0),},
            ['EAR'] = { -- Prop position behind the ear
                BoneID = 12844,
                Offset = vector3(0.05, -0.03, -0.08),
                Rot = vector3(80.0, 7.0, -118.0),},
        },
        Particles = {
            ['Exhale'] = {-- Smoke particles after exhale
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.1,
                Offset = vector3(0.014, -0.003, 0.008),
                Rot = vector3(55.0, 0.0, 110.0),},
            ['Prop'] = {-- Smoke particles on prop 
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.02,
                Offset = vector3(-0.050, 0.0, 0.0),
                Rot = vector3(0.0, 0.0, 0.0),}
        },
        Anims = { -- New animations can be added to each category and are always selected randomly
            ['USE'] = {
                [1] = {anim = 'idle_a', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [2] = {anim = 'idle_b', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [3] = {anim = 'idle_c', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [4] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1600, 49, 1}},
            },
            ['MOUTH'] = {
                [1] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['EAR'] = {
                [1] = {anim = 'takecig_clip', dict = 'takecig@devcore', params = {1.0, -1.0, 1500, 49, 1}},
            },
            ['COUGH'] = {
                [1] = {anim = 'idle_cough', dict = 'timetable@gardener@smoking_joint', params = {1.0, -1.0, 2500, 49, 1}},
            },
            ['THROW'] = {
                [1] = {anim = 'putdown_low', dict = 'pickup_object', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['GIVE'] = {
                [1] = {anim = 'givetake1_a', dict = 'mp_common', params = {1.0, -1.0, 1800, 49, 1}},
            },
            ['TAKEOUT'] = {
                [1] = {anim = 'enter', dict = 'amb@world_human_smoking@male@male_a@enter', params = {1.0, -1.0, 1300, 49, 1}},
            },
        },
    },
    ['snowblower'] = {
        Type = 'NEED_LIGHTER', -- NEED_LIGHTER (joints, cigarette, cigars), BONG(bongs), VAPE(Must be topped up with liquid), VAPE-PUFF(It has an adjustable maximum amount of puffs)
        Prop = {Default = 'prop_cs_ciggy_01', Light = 'prop_cs_ciggy_01b'}, -- Default is prop before lighting and Light is prop after lighting the cigarette (if you want to use only one prop, remove the Light prop and leave only the Default prop)
        Setting = {
            Size = 100, -- The size for the NEED_LIGHTER type sets a value that decreases gradually after the item is lit, the larger the number you set, the longer the item can be smoked.
            Remove = 0.3, Time = 3}, -- The size is subtracted by 0.3 every 3 seconds during smoking, depending on the setting (ONLY FOR: NEED_LIGHTER).
        AfterExhale = {
            ExhaleTime = {min = 1, max = 1}, -- Smoke exhalation time
            RemoveAfterExhale = 5, -- The set value is taken after each exhalation of smoke from the size 
            ExhaleEffect = {enable = true, intensity = 60, effect = false}, -- screen blackout effect after smoke exhalation and Animpostfx effect-- https://forge.plebmasters.de/animpostfx
            ShakeCam = {effect = 'VIBRATE_SHAKE', value = 0.5}, -- Shake cam effect after exhale smoke for disable, effect = false
            CoughChance = false, -- false if you don't want to have any chance of coughing / 1 is a 100% chance, the higher the value you set, the lower the chance will be.
        Status = {
            ['ARMOR'] = 35, -- Adds armor after each puff of smoke
            ['STRESS'] = math.random(5, 10), -- Removes stress after smoke exhalation
            ['HIGH'] = 1, -- Adds a high after exhaling smoke
            }
        },
        Positions = {
            ['HAND'] = { -- Prop position in hand
                BoneID = 64097,
                Offset = vector3(0.020, 0.02, -0.008),
                Rot = vector3(100.0, 0.0, 100.0),},
            ['MOUTH'] = { -- Prop position in mouth 
                BoneID = 47419,
                Offset = vector3(0.014, -0.003, 0.008),
                Rot = vector3(55.0, 0.0, 110.0),},
            ['EAR'] = { -- Prop position behind the ear
                BoneID = 12844,
                Offset = vector3(0.05, -0.03, -0.08),
                Rot = vector3(80.0, 7.0, -118.0),},
        },
        Particles = {
            ['Exhale'] = {-- Smoke particles after exhale
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.1,
                Offset = vector3(0.014, -0.003, 0.008),
                Rot = vector3(55.0, 0.0, 110.0),},
            ['Prop'] = {-- Smoke particles on prop 
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.02,
                Offset = vector3(-0.050, 0.0, 0.0),
                Rot = vector3(0.0, 0.0, 0.0),}
        },
        Anims = { -- New animations can be added to each category and are always selected randomly
            ['USE'] = {
                [1] = {anim = 'idle_a', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [2] = {anim = 'idle_b', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [3] = {anim = 'idle_c', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [4] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1600, 49, 1}},
            },
            ['MOUTH'] = {
                [1] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['EAR'] = {
                [1] = {anim = 'takecig_clip', dict = 'takecig@devcore', params = {1.0, -1.0, 1500, 49, 1}},
            },
            ['COUGH'] = {
                [1] = {anim = 'idle_cough', dict = 'timetable@gardener@smoking_joint', params = {1.0, -1.0, 2500, 49, 1}},
            },
            ['THROW'] = {
                [1] = {anim = 'putdown_low', dict = 'pickup_object', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['GIVE'] = {
                [1] = {anim = 'givetake1_a', dict = 'mp_common', params = {1.0, -1.0, 1800, 49, 1}},
            },
            ['TAKEOUT'] = {
                [1] = {anim = 'enter', dict = 'amb@world_human_smoking@male@male_a@enter', params = {1.0, -1.0, 1300, 49, 1}},
            },
        },
    },
    ['teluzacoffee'] = {
        Type = 'NEED_LIGHTER', -- NEED_LIGHTER (joints, cigarette, cigars), BONG(bongs), VAPE(Must be topped up with liquid), VAPE-PUFF(It has an adjustable maximum amount of puffs)
        Prop = {Default = 'prop_cs_ciggy_01', Light = 'prop_cs_ciggy_01b'}, -- Default is prop before lighting and Light is prop after lighting the cigarette (if you want to use only one prop, remove the Light prop and leave only the Default prop)
        Setting = {
            Size = 100, -- The size for the NEED_LIGHTER type sets a value that decreases gradually after the item is lit, the larger the number you set, the longer the item can be smoked.
            Remove = 0.3, Time = 3}, -- The size is subtracted by 0.3 every 3 seconds during smoking, depending on the setting (ONLY FOR: NEED_LIGHTER).
        AfterExhale = {
            ExhaleTime = {min = 1, max = 1}, -- Smoke exhalation time
            RemoveAfterExhale = 5, -- The set value is taken after each exhalation of smoke from the size 
            ExhaleEffect = {enable = true, intensity = 60, effect = false}, -- screen blackout effect after smoke exhalation and Animpostfx effect-- https://forge.plebmasters.de/animpostfx
            ShakeCam = {effect = 'VIBRATE_SHAKE', value = 0.5}, -- Shake cam effect after exhale smoke for disable, effect = false
            CoughChance = false, -- false if you don't want to have any chance of coughing / 1 is a 100% chance, the higher the value you set, the lower the chance will be.
        Status = {
            ['ARMOR'] = 35, -- Adds armor after each puff of smoke
            ['STRESS'] = math.random(5, 10), -- Removes stress after smoke exhalation
            ['HIGH'] = 1, -- Adds a high after exhaling smoke
            }
        },
        Positions = {
            ['HAND'] = { -- Prop position in hand
                BoneID = 64097,
                Offset = vector3(0.020, 0.02, -0.008),
                Rot = vector3(100.0, 0.0, 100.0),},
            ['MOUTH'] = { -- Prop position in mouth 
                BoneID = 47419,
                Offset = vector3(0.014, -0.003, 0.008),
                Rot = vector3(55.0, 0.0, 110.0),},
            ['EAR'] = { -- Prop position behind the ear
                BoneID = 12844,
                Offset = vector3(0.05, -0.03, -0.08),
                Rot = vector3(80.0, 7.0, -118.0),},
        },
        Particles = {
            ['Exhale'] = {-- Smoke particles after exhale
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.1,
                Offset = vector3(0.014, -0.003, 0.008),
                Rot = vector3(55.0, 0.0, 110.0),},
            ['Prop'] = {-- Smoke particles on prop 
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.02,
                Offset = vector3(-0.050, 0.0, 0.0),
                Rot = vector3(0.0, 0.0, 0.0),}
        },
        Anims = { -- New animations can be added to each category and are always selected randomly
            ['USE'] = {
                [1] = {anim = 'idle_a', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [2] = {anim = 'idle_b', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [3] = {anim = 'idle_c', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [4] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1600, 49, 1}},
            },
            ['MOUTH'] = {
                [1] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['EAR'] = {
                [1] = {anim = 'takecig_clip', dict = 'takecig@devcore', params = {1.0, -1.0, 1500, 49, 1}},
            },
            ['COUGH'] = {
                [1] = {anim = 'idle_cough', dict = 'timetable@gardener@smoking_joint', params = {1.0, -1.0, 2500, 49, 1}},
            },
            ['THROW'] = {
                [1] = {anim = 'putdown_low', dict = 'pickup_object', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['GIVE'] = {
                [1] = {anim = 'givetake1_a', dict = 'mp_common', params = {1.0, -1.0, 1800, 49, 1}},
            },
            ['TAKEOUT'] = {
                [1] = {anim = 'enter', dict = 'amb@world_human_smoking@male@male_a@enter', params = {1.0, -1.0, 1300, 49, 1}},
            },
        },
    },
    ['hotryleigh'] = {
        Type = 'NEED_LIGHTER', -- NEED_LIGHTER (joints, cigarette, cigars), BONG(bongs), VAPE(Must be topped up with liquid), VAPE-PUFF(It has an adjustable maximum amount of puffs)
        Prop = {Default = 'prop_cs_ciggy_01', Light = 'prop_cs_ciggy_01b'}, -- Default is prop before lighting and Light is prop after lighting the cigarette (if you want to use only one prop, remove the Light prop and leave only the Default prop)
        Setting = {
            Size = 100, -- The size for the NEED_LIGHTER type sets a value that decreases gradually after the item is lit, the larger the number you set, the longer the item can be smoked.
            Remove = 0.3, Time = 3}, -- The size is subtracted by 0.3 every 3 seconds during smoking, depending on the setting (ONLY FOR: NEED_LIGHTER).
        AfterExhale = {
            ExhaleTime = {min = 1, max = 1}, -- Smoke exhalation time
            RemoveAfterExhale = 5, -- The set value is taken after each exhalation of smoke from the size 
            ExhaleEffect = {enable = true, intensity = 60, effect = false}, -- screen blackout effect after smoke exhalation and Animpostfx effect-- https://forge.plebmasters.de/animpostfx
            ShakeCam = {effect = 'VIBRATE_SHAKE', value = 0.5}, -- Shake cam effect after exhale smoke for disable, effect = false
            CoughChance = false, -- false if you don't want to have any chance of coughing / 1 is a 100% chance, the higher the value you set, the lower the chance will be.
        Status = {
            ['ARMOR'] = 35, -- Adds armor after each puff of smoke
            ['STRESS'] = math.random(5, 10), -- Removes stress after smoke exhalation
            ['HIGH'] = 1, -- Adds a high after exhaling smoke
            }
        },
        Positions = {
            ['HAND'] = { -- Prop position in hand
                BoneID = 64097,
                Offset = vector3(0.020, 0.02, -0.008),
                Rot = vector3(100.0, 0.0, 100.0),},
            ['MOUTH'] = { -- Prop position in mouth 
                BoneID = 47419,
                Offset = vector3(0.014, -0.003, 0.008),
                Rot = vector3(55.0, 0.0, 110.0),},
            ['EAR'] = { -- Prop position behind the ear
                BoneID = 12844,
                Offset = vector3(0.05, -0.03, -0.08),
                Rot = vector3(80.0, 7.0, -118.0),},
        },
        Particles = {
            ['Exhale'] = {-- Smoke particles after exhale
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.1,
                Offset = vector3(0.014, -0.003, 0.008),
                Rot = vector3(55.0, 0.0, 110.0),},
            ['Prop'] = {-- Smoke particles on prop 
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.02,
                Offset = vector3(-0.050, 0.0, 0.0),
                Rot = vector3(0.0, 0.0, 0.0),}
        },
        Anims = { -- New animations can be added to each category and are always selected randomly
            ['USE'] = {
                [1] = {anim = 'idle_a', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [2] = {anim = 'idle_b', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [3] = {anim = 'idle_c', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [4] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1600, 49, 1}},
            },
            ['MOUTH'] = {
                [1] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['EAR'] = {
                [1] = {anim = 'takecig_clip', dict = 'takecig@devcore', params = {1.0, -1.0, 1500, 49, 1}},
            },
            ['COUGH'] = {
                [1] = {anim = 'idle_cough', dict = 'timetable@gardener@smoking_joint', params = {1.0, -1.0, 2500, 49, 1}},
            },
            ['THROW'] = {
                [1] = {anim = 'putdown_low', dict = 'pickup_object', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['GIVE'] = {
                [1] = {anim = 'givetake1_a', dict = 'mp_common', params = {1.0, -1.0, 1800, 49, 1}},
            },
            ['TAKEOUT'] = {
                [1] = {anim = 'enter', dict = 'amb@world_human_smoking@male@male_a@enter', params = {1.0, -1.0, 1300, 49, 1}},
            },
        },
    },
    ['weedmachine'] = {
        Type = 'NEED_LIGHTER', -- NEED_LIGHTER (joints, cigarette, cigars), BONG(bongs), VAPE(Must be topped up with liquid), VAPE-PUFF(It has an adjustable maximum amount of puffs)
        Prop = {Default = 'prop_cs_ciggy_01', Light = 'prop_cs_ciggy_01b'}, -- Default is prop before lighting and Light is prop after lighting the cigarette (if you want to use only one prop, remove the Light prop and leave only the Default prop)
        Setting = {
            Size = 100, -- The size for the NEED_LIGHTER type sets a value that decreases gradually after the item is lit, the larger the number you set, the longer the item can be smoked.
            Remove = 0.3, Time = 3}, -- The size is subtracted by 0.3 every 3 seconds during smoking, depending on the setting (ONLY FOR: NEED_LIGHTER).
        AfterExhale = {
            ExhaleTime = {min = 1, max = 1}, -- Smoke exhalation time
            RemoveAfterExhale = 5, -- The set value is taken after each exhalation of smoke from the size 
            ExhaleEffect = {enable = true, intensity = 60, effect = false}, -- screen blackout effect after smoke exhalation and Animpostfx effect-- https://forge.plebmasters.de/animpostfx
            ShakeCam = {effect = 'VIBRATE_SHAKE', value = 0.5}, -- Shake cam effect after exhale smoke for disable, effect = false
            CoughChance = false, -- false if you don't want to have any chance of coughing / 1 is a 100% chance, the higher the value you set, the lower the chance will be.
        Status = {
            ['ARMOR'] = 35, -- Adds armor after each puff of smoke
            ['STRESS'] = math.random(5, 10), -- Removes stress after smoke exhalation
            ['HIGH'] = 1, -- Adds a high after exhaling smoke
            }
        },
        Positions = {
            ['HAND'] = { -- Prop position in hand
                BoneID = 64097,
                Offset = vector3(0.020, 0.02, -0.008),
                Rot = vector3(100.0, 0.0, 100.0),},
            ['MOUTH'] = { -- Prop position in mouth 
                BoneID = 47419,
                Offset = vector3(0.014, -0.003, 0.008),
                Rot = vector3(55.0, 0.0, 110.0),},
            ['EAR'] = { -- Prop position behind the ear
                BoneID = 12844,
                Offset = vector3(0.05, -0.03, -0.08),
                Rot = vector3(80.0, 7.0, -118.0),},
        },
        Particles = {
            ['Exhale'] = {-- Smoke particles after exhale
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.1,
                Offset = vector3(0.014, -0.003, 0.008),
                Rot = vector3(55.0, 0.0, 110.0),},
            ['Prop'] = {-- Smoke particles on prop 
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.02,
                Offset = vector3(-0.050, 0.0, 0.0),
                Rot = vector3(0.0, 0.0, 0.0),}
        },
        Anims = { -- New animations can be added to each category and are always selected randomly
            ['USE'] = {
                [1] = {anim = 'idle_a', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [2] = {anim = 'idle_b', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [3] = {anim = 'idle_c', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [4] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1600, 49, 1}},
            },
            ['MOUTH'] = {
                [1] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['EAR'] = {
                [1] = {anim = 'takecig_clip', dict = 'takecig@devcore', params = {1.0, -1.0, 1500, 49, 1}},
            },
            ['COUGH'] = {
                [1] = {anim = 'idle_cough', dict = 'timetable@gardener@smoking_joint', params = {1.0, -1.0, 2500, 49, 1}},
            },
            ['THROW'] = {
                [1] = {anim = 'putdown_low', dict = 'pickup_object', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['GIVE'] = {
                [1] = {anim = 'givetake1_a', dict = 'mp_common', params = {1.0, -1.0, 1800, 49, 1}},
            },
            ['TAKEOUT'] = {
                [1] = {anim = 'enter', dict = 'amb@world_human_smoking@male@male_a@enter', params = {1.0, -1.0, 1300, 49, 1}},
            },
        },
    },


    -- JOINTS -- TYPE : NEED_LIGHTER

    -- ['banana_kush_joint'] = { -- ITEM NAME
    --     Type = 'NEED_LIGHTER', -- NEED_LIGHTER (joints, cigarette, cigars), BONG(bongs), VAPE(Must be topped up with liquid), VAPE-PUFF(It has an adjustable maximum amount of puffs)
    --     Prop = {Default = 'p_cs_joint_02', Light = 'p_cs_joint_01'}, -- Default is prop before lighting and Light is prop after lighting the cigarette (if you want to use only one prop, remove the Light prop and leave only the Default prop)
    --     Setting = {
    --         Size = 100,  --  TYPE : NEED_LIGHTER : set a value that decreases gradually after the item is lit, the larger the number you set, the longer the item can be smoked. 
    --         Remove = 0.3, Time = 3}, -- The size is subtracted by 0.3 every 3 seconds during smoking, depending on the setting (ONLY FOR: NEED_LIGHTER).
    --     AfterExhale = {
    --         ExhaleTime = {min = 1, max = 1}, -- Smoke exhalation time
    --         RemoveAfterExhale = 5, -- The set value is taken after each exhalation of smoke from the size
    --         ExhaleEffect = {enable = true, intensity = 70, effect = false}, -- screen blackout effect after smoke exhalation and Animpostfx effect-- https://forge.plebmasters.de/animpostfx
    --         ShakeCam = {effect = 'VIBRATE_SHAKE', value = 2.5}, -- Shake cam effect after exhale smoke for disable, effect = false
    --         CoughChance = 3,-- false if you don't want to have any chance of coughing / 1 is a 100% chance, the higher the value you set, the lower the chance will be.
    --     Status = {
    --         ['ARMOR'] = 2, -- Adds armor after each puff of smoke
    --         ['STRESS'] = nil, -- Removes stress after smoke exhalation
    --         ['HIGH'] = 1.3, -- Adds a high after exhaling smoke
    --         }
    --     },
    --     Positions = {
    --         ['HAND'] = { -- Prop position in hand
    --             BoneID = 64097,
    --             Offset = vector3(0.020, 0.02, -0.008),
    --             Rot = vector3(100.0, 0.0, 100.0),},
    --         ['MOUTH'] = { -- Prop position in mouth 
    --             BoneID = 47419,
    --             Offset = vector3(0.01, -0.01, 0.01),
    --             Rot = vector3(-2.0, 0.0, 96.0),},
    --         ['EAR'] = { -- Prop position behind the ear
    --             BoneID = 12844,
    --             Offset = vector3(0.05, -0.03, -0.08),
    --             Rot = vector3(80.0, 7.0, -118.0),},
    --     },
    --     Particles = {
    --         ['Exhale'] = { -- Smoke particles after exhale
    --             particleName = 'exp_grd_bzgas_smoke',
    --             particleDict = 'core',
    --             Scale = 0.1,
    --             Offset = vector3(0.014, -0.003, 0.008),
    --             Rot = vector3(55.0, 0.0, 110.0),},
    --         ['Prop'] = {-- Smoke particles on prop
    --             particleName = 'exp_grd_bzgas_smoke',
    --             particleDict = 'core',
    --             Scale = 0.03,
    --             Offset = vector3(-0.050, 0.0, 0.0),
    --             Rot = vector3(0.0, 0.0, 0.0),}
    --     },
    --     Anims = {-- New animations can be added to each category and are always selected randomly
    --         ['USE'] = {
    --             [1] = {anim = 'idle_a', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
    --             [2] = {anim = 'idle_b', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
    --             [3] = {anim = 'idle_c', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
    --             [4] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1300, 49, 1}},
    --         },
    --         ['MOUTH'] = {
    --             [1] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1000, 49, 1}},
    --         },
    --         ['EAR'] = {
    --             [1] = {anim = 'takecig_clip', dict = 'takecig@devcore', params = {1.0, -1.0, 1500, 49, 1}},
    --         },
    --         ['COUGH'] = {
    --             [1] = {anim = 'idle_cough', dict = 'timetable@gardener@smoking_joint', params = {1.0, -1.0, 2500, 49, 1}},
    --         },
    --         ['THROW'] = {
    --             [1] = {anim = 'putdown_low', dict = 'pickup_object', params = {1.0, -1.0, 1000, 49, 1}},
    --         },
    --         ['GIVE'] = {
    --             [1] = {anim = 'givetake1_a', dict = 'mp_common', params = {1.0, -1.0, 1800, 49, 1}},
    --         },
    --         ['TAKEOUT'] = {
    --             [1] = {anim = 'enter', dict = 'amb@world_human_smoking@male@male_a@enter', params = {1.0, -1.0, 1300, 49, 1}},
    --         },
    --     },
    -- },

    -- JOINTS -- TYPE : NEED_LIGHTER

    -- ['purple_haze_joint'] = { -- ITEM NAME
    --     Type = 'NEED_LIGHTER', -- NEED_LIGHTER (joints, cigarette, cigars), BONG(bongs), VAPE(Must be topped up with liquid), VAPE-PUFF(It has an adjustable maximum amount of puffs)
    --     Prop = {Default = 'p_cs_joint_02', Light = 'p_cs_joint_01'}, -- Default is prop before lighting and Light is prop after lighting the cigarette (if you want to use only one prop, remove the Light prop and leave only the Default prop)
    --     Setting = {
    --         Size = 100, --  TYPE : NEED_LIGHTER : set a value that decreases gradually after the item is lit, the larger the number you set, the longer the item can be smoked. 
    --         Remove = 0.3, Time = 3}, -- The size is subtracted by 0.3 every 3 seconds during smoking, depending on the setting (ONLY FOR: NEED_LIGHTER).
    --     AfterExhale = {
    --         ExhaleTime = {min = 1, max = 1}, -- Smoke exhalation time
    --         RemoveAfterExhale = 5, -- The set value is taken after each exhalation of smoke from the size
    --         ExhaleEffect = {enable = true, intensity = 90, effect = 'SwitchShortNeutralIn'}, -- screen blackout effect after smoke exhalation and Animpostfx effect-- https://forge.plebmasters.de/animpostfx
    --         ShakeCam = {effect = 'VIBRATE_SHAKE', value = 2.8}, -- Shake cam effect after exhale smoke for disable, effect = false
    --         CoughChance = 4,-- false if you don't want to have any chance of coughing / 1 is a 100% chance, the higher the value you set, the lower the chance will be.
    --     Status = {
    --         ['ARMOR'] = 3, -- Adds armor after each puff of smoke
    --         ['STRESS'] = nil, -- Removes stress after smoke exhalation
    --         ['HIGH'] = 1.5, -- Adds a high after exhaling smoke
    --         }
    --     },
    --     Positions = {
    --         ['HAND'] = { -- Prop position in hand
    --             BoneID = 64097,
    --             Offset = vector3(0.020, 0.02, -0.008),
    --             Rot = vector3(100.0, 0.0, 100.0),},
    --         ['MOUTH'] = { -- Prop position in mouth 
    --             BoneID = 47419,
    --             Offset = vector3(0.01, -0.01, 0.01),
    --             Rot = vector3(-2.0, 0.0, 96.0),},
    --         ['EAR'] = { -- Prop position behind the ear
    --             BoneID = 12844,
    --             Offset = vector3(0.05, -0.03, -0.08),
    --             Rot = vector3(80.0, 7.0, -118.0),},
    --     },
    --     Particles = {
    --         ['Exhale'] = { -- Smoke particles after exhale
    --             particleName = 'exp_grd_bzgas_smoke',
    --             particleDict = 'core',
    --             Scale = 0.1,
    --             Offset = vector3(0.014, -0.003, 0.008),
    --             Rot = vector3(55.0, 0.0, 110.0),},
    --         ['Prop'] = {-- Smoke particles on prop
    --             particleName = 'exp_grd_bzgas_smoke',
    --             particleDict = 'core',
    --             Scale = 0.03,
    --             Offset = vector3(-0.050, 0.0, 0.0),
    --             Rot = vector3(0.0, 0.0, 0.0),}
    --     },
    --     Anims = {-- New animations can be added to each category and are always selected randomly
    --         ['USE'] = {
    --             [1] = {anim = 'idle_a', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
    --             [2] = {anim = 'idle_b', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
    --             [3] = {anim = 'idle_c', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
    --             [4] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1300, 49, 1}},
    --         },
    --         ['MOUTH'] = {
    --             [1] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1000, 49, 1}},
    --         },
    --         ['EAR'] = {
    --             [1] = {anim = 'takecig_clip', dict = 'takecig@devcore', params = {1.0, -1.0, 1500, 49, 1}},
    --         },
    --         ['COUGH'] = {
    --             [1] = {anim = 'idle_cough', dict = 'timetable@gardener@smoking_joint', params = {1.0, -1.0, 2500, 49, 1}},
    --         },
    --         ['THROW'] = {
    --             [1] = {anim = 'putdown_low', dict = 'pickup_object', params = {1.0, -1.0, 1000, 49, 1}},
    --         },
    --         ['GIVE'] = {
    --             [1] = {anim = 'givetake1_a', dict = 'mp_common', params = {1.0, -1.0, 1800, 49, 1}},
    --         },
    --         ['TAKEOUT'] = {
    --             [1] = {anim = 'enter', dict = 'amb@world_human_smoking@male@male_a@enter', params = {1.0, -1.0, 1300, 49, 1}},
    --         },
    --     },
    -- },

    -- JOINTS -- TYPE : NEED_LIGHTER
    ['blue_dream_joint'] = { -- ITEM NAME
        Type = 'NEED_LIGHTER', -- NEED_LIGHTER (joints, cigarette, cigars), BONG(bongs), VAPE(Must be topped up with liquid), VAPE-PUFF(It has an adjustable maximum amount of puffs)
        Prop = {Default = 'p_cs_joint_02', Light = 'p_cs_joint_01'}, -- Default is prop before lighting and Light is prop after lighting the cigarette (if you want to use only one prop, remove the Light prop and leave only the Default prop)
        Setting = {
            Size = 100,  --  TYPE : NEED_LIGHTER : set a value that decreases gradually after the item is lit, the larger the number you set, the longer the item can be smoked. 
            Remove = 0.3, Time = 3},  -- The size is subtracted by 0.3 every 3 seconds during smoking, depending on the setting (ONLY FOR: NEED_LIGHTER).
        AfterExhale = { 
            ExhaleTime = {min = 1, max = 2}, -- Smoke exhalation time
            RemoveAfterExhale = 5, -- The set value is taken after each exhalation of smoke from the size
            ExhaleEffect = {enable = true, intensity = 100, effect = 'SwitchShortNeutralIn'}, -- screen blackout effect after smoke exhalation and Animpostfx effect-- https://forge.plebmasters.de/animpostfx
            ShakeCam = {effect = 'VIBRATE_SHAKE', value = 3.0}, -- Shake cam effect after exhale smoke for disable, effect = false
            CoughChance = 5, -- false if you don't want to have any chance of coughing / 1 is a 100% chance, the higher the value you set, the lower the chance will be.
        Status = {
            ['ARMOR'] = 35, -- Adds armor after each puff of smoke
            ['STRESS'] = math.random(5, 10), -- Removes stress after smoke exhalation
            ['HIGH'] = 1, -- Adds a high after exhaling smoke
            }
        },
        Positions = {
            ['HAND'] = { -- Prop position in hand
                BoneID = 64097,
                Offset = vector3(0.020, 0.02, -0.008),
                Rot = vector3(100.0, 0.0, 100.0),},
            ['MOUTH'] = { -- Prop position in mouth 
                BoneID = 47419,
                Offset = vector3(0.01, -0.01, 0.01),
                Rot = vector3(-2.0, 0.0, 96.0),},
            ['EAR'] = { -- Prop position behind the ear
                BoneID = 12844,
                Offset = vector3(0.05, -0.03, -0.08),
                Rot = vector3(80.0, 7.0, -118.0),},
        },
        Particles = {
            ['Exhale'] = { -- Smoke particles after exhale
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.1,
                Offset = vector3(0.014, -0.003, 0.008),
                Rot = vector3(55.0, 0.0, 110.0),},
            ['Prop'] = {-- Smoke particles on prop
                particleName = 'exp_grd_bzgas_smoke',
                particleDict = 'core',
                Scale = 0.03,
                Offset = vector3(-0.050, 0.0, 0.0),
                Rot = vector3(0.0, 0.0, 0.0),}
        },
        Anims = {-- New animations can be added to each category and are always selected randomly
            ['USE'] = {
                [1] = {anim = 'idle_a', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [2] = {anim = 'idle_b', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [3] = {anim = 'idle_c', dict = 'amb@world_human_aa_smoke@male@idle_a', params = {1.0, -1.0, 2800, 49, 1}},
                [4] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1300, 49, 1}},
            },
            ['MOUTH'] = {
                [1] = {anim = 'take_mouth_c', dict = 'pos_mouth@devcore', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['EAR'] = {
                [1] = {anim = 'takecig_clip', dict = 'takecig@devcore', params = {1.0, -1.0, 1500, 49, 1}},
            },
            ['COUGH'] = {
                [1] = {anim = 'idle_cough', dict = 'timetable@gardener@smoking_joint', params = {1.0, -1.0, 2500, 49, 1}},
            },
            ['THROW'] = {
                [1] = {anim = 'putdown_low', dict = 'pickup_object', params = {1.0, -1.0, 1000, 49, 1}},
            },
            ['GIVE'] = {
                [1] = {anim = 'givetake1_a', dict = 'mp_common', params = {1.0, -1.0, 1800, 49, 1}},
            },
            ['TAKEOUT'] = {
                [1] = {anim = 'enter', dict = 'amb@world_human_smoking@male@male_a@enter', params = {1.0, -1.0, 1300, 49, 1}},
            },
        },
    },

    -- BONGS -- TYPE : BONG

    -- ['bong'] = { -- ITEM NAME
    --     Type = 'BONG', -- NEED_LIGHTER (joints, cigarette, cigar), BONG(bongs), VAPE(Must be topped up with liquid), VAPE-PUFF(It has an adjustable maximum amount of puffs)
    --     Prop = {Default = 'prop_bong_01'}, -- For the BONG / VAPE / VAPE-PUFF type, only the Default prop can be set
    --     Setting = {
    --         Size = 1, -- TYPE : BONG : After filling the bong, the set size is added.
    --         Remove = nil, Time = nil}, -- The size is subtracted by 0.3 every 3 seconds during smoking, depending on the setting (ONLY FOR: NEED_LIGHTER).
    --     AfterExhale = {
    --         ExhaleTime = {min = 2, max = 2}, -- Smoke exhalation time
    --         RemoveAfterExhale = 1, -- The set value is taken after each exhalation of smoke from the size
    --         ExhaleEffect = {enable = true, intensity = 100, effect = 'SwitchShortNeutralIn'}, -- screen blackout effect after smoke exhalation and Animpostfx effect-- https://forge.plebmasters.de/animpostfx
    --         ShakeCam = {effect = 'VIBRATE_SHAKE', value = 2.8}, -- Shake cam effect after exhale smoke for disable, effect = false
    --         CoughChance = 2, -- false if you don't want to have any chance of coughing / 1 is a 100% chance, the higher the value you set, the lower the chance will be.
    --     Status = {
    --         ['ARMOR'] = 80, -- Adds armor after each puff of smoke
    --         ['STRESS'] = nil, -- Removes stress after smoke exhalation
    --         ['HIGH'] = 5, -- Adds a high after exhaling smoke
    --         }
    --     },
    --     AllowedItems = {-- items with which to fill the bong, filling is up to the maximum size value
    --             ['og_kush_weed'] = { label = 'Og Kush Weed'},
    --             ['banana_kush_weed'] = { label = 'Banana Kush Weed'},
    --             ['purple_haze_weed'] = { label = 'Purple Haze Weed'},
    --             ['blue_dream_weed'] = { label = 'Blue Dream Weed'},
    --           --['example_weed'] = { label = 'Example Weed'},
    --     },
    --     Positions = {
    --         ['HAND'] = { -- Prop position in hand
    --             BoneID = 57005,
    --             Offset = vector3(0.14, -0.26, -0.1),
    --             Rot = vector3(-72.0, 0.0, 8.0),},
    --     },
    --     Particles = {
    --         ['Exhale'] = { -- Smoke particles after exhale
    --             particleName = 'exp_grd_bzgas_smoke',
    --             particleDict = 'core',
    --             Scale = 0.3,
    --             Offset = vector3(0.014, -0.003, 0.008),
    --             Rot = vector3(55.0, 0.0, 110.0),},
    --     },
    --     Anims = {-- New animations can be added to each category and are always selected randomly
    --         ['USE'] = {
    --             [1] = {anim = 'smokebong_c', dict = 'smokebong@devcore', params = {1.0, -1.0, 4000, 49, 1}, sound = {url = './sounds/devcore_bonghit_1.mp3', volume = 0.5, distance = 10}},
    --         },
    --         ['COUGH'] = {
    --             [1] = {anim = 'idle_cough', dict = 'timetable@gardener@smoking_joint', params = {1.0, -1.0, 2500, 49, 1}},
    --         },
    --         ['CHARGE'] = {
    --             [1] = {anim = 'a_uncuff', dict = 'mp_arresting', params = {1.0, -1.0, 2000, 49, 1}},
    --         },
    --         ['GIVE'] = {
    --             [1] = {anim = 'givetake1_a', dict = 'mp_common', params = {1.0, -1.0, 1800, 49, 1}},
    --         },
    --         ['TAKEOUT'] = {
    --             [1] = {anim = 'enter', dict = 'amb@world_human_smoking@male@male_a@enter', params = {1.0, -1.0, 1300, 49, 1}},
    --         },
    --     },
    -- },

    -- VAPES -- TYPE : VAPE

    -- ['vape'] = { -- ITEM NAME
    --     Type = 'VAPE', -- NEED_LIGHTER (joints, cigarette, cigar), BONG(bongs), VAPE(Must be topped up with liquid), VAPE-PUFF(It has an adjustable maximum amount of puffs)
    --     Prop = {Default = 'xm3_prop_xm3_vape_01a'}, -- For the BONG / VAPE / VAPE-PUFF type, only the Default prop can be set
    --     Setting = {
    --         Size = 5, -- TYPE : VAPE : After filling the vape, the set size is added.
    --         Remove = nil, Time = nil}, -- The size is subtracted by 0.3 every 3 seconds during smoking, depending on the setting (ONLY FOR: NEED_LIGHTER).
    --     AfterExhale = {
    --         ExhaleTime = {min = 1, max = 2}, -- Smoke exhalation time
    --         RemoveAfterExhale = 0.2, -- The set value is taken after each exhalation of smoke from the size 
    --         ExhaleEffect = {enable = true, intensity = 60, effect = false}, -- screen blackout effect after smoke exhalation and Animpostfx effect-- https://forge.plebmasters.de/animpostfx
    --         ShakeCam = {effect = 'VIBRATE_SHAKE', value = 1.5}, -- Shake cam effect after exhale smoke for disable, effect = false
    --         CoughChance = false, -- false if you don't want to have any chance of coughing / 1 is a 100% chance, the higher the value you set, the lower the chance will be.
    --     Status = {
    --         ['ARMOR'] = nil, -- Adds armor after each puff of smoke
    --         ['STRESS'] = 1, -- Removes stress after smoke exhalation
    --         ['HIGH'] = nil, -- Adds a high after exhaling smoke
    --         }
    --     },
    --     AllowedItems = {-- items with which to fill the vape, filling is up to the maximum size value
    --             ['cherry_liquid'] = {label = 'Cherry liquid'},
    --             ['blueberry_liquid'] = {label = 'Blueberry liquid'},
    --         --  ['example_liquid'] = {label = 'Example liquid'},
    --     },
    --     Positions = {
    --         ['HAND'] = { -- Prop position in hand
    --             BoneID = 57005,
    --             Offset = vector3(0.1, 0.04, -0.01),
    --             Rot = vector3( 0.0, 0.0, 0.0),},
    --     },
    --     Particles = {
    --         ['Exhale'] = { -- Smoke particles after exhale
    --             particleName = 'exp_grd_bzgas_smoke',
    --             particleDict = 'core',
    --             Scale = 0.2,
    --             Offset = vector3(0.014, -0.003, 0.008),
    --             Rot = vector3(55.0, 0.0, 110.0),}
    --     },
    --     Anims = {-- New animations can be added to each category and are always selected randomly
    --         ['USE'] = {
    --             [1] = {anim = 'smokevape_c', dict = 'smoke_vape@devcore', params = {1.0, -1.0, 2000, 49, 1}, sound = {url = './sounds/devcore_vape_1.mp3', volume = 0.5, distance = 5}},
    --         },
    --         ['CHARGE'] = {
    --             [1] = {anim = 'a_uncuff', dict = 'mp_arresting', params = {1.0, -1.0, 2000, 49, 1}},
    --         },
    --         ['COUGH'] = {
    --             [1] = {anim = 'idle_cough', dict = 'timetable@gardener@smoking_joint', params = {1.0, -1.0, 2500, 49, 1}},
    --         },
    --         ['THROW'] = {
    --             [1] = {anim = 'putdown_low', dict = 'pickup_object', params = {1.0, -1.0, 1000, 49, 1}},
    --         },
    --         ['GIVE'] = {
    --             [1] = {anim = 'givetake1_a', dict = 'mp_common', params = {1.0, -1.0, 1800, 49, 1}},
    --         },
    --         ['TAKEOUT'] = {
    --             [1] = {anim = 'enter', dict = 'amb@world_human_smoking@male@male_a@enter', params = {1.0, -1.0, 1300, 49, 1}},
    --         },
    --     },
    -- },


    -- PUFF VAPES -- TYPE : VAPE-PUFF

    -- ['puff_vape'] = { -- ITEM NAME
    --     Type = 'VAPE-PUFF', -- NEED_LIGHTER (joints, cigarette, cigar), BONG(bongs), VAPE(Must be topped up with liquid), VAPE-PUFF(It has an adjustable maximum amount of puffs)
    --     Prop = {Default = 'ba_prop_battle_vape_01'}, -- For the BONG / VAPE / VAPE-PUFF type, only the Default prop can be set 
    --     Setting = {
    --         Size = 100, -- TYPE : VAPE-PUFF : Size is maximum puffs
    --         Remove = nil, Time = nil}, -- The size is subtracted by 0.3 every 3 seconds during smoking, depending on the setting (ONLY FOR: NEED_LIGHTER).
    --     AfterExhale = {
    --         ExhaleTime = {min = 1, max = 2},  -- Smoke exhalation time
    --         RemoveAfterExhale = 1, -- The set value is taken after each exhalation of smoke from the size 
    --         ExhaleEffect = {enable = true, intensity = 60, effect = false}, -- screen blackout effect after smoke exhalation and Animpostfx effect-- https://forge.plebmasters.de/animpostfx
    --         ShakeCam = {effect = 'VIBRATE_SHAKE', value = 1.5},  -- Shake cam effect after exhale smoke for disable, effect = false
    --         CoughChance = false, -- false if you don't want to have any chance of coughing / 1 is a 100% chance, the higher the value you set, the lower the chance will be.
    --     Status = {
    --         ['ARMOR'] = nil, -- Adds armor after each puff of smoke
    --         ['STRESS'] = 1, -- Removes stress after smoke exhalation
    --         ['HIGH'] = nil, -- Adds a high after exhaling smoke
    --         }
    --     },
    --     Positions = {
    --         ['HAND'] = { -- Prop position in hand
    --             BoneID = 57005,
    --             Offset = vector3(0.1, 0.0, -0.01),
    --             Rot = vector3( 190.0, 0.0, 0.0),},
    --     },
    --     Particles = {
    --         ['Exhale'] = { -- Smoke particles effect after exhale 
    --             particleName = 'exp_grd_bzgas_smoke',
    --             particleDict = 'core',
    --             Scale = 0.2,
    --             Offset = vector3(0.014, -0.003, 0.008),
    --             Rot = vector3(55.0, 0.0, 110.0),}
    --     },
    --     Anims = {-- New animations can be added to each category and are always selected randomly
    --         ['USE'] = {
    --             [1] = {anim = 'smokevape_c', dict = 'smoke_vape@devcore', params = {1.0, -1.0, 2000, 49, 1}, sound = {url = './sounds/devcore_vape_1.mp3', volume = 0.5, distance = 5}},
    --         },
    --         ['COUGH'] = {
    --             [1] = {anim = 'idle_cough', dict = 'timetable@gardener@smoking_joint', params = {1.0, -1.0, 2500, 49, 1}},
    --         },
    --         ['GIVE'] = {
    --             [1] = {anim = 'givetake1_a', dict = 'mp_common', params = {1.0, -1.0, 1800, 49, 1}},
    --         },
    --         ['TAKEOUT'] = {
    --             [1] = {anim = 'enter', dict = 'amb@world_human_smoking@male@male_a@enter', params = {1.0, -1.0, 1300, 49, 1}},
    --         },
    --     },
    -- },
    --

}