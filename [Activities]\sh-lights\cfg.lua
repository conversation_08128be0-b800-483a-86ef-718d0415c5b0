cfg = {
    addLightCommand = 'addlight', -- if you don't want to use it as a command, set it to false. event: 'sh-lights:client:addLightMode'
    removeLightCommand = 'removelight', -- if you don't want to use it as a command, set it to false. event: 'sh-lights:client:deleteLightMode'
    drawDistance = 200, -- light distance check
    customLightWait = 300, -- ms
    idleWait = 1000, -- ms
    raycastDistance = 20,
    markers = {
        [1] = {r = 96, g = 165, b = 250, a = 180}, -- color
        [2] = {r = 25, g = 20, b = 125, a = 180} -- color
    },
    identifierType = 'steam', -- steam, license, discord etc.
    useWhitelist = true,
    whitelists = { -- steam:hex, discord:discordID, license:rockstarID
    -- ['steam:change'] = true,


    -- God
    ["steam:11000013f26aa4e"] = true, -- Mr_Dx
    ["steam:110000133483033"] = true, -- Cyclops
    ["steam:11000010627a8e3"] = true, -- nuke
    ["steam:11000013bd9833f"] = true, -- J<PERSON>K
    ["steam:110000105f7a98c"] = true, -- Ethan
    ["steam:1100001420e1493"] = true, -- Ghxsty
    ["steam:110000132030c85"] = true, -- hanners
    ["steam:110000108a16989"] = true, -- rdotz
    ["steam:1100001084c0150"] = true, -- Lunar
    ["steam:11000014325f636"] = true, -- Finn (Tommy)
  

    -- -- Senior Administrator
    -- ["steam:110000131ff2087"] = true, -- Blaze
    -- ["steam:1100001057122a8"] = true, -- jrD
    -- ["steam:110000164a61df7"] = true, -- Cleopatra
    -- ["steam:1100001415e23d7"] = true, -- Natty

    -- -- Administrator
    -- ["steam:11000016d504c22"] = true, -- Gary
    -- ["steam:110000136732275"] = true, -- OniTello
    -- ["steam:11000014b52a3c2"] = true, -- Andrew
    -- ["steam:110000104ddaadd"] = true, -- Cubanborn
    -- ["steam:110000146d1d99c"] = true, -- leigh
    -- ["steam:110000167783e6b"] = true, -- Billy

    -- -- Senior Mod
    -- ["steam:110000148e90a73"] = true, -- Jay
    -- ["steam:110000142d1618c"] = true, -- Coronaboy
    -- ["steam:1100001544a0b19"] = true, -- Grey
    -- ["steam:110000100e333be"] = true, -- Love Muscle
    -- ["steam:11000014287eb36"] = true, -- Sky

    -- -- Mod
    -- ["steam:11000015c3c4f39"] = true, -- Mula
    -- ["steam:1100001326f606f"] = true, -- Shadowkeeper
    -- ["steam:1100001551c232f"] = true, -- Miley
    -- ["steam:11000011b498190"] = true, -- Curtis
    -- ["steam:110000145f3e531"] = true, -- Emerie
    -- ["steam:1100001365833e7"] = true, -- Rylee
    -- ["steam:11000016da3bcde"] = true, -- Alex
    -- ["steam:1100001686f9f3f"] = true, -- Jade
    -- ["steam:110000118f6febf"] = true, -- Brian
    -- ["steam:11000013edc3118"] = true, -- KAY
    -- ["steam:11000014e59856f"] = true, -- Galaxy
    -- ["steam:1100001483fed27"] = true, -- danebk
    -- ["steam:110000148f48251"] = true, -- Cuppcake

    -- -- Tmod
    -- ["steam:11000015918a0b9"] = true, -- Rolanda
    -- ["steam:11000011a382510"] = true, -- BennySwift
    -- ["steam:11000011938ce25"] = true, -- Daniel
    -- ["steam:11000016f68a1ee"] = true, -- Skylar
    -- ["steam:11000015223db20"] = true, -- Beanie
    -- ["steam:1100001605e5e01"] = true, -- burnedcandy
    -- ["steam:110000105cc3d6a"] = true, -- James (Pug)

    -- -- Discord Support
    -- ["steam:1100001436c7f70"] = true, -- Flowerz
    -- ["steam:110000159db099a"] = true, -- Jack
    -- ["steam:110000145839ff2"] = true, -- Tyler
    -- ["steam:110000158ec6141"] = true, -- Don
    -- ["steam:11000011ad2fd68"] = true, -- Declan
    -- ["steam:1100001156c8b2c"] = true, -- Belle
    -- ["steam:110000155e615e5"] = true, -- Olivia
    -- ["steam:11000013484336b"] = true, -- Blackenrose
    -- ["steam:110000160454c37"] = true, -- Sheila
    -- ["steam:11000011abc6614"] = true, -- Isabela
    },

    useAce = false,
    aces = {
        -- "command"
    },
    webhookURL = 'https://discord.com/api/webhooks/1398374733497761905/HBz5BoWS8gsbb8ha5XMQBN6a0-3yV_5aGnimjdsfXNrrMNv70Vs4zwxcbWNh3BGKP8Jy',
    locale = 'en',
    locales = {
        ['en'] = {
            radius = 'Radius',
            distance = 'Distance',
            brightness = 'Brightness',
            validation = 'With validation',
            submit = 'Submit',
            close = 'Close',
            dynamic = 'Dynamic',
            static = 'Static',
            blink = 'Blink',

            light_source = 'Press ~g~E~w~ to set light source',
            light_direction = 'Press ~g~E~w~ to set light direction',
            light_delete = 'Press ~g~E~w~ to delete light'
        },
    },
}