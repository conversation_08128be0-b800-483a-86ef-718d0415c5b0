import{u as U,r as S,j as c,m as q,a as e,F as te,L as i,s as I,C as R,_ as Le,$ as pe,b as W,d as re,f as Y,q as L,P as B,G as Ce,K as _e,a0 as ne,a1 as be,S as Te,O as ie,A as Q,I as $e,g as se,a2 as Ve,i as Ee,a3 as xe,a4 as we,a5 as ce,a6 as fe,a7 as Ie,a8 as Fe,J as de,a9 as ve,aa as oe,t as F,v as ge,ab as Ae,ac as Ye,V as me,ad as Pe,o as Ne,Q as He,R as je,ae as ue,p as We,af as Be,e as J,ag as Xe,z as qe,B as ze,ah as Ze,ai as Qe,x as Ke,aj as Je,ak as et,y as tt}from"./index-a04bc7c5.js";import{A as at,g as st}from"./audioRecorder-c5089776.js";import{g as nt,i as it,M as rt,L as lt,C as ot,D as Me,T as ct,a as dt,b as mt,c as ut,A as St}from"./Maps-6f0d30bc.js";const Oe=t=>{if(t)return/<!SENT-LOCATION-X=(-?\d*\.?\d*)Y=(-?\d*\.?\d*)!>/.test(t)},Ge=t=>{if(t)return t==="<!CALL-NO-ANSWER!>"},ht=t=>{if(t)return/<!AUDIO-MESSAGE-IMAGE="(.*)"-AUDIO="(.*)"-DURATION="(.*)"!>/.test(t)},Et=t=>{if(t.length===0)return!0;if(t)return!/^<!.*!>$/.test(t)},ft=({onEnd:t,close:o})=>{const g=U(W),[b,M]=S.useState(!1),[p,r]=S.useState(0),[a,T]=S.useState(null),f=S.useRef(null);if(!g.EnableVoiceMessages)return null;S.useEffect(()=>{if(!b)return;let l=setInterval(()=>{r(E=>E+1)},1e3);return()=>clearInterval(l)},[b]),S.useEffect(()=>(f.current=new at,()=>{f.current&&f.current.stop()}),[]);const O=l=>{let E=Math.floor(l/60),h=l%60;return`${E}:${h<10?"0":""}${h}`},s=async l=>{const E=await st(new Blob(l),25,7,324,55,3);T(E)};return c(q.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:0},transition:{duration:.2,ease:"easeInOut"},className:"audioMessage-container",children:[e("div",{className:"close",onClick:o}),c("div",{className:"audioMessage-wrapper",children:[c("div",{className:"duration",children:[b&&e("div",{className:"dot"}),O(p)]}),e("div",{className:"content",children:b?e(te,{children:a!=null&&a.base64?e("img",{src:a==null?void 0:a.base64}):i("APPS.MESSAGES.LOADING")}):i("APPS.MESSAGES.START_RECORDING")}),e("div",{className:"button","data-recording":b,onClick:()=>{var E;if(!((E=navigator.mediaDevices)!=null&&E.getUserMedia)||!(f!=null&&f.current))return I("error","No media devices found!");let l=f.current;b?l.stop().then(h=>{if(!h){r(0),M(m=>!m),R.PopUp.set({title:i("APPS.MESSAGES.EMPTY_RECORDING.TITLE"),description:i("APPS.MESSAGES.EMPTY_RECORDING.DESCRIPTION"),buttons:[{title:i("APPS.MESSAGES.EMPTY_RECORDING.OK")}]});return}t(h),M(m=>!m)}):(l.start(100,s),M(h=>!h))},children:b?e(Le,{}):e(pe,{})})]})]})},vt=({paymentAmount:t,setPaymentAmount:o,close:g})=>{const{User:b}=S.useContext(ae),M=U(W),[p]=b,[r,a]=S.useState(0),[T,f]=S.useState(4);let O={0:3,11:2.75,14:2.2};return S.useEffect(()=>{r===0&&f(O[0]);let s=0;for(let l=0;l<Object.keys(O).length;l++)r>=parseInt(Object.keys(O)[l])&&(s=O[Object.keys(O)[l]]);f(s)},[r]),S.useEffect(()=>{o(r)},[r]),c(q.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:0},transition:{duration:.2,ease:"easeInOut"},className:"payment-container",children:[e("div",{className:"close",onClick:g}),c("div",{className:"payment-wrapper",children:[e("div",{className:"button",onClick:()=>t>0&&a(s=>s-1),children:"-"}),e("div",{className:"amount",children:e(re,{type:"number",value:t,style:{fontSize:`${T}rem`},onChange:s=>{if(s.target.value.match(/^[0-9]+$/)&&parseFloat(s.target.value)>0&&parseFloat(s.target.value)<(M.MaxTransferAmount??Number.MAX_SAFE_INTEGER))a(parseFloat(s.target.value));else return s.preventDefault()}})}),e("div",{className:"button",onClick:()=>a(s=>s+1),children:"+"})]}),c("div",{className:"payment-buttons",children:[e("div",{className:"button",onClick:()=>gt(t,p.number),children:i("APPS.MESSAGES.PAY.REQUEST")}),e("div",{className:"button",onClick:()=>ye(t,{id:p.id,name:p.name,number:p.number}),children:i("APPS.MESSAGES.PAY.SEND")})]})]})},ye=(t,o)=>{var g;if(W.value.EnableMessagePay&&!(!t&&t<=0)){if(t>(((g=W.value)==null?void 0:g.MaxTransferAmount)??Number.MAX_SAFE_INTEGER))return I("error","Amount is too high");R.PopUp.set({title:i("APPS.MESSAGES.PAY.SEND_TITLE").format({amount:W.value.CurrencyFormat.replace("%s",t.toString())}),description:i("APPS.MESSAGES.PAY.SEND_DESCRIPTION").format({amount:W.value.CurrencyFormat.replace("%s",t.toString()),name:o.name??Y(o.number)}),buttons:[{title:i("APPS.MESSAGES.PAY.SEND_BUTTON_CANCEL")},{title:i("APPS.MESSAGES.PAY.SEND_BUTTON_SEND"),cb:()=>{L("Wallet",{action:"sendPayment",number:o.number,amount:t},{success:!0}).then(b=>{var M,p;if(b.success){let r={id:o.id,sender:(p=(M=B)==null?void 0:M.PhoneNumber)==null?void 0:p.value,content:`<!SENT-PAYMENT-${t}!>`,attachments:[],timestamp:Date.now()};y.set([...y.value,r])}else{I("error","Failed to send payment "+JSON.stringify(b)),setTimeout(()=>{R.PopUp.set({title:i(`APPS.WALLET.${b.reason}.TITLE`),description:i(`APPS.WALLET.${b.reason}.DESCRIPTION`),buttons:[{title:i("APPS.WALLET.OK")}]})},500);return}})}}]})}},gt=(t,o)=>{var b,M;if(!W.value.EnableMessagePay||!t&&t<=0)return;let g={sender:(M=(b=B)==null?void 0:b.PhoneNumber)==null?void 0:M.value,content:`<!REQUESTED-PAYMENT-${t}!>`,attachments:[],timestamp:Date.now()};L("Messages",{action:"sendMessage",number:o,content:g.content,attachments:[]},{messageId:Math.floor(Math.random()*1e3).toString()}).then(p=>{if(!p)return y.set([...y.value,{...g,delivered:!1}]);y.set([...y.value,{...g,id:p.messageId}])})},De={Messagelist:[{id:"1",number:"4802940940",name:"Loaf Scripts",unread:!0,lastMessage:"Sure Thing!",messages:[{sender:"4802940940",content:"Sure Thing!",timestamp:Date.now()-1e3*60*60*2},{sender:"1234567890",content:"Ready to release more products?",timestamp:Date.now()-1e3*60*60*2-1e3*60*5},{sender:"4802940940",content:"Hey!",timestamp:Date.now()-1e3*60*60*2-1e3*60*10}],timestamp:Date.now()-1e3*60*60*2},{id:"2",name:"Office Chat",unread:!0,lastMessage:"is the meeting at 8pm?",timestamp:Date.now()-1e3*60*60*4,isGroup:!0,members:[{number:"4802940940",name:"Loaf Scripts",isOwner:!1},{number:"1566444151",name:"Peter",isOwner:!1},{number:"2051440412",name:"Peter",isOwner:!1},{number:"2051440412",isOwner:!1}],messages:[{sender:"4802940940",content:"is the meeting at 8pm?",timestamp:Date.now()-1e3*60*60*4},{sender:"1566444151",content:"please confirm the time",timestamp:Date.now()-1e3*60*60*4-1e3*60*5},{sender:"2051440412",content:"Hey everyone, please make sure to be on time for the meeting",timestamp:Date.now()-1e3*60*60*4-1e3*60*10}]},{id:"3",number:"4802940963",unread:!1,lastMessage:"yeah i dont know whats going on, it was super weird. i think i might have to go to the doctor",timestamp:Date.now()-1e3*60*60*2*24,messages:[{sender:"4802940940",content:"yeah i dont know whats going on, it was super weird. i think i might have to go to the doctor",timestamp:Date.now()-1e3*60*60*2*24},{sender:"1234567890",content:"i hope you feel better soon",timestamp:Date.now()-1e3*60*60*2*24-1e3*60*5},{sender:"4802940940",content:"still sick :(",timestamp:Date.now()-1e3*60*60*2*24-1e3*60*10}]},{id:"4",number:"4802940942",unread:!0,lastMessage:"see you!",timestamp:Date.now()-1e3*60*60*2*24*2,messages:[{sender:"4802940942",content:"<!SENT-LOCATION-X=400.2Y=-1550.3!>",timestamp:Date.now()-1e3*60*60*2*24},{sender:"4802940942",content:`see you!

heres the location`,timestamp:Date.now()-1e3*60*60*2*24*2},{sender:"1234567890",content:"ofcourse, i will be there",timestamp:Date.now()-1e3*60*60*2*24*2-1e3*60*3},{sender:"4802940942",content:"hey buddy, still on for tomorrow?",timestamp:Date.now()-1e3*60*60*2*24*2-1e3*60*3}]}]};function At({location:t}){const o=U(St),[g,b]=S.useState("losSantos"),M=Me.mapTypes[g],p=nt(g);return S.useEffect(()=>{b(it({x:t[0],y:t[1]})?"losSantos":"cayoPerico")},[t]),S.useEffect(()=>{setTimeout(()=>{const r=document.querySelector(".leaflet-control-attribution.leaflet-control a");r&&(r.removeAttribute("href"),r.onclick=()=>{window.invokeNative("openUrl","https://leafletjs.com/")})},500)},[]),e("div",{className:"mapscomponent-wrapper",children:c(rt,{className:"map",crs:g==="losSantos"?lt:ot,style:{backgroundColor:Me.backgrounds[o]},center:[t[0],t[1]],zoom:4,scrollWheelZoom:!1,zoomControl:!1,doubleClickZoom:!1,dragging:!1,bounceAtZoomLimits:!0,boxZoom:!1,maxBoundsViscosity:.95,maxBounds:p,attributionControl:!1,preferCanvas:!0,children:[e(ct,{url:dt[g].tileServer.replace("{layer}",o),minZoom:M.minZoom,maxZoom:M.maxZoom,maxNativeZoom:M.maxZoom,noWrap:!0},`${g}-${o}`),e(mt,{position:t,icon:ut()})]})})}const y=de([]);function Pt(){const{User:t,View:o,UnreadMessages:g}=S.useContext(ae),b=U(B.Settings),M=U(W),p=U(B.PhoneNumber),r=U(Q),[a,T]=t,[f,O]=o,[s,l]=g,E=U(y),h=U(R.Emoji),m=U(R.Gif),[D,x]=S.useState(!1),[k,V]=S.useState(null),[_,z]=S.useState(!1),[G,K]=S.useState(!1),[X,A]=S.useState(0),[C,d]=S.useState(null),v=S.useRef(null),w=S.useRef(0),N=S.useRef(!1),[P,H]=S.useState({content:"",attachments:[]});if(!a)return null;S.useEffect(()=>{var n;Ce("Messages")&&L("Messages",{action:"getMessages",page:0,id:a==null?void 0:a.id},((n=De.Messagelist.find(u=>u.id===a.id))==null?void 0:n.messages)??[]).then(u=>{u&&u.length>0&&y.set([...u.reverse()])})},[r==null?void 0:r.active]);const Se=()=>{if(P.content.length<=0&&P.attachments.length<=0&&!C)return I("info","Message is empty, returning");let n={sender:p,timestamp:Date.now(),id:a.id,content:P.content,attachments:P.attachments};if(!Et(P.content))return I("error","Message contains invalid characters, returning",P.content);if(C){if(N.current)return;N.current=!0,ve("Image",C.waves.message).then(u=>{ve("Audio",C.blob).then($=>{n.content=`<!AUDIO-MESSAGE-IMAGE="${u}"-AUDIO="${$}"-DURATION="${C.duration}"!>`,L("Messages",{action:"sendMessage",number:a.number,content:n.content,id:a.id},{messageId:Math.random().toString(36).substring(7)}).then(j=>{j&&oe()?y.set([...y.value,{...n,id:j.messageId}]):y.set([...y.value,{...n,delivered:!1}]),N.current=!1,d(null)})}).catch($=>{console.error("Failed to upload voice message audio",$),N.current=!1})}).catch(u=>{console.error("Failed to upload voice message image",u),N.current=!1});return}if(!oe())return y.set([...y.value,{...n,delivered:!1}]);L("Messages",{action:"sendMessage",number:a.number,content:P.content,attachments:P.attachments,id:a.id},{messageId:Math.random().toString(36).substring(7)}).then(u=>{u?(y.set([...y.value,{...n,id:u.messageId}]),d(null)):y.set([...y.value,{...n,delivered:!1}]),H({content:"",attachments:[]}),v.current&&(v.current.value=""),I("info","Updating recent message cache state"),F.APPS.MESSAGES.messages.set(F.APPS.MESSAGES.messages.value.map($=>$.id===a.id?{...$,timestamp:new Date().getTime(),lastMessage:n.content.length>0&&n.content,unread:!1,deleted:!1}:$))})},he=()=>{R.PopUp.set({title:i("APPS.MESSAGES.SEND_LOCATION_POPUP.TITLE"),description:i("APPS.MESSAGES.SEND_LOCATION_POPUP.TEXT"),buttons:[{title:i("APPS.MESSAGES.SEND_LOCATION_POPUP.CANCEL")},{title:i("APPS.MESSAGES.SEND_LOCATION_POPUP.SEND"),cb:()=>{L("Maps",{action:"getCurrentLocation"},{x:0,y:0}).then(n=>{if(!n)return;let u={id:a.id,sender:p,content:`<!SENT-LOCATION-X=${ge(n.x,2)}Y=${ge(n.y,2)}!>`,attachments:[],timestamp:Date.now()};L("Messages",{action:"sendMessage",number:a.number,content:u.content,attachments:u.attachments,id:u.id},{messageId:Math.random().toString(36).substring(7)}).then($=>{if(!$)return y.set([...y.value,{...u,delivered:!1}]);y.set([...y.value,{...u,id:$.messageId}])})})}}]})},{handleScroll:Ue}=_e({fetchData:n=>L("Messages",{action:"getMessages",id:a.id,page:n}),onDataFetched:n=>{let u=document.querySelector(".message-container");w.current=u.scrollHeight,y.set([...n.reverse(),...y.value])},isReversed:!0,perPage:25});S.useEffect(()=>{let n=document.querySelector(".message-container");const u=n.scrollHeight;n.scrollTop+=u-w.current,n.scroll},[E]),ne("messages:newMessage",n=>{a.id===n.channelId&&y.set([...y.value,{...n,timestamp:Date.now()}])},{waitUntilService:!0}),ne("messages:renameGroup",n=>{a.id===n.channelId&&T(u=>({...u,name:n.name}))},{waitUntilService:!0}),ne("messages:messageDeleted",n=>{if(I("info","messages:messageDeleted triggered",n),!n)return I("error","Did not get any data from messages:messageDeleted, returning");y.set(y.value.filter(u=>u.id!==n.messageId))},{waitUntilService:!0}),ne("messages:removeMember",n=>{a.id===n.channelId&&n.number===p&&(O("userlist"),T(null))},{waitUntilService:!0});const ke=be(n=>{if(!M.DeleteMessages)return I("error","Config.DeleteMessages is set to false");let u=n.target;for(;!u.classList.contains("message");)u=u.parentElement;let $=u.getAttribute("data-id");if(!$)return I("error","Failed to get message id when longpressing");isNaN($)||($=parseInt($));let j=E.find(le=>le.id===$);if(!j)return I("error","Failed to find message with id",$);j.sender===p&&(Oe(j.content)||Re(j.content)||Ge(j.content)||R.ContextMenu.set({buttons:[{title:i("APPS.MESSAGES.DELETE"),color:"red",cb:()=>{L("Messages",{action:"deleteMessage",channel:a.id,id:$}).then(le=>{if(!le)return I("error","Failed to delete message")})}}]}))}),Re=n=>!n||!M.EnableMessagePay?!1:/<!SENT-PAYMENT-(\d*)!>/.test(n)?!0:!!/<!REQUESTED-PAYMENT-(\d*)!>/.test(n);return e(te,{children:c(q.div,{...Te("right","message",.2),className:"animation-container",children:[c(ie,{children:[D&&e(Ct,{setShow:x,setShowUserInfo:V,sendLocation:he,setData:T,data:a}),k&&e(pt,{setShow:V,user:a!=null&&a.isGroup?k:a})]}),c("div",{className:"message-header",children:[c("div",{className:"back",onClick:()=>{var n;O("userlist"),T(null),(n=Q.value.active)!=null&&n.data&&Q.patch({active:{name:"Messages",data:null}}),a.unread&&(L("Messages",{action:"markRead",id:a.id}),l(u=>u-1))},children:[e($e,{}),s>0&&e("span",{className:"back-title",children:s})]}),c("div",{className:"user",onClick:()=>{a!=null&&a.isGroup?x(!0):V(!0)},children:[a!=null&&a.isGroup?e("div",{className:"group-avatar",style:{backgroundImage:a!=null&&a.avatar?`url(${a.avatar})`:null},children:!(a!=null&&a.avatar)&&c(te,{children:[a.members.sort((n,u)=>n.name&&u.name?n.name.localeCompare(u.name):n.name?-1:u.name?1:0).sort((n,u)=>(n.avatar?1:-1)-(u.avatar?1:-1)).slice(0,5).map((n,u)=>e("div",{className:"avatar","data-hasavatar":(n==null?void 0:n.avatar)!==void 0,style:{backgroundImage:n!=null&&n.avatar?`url(${n.avatar})`:null,zIndex:a.members.length-u},children:n.name?!(n!=null&&n.avatar)&&se(n.name):e("img",{src:`./assets/img/avatar-placeholder-${b.display.theme}.svg`,alt:""})},u)),a.members.length===0&&e("img",{src:`./assets/img/avatar-placeholder-${b.display.theme}.svg`,alt:""})]})}):e("div",{className:"avatar","data-hasavatar":(a==null?void 0:a.avatar)!==void 0,style:{backgroundImage:a!=null&&a.avatar?`url(${a.avatar})`:null},children:a.name?!(a!=null&&a.avatar)&&se(a.name):e("img",{src:`./assets/img/avatar-placeholder-${b.display.theme}.svg`,alt:""})}),e("div",{className:"name",children:a!=null&&a.isGroup?a.name??`${a.members.length===0?1:a.members.length} ${i("APPS.MESSAGES.PEOPLE")}`:a.name??Y(a.number)})]}),c("div",{className:"buttons","data-hidden":a==null?void 0:a.isGroup,children:[e(Ve,{onClick:()=>{a!=null&&a.isGroup||Ee({number:a.number})}}),e(xe,{onClick:()=>{a!=null&&a.isGroup||Ee({number:a.number,videoCall:!0})}})]})]}),e("div",{className:"message-container",onScroll:Ue,style:G||_?{height:"48%"}:{},children:e("div",{className:"message-body",children:E.map((n,u)=>e(Nt,{index:u,messages:E,message:n,longPressEvent:ke},u))})}),e("div",{className:"attachments",children:P.attachments.map((n,u)=>c("div",{className:"attachment",children:[we(n)?e("video",{src:n,muted:!0,controls:!1,loop:!0,autoPlay:!0}):e(ce,{src:n,blur:!0}),e(fe,{onClick:()=>{H({...P,attachments:P.attachments.filter(($,j)=>j!==u)})}})]},u))}),c("div",{className:"message-bottom","data-expanded":!!(G||_),children:[e("div",{className:"upper",children:c("div",{className:"input","data-border":!C,children:[C?c("div",{className:"audio-message",children:[e(fe,{onClick:()=>d(null)}),e("div",{className:"audio-waves",children:e("img",{src:C.waves.placeholder})})]}):e(re,{placeholder:i("APPS.MESSAGES.PLACEHOLDER"),ref:v,value:P.content,onChange:n=>{H({content:n.target.value,attachments:P.attachments})},onKeyDown:n=>{n.key=="Enter"&&Se()}}),(P.content.length>0||P.attachments.length>0||C)&&e("div",{className:"send",onClick:Se,children:e(Ie,{})})]})}),!G&&!_&&e("div",{className:"actions-wrapper",children:c("div",{className:"actions",children:[e("div",{className:"action",onClick:()=>{if(h)return R.Emoji.reset();R.Emoji.set({onSelect:n=>H(u=>({content:`${u.content}${n.emoji}`,attachments:u.attachments}))})},children:"😀"}),e("div",{className:"action",onClick:()=>{var n,u,$;P.attachments.length<3&&R.Gallery.set({includeVideos:!0,allowExternal:($=(u=(n=W)==null?void 0:n.value)==null?void 0:u.AllowExternal)==null?void 0:$.Messages,onSelect:j=>H({...P,attachments:[...P.attachments,j.src]})})},children:e("img",{src:"./assets/img/icons/messages/gallery.png"})}),M.EnableGIFs!==!1&&e("div",{className:"action small",onClick:()=>{if(m)return R.Gif.reset();R.Gif.set({onSelect:n=>H(u=>({content:u.content,attachments:[...u.attachments??[],n]}))})},children:"GIF"}),(M==null?void 0:M.EnableMessagePay)&&!(a!=null&&a.isGroup)&&e("div",{className:"action ",onClick:()=>{z(!1),K(n=>!n)},children:"$"}),e("div",{className:"action",onClick:()=>he(),children:e(Fe,{})}),(M==null?void 0:M.EnableVoiceMessages)&&e("div",{className:"action",onClick:()=>{K(!1),z(n=>!n)},children:e(pe,{})})]})}),e(ie,{children:G&&e(vt,{paymentAmount:X,setPaymentAmount:A,close:()=>K(!1)})}),e(ie,{children:_&&e(ft,{onEnd:n=>{if(!n)return I("error","Failed to get audio message data");d({blob:n.blob,waves:n.waveform,duration:n.duration}),z(!1)},close:()=>z(!1)})})]})]})})}const Nt=({messages:t,message:o,index:g,longPressEvent:b})=>{var D,x;const{User:M}=S.useContext(ae),p=U(B.PhoneNumber),[r]=M,a=U(W);let T,f,O,s,l,E,h=o.sender===p?"self":"other",m=((D=t[g+1])==null?void 0:D.sender)===p?"self":"other";if(t[g+1]?O=Math.abs(o.timestamp-t[g+1].timestamp)/36e5:m=void 0,r!=null&&r.isGroup)T=(x=r.members.find(k=>k.number===o.sender))==null?void 0:x.name,f=!t[g-1]||t[g-1].sender!==o.sender;else if(T=r.name,o.content)if(Ge(o.content))if(h==="other")o.content=i("APPS.MESSAGES.MISSED_CALL").format({number:o.sender});else return null;else/<!SENT-PAYMENT-(\d*)!>/.test(o.content)?s={amount:o.content.match(/\d/g).join(""),request:!1}:/<!REQUESTED-PAYMENT-(\d*)!>/.test(o.content)&&(s={amount:o.content.match(/\d/g).join(""),request:!0});if(Oe(o.content)){let k=o.content.match(/X=(-?\d*\.?\d*)Y/)[1],V=o.content.match(/Y=(-?\d*\.?\d*)!>/)[1];l={x:k,y:V}}return ht(o.content)&&(E={wave:o.content.match(/AUDIO-MESSAGE-IMAGE="([^"]+)"/)[1],src:o.content.match(/AUDIO="([^"]+)"/)[1],duration:o.content.match(/DURATION="([^"]+)"/)[1]}),c("div",{className:`message ${h}`,"data-id":o.id,...b,children:[f&&h=="other"&&e("div",{className:"user",children:T??Y(o.sender)}),o.delivered===!1?c("div",{className:"content-wrapper",children:[s&&e("div",{className:"payment",children:a.CurrencyFormat.replace("%s",s.amount)}),!s&&e("div",{className:"content",children:Ae(o.content)}),e(Ye,{})]}):s||l||E?c(te,{children:[l&&c("div",{className:"location",onClick:()=>{R.ContextMenu.set({buttons:[{title:i("APPS.MESSAGES.OPEN_IN_MAPS"),cb:()=>{Q.patch({active:{name:"Maps",data:{location:[l.y,l.x],name:i("APPS.MESSAGES.USERS_LOCATION").format({name:T??Y(o.sender)}),icon:r.avatar}}})}},{title:i("APPS.MAPS.SET_WAYPOINT"),cb:()=>{L("Maps",{action:"setWaypoint",data:{x:l.x,y:l.y}})}}]})},children:[e(At,{location:[parseFloat(l.y),parseFloat(l.x)]}),h==="other"&&c("div",{className:"sender",children:[T??Y(o.sender)," ",i("APPS.MESSAGES.SENT_LOCATION")]})]}),s&&e("div",{className:"payment",children:s.request?c("div",{className:me("request",h),children:[c("div",{className:"title",children:[a.CurrencyFormat.replace("%s",Pe(s.amount).toString())," ",i("APPS.MESSAGES.PAY.REQUESTED")]}),h==="other"&&e("div",{className:"button",onClick:()=>ye(s.amount,{id:r.id,number:r.number,name:r.name}),children:i("APPS.MESSAGES.PAY.PAY")})]}):e("div",{className:"sent",children:a.CurrencyFormat.replace("%s",Pe(s.amount).toString())})}),E&&e(Mt,{data:E,sender:h})]}):o.content&&e("div",{className:"content",children:Ae(o.content)}),o.attachments&&o.attachments.length>0&&e("div",{className:"attatchments",children:o.attachments.map((k,V)=>we(k)?e("video",{src:k,controls:!1,loop:!0,autoPlay:!0,muted:!0,onClick:_=>R.FullscreenImage.set(k)},V):e(ce,{src:k,blur:!0,onClick:()=>R.FullscreenImage.set(k)},V))}),o.delivered===!1?e("div",{className:"status",children:i("APPS.MESSAGES.NOT_DELIVERED")}):t[g+1]&&O>6?e("div",{className:"date",children:Ne(o.timestamp)}):h!==m&&e("div",{className:"date",children:Ne(o.timestamp)})]},g)},Mt=({data:t,sender:o})=>{var T;const[g,b]=S.useState(!1),[M,p]=S.useState(0),r=S.useRef(null);S.useEffect(()=>{r.current&&(r.current.onended=()=>b(!1))},[r]);const a=f=>{f=Math.floor(f);const O=Math.floor(f/60),s=f-O*60;return`${O<10?"0"+O:O}:${s<10?"0"+s:s}`};return c("div",{className:`voice-message ${o}`,children:[e("a",{onClick:()=>{r.current&&(g?(r.current.pause(),r.current.currentTime=0):r.current.play().catch(()=>I("error","Failed to play audio message")),b(f=>!f))},children:g?e(He,{}):e(je,{})}),c("div",{className:"wave",children:[e("div",{className:"overlay",style:{width:`${(t.duration-M)/t.duration*100}%`}}),e("img",{src:t.wave,alt:"wave"})]}),e("div",{className:"duration",children:a(g&&Math.floor(((T=r.current)==null?void 0:T.currentTime)+.5)!==0?r.current.currentTime:t.duration)}),e("audio",{ref:r,onTimeUpdate:f=>p(f.currentTarget.currentTime),children:e("source",{src:t.src,type:"audio/mpeg"})})]})},pt=({user:t,setShow:o})=>{const g=U(B.Settings);return c(q.div,{...ue,className:"info-panel",children:[e("div",{className:"info-panel-header",children:e("div",{className:"done",onClick:()=>o(!1),children:i("APPS.MESSAGES.DONE")})}),c("div",{className:"info-panel-body",children:[c("div",{className:"info-panel-top",children:[e("div",{className:"avatar","data-hasavatar":(t==null?void 0:t.avatar)!==void 0,style:{backgroundImage:t!=null&&t.avatar?`url(${t.avatar})`:null},children:t.name?!(t!=null&&t.avatar)&&se(t.name):e("img",{src:`./assets/img/avatar-placeholder-${g.display.theme}.svg`,alt:""})}),e("div",{className:"name",children:t.name??Y(t.number)})]}),e("div",{className:"items",children:!t.company&&c(te,{children:[t.name&&e("div",{className:"info-section",children:e("div",{className:"item blue",onClick:()=>We(t.number),children:Y(t.number)})}),e("div",{className:"info-section",children:t.name?e("div",{className:"item blue",onClick:()=>{R.Share.set({type:"contact",data:{firstname:t.firstname,lastname:t.lastname,number:t.number,avatar:t.avatar}})},children:i("APPS.PHONE.CONTACTS.SHARE_CONTACT")}):e("div",{className:"item blue",onClick:()=>{Q.patch({active:{name:"Phone",data:{view:"newContact",number:t.number}}})},children:i("APPS.PHONE.CONTACTS.ADD_CONTACT")})})]})})]})]})},Ct=t=>{const{View:o}=S.useContext(ae),g=U(B.Settings),[b,M]=o,[p,r]=S.useState(t.data.name),[a,T]=S.useState(t.data.avatar);let f=t.data,O=!f.members.find(s=>s.isOwner);return e(te,{children:c(q.div,{...ue,className:"info-panel",children:[c("div",{className:"info-panel-header",children:[e("div",{}),e("div",{className:"close",children:e("div",{className:"button",onClick:()=>t.setShow(!1)})}),e("div",{className:"done",onClick:()=>{p&&p!==""&&p!==f.name?L("Messages",{action:"renameGroup",id:f.id,name:p}).then(s=>{t.setShow(!1)}):t.setShow(!1)},children:i("APPS.MESSAGES.DONE")})]}),c("div",{className:"info-panel-body",children:[c("div",{className:"info-panel-top",children:[a?e(ce,{className:"group-image",src:a,blur:!0}):c("div",{className:"group-avatar",children:[f.members.sort((s,l)=>s.avatar?1:-1).slice(0,5).map((s,l)=>e("div",{className:"avatar","data-hasavatar":(s==null?void 0:s.avatar)!==void 0,style:{backgroundImage:s!=null&&s.avatar?`url(${s.avatar})`:null,zIndex:f.members.length-l},children:s.name?!(s!=null&&s.avatar)&&se(s.name):e("img",{src:`./assets/img/avatar-placeholder-${g.display.theme}.svg`,alt:""})},l)),f.members.length===0&&e("img",{src:`./assets/img/avatar-placeholder-${g.display.theme}.svg`,alt:""})]}),e("div",{className:"add-photo",onClick:()=>{var s,l,E;a?R.PopUp.set({title:i("APPS.MESSAGES.REMOVE_AVATAR_POPUP.TITLE"),description:i("APPS.MESSAGES.REMOVE_AVATAR_POPUP.DESCRIPTION"),buttons:[{title:i("APPS.MESSAGES.REMOVE_AVATAR_POPUP.CANCEL")},{title:i("APPS.MESSAGES.REMOVE_AVATAR_POPUP.REMOVE"),color:"red",cb:()=>{L("Messages",{action:"removeGroupAvatar",id:f.id},!0).then(h=>{if(!h)return I("warning","Could not remove group avatar");t.setData(m=>{let D=m;return D.avatar=null,D}),T(null)})}}]}):R.Gallery.set({allowExternal:(E=(l=(s=W)==null?void 0:s.value)==null?void 0:l.AllowExternal)==null?void 0:E.Messages,onSelect:h=>L("Messages",{action:"setGroupAvatar",id:f.id,avatar:h.src},!0).then(m=>{if(!m)return I("warning","Could not set group avatar");t.setData(D=>{let x=D;return x.avatar=h.src,x}),T(h.src)})})},children:a?"Remove Photo":" Add Photo"})]}),c("div",{className:"items",children:[e("div",{className:"info-section",children:c("div",{className:"item blue",children:[e(Be,{className:"add"}),e(re,{defaultValue:f.name??i("APPS.MESSAGES.GROUP_NAME"),onChange:s=>r(s.target.value)})]})}),c("div",{className:"subtitle",children:[f.members.length===0?1:f.members.length," ",i("APPS.MESSAGES.MEMBERS")]}),c("div",{className:"info-section",children:[c("div",{className:"item",onClick:()=>R.ContactSelector.set({filter:f.members.map(s=>s.number),onSelect:s=>{if(f.members.find(l=>l.number===s.number))return I("info","User is already in group");L("Messages",{action:"addMember",id:f.id,number:s.number},!0).then(l=>{if(!l)return I("warning","Could not add member to group");t.setData(E=>{let h=E;return h.members.push({...s,name:J(s.firstname,s.lastname),isOwner:!1}),h})})}}),children:[e(Xe,{className:"add"}),e("div",{className:"name",children:i("APPS.MESSAGES.ADD_MEMBER")})]}),f.members.sort((s,l)=>s.name&&!l.name?-1:!s.name&&l.name?1:s.name<l.name?-1:s.name>l.name?1:0).map((s,l)=>c("div",{className:"item",children:[O&&e(qe,{className:"remove",onClick:()=>{R.PopUp.set({title:i("APPS.MESSAGES.REMOVE_POPUP.TITLE"),description:i("APPS.MESSAGES.REMOVE_POPUP.TEXT").format({name:s.name??Y(s.number)}),buttons:[{title:i("APPS.MESSAGES.CANCEL")},{title:i("APPS.MESSAGES.REMOVE_POPUP.REMOVE"),color:"red",cb:()=>{L("Messages",{action:"removeMember",number:s.number,id:f.id}).then(E=>{E&&(t.setData(h=>{let m=h;return m.members=m.members.filter(D=>D.number!==s.number),m}),t.setShow(!1))})}}]})}}),e("div",{className:"avatar","data-hasavatar":(s==null?void 0:s.avatar)!==void 0,style:{backgroundImage:s!=null&&s.avatar?`url(${s.avatar})`:null},children:s.name?!(s!=null&&s.avatar)&&se(s.name):e("img",{src:`./assets/img/avatar-placeholder-${g.display.theme}.svg`,alt:""})}),c("div",{className:"details",children:[e("div",{className:"name",children:s.name??Y(s.number)}),s.name&&e("div",{className:"phone-number",children:Y(s.number)})]}),e(ze,{className:"info",onClick:()=>t.setShowUserInfo({...s})})]},l))]}),e("div",{className:"info-section",children:e("div",{className:"item blue",onClick:()=>t.sendLocation(),children:i("APPS.MESSAGES.SHARE_LOCATION")})}),e("div",{className:"info-section",onClick:()=>{R.PopUp.set({title:i("APPS.MESSAGES.LEAVE_POPUP.TITLE"),description:i("APPS.MESSAGES.LEAVE_POPUP.TEXT"),buttons:[{title:i("APPS.MESSAGES.CANCEL")},{title:i("APPS.MESSAGES.LEAVE_POPUP.LEAVE"),color:"red",cb:()=>{L("Messages",{action:"leaveGroup",id:f.id}).then(s=>{if(!s)return I("info","Failed to leave group, server didnt callback request");M("userlist"),t.setShow(!1)})}}]})},children:c("div",{className:"item red",children:[e(Ze,{}),i("APPS.MESSAGES.LEAVE_GROUP")]})})]})]})]})})};function bt(){const{User:t,View:o,Newmessage:g,ImportedUser:b}=S.useContext(ae),[M,p]=t,[r,a]=o,[T,f]=b,O=U(B.Settings),s=U(B.PhoneNumber),[l,E]=g,h=U(F.APPS.PHONE.contacts),[m,D]=S.useState([]),x=S.useRef(null),[k,V]=S.useState(""),[_,z]=S.useState([]),[G,K]=S.useState({content:"",attachments:[]});S.useEffect(()=>{T&&(D([T]),f(null))},[T]);const X=()=>{(G.content.length>0||G.attachments.length>0)&&(m.length>1?L("Messages",{action:"createGroup",members:m,content:G.content,attachments:G.attachments}).then(A=>{if(!A)return I("error","Failed to create group");p({id:A.channelId,lastMessage:G.content,timestamp:Date.now(),isGroup:!0,members:m.map(C=>{let d=J(C.firstname,C.lastname);return{...C,name:d}})}),a("messages"),E(!1)}):(L("Messages",{action:"sendMessage",number:m[0].number,content:G.content,attachments:G.attachments}).then(A=>{var v,w,N,P;if(!A)return I("error","Failed to send message");let C;m[0].name?C=m[0].name:(v=m[0])!=null&&v.firstname&&(C=J((w=m[0])==null?void 0:w.firstname,(N=m[0])==null?void 0:N.lastname));let d={number:m[0].number,name:C,avatar:(P=m[0])==null?void 0:P.avatar};p({...d,id:A.channelId,lastMessage:G.content,timestamp:Date.now()}),I("info","Updating recent message cache state"),F.APPS.MESSAGES.messages.set(F.APPS.MESSAGES.messages.value.map(H=>H.id===A.channelId?{...H,timestamp:new Date().getTime(),lastMessage:G.content,unread:!1,deleted:!1}:H))}),a("messages"),E(!1)))};return S.useEffect(()=>{if(k.length>0){if(!h)return I("error","Contacts not loaded");z(h.filter(A=>{let C=J(A.firstname,A.lastname);return C&&(C==null?void 0:C.toLowerCase().includes(k==null?void 0:k.toLowerCase()))&&!A.company}))}else z([])},[k]),c(q.div,{...ue,className:"new-message-container",children:[c("div",{className:"new-message-header",children:[e("span",{}),e("div",{className:"title",children:i("APPS.MESSAGES.NEW_MESSAGE")}),e("div",{className:"button",onClick:()=>{var C,d,v;m.length>0&&(G.content.length>0||G.attachments.length>0)?X():(E(!1),(v=(d=(C=Q)==null?void 0:C.value)==null?void 0:d.active)!=null&&v.data&&Q.patch({active:{...Q.value.active,data:null}}))},children:m.length>0&&(G.content.length>0||G.attachments.length>0)?i("APPS.MESSAGES.SEND"):i("APPS.MESSAGES.CANCEL")})]}),c("div",{className:"new-message-body",children:[c("div",{className:"new-message-search",children:[c("div",{className:"to",children:[i("APPS.MESSAGES.TO"),":"]}),e("div",{className:"contacts",children:m.map((A,C)=>{let d=J(A.firstname,A.lastname),v=d!=="Unknown";return e("div",{className:me("contact",v?"green":"blue"),onClick:()=>{R.PopUp.set({title:i("APPS.MESSAGES.REMOVE_POPUP.TITLE"),description:i("APPS.MESSAGES.REMOVE_POPUP.TEXT").format({NAME:d??Y(A.number)}),buttons:[{title:i("APPS.MESSAGES.CANCEL")},{title:i("APPS.MESSAGES.REMOVE_POPUP.REMOVE"),color:"red",cb:()=>{let w=m.filter(N=>N.number!==A.number);D(w)}}]})},children:v?d:Y(A.number)},C)})}),e(re,{type:"text",ref:x,onChange:A=>{if(V(A.target.value),A.target.value.length==s.length&&/^\d+$/g.test(A.target.value)){if(A.target.value===s||m.find(d=>d.number==A.target.value))return;D([...m,{number:A.target.value}]),x.current.value="",V("")}},onKeyDown:A=>{var C;A.key=="Backspace"&&k.length==0?((C=m[m.length-1])==null?void 0:C.name)===void 0?(x.current.value=m[m.length-1].number,D(m.slice(0,m.length-1))):D(m.slice(0,-1)):A.key=="Tab"&&(A.preventDefault(),_.length==1&&(D([...m,_[0]]),x.current.value="",V("")))}})]}),e("div",{className:"search-results",children:_&&_.filter(A=>!m.find(C=>C.number==A.number)).map((A,C)=>{let d=J(A.firstname,A.lastname);return c("div",{className:"contact",onClick:()=>{m.find(w=>w.number==A.number)||(D([...m,A]),x.current.value="",V(""))},children:[e("img",{src:A.avatar??`./assets/img/avatar-placeholder-${O.display.theme}.svg`}),c("div",{className:"user",children:[e("div",{className:"name",children:d}),e("div",{className:"number",children:Y(A.number)})]})]},C)})})]}),m.length>0&&_.length===0&&e("div",{className:"message-bottom absolute",children:e("div",{className:"upper",children:c("div",{className:"input",children:[e(re,{placeholder:i("APPS.MESSAGES.PLACEHOLDER"),value:G.content,onChange:A=>{K({content:A.target.value??"",attachments:G.attachments})},onKeyDown:A=>{A.key=="Enter"&&X()}}),(G.content.length>0||G.attachments.length>0)&&e("div",{className:"send",onClick:()=>X(),children:e(Ie,{})})]})})})]})}const Z=de([]),ee=de([]);function Tt(){var C;const{User:t,View:o,Newmessage:g,UnreadMessages:b,ImportedUser:M}=S.useContext(ae),p=U(B.PhoneNumber),r=(C=U(Q))==null?void 0:C.active,[a,T]=t,[f,O]=o,[s,l]=M,[E,h]=g,[m,D]=b,x=U(F.APPS.PHONE.contacts),k=U(F.APPS.MESSAGES.messages),V=U(Z),[_,z]=S.useState(""),[G,K]=S.useState(!1),X=U(ee);S.useEffect(()=>{if(Ce("Messages"))if(k)I("info","Using cache for recent messages"),D(V.filter(d=>d.unread).length),Z.set(k.map(d=>{if(!d.name){let v=x.find(w=>w.number===d.number);if(!v)return d;d.name=J(v.firstname,v.lastname),d.avatar=v==null?void 0:v.avatar}return d})??[]);else{if(!oe())return I("info","No service, not fetching recent messages");L("Messages",{action:"getRecentMessages"},De.Messagelist).then(d=>{if(!d)return I("error","No recent messages");let v=d.map(w=>{if(w.isGroup)return w.members=w.members.filter(N=>N.number!==p).map(N=>{let P=x.find(H=>H.number===N.number);return{name:P&&P.firstname?J(P==null?void 0:P.firstname,P==null?void 0:P.lastname):void 0,avatar:P==null?void 0:P.avatar,blocked:P==null?void 0:P.blocked,favourite:P==null?void 0:P.favourite,number:N.number,isOwner:N.isOwner}}),w;{let N=x.find(P=>P.number===w.number);return w.name=N!=null&&N.lastname?`${N.firstname} ${N.lastname}`:N==null?void 0:N.firstname,w.avatar=N==null?void 0:N.avatar,w}});D(v.filter(w=>w.unread).length),Z.set(v),I("info","setting cache"),F.APPS.MESSAGES.messages.set(v)})}},[k,r]);let A=S.useRef(!1);return S.useEffect(()=>{var d;if(!A.current&&r!=null&&r.data&&(A.current=!0,r!=null&&r.data&&((d=r.data)==null?void 0:d.view)=="messages")){let v=V==null?void 0:V.find(w=>w.number===r.data.number);v?(T(v),O("messages")):(h(!0),l({number:r.data.number,name:r.data.name,avatar:r.data.avatar}))}},[r==null?void 0:r.data,V]),ne("messages:newMessage",d=>{let v=JSON.parse(JSON.stringify(Z.value)),w=v.findIndex(N=>N.id===d.channelId);if(w!==-1&&v.unshift(v.splice(w,1)[0]),v.length===0)return I("error","No recent messages");v[0].lastMessage=d.content,v[0].timestamp=new Date,Z.set(v)},{waitUntilService:!0}),c(te,{children:[e(ie,{children:E&&e(bt,{})}),c(q.div,{...Te("left","messagelist",.2),className:"animation-container",children:[c("div",{className:"messages-header",children:[c("div",{className:"buttons",children:[e("div",{className:"edit",onClick:()=>K(!G),children:G?i("APPS.MESSAGES.DONE"):i("APPS.MESSAGES.EDIT")}),e(Qe,{"data-disabled":G,onClick:()=>h(!0)})]}),e("div",{className:"title",children:i("APPS.MESSAGES.TITLE")})]}),e(Ke,{placeholder:i("SEARCH"),onChange:d=>z(d.target.value)}),e("div",{className:"users-list",children:V.filter(d=>{var v,w;return d.deleted?!1:(d.isGroup?d.members.find(N=>{var P;return N&&(((P=N.name)==null?void 0:P.toLowerCase().includes(_==null?void 0:_.toLowerCase()))||N.number.includes(_))}):((v=d.name)==null?void 0:v.toLowerCase().includes(_==null?void 0:_.toLowerCase()))||d.number.includes(_))||((w=d==null?void 0:d.lastMessage)==null?void 0:w.toLowerCase().includes(_==null?void 0:_.toLowerCase()))}).sort((d,v)=>v.timestamp-d.timestamp).map((d,v)=>e(wt,{user:d,editMode:G,onClick:()=>{if(T(d),O("messages"),d.unread){L("Messages",{action:"markRead",id:d.id},!0),D(N=>N-1);let w=F.APPS.MESSAGES.messages.value;F.APPS.MESSAGES.messages.set(w.map(N=>(N.id===d.id&&(N.unread=!1),N)))}}},v))}),e(ie,{children:G&&c(q.div,{initial:{opacity:0,y:50},animate:{opacity:1,y:0},exit:{opacity:0,y:50},transition:{duration:.2,ease:"easeInOut"},className:"messages-footer",children:[e("div",{className:"button","data-disabled":!0,children:i("APPS.MESSAGES.READ")}),e("div",{className:"button",onClick:()=>{if(X.length===0)return I("info","No messages selected, can't delete");R.PopUp.set({title:i("APPS.MESSAGES.DELETE_CONVERSATION.TITLE"),description:i("APPS.MESSAGES.DELETE_CONVERSATION.DESCRIPTION"),buttons:[{title:i("APPS.MESSAGES.CANCEL")},{title:i("APPS.MESSAGES.DELETE"),cb:()=>{L("Messages",{action:"deleteConversations",channels:X},!0).then(d=>{if(!d)return I("error","Failed to delete conversations");K(!1),Z.set(V.filter(v=>!X.includes(v.id))),F.APPS.MESSAGES.messages.set([...F.APPS.MESSAGES.messages.value.map(v=>(X.includes(v.id)&&(v.deleted=!0),v))]),ee.set([])})}}]})},children:i("APPS.MESSAGES.DELETE")})]})})]})]})}const wt=({user:t,editMode:o,onClick:g})=>{var O,s;const b=U(W),M=U(B.Settings),p=U(Z),[r,a]=S.useState(!1);let T;const f=be(l=>{R.ContextMenu.set({buttons:[{title:i("APPS.MESSAGES.MARK_AS_READ"),cb:()=>{L("Messages",{action:"markRead",id:t.id},!0).then(E=>{if(!E)return I("error","Failed to mark message as read");Z.set(p.map(m=>(m.id===t.id&&(m.unread=!1),m)));let h=F.APPS.MESSAGES.messages.value;F.APPS.MESSAGES.messages.set(h.map(m=>(m.id===t.id&&(m.unread=!1),m)))})}},{title:i("APPS.MESSAGES.DELETE_CONVERSATION.TITLE"),color:"red",cb:()=>{R.PopUp.set({title:i("APPS.MESSAGES.DELETE_CONVERSATION.TITLE"),description:i("APPS.MESSAGES.DELETE_CONVERSATION.DESCRIPTION"),buttons:[{title:i("APPS.MESSAGES.CANCEL")},{title:i("APPS.MESSAGES.DELETE"),cb:()=>{L("Messages",{action:"deleteConversations",channels:[t.id]},!0).then(E=>{if(!E)return I("error","Failed to delete conversations");Z.set(p.filter(h=>h.id!==t.id)),F.APPS.MESSAGES.messages.set([...F.APPS.MESSAGES.messages.value.map(h=>(h.id===t.id&&(h.deleted=!0),h))]),ee.set([])})}}]})}}]})});if(t.isGroup)if(t.name)T=t.name;else if(t.members.length===0)T=i("APPS.MESSAGES.GROUP");else{let l=t.members.length-1;T=t.members.sort((E,h)=>E.name&&!h.name?-1:!E.name&&h.name?1:E.name<h.name?-1:E.name>h.name?1:0).slice(0,2).map((E,h)=>{var D;let m=E.name?E.name:Y(E.number);return h===1&&l>2?`${m} +${l} ${(D=i("APPS.MESSAGES.OTHER"))==null?void 0:D.toLowerCase()}`:m}).join(", ")}else T=t.name;if(t.lastMessage){if(t.lastMessage==="Attachment"&&(t.lastMessage=i("APPS.MESSAGES.SENT_A_PHOTO")),/<!SENT-PAYMENT-(\d*)!>/.test(t.lastMessage)){let l=t.lastMessage.match(/\d/g).join("");t.lastMessage=`${i("APPS.MESSAGES.SENT")} ${b.CurrencyFormat.replace("%s",l)}`}else if(/<!REQUESTED-PAYMENT-(\d*)!>/.test(t.lastMessage)){let l=t.lastMessage.match(/\d/g).join("");t.lastMessage=`${i("APPS.MESSAGES.REQUESTED")} $${l}`}else if(/<!SENT-LOCATION-X=(-?\d*\.?\d*)Y=(-?\d*\.?\d*)!>/.test(t.lastMessage))t.lastMessage=`${i("APPS.MESSAGES.SENT_LOCATION_SHORT")}`;else if(t.lastMessage.startsWith('<!AUDIO-MESSAGE-IMAGE="'))t.lastMessage=i("APPS.MESSAGES.SENT_AUDIO_MESSAGE");else if(t.lastMessage==="<!CALL-NO-ANSWER!>")t.lastMessage=i("APPS.MESSAGES.TRIED_TO_CALL").format({number:Y(t.number)});else if(t.lastMessage==="")return}return S.useEffect(()=>{r?ee.set([...ee.value,t.id]):ee.set(ee.value.filter(l=>l!==t.id))},[r]),c(q.div,{initial:{opacity:0,scale:.9},whileInView:{opacity:1,scale:1},viewport:{once:!0},className:"user",...f,onClick:()=>{o?a(!r):g()},children:[c("div",{className:"items",children:[o&&e(q.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.9},className:"check","data-checked":r,onClick:l=>{l.stopPropagation(),a(!r)},children:r&&e(Je,{})},"check"),e("div",{className:me("dot",t.unread&&"unread")})]}),t.isGroup?c("div",{className:"avatar group",children:[t.members.map((l,E)=>{var h;if(E<=3)return l.avatar?e("div",{"data-hasavatar":"true",style:{backgroundImage:`url(${l.avatar})`}},E):l.name?e("div",{children:(h=l==null?void 0:l.name)==null?void 0:h.charAt(0)},E):e("div",{className:"unknown"},E)}),t.members.length===0&&e("img",{className:"avatar",src:`assets/img/avatar-placeholder-${M.display.theme}.svg`})]}):e("div",{className:"avatar",style:{backgroundImage:t!=null&&t.avatar?`url(${t.avatar})`:null},children:t.name?!(t!=null&&t.avatar)&&se(t.name):e("img",{src:`assets/img/avatar-placeholder-${M.display.theme}.svg`,alt:""})}),c("div",{className:"user-body",children:[c("div",{className:"user-header",children:[e("div",{className:"name",children:T??Y(t.number)}),c("div",{className:"right",children:[e("div",{className:"time",children:et(t.timestamp)}),e(tt,{})]})]}),e("div",{className:"content",children:((O=t.lastMessage)==null?void 0:O.length)>40?((s=t.lastMessage)==null?void 0:s.substring(0,40))+"...":t.lastMessage})]})]})};const ae=S.createContext(null);function yt(){const[t,o]=S.useState(null),[g,b]=S.useState("userlist"),[M,p]=S.useState(null),[r,a]=S.useState(0),[T,f]=S.useState(!1);return e("div",{className:"messages-container","data-view":g,children:e(ae.Provider,{value:{User:[t,o],View:[g,b],Newmessage:[T,f],ImportedUser:[M,p],UnreadMessages:[r,a]},children:g==="userlist"?e(Tt,{}):t&&e(Pt,{})})})}export{ae as MessagesContext,yt as default};
