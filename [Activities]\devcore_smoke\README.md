
REALISTIC SMOKING V3 HOW TO INSTALLATION

documentation: https://docs.devcore.cz/

1. After purchasing the script on the store https://store.devcore.cz/, the script can be downloaded here https://keymaster.fivem.net/assets

2. Upload the contents of the zip file to your server.

3. ensure devcore_smoking in server.cfg after your framework and ox_lib.

4. Now create items depending on the inventory

If you are using ox_inventory 
paste the contents of the file upload/inventory/ox_inventory.txt into the file ox_inventory/data/items.lua.
And upload/icons into ox_inventory/web/images.

If you are using qs-inventory
paste the contents of the file upload/inventory/qs_inventory.txt into the file qs-inventory/shared/items.lua.
and uplod/icons into qs-inventory/html/images

If you are using qb-inventory
paste the contents of the file upload/inventory/qb_inventory.txt into the file qb-core/shared/items.lua.
And upload/icons into qb-inventory/html/images.

If you are using a different inventory
paste the contents of the file upload/inventory/items.sql file into the database.

5. If you want to use xsound for sound effects, you can find it here: https://github.com/Xogy/xsound/releases/tag/1.4.3
If you have an xsound script running on your server, put the files from devcore_smoke/upload/xsound into xsound/html/sounds

6. For devcore_smoke to work properly, ox_lib needs to be added to the server: https://github.com/overextended/ox_lib/releases

7. After adding ox_lib, restart the server.