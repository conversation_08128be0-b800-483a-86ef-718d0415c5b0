shared_scripts { '@FiniAC/fini_events.js', '@FiniAC/fini_events.lua' }

fx_version 'cerulean'
game 'gta5'

name 'BodhixPK'
description 'Parkour & Freerunning Career'
author 'Bodhix'
version '2.1.0'

lua54 'yes'

shared_scripts {
    'config.lua',
}

client_scripts {
    'Client/*.lua',
}

server_scripts {
    'server/sv.lua',
    'Whitelist.lua',
}

files {
    'config.lua',
    'html/index.html',
    'server/version.json',
    'stream/<EMAIL>',
    'stream/bodhix@<EMAIL>',
    'stream/bodhix@<EMAIL>',
    'stream/bodhix@<EMAIL>',
    'stream/<EMAIL>',
    "stream/NotzfireLeft.ydr",
	"stream/NotzfireRight.ydr",	
    'html/LAADS-LoveYou.mp3',
    'html/MaxBrhon-AIMidtempo.mp3',
    'html/<PERSON><PERSON><PERSON>&<PERSON>eela-NeverKnewMe.mp3',
    'html/RobbieMendez-HomeHouse.mp3',
    'html/Ghostnaps-GrowApartGarage.mp3',
    'html/DoubleArabian.MP3',
    'html/Arabian.MP3',
    'html/Back.MP3',
    'html/CarlWheel.MP3',
    'html/Dash.MP3',
    'html/DFlip.MP3',
    'html/Flip.MP3',
    'html/Fly.MP3',
    'html/Full.MP3',
    'html/HighTricks.MP3',
    'html/Kash.MP3',
    'html/Monkey.MP3',
    'html/Reverse.MP3',
    'html/Roll.MP3',
    'html/Wallrun.MP3',
    'html/Climb.MP3',
    'html/Ledge.MP3',
    'html/Score.mp3',
    'html/MissScore.mp3',
}

ui_page 'html/index.html'

nui_page 'html/index.html'

data_file "DLC_ITYP_REQUEST" "stream/NotzfireLeft.ytyp"
data_file "DLC_ITYP_REQUEST" "stream/NotzfireRight.ytyp"

escrow_ignore {
    'config.lua',
    'Whitelist.lua',
    'Client/ActivationMethod.lua',
}





dependency '/assetpacks'