/* Define the Jack<PERSON>ilver font */
@font-face {
    font-family: 'JackSilver';
    src: url('fonts/Jacksilver.ttf') format('truetype');
    font-weight: normal;
    font-style: normal;
}
@font-face {
    font-family: 'Juliett-Regular';
    src: url('fonts/Juliett-Regular.ttf') format('truetype');
    font-weight: normal;
    font-style: normal;
}

/* Apply the font to the submenu title and buttons */
#submenu-title, button {
    font-family: 'JackSilver', sans-serif;
}

/* General Reset */
body {
    margin: 0;
    font-family: Arial, sans-serif;
    background: none;
    color: white;
    overflow: hidden; /* Prevent scrolling when menu is active */
}

.words-style {
    font-size: 20px;
    font-weight: bold;
    font-family: 'Juliett-Regular', sans-serif;
}

/* Menu Box Styling */
#menuBox {
    position: absolute;
    top: 50%;
    left: 10%; /* Position the menu box slightly away from the edge */
    transform: translateY(-50%); /* Center vertically */
    width: 350px;
    background: rgba(51, 51, 51, 0.8); /* Dark gray with 80% opacity */
    border-radius: 15px;
    box-shadow: 0px 0px 15px rgba(0, 0, 0, 0.5);
    padding: 20px;
    color: white;
    display: none;
    z-index: 9999; /* Ensure it's above other elements */
}

/* Logo Styling */
#logo {
    display: block;
    margin: 0 auto 20px auto;
    max-width: 200px;
}

/* Button Container------------------------------------------------------------------------------------------- */
.buttons {
    display: flex;
    flex-direction: column;
    align-items: center; /* Asegura que los botones se centren */
    gap: 10px; /* Espacio entre botones */
    width: 100%; /* Asegura que ocupen todo el ancho disponible */
}

/* Button Styling */
button {
    width: 80%;
    padding: 8px;
    font-size: 16px;
    background: #ffffff;
    color: rgb(0, 0, 0);
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: 'Arial', sans-serif; /* Default font for buttons */
}

button.close {
    background: rgb(255, 0, 128);
    width: 80%; /* Asegurar que el botón tenga el mismo tamaño que los otros */
    text-align: center;
}

button.close:hover {
    background-color: darkred;
}

/* Submenu Title Styling */
#submenu-title {
    font-family: 'Jacksilver', sans-serif; /* Replace with JackSilver if available */
    font-size: 28px;
    font-weight: bold;
    text-align: center;
    margin-bottom: 20px;
}

/* Options Grid Styling */
#options {
    display: flex;
    flex-wrap: wrap; /* Wrap items to next line */
    justify-content: center; /* Center the items */
    gap: 20px; /* Space between items */
    max-width: 350px;
    max-height: 450px; /* Limit height for scrolling */
    overflow-y: auto; /* Enable vertical scrolling */
    margin: 20px auto;
    padding-right: 10px; /* Prevent scrollbars from overlapping content */
}

/* Image Buttons */
#options img {
    width: 130px; /* Adjusted image size for better fit */
    height: auto;
    border-radius: 10px;
    cursor: pointer;
    transition: transform 0.3s ease, border 0.3s ease;
    border: 2px solid transparent;
}

#options img:hover {
    transform: scale(1.1); /* Slight zoom on hover */
    border-color: #007bff; /* Highlight border on hover */
}

/* Scrollbar Styling */
#options::-webkit-scrollbar {
    width: 8px;
}

#options::-webkit-scrollbar-thumb {
    background: #555; /* Dark scrollbar */
    border-radius: 4px;
}

#options::-webkit-scrollbar-thumb:hover {
    background: #007bff; /* Highlighted scrollbar */
}

/*------------------------------------------------------------------------------------CONTAINER------------------------------------------------------------------------------------------------*/
/* Reset de estilos */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* Estilos del cuerpo */
body {
    background: none;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    padding: 20px;
}

/* Contenedor principal */

#container {
    background-color: rgb(255, 255, 255);
    padding: 30px; /* Reduce padding for smaller size */
    padding-left: 45px;
    border-radius: 13px;
    box-shadow: 0 4px 13px rgb(129, 129, 129);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    border: 3px solid #999999;
    max-width: 1000px; /* Reduce width */
    width: 60%; /* Decrease from 70% */
    height: auto; /* Adjust dynamically */
    position: relative;
    text-align: center;
    overflow: hidden; /* Prevents excessive overflow */
}


/* ---------------------------------------------------------------------------------Línea decorativa superior--------------------------------------------------------------------------*/
.menu-line {
    position: absolute;
    top: 50px; /* Ajusta la altura de la línea */
    width: 22%; /* Abarca de los botones de la izquierda a los de la derecha */
    height: 13px; /* Grosor de la línea */
    background-color: rgb(124, 124, 124);
    z-index: 2; /* Asegura que se vea por encima del fondo */
}

/* Línea izquierda */
.menu-line.left {
    right: 70%;
    /* width: 35%; */ /* Ajusta hasta el inicio del título */
    /* z-index: 0; */ /* Asegura que se vea por encima del fondo */
}

/* Línea derecha */
.menu-line.right {
    left: 70%;
    /* width: 35%; */ /* Ajusta hasta el final del título */
    /* z-index: 0; */ /* Asegura que se vea por encima del fondo */
}

/* --------------------------------------------------------------------------------------Título del menú---------------------------------------------------------------------------*/
.menu-title {
    font-family: 'JackSilver', sans-serif; /* Apply JackSilver font */
    position: relative;
    top: -20px;
    font-size: 60px; /* Adjust if needed */
    font-weight: bold;
    color: rgb(124, 124, 124);
    text-align: center;
    z-index: 1;
}


/* Menú en grid */
.menu {
    display: grid;
    grid-template-columns: 1fr 1fr; /* Keeps two equal columns */
    grid-template-rows: 1fr 1fr;
    gap: 20px;
    width: 95%; /* Increase width slightly */
    margin-left: 4%; /* Push the entire grid to the right */
}



/* Ajuste de tamaños */
.best-spots, .select-sport {
    width: 100%;
    height: 100%;
}

.whats-new {
    width: 100%;
    height: 100%;
    grid-column: 2;
    grid-row: 1 / span 2;
}

/* Estilos de los botones---------------------------------------------------------------Menu Item---------------------------------------------------------------------------------------- */
.menu-item {
    width: 90%; /* Keeps images balanced */
    margin-left: 4%; /* Moves each item slightly right */
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.3);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    background-color: white;
    border-radius: 0;
    cursor: pointer;
    display: block; /* Evita espacios extra si la imagen es un inline element */
    flex: 1; /* Asegura que los botones ocupen todo el espacio */
    margin: 0; /* Elimina cualquier margen */
    padding: 0; /* Elimina cualquier padding */
    position: relative; /* Necesario para que la franja se posicione dentro del botón */
    overflow: hidden; /* Evita que la franja sobresalga del botón */
}

/* Franja semitransparente en la parte inferior de los botones */
.menu-item::after {
    content: "";
    position: absolute;
    bottom: 0; /* Alinea la franja en la parte inferior */
    left: 0;
    width: 100%; /* Ocupa todo el ancho del botón */
    height: 20%; /* Ajusta la altura de la franja (puedes modificar este valor) */
    background: rgba(0, 0, 0, 0.5); /* Color negro con 50% de transparencia */
    pointer-events: none; /* Evita que la franja bloquee los clics en los botones */
}

/* Segunda franja superior para "What's New" */
.whats-new::before {
    display: none; /* Hides the extra gray bar */
}



/* Franja específica para el botón "What's New" */
.whats-new::after {
    height: 10%; /* Ajusta la franja a una escala menor */
    background: rgba(51, 51, 51, 0.8); /* Color negro con 50% de transparencia */
}

/* Texto sobre la franja */
.menu-text {
    position: absolute;
    bottom: 2%; /* Ajusta la altura del texto dentro de la franja */
    left: 50%;
    transform: translateX(-50%); /* Centra el texto horizontalmente */
    color: rgb(255, 255, 255);
    font-size: 60px;
    font-family: 'Juliett-Regular', sans-serif;
    font-weight: bold;
    text-align: center;
    width: 100%;
    z-index: 2; /* Asegura que el texto esté sobre la franja */
}

/* Ajuste específico para el botón "What's New" */
.whats-new .menu-text {
    bottom: 1%; /* Ajusta la posición del texto */
}

.whatsnew-header {
    position: absolute;
    top: 2%; /* Ajusta la altura del texto dentro de la franja */
    left: 50%;
    transform: translateX(-50%); /* Centra el texto horizontalmente */
    color: rgb(255, 255, 255);
    font-size: 60px;
    font-weight: bold;
    text-align: center;
    width: 100%;
    z-index: 2; /* Asegura que el texto esté sobre la franja */
}

/* Imagen dentro del menú */
.menu-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 0;
    padding: 0;
    margin: 0;
    border: none; /* Por si algún borde afecta el tamaño */
}

.select-sport img {
    width: 100%; /* Aumenta el tamaño para evitar que se vea recortada */
    height: 100%; /* Mantiene la proporción original */
    object-fit: cover; /* Se asegura de llenar el espacio */
}

.whats-new img {
    width: 100%; /* Aumenta el tamaño para evitar que se vea recortada */
    height: 100%; /* Mantiene la proporción original */
    /* transform: scaleX(0.9); */
    object-fit: cover; /* Se asegura de llenar el espacio */
}

/* Efecto hover */
.menu-item:hover {
    transform: scale(1.05);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.4);
}

.whats-new:hover {
    transform: scale(1.03);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.4);
}

/* -----------------------------------------------------------------------------------Sección de enlaces----------------------------------------------------------------------------- */
.links {
    position: relative;
    z-index: 100;
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    justify-content: center;
    gap: 50px; /* Adjust gap as needed */
}

/* Estilos de los íconos sociales */
.social-icon {
    width: 60px; /* Ajusta el tamaño del ícono */
    height: 60px;
    transition: transform 0.3s ease; /* Agrega un efecto suave */
    cursor: pointer;
}

.discord-icon {
    width: 60px; /* Ajusta el tamaño solo para Discord */
    height: 60px;
}

/* Efecto hover: los iconos se agrandan al pasar el cursor */
.social-icon:hover {
    transform: scale(1.2);
}

/* Estilos del modal */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    justify-content: center;
    align-items: center;
}

/* Contenido del modal */
.modal-content {
    background-color: white;  /* White background */
    padding: 20px;  /* Add padding to create a border effect */
    border-radius: 12px;
    width: 85vw;  /* 85% of the screen width */
    height: calc(85vw * 9 / 16); /* Keep 16:9 aspect ratio */
    max-width: 1600px; /* Prevent it from getting too big */
    max-height: 900px; /* Keep close to 1080p but smaller */
    position: relative;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.8); /* Add a slight shadow for better visibility */
}

.modal-content iframe {
    width: 95%;  /* Make the video smaller than the white background */
    height: 95%;
    border-radius: 8px;  /* Smooth edges */
}


/* Ajustar el tamaño del video */
.video-container {
    width: 100%; /* Hace que el contenedor del video ocupe todo el ancho */
    height: 1000px; /* Aumenta la altura del video */
}

/* Ajustar el tamaño del iframe de YouTube */
.video-container iframe {
    width: 95%;  /* Make the video smaller than the white background */
    height: 95%;
    border-radius: 8px;  /* Smooth edges */
}

/* Botón para cerrar el modal */
.close {
    position: absolute;
    top: 10px;
    right: 15px;
    font-size: 30px;
    cursor: pointer;
    color: #333;
    background: transparent;
    border: none;
}

.close:hover {
    color: #007bff;
}

/* Contenedor del video */
.video-container {
    width: 100%;
    height: 400px;
}

/* Responsividad */
@media (max-width: 768px) {
    .menu {
        grid-template-columns: 1fr;
        grid-template-rows: auto;
    }

    .container {
        height: auto;
    }
}

/* Estilos específicos para el botón de cierre del contenedor principal */
#closeContainerNew {
    position: absolute;
    top: 15px;
    right: 15px;
    width: 60px;
    height: 60px; /* Bigger area */
    background: white;
    color: black;
    border: none;
    border-radius: 50%; /* Circle */
    cursor: pointer;
    font-size: 24px;
    font-weight: bold;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999; /* Ensure it's above everything */
}

#closeContainerNew:hover {
    background: white;
    transform: scale(1.1);
}



.back-arrow {
    position: absolute;
    top: 15px; /* Align near the top */
    left: 15px; /* Move to the left corner */
    width: 50px;
    height: 50px;
    font-size: 24px; /* Adjust arrow size */
    background: transparent; /* No background */
    color: white; /* Arrow color */
    border: none;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
}

.back-arrow:hover {
    color: #ff0077; /* Highlight when hovered */
}

.purchase-button {
    position: absolute;
    font-family: 'Juliett-Regular', sans-serif;
    bottom: 15px; /* Position near the bottom */
    left: 50%;
    transform: translateX(-50%); /* Center horizontally */
    width: 80%; /* Adjust size */
    padding: 12px;
    font-size: 20px;
    font-weight: bold;
    background: #ff0077; /* Neon pink style */
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
}

.purchase-button:hover {
    background: #ff3399; /* Lighter pink on hover */
    transform: translateX(-50%) scale(1.05); /* Slight zoom */
}
