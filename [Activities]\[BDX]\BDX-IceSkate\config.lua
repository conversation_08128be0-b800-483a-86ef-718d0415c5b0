--------------------------------------
--<!>-- BODHIX | STUDIO --<!>--
--------------------------------------
--------------------------------------
--<!>--  SKATE | CAREER --<!>--
--------------------------------------

-- Support & Feedback: https://discord.gg/PjN7AWqkpF

Config = {}

Config.Debug = false -- True / False for Debug System

Config.Framework = "esx" -- Pick your framework: "qb" or "esx" - Default: "qb"

-- Settings
Config.ItemName = 'iceskates'
Config.FrameworkResourceName = nil
Config.MaxSpeedKmh = 40 -- This does not really change that much unless you get a boost somehow.
Config.maxJumpHeigh = 5.0 -- We suggest not to mess to much with this (And yes, you can jump very high).
Config.LoseConnectionDistance = 2.0 -- This is the distance from you to the skateboard (Don't mess with this, unless you know, what you are doing).
Config.MinimumSkateSpeed = 2.0
Config.MinGroundHeight = 1.0
Config.EnablePeds = true
Config.PickupKey = 113
Config.IceSkatesSlotHomme = 1   -- Model Slot for men
Config.IceSkatesSlotFemme = 1   -- Model Slot for women
Config.IceSkatesColorHomme = 0  -- Color for men
Config.IceSkatesColorFemme = 0  -- Color for women

Config.Language = {
    Info = {
        ['controls'] = 'Press Space to Jump | Press G to save Ice Skates',
    }
}

-- Want to customize areas to use the Ice Skates or not? Here is an Event to take them off.
-- TriggerClientEvent("RemoveIceSkatesEvent", -1)