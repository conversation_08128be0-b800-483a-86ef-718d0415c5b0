<template>
	<div class="container q-pa-md rounded-borders" :class="visibleClass" style="background-color: #1e293b !important">
		<q-item dark>
			<q-item-section avatar>
				<q-icon name="wifi" />
			</q-item-section>
			<q-item-section>
				<q-item-label>{{ locales['radius'] }}</q-item-label>
				<q-slider v-model="radius" :min="20" :max="60" label color="primary" @change="updateLight()" />
			</q-item-section>
		</q-item>
		<q-item dark>
			<q-item-section avatar>
				<q-icon name="highlight" />
			</q-item-section>
			<q-item-section>
				<q-item-label>{{ locales['distance'] }}</q-item-label>
				<q-slider v-model="distance" :min="20" :max="50" label color="primary" @change="updateLight()" />
			</q-item-section>
		</q-item>
		<q-item dark :disable="mode == 'blink' ? true : false">
			<q-item-section avatar>
				<q-icon name="light_mode" />
			</q-item-section>
			<q-item-section>
				<q-item-label>{{ locales['brightness'] }}</q-item-label>
				<q-slider v-model="brightness" :min="10" :max="100" label color="primary" :disable="mode == 'blink' ? true : false" @change="updateLight()" />
			</q-item-section>
		</q-item>
		<q-item dark>
			<q-item-section avatar>
				<q-icon name="model_training" />
			</q-item-section>
			<q-item-section>
				<q-radio dark v-model="mode" val="static" :label="locales['static']" color="primary" :on="updateLight()" />
				<q-radio dark v-model="mode" val="dynamic" :label="locales['dynamic']" color="primary" :on="updateLight()" />
				<q-radio dark v-model="mode" val="blink" :label="locales['blink']" color="primary" :on="updateLight()" />
			</q-item-section>
		</q-item>
		<q-item dark :disable="mode == 'dynamic' ? true : false">
			<q-item-section avatar>
				<q-icon name="palette" />
			</q-item-section>
			<q-item-section>
				<q-input dark filled color="primary" v-model="color" :rules="['anyColor']" :hint="locales['validation']" :disable="mode == 'dynamic' ? true : false">
					<template v-slot:append>
						<q-icon name="colorize" class="cursor-pointer">
							<q-popup-proxy cover transition-show="scale" transition-hide="scale">
								<q-color v-model="color" dark @change="updateLight()" />
							</q-popup-proxy>
						</q-icon>
					</template>
				</q-input>
			</q-item-section>
		</q-item>
		<q-item>
			<q-item-section class="q-pt-xl">
				<div class="row q-gutter-sm justify-end">
					<q-btn class="text-lg" flat color="negative" icon="close" :label="locales['close']" @click="close()" />
					<q-btn class="text-lg" flat color="positive" icon="check" :label="locales['submit']" @click="submit()" />
				</div>
			</q-item-section>
		</q-item>
	</div>
</template>

<script>
import { ref, computed, reactive } from 'vue';
import Nui from './utils/Nui.js';

export default {
	setup() {
		const visible = ref(false);
		function submit() {
			Nui.send('createLight', {
				radius: this.radius,
				distance: this.distance,
				brightness: this.brightness,
				color: this.color,
				mode: this.mode,
			});
			setVisible(false);
		}
		function close() {
			Nui.send('close');
			setVisible(false);
		}
		function setVisible(value) {
			visible.value = value;
		}
		function updateLight() {
			Nui.send('updateCurrentLight', {
				radius: this.radius,
				distance: this.distance,
				brightness: this.brightness,
				color: this.color,
				mode: this.mode,
			});
		}
		return {
			close,
			submit,
			visible,
			setVisible,
			updateLight,
			locales: reactive({
				radius: 'Radius',
				distance: 'Distance',
				brightness: 'Brightness',
				validation: 'With validation',
				submit: 'Submit',
				close: 'Close',
				dynamic: 'Dynamic',
				static: 'Static',
				blink: 'Blink',
			}),
			messageListener: null,
			visibleClass: computed(() => `${visible.value ? '' : 'hidden'}`),
			radius: ref(30),
			distance: ref(30),
			brightness: ref(100),
			color: ref('#60a5fa'),
			mode: ref('static'),
		};
	},

	mounted() {
		this.messageListener = window.addEventListener('message', (e) => {
			switch (e.data.action) {
				case 'show':
					this.setVisible(true);
					// this.updateLight();
					break;
				case 'hide':
					this.setVisible(false);
					break;
				case 'locale':
					Object.entries(e.data.locales).forEach(([key, value]) => {
						if (this.locales[key]) this.locales[key] = value;
					});
					break;

				default:
					break;
			}
		});
	},

	unmounted() {
		window.removeEventListener('message', this.messageListener);
	},
};
</script>
