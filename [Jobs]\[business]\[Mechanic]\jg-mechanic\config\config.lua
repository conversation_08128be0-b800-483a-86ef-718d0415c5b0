Config = {}

-- Integrations (recommended to leave as "auto")
Config.Framework = "ESX" -- or "QBCore", "Qbox", "ESX"
Config.Inventory = "ox_inventory" -- or "ox_inventory", "qb-inventory", "esx_inventory", "codem-inventory", "qs-inventory"
Config.Notifications = "ox_lib" -- or "default", "ox_lib", "lation_ui", "ps-ui", "okokNotify", "nox_notify"
Config.ProgressBar = "ox-circle" -- or "ox-circle", "ox-bar", "lation_ui", "qb"
Config.SkillCheck = "ox" -- or "ox", "qb", "lation_ui"
Config.DrawText = "ox_lib" -- or "jg-textui", "ox_lib", "okokTextUI", "ps-ui", "lation_ui", "qb"
Config.SocietyBanking = "fd_banking" -- or "okokBanking", "fd_banking", "Renewed-Banking", "tgg-banking", "qb-banking", "qb-management", "esx_addonaccount"
Config.Menus = "ox" -- or "ox", "lation_ui"

-- Localisation
Config.Locale = "en"
Config.NumberAndDateFormat = "en-US"
Config.Currency = "USD"

-- Set to false to use built-in job system
Config.UseFrameworkJobs = true

-- Mechanic Tablet
Config.UseTabletCommand = false -- set to false to disable command
Config.TabletConnectionMaxDistance = 4.0

-- Shops
Config.Target = "ox_target" -- (shops/stashes only) "qb-target" or "ox_target"
Config.UseSocietyFund = true -- set to false to use player balance
Config.PlayerBalance = "bank" -- or "bank" or "cash"

-- Skill Bars
Config.UseSkillbars = false -- set to false to use progress bars instead of skill bars for installations
Config.ProgressBarDuration = 10000 -- if not using skill bars, this is the progress bar duration in ms (10000 = 10 seconds)
Config.MaximumSkillCheckAttempts = 3 -- How many times the player can attempt a skill check before the skill check fails
Config.SkillCheckDifficulty = { "easy", "easy", "easy", "easy", "easy" } -- for ox only
Config.SkillCheckInputs = { "w", "a", "s", "d" } -- for ox only

-- Servicing
Config.EnableVehicleServicing = true
Config.ServiceRequiredThreshold = 20 -- [%] if any of the servicable parts hit this %, it will flag that the vehicle needs servicing 
Config.ServicingBlacklist = {
  "police", "police2" -- Vehicles that are excluded from servicing damage
}

-- Nitrous
Config.NitrousScreenEffects = false
Config.NitrousRearLightTrails = true -- Only really visible at night
Config.NitrousPowerIncreaseMult = 2.0
Config.NitrousDefaultKeyMapping = "RMENU"
Config.NitrousMaxBottlesPerVehicle = 3 -- The UI can't really handle more than 7, more than that would be unrealistic anyway
Config.NitrousBottleDuration = 10 -- [in seconds] How long a nitrous tank lasts
Config.NitrousBottleCooldown = 5 -- [in seconds] How long until player can start using the next bottle
Config.NitrousPurgeDrainRate = 0.1 -- purging drains bottle only 10% as fast as actually boosting - set to 1 to drain at the same rate 

-- Stancing
Config.StanceMinSuspensionHeight = -0.3
Config.StanceMaxSuspensionHeight = 0.3
Config.StanceMinCamber = 0.0
Config.StanceMaxCamber = 0.5
Config.StanceMinTrackWidth = 0.5
Config.StanceMaxTrackWidth = 1.25
Config.StanceNearbyVehiclesFreqMs = 500

-- Repairs
Config.AllowFixingAtOwnedMechanicsIfNoOneOnDuty = false
Config.DuctTapeMinimumEngineHealth = 100.0
Config.DuctTapeEngineHealthIncrease = 150.0

-- Tuning
Config.TuningGiveInstalledItemBackOnRemoval = false

-- Locations
Config.UseCarLiftPrompt = "[E] Use car lift"
Config.UseCarLiftKey = 38
Config.CustomiseVehiclePrompt = "[E] Customise vehicle"
Config.CustomiseVehicleKey = 38

-- Update vehicle props whenever they are changed [probably should not touch]
-- You can set to false to leave saving any usual props vehicle changes such as
-- GTA performance, cosmetic, colours, wheels, etc to the garage or other scripts
-- that persist the props data to the database. Additional data from this script,
-- such as engine swaps, servicing etc is not affected as it's saved differently
Config.UpdatePropsOnChange = true

-- Stops vehicles from immediately going to redline, for a slightly more realistic feel and
-- reduced liklihood of wheelspin. Can make vehicle launch (slightly) slower.
-- No effect on electric vehicles!
-- May not work immediately for all vehicles; see: https://docs.jgscripts.com/mechanic/manual-transmissions-and-smooth-first-gear#smooth-first-gear
Config.SmoothFirstGear = false

-- If using a manual gearbox, show a notification with key binds when high RPMs 
-- have been detected for too long
Config.ManualHighRPMNotifications = true

-- Misc
Config.UniqueBlips = true
Config.ModsPricesAsPercentageOfVehicleValue = false -- Enable pricing tuning items as % of vehicle value - it tries jg-dealerships, then QBShared, then the vehicles meta file automagically for pricing data
Config.AdminsHaveEmployeePermissions = false -- admins can use tablet & interact with mechanics like an owner
Config.MechanicEmployeesCanSelfServiceMods = false -- set to true to allow mechanic employees to bypass the "place order" system at their own mechanic
Config.FullRepairAdminCommand = "vfix"
Config.MechanicAdminCommand = "mechanicadmin"
Config.ChangePlateDuringPreview = "PREVIEW"
Config.RequireManagementForOrderDeletion = true 
Config.UseCustomNamesInTuningMenu = true
Config.DisableNoPaymentOptionForEmployees = true

-- Mechanic Locations
Config.MechanicLocations = {
  police = {
    type = "self-service",
    logo = "ls_customs.png", -- logos go in /logos
    locations = {
      {
        coords = vector3(449.33, -974.87, 25.71), 
        size = 4.0,
        showBlip = false,
      }
    },
    blip = {
      id = 446,
      color = 47,
      scale = 0.7
    },
    mods = {
      repair           = { enabled = true, price = 100, percentVehVal = 0.01 },
      performance      = { enabled = false, price = 100, percentVehVal = 0.01, priceMult = 0.1 },
      cosmetics        = { enabled = true, price = 100, percentVehVal = 0.01, priceMult = 0.1 },
      stance           = { enabled = false, price = 100, percentVehVal = 0.01 },
      respray          = { enabled = true, price = 100, percentVehVal = 0.01 },
      wheels           = { enabled = true, price = 100, percentVehVal = 0.01, priceMult = 0.1 },
      neonLights       = { enabled = false, price = 100, percentVehVal = 0.01 },
      headlights       = { enabled = false, price = 100, percentVehVal = 0.01 },
      tyreSmoke        = { enabled = false, price = 100, percentVehVal = 0.01 },
      bulletproofTyres = { enabled = false, price = 100, percentVehVal = 0.01 },
      extras           = { enabled = true, price = 100, percentVehVal = 0.01 }
    },
  },
  sahp = {
    type = "self-service",
    logo = "ls_customs.png", -- logos go in /logos
    locations = {
      {
        coords = vector3(2796.79, 4788.8, 46.97), 
        size = 4.0,
        showBlip = false,
      }
    },
    blip = {
      id = 446,
      color = 47,
      scale = 0.7
    },
    mods = {
      repair           = { enabled = true, price = 100, percentVehVal = 0.01 },
      performance      = { enabled = false, price = 100, percentVehVal = 0.01, priceMult = 0.1 },
      cosmetics        = { enabled = true, price = 100, percentVehVal = 0.01, priceMult = 0.1 },
      stance           = { enabled = false, price = 100, percentVehVal = 0.01 },
      respray          = { enabled = true, price = 100, percentVehVal = 0.01 },
      wheels           = { enabled = true, price = 100, percentVehVal = 0.01, priceMult = 0.1 },
      neonLights       = { enabled = false, price = 100, percentVehVal = 0.01 },
      headlights       = { enabled = false, price = 100, percentVehVal = 0.01 },
      tyreSmoke        = { enabled = false, price = 100, percentVehVal = 0.01 },
      bulletproofTyres = { enabled = false, price = 100, percentVehVal = 0.01 },
      extras           = { enabled = true, price = 100, percentVehVal = 0.01 }
    },
  },
  ambulance = {
    type = "self-service",
    logo = "ls_customs.png", -- logos go in /logos
    locations = {
      {
        coords = vector3(-322.84, -582.72, 27.4),
        size = 8.0,
        showBlip = false,
      }
    },
    blip = {
      id = 446,
      color = 47,
      scale = 0.7
    },
    mods = {
      repair           = { enabled = true, price = 100, percentVehVal = 0.01 },
      performance      = { enabled = false, price = 100, percentVehVal = 0.01, priceMult = 0.1 },
      cosmetics        = { enabled = true, price = 100, percentVehVal = 0.01, priceMult = 0.1 },
      stance           = { enabled = false, price = 100, percentVehVal = 0.01 },
      respray          = { enabled = true, price = 100, percentVehVal = 0.01 },
      wheels           = { enabled = true, price = 100, percentVehVal = 0.01, priceMult = 0.1 },
      neonLights       = { enabled = false, price = 100, percentVehVal = 0.01 },
      headlights       = { enabled = false, price = 100, percentVehVal = 0.01 },
      tyreSmoke        = { enabled = false, price = 100, percentVehVal = 0.01 },
      bulletproofTyres = { enabled = false, price = 100, percentVehVal = 0.01 },
      extras           = { enabled = true, price = 100, percentVehVal = 0.01 }
    },
  },
  mommymm = {
    type = "owned",
    job = "mommymm",
    jobManagementRanks = {5},
    logo = "mommymm.png",
    commission = 10, -- %, 10 = 10%
    locations = {
      {
        coords = vector3(-1588.63, -974.95, 13.02),
        size = 3.5,
        showBlip = true,
      },
      {
        coords = vector3(-1584.95, -978.18, 13.02),
        size = 3.5,
      },
      {
        coords = vector3(-1578.43, -983.53, 13.02),
        size = 3.5,
      },
      
    },
    blip = {
      id = 446,
      color = 53,
      scale = 0.7
    },
    mods = {
      repair           = { enabled = true, price = 500, percentVehVal = 0.01 }, 
      performance      = { enabled = true, price = 5000, percentVehVal = 0.01, priceMult = 0.2 },
      cosmetics        = { enabled = true, price = 500, percentVehVal = 0.01, priceMult = 0.5 },
      stance           = { enabled = false, price = 45000, percentVehVal = 0.01 },
      respray          = { enabled = true, price = 1000, percentVehVal = 0.01 },
      wheels           = { enabled = true, price = 1500, percentVehVal = 0.01 },
      neonLights       = { enabled = true, price = 500, percentVehVal = 0.01 },
      headlights       = { enabled = true, price = 500, percentVehVal = 0.01 },
      tyreSmoke        = { enabled = true, price = 500, percentVehVal = 0.01 },
      bulletproofTyres = { enabled = false, price = 500, percentVehVal = 0.01 },
      extras           = { enabled = true, price = 500, percentVehVal = 0.01 }
    },
    tuning = {
      engineSwaps      = { enabled = true, requiresItem = true },
      drivetrains   = { enabled = false, requiresItem = true },
      turbocharging    = { enabled = true, requiresItem = true },
      tyres            = { enabled = true, requiresItem = true },
      brakes           = { enabled = true, requiresItem = true },
      driftTuning   = { enabled = false, requiresItem = true },
      gearboxes        = { enabled = true, requiresItem = true },
      axles            = { enabled = false, requiresItem = true },
    },
    carLifts = { -- only usable by employees
    vector4(72.24, 6483.57, 31.24, 149.58)
    },
    shops = {
      {
        name = "Servicing Supplies",
        coords = vector3(-1603.98, -995.27, 13.02),
        size = 2.0,
        usePed = false,
        ranktoaccess = 3,
        pedModel = "s_m_m_lathandy_01",
        marker = { id = 21, size = { x = 0.3, y = 0.3, z = 0.3 }, color = { r = 255, g = 255, b = 255, a = 120 }, bobUpAndDown = 0, faceCamera = 0, rotate = 1, drawOnEnts = 0 },
        items = {
          { name = "engine_oil", label = "Engine Oil", price = 200 },
          { name = "tyre_replacement", label = "Tyre Replacement", price = 100 },
          { name = "clutch_replacement", label = "Clutch Replacement", price = 100 },
          { name = "air_filter", label = "Air Filter", price = 100 },
          { name = "spark_plug", label = "Spark Plug", price = 100 },
          { name = "suspension_parts", label = "Suspension Parts", price = 150 },
          { name = "brakepad_replacement", label = "Brakepad Replacement", price = 300 },
          { name = "ev_motor", label = "Electric Vehicle Motor", price = 10000 },
          { name = "ev_battery", label = "Electric Vehicle Battery", price = 5000 },
          { name = "ev_coolant", label = "Electric Vehicle Coolant", price = 1000 },
        },
      },
      {
        name = "Advanced Upgrades",
        coords = vector3(-1606.37, -995.58, 13.02),
        size = 2.0,
        usePed = false,
        ranktoaccess = 3,
        pedModel = "s_m_m_lathandy_01",
        marker = { id = 21, size = { x = 0.3, y = 0.3, z = 0.3 }, color = { r = 255, g = 255, b = 255, a = 120 }, bobUpAndDown = 0, faceCamera = 0, rotate = 1, drawOnEnts = 0 },
        items = {
          { name = "awd_drivetrain", label = "AWD Drivetrain", price = 5000 },
          { name = "rwd_drivetrain", label = "RWD Drivetrain", price = 5000 },
          { name = "fwd_drivetrain", label = "FWD Drivetrain", price = 5000 },
          { name = "slick_tyres", label = "Slick Tyres", price = 3000 },
          { name = "semi_slick_tyres", label = "Semi Slick Tyres", price = 3000 },
          { name = "offroad_tyres", label = "Offroad Tyres", price = 3000 },
          { name = "ceramic_brakes", label = "Ceramic Brakes", price = 6000 },
          { name = "turbocharger", label = "Turbo", price = 5000 },
          { name = "carbon_axles", label = "Carbon Steel Axles", price = 2500 },
          { name = "chrome_axles", label = "Chrome-Molybdenum Axles", price = 2500 },
          { name = "alloy_axles", label = "Alloy Steel Axles", price = 2500 },
          { name = "rubber_brakes", label = "Rubber Brakes", price = 2500 },
          { name = "nao_brakes", label = "Low-Metal NAO Brakes", price = 2500 },
--          { name = "i4_engine", label = "Upgraded Engine", price = 25000 },
--          { name = "bronze_engine", label = "Upgraded Engine V2", price = 30000 },
          { name = "v6_engine", label = "V6 Engine", price = 75000 },
          { name = "v8_engine", label = "V8 Engine", price = 375000 },
          { name = "v12_engine", label = "V10 Engine", price = 900000 },
        },
      },
      {
        name = "Parts Store",
        coords = vector3(-1609.95, -992.31, 13.02),
        size = 2.0,
        usePed = false,
        ranktoaccess = 3,
        pedModel = "s_m_m_lathandy_01",
        marker = { id = 21, size = { x = 0.3, y = 0.3, z = 0.3 }, color = { r = 255, g = 255, b = 255, a = 120 }, bobUpAndDown = 0, faceCamera = 0, rotate = 1, drawOnEnts = 0 },
        items = {
          { name = "mechanic_tablet", label = "Mechanic Tablet", price = 50 },
          { name = "performance_part", label = "Performance Parts", price = 2500 },
          { name = "duct_tape", label = "Duct Tape", price = 30 },
          { name = "repair_kit", label = "Repair Kit", price = 150 },
          { name = "cleaning_kit", label = "Cleaning Kit", price = 10 },
          { name = "extras_kit", label = "Extras Kit", price = 200 },
          { name = "tyre_smoke_kit", label = "Tyre Smoke Kit", price = 300 },
          { name = "vehicle_wheels", label = "Vehicle Wheels Set", price = 200 },
          { name = "respray_kit", label = "Respray Kit", price = 500 },
          { name = "cosmetic_part", label = "Cosmetic Parts", price = 200 },
          { name = "lighting_controller", label = "Lighting Controller", price = 300 },
        },
      },
    },
    stashes = {
      {
        name = "mommy Stash",
        coords = vector3(-1587.07, -990.99, 13.02),
        size = 2.0,
        usePed = false,
        ranktoaccess = 4,
        pedModel = "s_m_m_lathandy_01",
        marker = { id = 21, size = { x = 0.3, y = 0.3, z = 0.3 }, color = { r = 255, g = 255, b = 255, a = 120 }, bobUpAndDown = 0, faceCamera = 0, rotate = 1, drawOnEnts = 0 },
        slots = 50,
        weight = 2000000,
      },
    }
  },
  vanguardautos = {
    type = "owned",
    job = "vanguardautos",
    jobManagementRanks = {4},
    logo = "vanguardautos.png",
    commission = 10, -- %, 10 = 10%
    locations = {
      {
        coords = vector3(-513.01, 52.74, 44.46),
        size = 3.0,
        showBlip = true,
      },
      {
        coords = vector3(-519.08, 53.71, 44.92),
        size = 3.0,
      },
      {
        coords = vector3(-526.57, 54.31, 44.71),
        size = 3.0,
      },
      {
        coords = vector3(-534.63, 55.15, 44.71),
        size = 3.0,
      },
      
    },
    blip = {
      id = 446,
      color = 53,
      scale = 0.7
    },
    mods = {
      repair           = { enabled = true, price = 500, percentVehVal = 0.01 }, 
      performance      = { enabled = true, price = 5000, percentVehVal = 0.01, priceMult = 0.2 },
      cosmetics        = { enabled = true, price = 500, percentVehVal = 0.01, priceMult = 0.5 },
      stance           = { enabled = false, price = 45000, percentVehVal = 0.01 },
      respray          = { enabled = true, price = 1000, percentVehVal = 0.01 },
      wheels           = { enabled = true, price = 1500, percentVehVal = 0.01 },
      neonLights       = { enabled = true, price = 500, percentVehVal = 0.01 },
      headlights       = { enabled = true, price = 500, percentVehVal = 0.01 },
      tyreSmoke        = { enabled = true, price = 500, percentVehVal = 0.01 },
      bulletproofTyres = { enabled = false, price = 500, percentVehVal = 0.01 },
      extras           = { enabled = true, price = 500, percentVehVal = 0.01 }
    },
    tuning = {
      engineSwaps      = { enabled = true, requiresItem = true },
      drivetrains   = { enabled = false, requiresItem = true },
      turbocharging    = { enabled = true, requiresItem = true },
      tyres            = { enabled = true, requiresItem = true },
      brakes           = { enabled = true, requiresItem = true },
      driftTuning   = { enabled = false, requiresItem = true },
      gearboxes        = { enabled = true, requiresItem = true },
      axles            = { enabled = false, requiresItem = true },
    },
    carLifts = { -- only usable by employees
    vector4(-504.57, 29.58, 45.03, 285.09)
    },
    shops = {
      {
        name = "Servicing Supplies",
        coords = vector3(-511.48, 38.14, 44.78),
        size = 2.0,
        usePed = false,
        ranktoaccess = 4,
        pedModel = "s_m_m_lathandy_01",
        marker = { id = 21, size = { x = 0.3, y = 0.3, z = 0.3 }, color = { r = 255, g = 255, b = 255, a = 120 }, bobUpAndDown = 0, faceCamera = 0, rotate = 1, drawOnEnts = 0 },
        items = {
          { name = "engine_oil", label = "Engine Oil", price = 200 },
          { name = "tyre_replacement", label = "Tyre Replacement", price = 100 },
          { name = "clutch_replacement", label = "Clutch Replacement", price = 100 },
          { name = "air_filter", label = "Air Filter", price = 100 },
          { name = "spark_plug", label = "Spark Plug", price = 100 },
          { name = "suspension_parts", label = "Suspension Parts", price = 150 },
          { name = "brakepad_replacement", label = "Brakepad Replacement", price = 300 },
          { name = "ev_motor", label = "Electric Vehicle Motor", price = 10000 },
          { name = "ev_battery", label = "Electric Vehicle Battery", price = 5000 },
          { name = "ev_coolant", label = "Electric Vehicle Coolant", price = 1000 },
        },
      },
      {
        name = "Advanced Upgrades",
        coords = vector3(-511.26, 40.74, 44.78),
        size = 2.0,
        usePed = false,
        ranktoaccess = 4,
        pedModel = "s_m_m_lathandy_01",
        marker = { id = 21, size = { x = 0.3, y = 0.3, z = 0.3 }, color = { r = 255, g = 255, b = 255, a = 120 }, bobUpAndDown = 0, faceCamera = 0, rotate = 1, drawOnEnts = 0 },
        items = {
          { name = "awd_drivetrain", label = "AWD Drivetrain", price = 5000 },
          { name = "rwd_drivetrain", label = "RWD Drivetrain", price = 5000 },
          { name = "fwd_drivetrain", label = "FWD Drivetrain", price = 5000 },
          { name = "slick_tyres", label = "Slick Tyres", price = 3000 },
          { name = "semi_slick_tyres", label = "Semi Slick Tyres", price = 3000 },
          { name = "offroad_tyres", label = "Offroad Tyres", price = 3000 },
          { name = "ceramic_brakes", label = "Ceramic Brakes", price = 6000 },
          { name = "turbocharger", label = "Turbo", price = 5000 },
          { name = "carbon_axles", label = "Carbon Steel Axles", price = 2500 },
          { name = "chrome_axles", label = "Chrome-Molybdenum Axles", price = 2500 },
          { name = "alloy_axles", label = "Alloy Steel Axles", price = 2500 },
          { name = "rubber_brakes", label = "Rubber Brakes", price = 2500 },
          { name = "nao_brakes", label = "Low-Metal NAO Brakes", price = 2500 },
--          { name = "i4_engine", label = "Upgraded Engine", price = 25000 },
--          { name = "bronze_engine", label = "Upgraded Engine V2", price = 30000 },
          { name = "v6_engine", label = "V6 Engine", price = 75000 },
          { name = "v8_engine", label = "V8 Engine", price = 375000 },
          { name = "v12_engine", label = "V10 Engine", price = 900000 },
        },
      },
      {
        name = "Parts Store",
        coords = vector3(-538.10, 42.30, 44.58),
        size = 2.0,
        usePed = false,
        ranktoaccess = 4,
        pedModel = "s_m_m_lathandy_01",
        marker = { id = 21, size = { x = 0.3, y = 0.3, z = 0.3 }, color = { r = 255, g = 255, b = 255, a = 120 }, bobUpAndDown = 0, faceCamera = 0, rotate = 1, drawOnEnts = 0 },
        items = {
          { name = "mechanic_tablet", label = "Mechanic Tablet", price = 50 },
          { name = "performance_part", label = "Performance Parts", price = 2500 },
          { name = "duct_tape", label = "Duct Tape", price = 30 },
          { name = "repair_kit", label = "Repair Kit", price = 150 },
          { name = "cleaning_kit", label = "Cleaning Kit", price = 10 },
          { name = "extras_kit", label = "Extras Kit", price = 200 },
          { name = "tyre_smoke_kit", label = "Tyre Smoke Kit", price = 300 },
          { name = "vehicle_wheels", label = "Vehicle Wheels Set", price = 200 },
          { name = "respray_kit", label = "Respray Kit", price = 500 },
          { name = "cosmetic_part", label = "Cosmetic Parts", price = 200 },
          { name = "lighting_controller", label = "Lighting Controller", price = 300 },
        },
      },
    },
    stashes = {
      {
        name = "Vanguard Stash",
        coords = vector3(-524.98, 58.43, 44.58),
        size = 2.0,
        usePed = false,
        ranktoaccess = 4,
        pedModel = "s_m_m_lathandy_01",
        marker = { id = 21, size = { x = 0.3, y = 0.3, z = 0.3 }, color = { r = 255, g = 255, b = 255, a = 120 }, bobUpAndDown = 0, faceCamera = 0, rotate = 1, drawOnEnts = 0 },
        slots = 50,
        weight = 2000000,
      },
    }
  },

  harmony = { --[[ Harmony Custom ]]
    type = "owned",
    job = "harmony",
    jobManagementRanks = { 4 },
    logo = "harmony.png",
    commission = 10, -- %, 10 = 10%
    locations = {
      {
        coords = vector3(1167.93, 2639.68, 38.39),
        size = 3.5,
        showBlip = true,
      },
      {
        coords = vector3(1168.17, 2632.13, 38.39),
        size = 3.5,
      },
      {
        coords = vector3(1195.49, 2626.88, 38.37),
        size = 3.5,
      },
      {
        coords = vector3(1201.31, 2626.85, 38.37),
        size = 3.5,
      },
      {
        coords = vector3(1207.4, 2627.12, 38.37),
        size = 3.5,
      },

    },
    blip = {
      id = 446,
      color = 47,
      scale = 0.7
    },
    mods = {
      repair           = { enabled = true, price = 500, percentVehVal = 0.01 },
      performance      = { enabled = true, price = 5000, percentVehVal = 0.01, priceMult = 0.2 },
      cosmetics        = { enabled = true, price = 500, percentVehVal = 0.01 },
      stance           = { enabled = false, price = 45000, percentVehVal = 0.01 },
      respray          = { enabled = true, price = 1000, percentVehVal = 0.01 },
      wheels           = { enabled = true, price = 1500, percentVehVal = 0.01 },
      neonLights       = { enabled = true, price = 500, percentVehVal = 0.01 },
      headlights       = { enabled = true, price = 500, percentVehVal = 0.01 },
      tyreSmoke        = { enabled = true, price = 500, percentVehVal = 0.01 },
      bulletproofTyres = { enabled = false, price = 500, percentVehVal = 0.01 },
      extras           = { enabled = true, price = 500, percentVehVal = 0.01 }
    },
    tuning = {
      engineSwaps   = { enabled = true, requiresItem = true },
      drivetrains   = { enabled = false, requiresItem = true },
      turbocharging = { enabled = true, requiresItem = true },
      tyres         = { enabled = true, requiresItem = true },
      brakes        = { enabled = true, requiresItem = true },
      driftTuning   = { enabled = true, requiresItem = true },
      gearboxes     = { enabled = true, requiresItem = true },
      axles            = { enabled = true, requiresItem = true },
    },
    carLifts = { -- only usable by employees
      vector4(1213.42, 2627.2, 38.37, 181.32)
    },
    shops = {
      {
        name = "Servicing Supplies",
        coords = vector3(1184.5, 2625.98, 38.37),
        size = 2.0,
        usePed = false,
        pedModel = "s_m_m_lathandy_01",
        ranktoaccess = 3,
        marker = { id = 21, size = { x = 0.3, y = 0.3, z = 0.3 }, color = { r = 255, g = 255, b = 255, a = 120 }, bobUpAndDown = 0, faceCamera = 0, rotate = 1, drawOnEnts = 0 },
        items = {
          { name = "engine_oil", label = "Engine Oil", price = 200 },
          { name = "tyre_replacement", label = "Tyre Replacement", price = 100 },
          { name = "clutch_replacement", label = "Clutch Replacement", price = 100 },
          { name = "air_filter", label = "Air Filter", price = 100 },
          { name = "spark_plug", label = "Spark Plug", price = 100 },
          { name = "suspension_parts", label = "Suspension Parts", price = 150 },
          { name = "brakepad_replacement", label = "Brakepad Replacement", price = 100 },
          { name = "ev_motor", label = "Electric Vehicle Motor", price = 10000 },
          { name = "ev_battery", label = "Electric Vehicle Battery", price = 5000 },
          { name = "ev_coolant", label = "Electric Vehicle Coolant", price = 1000 },
          { name = "awd_drivetrain", label = "AWD Drivetrain", price = 5000 },
          { name = "rwd_drivetrain", label = "RWD Drivetrain", price = 5000 },
          { name = "fwd_drivetrain", label = "FWD Drivetrain", price = 5000 },
          { name = "slick_tyres", label = "Slick Tyres", price = 3000 },
          { name = "semi_slick_tyres", label = "Semi Slick Tyres", price = 3000 },
          { name = "offroad_tyres", label = "Offroad Tyres", price = 3000 },
          { name = "ceramic_brakes", label = "Ceramic Brakes", price = 6000 },
        },
      },
      {
        name = "Parts Store",
        coords = vector3(1204.18, 2622.88, 38.37),
        size = 2.0,
        usePed = false,
        pedModel = "s_m_m_lathandy_01",
        ranktoaccess = 3,
        marker = { id = 21, size = { x = 0.3, y = 0.3, z = 0.3 }, color = { r = 255, g = 255, b = 255, a = 120 }, bobUpAndDown = 0, faceCamera = 0, rotate = 1, drawOnEnts = 0 },
        items = {
          { name = "mechanic_tablet", label = "Mechanic Tablet", price = 50 },
          { name = "performance_part", label = "Performance Parts", price = 2500 },
          { name = "duct_tape", label = "Duct Tape", price = 30 },
          { name = "repair_kit", label = "Repair Kit", price = 150 },
          { name = "cleaning_kit", label = "Cleaning Kit", price = 10 },
          { name = "extras_kit", label = "Extras Kit", price = 200 },
          { name = "tyre_smoke_kit", label = "Tyre Smoke Kit", price = 300 },
          { name = "vehicle_wheels", label = "Vehicle Wheels Set", price = 200 },
          { name = "respray_kit", label = "Respray Kit", price = 500 },
          { name = "turbocharger", label = "Turbo", price = 5000 },
          { name = "cosmetic_part", label = "Cosmetic Parts", price = 200 },
          { name = "lighting_controller", label = "Lighting Controller", price = 300 },
          { name = "carbon_axles", label = "Carbon Steel Axles", price = 2500 },
          { name = "chrome_axles", label = "Chrome-Molybdenum Axles", price = 2500 },
          { name = "rubber_brakes", label = "Rubber Brakes", price = 2500 },
          { name = "nao_brakes", label = "Low-Metal NAO Brakes", price = 2500 },
--          { name = "i4_engine", label = "Upgraded Engine", price = 25000 },
--          { name = "bronze_engine", label = "Upgraded Engine V2", price = 30000 },
          { name = "v6_engine", label = "V6 Engine", price = 75000 },
          { name = "v8_engine", label = "V8 Engine", price = 375000 },
          { name = "v12_engine", label = "V10 Engine", price = 900000 },
        },
      },
    },
    stashes = {
      {
        name = "Harmony Autos Stash 1",
        coords = vector3(1195.39, 2622.73, 38.37),
        size = 2.0,
        usePed = false,
        ranktoaccess = 4,
        pedModel = "s_m_m_lathandy_01",
        marker = { id = 21, size = { x = 0.3, y = 0.3, z = 0.3 }, color = { r = 255, g = 255, b = 255, a = 120 }, bobUpAndDown = 0, faceCamera = 0, rotate = 1, drawOnEnts = 0 },
        slots = 50,
        weight = 2000000,
      },
      {
        name = "Harmony Autos Stash 2",
        coords = vector3(1162.82, 2621.96, 38.37),
        size = 2.0,
        usePed = false,
        ranktoaccess = 4,
        pedModel = "s_m_m_lathandy_01",
        marker = { id = 21, size = { x = 0.3, y = 0.3, z = 0.3 }, color = { r = 255, g = 255, b = 255, a = 120 }, bobUpAndDown = 0, faceCamera = 0, rotate = 1, drawOnEnts = 0 },
        slots = 50,
        weight = 2000000,
      }
    }
  },
  dragonautos = {
    type = "owned",
    job = "dragonautos",
    jobManagementRanks = {5},
    logo = "dragonautos.png",
    commission = 10, -- %, 10 = 10%
    locations = {
      {
        coords = vector3(536.19, -163.54, 54.47),
        size = 3.0,
        showBlip = true,
      },
      {
        coords = vector3(540.66, -163.19, 54.47),
        size = 3.0,
      },
      {
        coords = vector3(545.26, -163.23, 54.47),
        size = 3.0,
      },    
     
      
    },
    blip = {
      id = 446,
      color = 1,
      scale = 0.7
    },
    mods = {
      repair           = { enabled = true, price = 500, percentVehVal = 0.01 }, 
      performance      = { enabled = true, price = 5000, percentVehVal = 0.01, priceMult = 0.2 },
      cosmetics        = { enabled = true, price = 500, percentVehVal = 0.01 },
      stance           = { enabled = false, price = 45000, percentVehVal = 0.01 },
      respray          = { enabled = true, price = 1000, percentVehVal = 0.01 },
      wheels           = { enabled = true, price = 1500, percentVehVal = 0.01 },
      neonLights       = { enabled = true, price = 500, percentVehVal = 0.01 },
      headlights       = { enabled = true, price = 500, percentVehVal = 0.01 },
      tyreSmoke        = { enabled = true, price = 500, percentVehVal = 0.01 },
      bulletproofTyres = { enabled = false, price = 500, percentVehVal = 0.01 },
      extras           = { enabled = true, price = 500, percentVehVal = 0.01 }
    },
    tuning = {
      engineSwaps      = { enabled = true, requiresItem = true },
      drivetrains   = { enabled = false, requiresItem = true },
      turbocharging    = { enabled = true, requiresItem = true },
      tyres            = { enabled = true, requiresItem = true },
      brakes           = { enabled = true, requiresItem = true },
      driftTuning   = { enabled = false, requiresItem = true },
      gearboxes        = { enabled = true, requiresItem = true },
      axles            = { enabled = false, requiresItem = true },
      
    },
    carLifts = { -- only usable by employees
    vector4(549.19, -178.19, 54.48, 271.31)
    },
    shops = {
      {
        name = "Servicing Supplies",
        coords = vector3(535.71, -153.5, 54.47),
        size = 2.0,
        ranktoaccess = 4,
        usePed = false,
        pedModel = "s_m_m_lathandy_01",
        marker = { id = 21, size = { x = 0.3, y = 0.3, z = 0.3 }, color = { r = 255, g = 255, b = 255, a = 120 }, bobUpAndDown = 0, faceCamera = 0, rotate = 1, drawOnEnts = 0 },
        items = {
          { name = "engine_oil", label = "Engine Oil", price = 200 },
          { name = "tyre_replacement", label = "Tyre Replacement", price = 100 },
          { name = "clutch_replacement", label = "Clutch Replacement", price = 100 },
          { name = "air_filter", label = "Air Filter", price = 100 },
          { name = "spark_plug", label = "Spark Plug", price = 100 },
          { name = "suspension_parts", label = "Suspension Parts", price = 150 },
          { name = "brakepad_replacement", label = "Brakepad Replacement", price = 100 },
          { name = "ev_motor", label = "Electric Vehicle Motor", price = 10000 },
          { name = "ev_battery", label = "Electric Vehicle Battery", price = 5000 },
          { name = "ev_coolant", label = "Electric Vehicle Coolant", price = 1000 },
          { name = "awd_drivetrain", label = "AWD Drivetrain", price = 5000 },
          { name = "rwd_drivetrain", label = "RWD Drivetrain", price = 5000 },
          { name = "fwd_drivetrain", label = "FWD Drivetrain", price = 5000 },
          { name = "slick_tyres", label = "Slick Tyres", price = 3000 },
          { name = "semi_slick_tyres", label = "Ceramic Brakes", price = 3000 },
          { name = "offroad_tyres", label = "Offroad Tyres", price = 3000 },
          { name = "ceramic_brakes", label = "Ceramic Brakes", price = 6000 },
        },
      },
      {
        name = "Parts Store",
        coords = vector3(533.47, -153.5, 54.47),
        size = 2.0,
        usePed = false,
        ranktoaccess = 4,
        pedModel = "s_m_m_lathandy_01",
        marker = { id = 21, size = { x = 0.3, y = 0.3, z = 0.3 }, color = { r = 255, g = 255, b = 255, a = 120 }, bobUpAndDown = 0, faceCamera = 0, rotate = 1, drawOnEnts = 0 },
        items = {
          { name = "mechanic_tablet", label = "Mechanic Tablet", price = 50 },
          { name = "performance_part", label = "Performance Parts", price = 2500 },
          { name = "duct_tape", label = "Duct Tape", price = 30 },
          { name = "repair_kit", label = "Repair Kit", price = 150 },
          { name = "cleaning_kit", label = "Cleaning Kit", price = 10 },
          { name = "extras_kit", label = "Extras Kit", price = 200 },
          { name = "tyre_smoke_kit", label = "Tyre Smoke Kit", price = 300 },
          { name = "vehicle_wheels", label = "Vehicle Wheels Set", price = 200 },
          { name = "respray_kit", label = "Respray Kit", price = 500 },
          { name = "cosmetic_part", label = "Cosmetic Parts", price = 200 },
          { name = "lighting_controller", label = "Lighting Controller", price = 300 },
          { name = "turbocharger", label = "Turbo", price = 5000 },
          { name = "carbon_axles", label = "Carbon Steel Axles", price = 2500 },
          { name = "chrome_axles", label = "Chrome-Molybdenum Axles", price = 2500 },
          { name = "rubber_brakes", label = "Rubber Brakes", price = 2500 },
          { name = "nao_brakes", label = "Low-Metal NAO Brakes", price = 2500 },
--          { name = "i4_engine", label = "Upgraded Engine", price = 25000 },
--          { name = "bronze_engine", label = "Upgraded Engine V2", price = 30000 },
          { name = "v6_engine", label = "V6 Engine", price = 75000 },
          { name = "v8_engine", label = "V8 Engine", price = 375000 },
          { name = "v12_engine", label = "V10 Engine", price = 900000 },
        },
      },
    },
    stashes = {
      {
        name = "Driveline Bay 1",
        coords = vector3(532.59, -162.37, 54.47),
        size = 2.0,
        usePed = false,
        ranktoaccess = 2,
        pedModel = "s_m_m_lathandy_01",
        marker = { id = 21, size = { x = 0.3, y = 0.3, z = 0.3 }, color = { r = 255, g = 255, b = 255, a = 120 }, bobUpAndDown = 0, faceCamera = 0, rotate = 1, drawOnEnts = 0 },
        slots = 50,
        weight = 2000000,
      },
     
    }
  },
  driveline = {
    type = "owned",
    job = "drivelinecustoms",
    jobManagementRanks = {5},
    logo = "ls_customs.png",
    commission = 10, -- %, 10 = 10%
    locations = {
      {
        coords = vector3(-814.874, -421.393, 36.146),
        size = 3.5,
        showBlip = true,
      },
      {
        coords = vector3(-812.9, -427.15, 36.715),
        size = 3.5,
      },
      {
        coords = vector3(-808.3173, -435.7188, 35.7051),
        size = 3.5,
      },    
      {
        coords = vector3(-802.5855, -439.7685, 35.6574),
        size = 3.5,
      }, 
      
    },
    blip = {
      id = 446,
      color = 47,
      scale = 0.7
    },
    mods = {
      repair           = { enabled = true, price = 500, percentVehVal = 0.01 }, 
      performance      = { enabled = true, price = 5000, percentVehVal = 0.01, priceMult = 0.2 },
      cosmetics        = { enabled = true, price = 500, percentVehVal = 0.01 },
      stance           = { enabled = false, price = 45000, percentVehVal = 0.01 },
      respray          = { enabled = true, price = 1000, percentVehVal = 0.01 },
      wheels           = { enabled = true, price = 1500, percentVehVal = 0.01 },
      neonLights       = { enabled = true, price = 500, percentVehVal = 0.01 },
      headlights       = { enabled = true, price = 500, percentVehVal = 0.01 },
      tyreSmoke        = { enabled = true, price = 500, percentVehVal = 0.01 },
      bulletproofTyres = { enabled = false, price = 500, percentVehVal = 0.01 },
      extras           = { enabled = true, price = 500, percentVehVal = 0.01 }
    },
    tuning = {
      engineSwaps      = { enabled = true, requiresItem = true },
      drivetrains   = { enabled = false, requiresItem = true },
      turbocharging    = { enabled = true, requiresItem = true },
      tyres            = { enabled = true, requiresItem = true },
      brakes           = { enabled = true, requiresItem = true },
      driftTuning   = { enabled = false, requiresItem = true },
      gearboxes        = { enabled = true, requiresItem = true },
      axles            = { enabled = true, requiresItem = true },
      
    },
    carLifts = { -- only usable by employees
    vector4(-826.503, -450.897, 36.641, 189.765)
    },
    shops = {
      {
        name = "Servicing Supplies",
        coords = vector3(-811.201, -448.764, 36.637),
        size = 2.0,
        ranktoaccess = 4,
        usePed = false,
        pedModel = "s_m_m_lathandy_01",
        marker = { id = 21, size = { x = 0.3, y = 0.3, z = 0.3 }, color = { r = 255, g = 255, b = 255, a = 120 }, bobUpAndDown = 0, faceCamera = 0, rotate = 1, drawOnEnts = 0 },
        items = {
          { name = "engine_oil", label = "Engine Oil", price = 200 },
          { name = "tyre_replacement", label = "Tyre Replacement", price = 100 },
          { name = "clutch_replacement", label = "Clutch Replacement", price = 100 },
          { name = "air_filter", label = "Air Filter", price = 100 },
          { name = "spark_plug", label = "Spark Plug", price = 100 },
          { name = "suspension_parts", label = "Suspension Parts", price = 150 },
          { name = "brakepad_replacement", label = "Brakepad Replacement", price = 100 },
          { name = "ev_motor", label = "Electric Vehicle Motor", price = 10000 },
          { name = "ev_battery", label = "Electric Vehicle Battery", price = 5000 },
          { name = "ev_coolant", label = "Electric Vehicle Coolant", price = 1000 },
          { name = "awd_drivetrain", label = "AWD Drivetrain", price = 5000 },
          { name = "rwd_drivetrain", label = "RWD Drivetrain", price = 5000 },
          { name = "fwd_drivetrain", label = "FWD Drivetrain", price = 5000 },
          { name = "slick_tyres", label = "Slick Tyres", price = 3000 },
          { name = "semi_slick_tyres", label = "Ceramic Brakes", price = 3000 },
          { name = "offroad_tyres", label = "Offroad Tyres", price = 3000 },
          { name = "ceramic_brakes", label = "Ceramic Brakes", price = 6000 },
        },
      },
      {
        name = "Parts Store",
        coords = vector3(-806.832, -446.789, 36.637),
        size = 2.0,
        usePed = false,
        ranktoaccess = 4,
        pedModel = "s_m_m_lathandy_01",
        marker = { id = 21, size = { x = 0.3, y = 0.3, z = 0.3 }, color = { r = 255, g = 255, b = 255, a = 120 }, bobUpAndDown = 0, faceCamera = 0, rotate = 1, drawOnEnts = 0 },
        items = {
          { name = "mechanic_tablet", label = "Mechanic Tablet", price = 50 },
          { name = "performance_part", label = "Performance Parts", price = 2500 },
          { name = "duct_tape", label = "Duct Tape", price = 30 },
          { name = "repair_kit", label = "Repair Kit", price = 150 },
          { name = "cleaning_kit", label = "Cleaning Kit", price = 10 },
          { name = "extras_kit", label = "Extras Kit", price = 200 },
          { name = "tyre_smoke_kit", label = "Tyre Smoke Kit", price = 300 },
          { name = "vehicle_wheels", label = "Vehicle Wheels Set", price = 200 },
          { name = "respray_kit", label = "Respray Kit", price = 500 },
          { name = "cosmetic_part", label = "Cosmetic Parts", price = 200 },
          { name = "lighting_controller", label = "Lighting Controller", price = 300 },
          { name = "turbocharger", label = "Turbo", price = 5000 },
          { name = "carbon_axles", label = "Carbon Steel Axles", price = 2500 },
          { name = "chrome_axles", label = "Chrome-Molybdenum Axles", price = 2500 },
          { name = "rubber_brakes", label = "Rubber Brakes", price = 2500 },
          { name = "nao_brakes", label = "Low-Metal NAO Brakes", price = 2500 },
--          { name = "i4_engine", label = "Upgraded Engine", price = 25000 },
--          { name = "bronze_engine", label = "Upgraded Engine V2", price = 30000 },
          { name = "v6_engine", label = "V6 Engine", price = 75000 },
          { name = "v8_engine", label = "V8 Engine", price = 375000 },
          { name = "v12_engine", label = "V10 Engine", price = 900000 },
        },
      },
    },
    stashes = {
      {
        name = "Driveline Bay 1",
        coords = vector3(-803.807, -431.784, 36.637),
        size = 2.0,
        usePed = false,
        ranktoaccess = 4,
        pedModel = "s_m_m_lathandy_01",
        marker = { id = 21, size = { x = 0.3, y = 0.3, z = 0.3 }, color = { r = 255, g = 255, b = 255, a = 120 }, bobUpAndDown = 0, faceCamera = 0, rotate = 1, drawOnEnts = 0 },
        slots = 50,
        weight = 2000000,
      },
      {
        name = "Driveline Bay 2",
        coords = vector3(-806.94, -425.806, 36.637),
        size = 2.0,
        usePed = false,
        ranktoaccess = 4,
        pedModel = "s_m_m_lathandy_01",
        marker = { id = 21, size = { x = 0.3, y = 0.3, z = 0.3 }, color = { r = 255, g = 255, b = 255, a = 120 }, bobUpAndDown = 0, faceCamera = 0, rotate = 1, drawOnEnts = 0 },
        slots = 50,
        weight = 2000000,
      },
      {
        name = "Driveline Bay 3",
        coords = vector3(-811.589, -417.399, 36.637),
        size = 2.0,
        usePed = false,
        ranktoaccess = 4,
        pedModel = "s_m_m_lathandy_01",
        marker = { id = 21, size = { x = 0.3, y = 0.3, z = 0.3 }, color = { r = 255, g = 255, b = 255, a = 120 }, bobUpAndDown = 0, faceCamera = 0, rotate = 1, drawOnEnts = 0 },
        slots = 50,
        weight = 2000000,
      },
    }
  },
  speedcustoms = {
    type = "owned",
    job = "speedcustoms",
    jobManagementRanks = {5},
    logo = "speedcustoms.png",
    commission = 10, -- %, 10 = 10%
    locations = {
      {
        coords = vector3(-114.48, -1777.54, 29.86),
        size = 3.5,
        showBlip = true,
      },
      {
        coords = vector3(-108.26, -1782.17, 29.86),
        size = 3.5,
      },
      {
        coords = vector3(-101.32, -1787.03, 29.86),
        size = 3.5,
      },
      {
        coords = vector3(-95.08, -1791.72, 29.86),
        size = 3.5,
      },
      {
        coords = vector3(-88.72, -1796.56, 29.86),
        size = 3.5,
      },
      {
        coords = vector3(-83.19, -1801.65, 29.86),
        size = 3.5,
      },
      {
        coords = vector3(-96.77, -1818.43, 29.86),
        size = 3.5,
      },
      {
        coords = vector3(-102.38, -1813.45, 29.86),
        size = 3.5,
      },
      {
        coords = vector3(-109.84, -1808.16, 29.86),
        size = 3.5,
      },
      {
        coords = vector3(-114.85, -1802.8, 29.86),
        size = 3.5,
      },
      {
        coords = vector3(-122.21, -1798.94, 29.86),
        size = 3.5,
      },
      {
        coords = vector3(-128.45, -1794.09, 29.86),
        size = 3.5,
      },
      
    },
    blip = {
      id = 446,
      color = 53,
      scale = 0.7
    },
    mods = {
       repair           = { enabled = true, price = 500, percentVehVal = 0.01 }, 
      performance      = { enabled = true, price = 5000, percentVehVal = 0.01, priceMult = 0.2 },
      cosmetics        = { enabled = true, price = 500, percentVehVal = 0.01, priceMult = 0.5 },
      stance           = { enabled = false, price = 45000, percentVehVal = 0.01 },
      respray          = { enabled = true, price = 1000, percentVehVal = 0.01 },
      wheels           = { enabled = true, price = 1500, percentVehVal = 0.01 },
      neonLights       = { enabled = true, price = 500, percentVehVal = 0.01 },
      headlights       = { enabled = true, price = 500, percentVehVal = 0.01 },
      tyreSmoke        = { enabled = true, price = 500, percentVehVal = 0.01 },
      bulletproofTyres = { enabled = false, price = 500, percentVehVal = 0.01 },
      extras           = { enabled = true, price = 500, percentVehVal = 0.01 }
    },
    tuning = {
      engineSwaps      = { enabled = true, requiresItem = true },
      drivetrains   = { enabled = false, requiresItem = true },
      turbocharging    = { enabled = true, requiresItem = true },
      tyres            = { enabled = true, requiresItem = true },
      brakes           = { enabled = true, requiresItem = true },
      driftTuning   = { enabled = false, requiresItem = true },
      gearboxes        = { enabled = true, requiresItem = true },
      axles            = { enabled = true, requiresItem = true },
    },
    carLifts = { -- only usable by employees
    vector4(-74.01, -1832.44, 26.94, 50.65)
    },
    shops = {
      {
        name = "Servicing Supplies",
        coords = vector3(-110.21, -1780.71, 29.86),
        size = 2.0,
        usePed = false,
        ranktoaccess = 4,
        pedModel = "s_m_m_lathandy_01",
        marker = { id = 21, size = { x = 0.3, y = 0.3, z = 0.3 }, color = { r = 255, g = 255, b = 255, a = 120 }, bobUpAndDown = 0, faceCamera = 0, rotate = 1, drawOnEnts = 0 },
        items = {
           { name = "engine_oil", label = "Engine Oil", price = 200 },
          { name = "tyre_replacement", label = "Tyre Replacement", price = 100 },
          { name = "clutch_replacement", label = "Clutch Replacement", price = 100 },
          { name = "air_filter", label = "Air Filter", price = 100 },
          { name = "spark_plug", label = "Spark Plug", price = 100 },
          { name = "suspension_parts", label = "Suspension Parts", price = 150 },
          { name = "brakepad_replacement", label = "Brakepad Replacement", price = 300 },
          { name = "ev_motor", label = "Electric Vehicle Motor", price = 10000 },
          { name = "ev_battery", label = "Electric Vehicle Battery", price = 5000 },
          { name = "ev_coolant", label = "Electric Vehicle Coolant", price = 1000 },
        },
      },
      {
        name = "Advanced Upgrades",
        coords = vector3(-97.98, -1790.72, 29.86),
        size = 2.0,
        usePed = false,
        ranktoaccess = 4,
        pedModel = "s_m_m_lathandy_01",
        marker = { id = 21, size = { x = 0.3, y = 0.3, z = 0.3 }, color = { r = 255, g = 255, b = 255, a = 120 }, bobUpAndDown = 0, faceCamera = 0, rotate = 1, drawOnEnts = 0 },
        items = {
          { name = "awd_drivetrain", label = "AWD Drivetrain", price = 5000 },
          { name = "rwd_drivetrain", label = "RWD Drivetrain", price = 5000 },
          { name = "fwd_drivetrain", label = "FWD Drivetrain", price = 5000 },
          { name = "slick_tyres", label = "Slick Tyres", price = 3000 },
          { name = "semi_slick_tyres", label = "Semi Slick Tyres", price = 3000 },
          { name = "offroad_tyres", label = "Offroad Tyres", price = 3000 },
          { name = "ceramic_brakes", label = "Ceramic Brakes", price = 6000 },
          { name = "turbocharger", label = "Turbo", price = 5000 },
          { name = "carbon_axles", label = "Carbon Steel Axles", price = 2500 },
          { name = "chrome_axles", label = "Chrome-Molybdenum Axles", price = 2500 },
          { name = "alloy_axles", label = "Alloy Steel Axles", price = 2500 },
          { name = "rubber_brakes", label = "Rubber Brakes", price = 2500 },
          { name = "nao_brakes", label = "Low-Metal NAO Brakes", price = 2500 },
--          { name = "i4_engine", label = "Upgraded Engine", price = 25000 },
--          { name = "bronze_engine", label = "Upgraded Engine V2", price = 30000 },
          { name = "v6_engine", label = "V6 Engine", price = 75000 },
          { name = "v8_engine", label = "V8 Engine", price = 375000 },
          { name = "v12_engine", label = "V10 Engine", price = 900000 },
        },
      },
      {
        name = "Parts Store",
        coords = vector3(-85.33, -1800.52, 29.86),
        size = 2.0,
        usePed = false,
        ranktoaccess = 4,
        pedModel = "s_m_m_lathandy_01",
        marker = { id = 21, size = { x = 0.3, y = 0.3, z = 0.3 }, color = { r = 255, g = 255, b = 255, a = 120 }, bobUpAndDown = 0, faceCamera = 0, rotate = 1, drawOnEnts = 0 },
        items = {
         { name = "mechanic_tablet", label = "Mechanic Tablet", price = 50 },
          { name = "performance_part", label = "Performance Parts", price = 2500 },
          { name = "duct_tape", label = "Duct Tape", price = 30 },
          { name = "repair_kit", label = "Repair Kit", price = 150 },
          { name = "cleaning_kit", label = "Cleaning Kit", price = 10 },
          { name = "extras_kit", label = "Extras Kit", price = 200 },
          { name = "tyre_smoke_kit", label = "Tyre Smoke Kit", price = 300 },
          { name = "vehicle_wheels", label = "Vehicle Wheels Set", price = 200 },
          { name = "respray_kit", label = "Respray Kit", price = 500 },
          { name = "cosmetic_part", label = "Cosmetic Parts", price = 200 },
          { name = "lighting_controller", label = "Lighting Controller", price = 300 },
        },
      },
    },
    stashes = {
      {
        name = "Stash",
        coords = vector3(-111.25, -1806.12, 29.86),
        size = 2.0,
        usePed = false,
        ranktoaccess = 4,
        pedModel = "s_m_m_lathandy_01",
        marker = { id = 21, size = { x = 0.3, y = 0.3, z = 0.3 }, color = { r = 255, g = 255, b = 255, a = 120 }, bobUpAndDown = 0, faceCamera = 0, rotate = 1, drawOnEnts = 0 },
        slots = 50,
        weight = 2000000,
      },
    }
  },
  adminmechanic = {
    type = "owned",
    job = "adminmechanic",
    jobManagementRanks = {5},
    logo = "ls_customs.png",
    commission = 0, -- %, 10 = 10%
    locations = {
      {
        coords = vector3(733.98, -1085.01, 22.17),
        size = 10.0,
        showBlip = true,
      },
    },
    blip = {
      id = 446,
      color = 47,
      scale = 0.7
    },
    mods = {
      repair           = { enabled = false, price = 500, percentVehVal = 0.01 }, 
      performance      = { enabled = false, price = 5000, percentVehVal = 0.01, priceMult = 0.2 },
      cosmetics        = { enabled = false, price = 500, percentVehVal = 0.01, priceMult = 0.5 },
      stance           = { enabled = false, price = 45000, percentVehVal = 0.01 },
      respray          = { enabled = false, price = 1000, percentVehVal = 0.01 },
      wheels           = { enabled = false, price = 1500, percentVehVal = 0.01 },
      neonLights       = { enabled = false, price = 500, percentVehVal = 0.01 },
      headlights       = { enabled = false, price = 500, percentVehVal = 0.01 },
      tyreSmoke        = { enabled = false, price = 500, percentVehVal = 0.01 },
      bulletproofTyres = { enabled = false, price = 500, percentVehVal = 0.01 },
      extras           = { enabled = false, price = 500, percentVehVal = 0.01 }
    },
    tuning = {
      adminengineSwaps = { enabled = true, requiresItem = true },
      engineSwaps   = { enabled = true, requiresItem = true },
      drivetrains   = { enabled = false, requiresItem = true },
      turbocharging = { enabled = true, requiresItem = true },
      tyres         = { enabled = true, requiresItem = true },
      brakes        = { enabled = true, requiresItem = true },
      driftTuning   = { enabled = true, requiresItem = true },
      gearboxes     = { enabled = true, requiresItem = true },
      tuningchip    = { enabled = true, requiresItem = true },
      axles         = { enabled = true, requiresItem = true }
    },
    carLifts = { -- only usable by employees
    vector4(733.62, -1089.01, 22.17, 87.24)
    },
    shops = {
      {
        name = "Engines",
        coords = vector3(725.19, -1074.25, 28.31),
        size = 2.0,
        usePed = false,
        ranktoaccess = 1,
        pedModel = "s_m_m_lathandy_01",
        marker = { id = 21, size = { x = 0.3, y = 0.3, z = 0.3 }, color = { r = 255, g = 255, b = 255, a = 120 }, bobUpAndDown = 0, faceCamera = 0, rotate = 1, drawOnEnts = 0 },
        items = {
          { name = "ttunechip", label = "Tuning Chip", price = 0 },
          { name = "mechanic_tablet", label = "Mechanic Tablet", price = 0 },
        },
      },
    },
    stashes = {
      {
        name = "Stash",
        coords = vector3(725.53, -1066.71, 28.31),
        size = 2.0,
        usePed = false,
        ranktoaccess = 4,
        pedModel = "s_m_m_lathandy_01",
        marker = { id = 21, size = { x = 0.3, y = 0.3, z = 0.3 }, color = { r = 255, g = 255, b = 255, a = 120 }, bobUpAndDown = 0, faceCamera = 0, rotate = 1, drawOnEnts = 0 },
        slots = 50,
        weight = 2000000,
      },
    }
  }
}

-- Add electric vehicles to disable combustion engine features
-----------------------------------------------------------------------
-- PLEASE NOTE: In b3258 (Bottom Dollar Bounties) and newer, electric
-- vehicles are detected automatically, so this list is not used! 
Config.ElectricVehicles = {
  "Airtug",     "buffalo5",   "caddy",
  "Caddy2",     "caddy3",     "coureur",
  "cyclone",    "cyclone2",   "imorgon",
  "inductor",   "iwagen",     "khamelion",
  "metrotrain", "minitank",   "neon",
  "omnisegt",   "powersurge", "raiden",
  "rcbandito",  "surge",      "tezeract",
  "virtue",     "vivanite",   "voltic",
  "voltic2",
}

-- Nerd options
Config.DisableSound = false
Config.AutoRunSQL = true
Config.Debug = false