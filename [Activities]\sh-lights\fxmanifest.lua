shared_scripts { '@FiniAC/fini_events.js', '@FiniAC/fini_events.lua' }

fx_version 'cerulean'
game 'gta5'

author 'sh-scripts <sh-store.tebex.io>'
version '1.1.2'

ui_page 'nui/index.html'

files {
    'nui/index.html',
    'nui/css/*.css',
    'nui/js/*.js',
    'nui/fonts/*.woff',
    'nui/fonts/*.woff2',
}

shared_scripts {
    'cfg.lua',
    'utils.lua',
}

client_script 'client.lua'
server_scripts {
    'server.lua',
    'lights.json'
}

lua54 'yes'
escrow_ignore {
    'cfg.lua',
    'utils.lua',
}
dependency '/assetpacks'