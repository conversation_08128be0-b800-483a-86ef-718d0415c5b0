if Config.Framework == "qb" then
    local QBCore = exports[Config.FrameworkResourceName or "qb-core"]:GetCoreObject()
    QBCore.Functions.CreateUseableItem(Config.ItemName, function(source, item)
        local Player = QBCore.Functions.GetPlayer(source)
        TriggerClientEvent('bodhix-skating:client:start', source, item)
    end)
    RegisterServerEvent("bodhix-skating:server:skate", function(source)
        TriggerClientEvent("bodhix-skating:client:skate", -1, source)
    end)
elseif Config.Framework == "esx" then
    ESX = exports[Config.FrameworkResourceName or "es_extended"]:getSharedObject()
    ESX.RegisterUsableItem(Config.ItemName, function(source, item)
        local Player = ESX.GetPlayerFromId(source)
        TriggerClientEvent('bodhix-skating:client:start', source, item)
    end)
    RegisterServerEvent("bodhix-skating:server:skate", function(source)
        TriggerClientEvent("bodhix-skating:client:skate", -1, source)
    end)
elseif Config.Framework == "vrp" then
    local Proxy = module("vrp", "lib/Proxy")
    local Tunnel = module("vrp", "lib/Tunnel")
    local vRP = Proxy.getInterface("vRP")
    local vRPclient = Tunnel.getInterface("vRP", "your_resource_name") -- replace with your resource name
    -- Register Usable Item
    vRP.defInventoryItem(Config.ItemName, {
        name = Config.ItemName,
        description = "Skateboard",
        choices = {
            ["Use"] = function(player, choice)
                local user_id = vRP.getUserId({ player })
                if user_id then
                    TriggerClientEvent('bodhix-skating:client:start', player, { name = Config.ItemName })
                end
            end
        },
        weight = 1.0
    })
    RegisterServerEvent("bodhix-skating:server:skate")
    AddEventHandler("bodhix-skating:server:skate", function(source)
        TriggerClientEvent("bodhix-skating:client:skate", -1, source)
    end)
elseif Config.Framework == "custom" then
    RegisterServerEvent("bodhix-skating:server:skate", function(source)
        TriggerClientEvent("bodhix-skating:client:skate", -1, source)
    end)
end

local spawnedShops, netIdTableS = {}, {}

AddEventHandler('onResourceStart', function(resourceName)
    if (GetCurrentResourceName() ~= resourceName) then return end
    if Config.EnablePeds then Shop.CreateShopPeds() end
end)

AddEventHandler('onResourceStop', function(resourceName)
    if (GetCurrentResourceName() ~= resourceName) then return end
    Shop.DeleteEntities()
end)
RegisterServerEvent("BDX-Skate:SyncPeds")
AddEventHandler("BDX-Skate:SyncPeds", function()
    TriggerClientEvent('SkateShop:pedHandlers', source, netIdTableS)
end)
Shop = {
    CreateShopPeds = function()
        for i = 1, #Config.Shops.ShopPeds do
            local model = Config.Shops.ShopPeds[i].Model
            local coords = Config.Shops.ShopPeds[i].Position

            spawnedShops[i] = CreatePed(4, model, coords.x, coords.y, coords.z, coords.w, true, true)

            while not DoesEntityExist(spawnedShops[i]) do Wait(5) end

            FreezeEntityPosition(spawnedShops[i], true)
            netIdTableS[i] = NetworkGetNetworkIdFromEntity(spawnedShops[i])

            if Config.Debug then
                print("^2[INFO] Spawned NPC " .. i .. " with NetID: " .. tostring(netIdTableS[i]) .. "^0")
            end
        end
    end,
    DeleteEntities = function()
        for i = 1, #spawnedShops do
            if DoesEntityExist(spawnedShops[i]) then
                DeleteEntity(spawnedShops[i])
            end
            spawnedShops[i] = nil
        end
    end
}

RegisterNetEvent("bodhix:purchaseItem")
AddEventHandler("bodhix:purchaseItem", function(itemType, itemId, SkateStyle)
    local src = source
    local price = nil
    local xPlayer = nil
    local hasMoney = false

    if itemType == "deck" then
        price = Config.DeckPrice
    elseif itemType == "trucks" then
        price = Config.TrucksPrice
    elseif itemType == "wheels" then
        price = Config.WheelsPrice
    else
        if Config.Debug then
            print("^1[ERROR] Invalid purchase type!^0")
        end

        cb("error")
        return
    end

    if Config.Debug then
        print("Server: Purchasing " .. itemType .. " Model: " .. itemId)
    end
    -- Framework detection based on config
    if Config.Framework == "qb" then
        local QBCore = exports[Config.FrameworkResourceName or "qb-core"]:GetCoreObject()
        xPlayer = QBCore.Functions.GetPlayer(source)
        if xPlayer then
            hasMoney = xPlayer.Functions.RemoveMoney("cash", price)
        end
        if Config.Debug then
            print('QB: Player has been Charged for ' .. price)
        end
    elseif Config.Framework == "esx" then
        local ESX = exports[Config.FrameworkResourceName or "es_extended"]:getSharedObject()
        xPlayer = ESX.GetPlayerFromId(source)
        if xPlayer and xPlayer.getMoney() >= price then
            xPlayer.removeMoney(price)
            hasMoney = true
        end
        if Config.Debug then
            print('ESX: Player has been Charged for ' .. price)
        end
    elseif Config.Framework == "vrp" then
        local user_id = vRP.getUserId({ src })
        if user_id then
            hasMoney = vRP.tryPayment({ user_id, price }) -- Tries to remove cash
            if Config.Debug then
                if hasMoney then
                    print("vRP: Charged user_id " .. user_id .. " $" .. price)
                else
                    print("vRP: Player " .. user_id .. " does not have enough money.")
                end
            end
        end
    elseif Config.Framework == "custom" and Config.CustomFrameworkFunctions then
        -- Calls the custom money check & deduction functions set in config
        xPlayer = Config.CustomFrameworkFunctions.GetPlayer(source)
        if xPlayer then
            hasMoney = Config.CustomFrameworkFunctions.RemoveMoney(source, price)
        end
        if Config.Debug then
            print('Custom: Player has been Charged for ' .. price)
        end
    end

    -- If player was not found or framework isn't set properly
    if not xPlayer then
        if Config.Debug then
            print("^1[ERROR] Player not found! Check Config.Framework settings.^0")
        end
        return
    end

    -- If player has enough money, process the purchase
    if hasMoney then
        local baseName = nil
        if itemType == "deck" then
            local preName = (SkateStyle == "classic") and "deck_" or "board_"
            baseName = preName .. tostring(itemId)
            local prefix = (SkateStyle == "classic") and "c_" or ""
            local modelName = prefix .. baseName

            if Config.Debug then
                print('Deck model: ' .. modelName)
            end
            TriggerClientEvent('bodhix-skate:purchase', src, itemType, modelName)
        elseif itemType == "trucks" then
            baseName = "trucks_" .. tostring(itemId)
            local prefix = (SkateStyle == "classic") and "c_" or ""
            local modelName = prefix .. baseName

            if Config.Debug then
                print('Trucks model: ' .. modelName)
            end
            TriggerClientEvent('bodhix-skate:purchase', src, itemType, modelName)
        elseif itemType == "wheels" then
            baseName = "wheels_" .. tostring(itemId)
            local prefix = (SkateStyle == "classic") and "c_" or ""
            local modelName = prefix .. baseName
            
            if Config.Debug then
                print('Wheels model: ' .. modelName)
            end
            TriggerClientEvent('bodhix-skate:purchase', src, itemType, modelName)
        else
            print("^1[ERROR] Invalid purchase type!^0")
            cb("error")
            return
        end
        if Config.Debug then
            print("^2[INFO] Player " .. src .. " purchased " .. modelName .. " for $" .. price .. "^0")
        end
    else
        TriggerClientEvent('SStore:PurchaseFailed', src)
        if Config.Debug then
            print("^1[ERROR] Player " .. src .. " does not have enough money!^0")
        end
    end
end)
