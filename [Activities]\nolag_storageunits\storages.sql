CREATE TABLE
  IF NOT EXISTS `storage_locations` (
    `storage` int (11) NOT NULL AUTO_INCREMENT,
    `label` varchar(50) DEFAULT NULL,
    `price` int (11) DEFAULT NULL,
    `forever` tinyint (4) DEFAULT NULL,
    `blip` tinyint (4) DEFAULT NULL,
    `rental_days` int (11) DEFAULT NULL,
    `inventory` varchar(255) DEFAULT NULL,
    `coords` varchar(255) DEFAULT NULL,
    PRIMARY KEY (`storage`)
  ) ENGINE = InnoDB AUTO_INCREMENT = 1 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci;

CREATE TABLE
  IF NOT EXISTS `storages` (
    `location` int (11) DEFAULT NULL,
    `owner` varchar(50) DEFAULT NULL,
    `label` varchar(50) DEFAULT NULL,
    `password` varchar(50) DEFAULT NULL,
    `rented_date` datetime DEFAULT NULL,
    `expiring_date` datetime DEFAULT NULL,
    `expired` tinyint (4) DEFAULT NULL,
    `failed_payments` int (11) DEFAULT NULL,
    `next_payment_attempt` datetime DEFAULT NULL,
    KEY `FK_storages_storage_locations` (`location`),
    CONSTRAINT `FK_storages_storage_locations` FOREIGN KEY (`location`) REFERENCES `storage_locations` (`storage`) ON DELETE CASCADE ON UPDATE CASCADE
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci;