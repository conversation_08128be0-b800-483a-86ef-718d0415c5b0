import{al as je,r as $,u as Rn,G as $r,t as K,q as ae,s as se,K as Vr,j as D,m as _n,S as $t,a as I,L as F,am as qr,C as Ie,b as Be,an as Wr,ao as Qr,x as Gr,F as Xr,o as Vt,y as Yr,ap as Kr,ai as Jr,A as Zr,aq as ze,I as ei,a5 as qt,T as ni,ar as ti,d as Te,a6 as ri,as as ii,a0 as li,O as ai,J as Wt}from"./index-a04bc7c5.js";import{T as oi}from"./Textarea-63971279.js";var ui=function(e,n){n=n||{},n.listUnicodeChar=n.hasOwnProperty("listUnicodeChar")?n.listUnicodeChar:!1,n.stripListLeaders=n.hasOwnProperty("stripListLeaders")?n.stripListLeaders:!0,n.gfm=n.hasOwnProperty("gfm")?n.gfm:!0,n.useImgAltText=n.hasOwnProperty("useImgAltText")?n.useImgAltText:!0,n.abbr=n.hasOwnProperty("abbr")?n.abbr:!1,n.replaceLinksWithURL=n.hasOwnProperty("replaceLinksWithURL")?n.replaceLinksWithURL:!1,n.htmlTagsToSkip=n.hasOwnProperty("htmlTagsToSkip")?n.htmlTagsToSkip:[];var t=e||"";t=t.replace(/^(-\s*?|\*\s*?|_\s*?){3,}\s*/gm,"");try{n.stripListLeaders&&(n.listUnicodeChar?t=t.replace(/^([\s\t]*)([\*\-\+]|\d+\.)\s+/gm,n.listUnicodeChar+" $1"):t=t.replace(/^([\s\t]*)([\*\-\+]|\d+\.)\s+/gm,"$1")),n.gfm&&(t=t.replace(/\n={2,}/g,`
`).replace(/~{3}.*\n/g,"").replace(/~~/g,"").replace(/`{3}.*\n/g,"")),n.abbr&&(t=t.replace(/\*\[.*\]:.*\n/,"")),t=t.replace(/<[^>]*>/g,"");var r=new RegExp("<[^>]*>","g");if(n.htmlTagsToSkip.length>0){var i="(?!"+n.htmlTagsToSkip.join("|")+")";r=new RegExp("<"+i+"[^>]*>","ig")}t=t.replace(r,"").replace(/^[=\-]{2,}\s*$/g,"").replace(/\[\^.+?\](\: .*?$)?/g,"").replace(/\s{0,2}\[.*?\]: .*?$/g,"").replace(/\!\[(.*?)\][\[\(].*?[\]\)]/g,n.useImgAltText?"$1":"").replace(/\[([^\]]*?)\][\[\(].*?[\]\)]/g,n.replaceLinksWithURL?"$2":"$1").replace(/^\s{0,3}>\s?/gm,"").replace(/^\s{1,2}\[(.*?)\]: (\S+)( ".*?")?\s*$/g,"").replace(/^(\n)?\s{0,}#{1,6}\s+| {0,}(\n)?\s{0,}#{0,} #{0,}(\n)?\s{0,}$/gm,"$1$2$3").replace(/([\*]+)(\S)(.*?\S)??\1/g,"$2$3").replace(/(^|\W)([_]+)(\S)(.*?\S)??\2($|\W)/g,"$1$3$4$5").replace(/(`{3,})(.*?)\1/gm,"$2").replace(/`(.+?)`/g,"$1").replace(/~(.*?)~/g,"$1")}catch(l){return console.error(l),e}return t};const si=je(ui),In={address:"<EMAIL>",mails:[{id:"1",to:"<EMAIL>",sender:"<EMAIL>",read:!0,subject:"Meeting appointment",message:"Hello, I would like to **schedule a meeting with you.** Please let me know when you are available.",timestamp:Date.now()-1e3*60*60},{id:"2",to:"<EMAIL>",sender:"<EMAIL>",read:!1,subject:"Look at these puppies!",message:"I was at a pet store earlier today and saw these cute puppies. I thought you might like them too. Here's a picture of them.",attachments:["https://media.cnn.com/api/v1/images/stellar/prod/210602230802-01-puppy-social-skills-wellness-sci.jpg?q=w_1600,h_1453,x_0,y_0,c_fill","https://media.licdn.com/dms/image/D5612AQE2GQkfwc73Jw/article-cover_image-shrink_720_1280/0/1697343821040?e=2147483647&v=beta&t=qRaJYBtAZy7jRde_A4P5dF8yK9uWNSxQpM9nkW96Nv8"],timestamp:Date.now()-1e3*60*60},{id:"3",to:"<EMAIL>",sender:"<EMAIL>",read:!1,subject:"Action required",message:"Please verify your email address.",actions:[{label:"Verify",data:"verify-email"}],timestamp:Date.now()-1e3*60*60},{id:"4",to:"<EMAIL>",sender:"<EMAIL>",read:!1,subject:"Markdown support",message:`Our *mail* app **supports** __markdown__.

List:
- Item 1
- Item 2
- Item 3`,timestamp:Date.now()-1e3*60*60}]};function ci(){var J;const{Email:e,View:n,Mails:t,LoggedIn:r,ShowNewMail:i,fetchAndSetMail:l}=$.useContext($e),[a,o]=e,[u,c]=t,[p,s]=n,[d,h]=r,[b,y]=i,S=(J=Rn(Zr))==null?void 0:J.active,[x,v]=$.useState(""),[P,_]=$.useState(""),[z,g]=$.useState([]);$.useEffect(()=>{if($r("Mail")){if(K.APPS.MAIL.recentmails.value)return c(K.APPS.MAIL.recentmails.value);ae("Mail",{action:"getMails"},In.mails).then(w=>{if(!w)return se("warning","No mails found");c(w),K.APPS.MAIL.recentmails.set(w)})}},[]),$.useEffect(()=>{const w=setTimeout(()=>v(P),500);return()=>clearTimeout(w)},[P]),$.useEffect(()=>{x.length>0?ae("Mail",{action:"search",query:x},In.mails.filter(w=>{var C,H;return w&&(((C=w.subject)==null?void 0:C.toLowerCase().includes(x.toLowerCase()))||((H=w.message)==null?void 0:H.toLowerCase().includes(x.toLocaleLowerCase())))})).then(w=>{w&&g(w)}):g([])},[x]);let L=$.useRef(!1);$.useEffect(()=>{var w,C;L.current||S!=null&&S.data&&(L.current=!0,S!=null&&S.data&&((w=S.data)==null?void 0:w.view)=="newMail"&&y({to:(C=S.data)==null?void 0:C.recipient}))},[S==null?void 0:S.data]);const{handleScroll:R}=Vr({fetchData:()=>{var w;return ae("Mail",{action:x.length>0?"search":"getMails",query:x.length>0&&x,lastId:(w=z[z.length-1])==null?void 0:w.id})},onDataFetched:w=>{x.length>0?g([...z,...w]):c([...u,...w])},perPage:10}),U=()=>{ae("AccountSwitcher",{action:"getAccounts",app:"Mail"},null).then(w=>{var H;if(!w)return se("info","No accounts found");let C=(H=w==null?void 0:w.filter(j=>j!==a))==null?void 0:H.map(j=>({title:j,cb:()=>{ae("AccountSwitcher",{action:"switch",app:"Mail",account:j}).then(()=>{K.APPS.MAIL.address.set(j),K.APPS.MAIL.recentmails.set(null),o(j),s("refresh")})}}));Ie.ContextMenu.set({buttons:[{title:a},...C,{title:F("APPS.MAIL.ADD_EMAIL"),cb:()=>{h(!1),s("login")}},{title:F("APPS.MAIL.SIGN_OUT_POPUP.TITLE"),color:"red",cb:()=>{Ie.PopUp.set({title:F("APPS.MAIL.SIGN_OUT_POPUP.TITLE"),description:F("APPS.MAIL.SIGN_OUT_POPUP.DESCRIPTION"),buttons:[{title:F("APPS.MAIL.SIGN_OUT_POPUP.CANCEL")},{title:F("APPS.MAIL.SIGN_OUT_POPUP.PROCEED"),color:"red",cb:()=>{ae("Mail",{action:"logout"},!0).then(j=>{j!=null&&j.success&&(s("login"),h(!1),o(null),K.APPS.MAIL.address.reset())})}}]})}}]})})};return D(_n.div,{...$t("left","home"),className:"animation-container home",children:[D("div",{className:"mail-header",style:{paddingLeft:"1rem"},children:[I("div",{className:"title",children:F("APPS.MAIL.TITLE")}),I(qr,{className:"options",onClick:()=>{var w,C,H,j,B,ee;Ie.ContextMenu.set({buttons:[{title:F("APPS.MAIL.CHANGE_ACCOUNT"),cb:()=>{setTimeout(()=>{U()},250)}},((H=(C=(w=Be)==null?void 0:w.value)==null?void 0:C.ChangePassword)==null?void 0:H.Mail)&&{title:F("APPS.MAIL.CHANGE_PASSWORD"),cb:()=>{setTimeout(()=>{Wr("Mail",()=>{})},250)}},((ee=(B=(j=Be)==null?void 0:j.value)==null?void 0:B.DeleteAccount)==null?void 0:ee.Mail)&&{title:F("APPS.MAIL.DELETE_ACCOUNT"),color:"red",cb:()=>{setTimeout(()=>{Qr("Mail",()=>{h(!1),s("login"),o(null),K.APPS.MAIL.address.reset()})},250)}}]})}})]}),I(Gr,{placeholder:F("SEARCH"),onChange:w=>_(w.target.value)}),I("div",{className:"mail-list",onScroll:R,children:(x.length>0?z:u).map((w,C)=>D("div",{className:"mail",onClick:()=>l(w),children:[I("div",{className:"symbol","data-read":(w.read||w.sender===a)??!1}),D("div",{className:"item-header",children:[I("div",{className:"user",children:I("div",{className:"sender",children:w.sender===a?D(Xr,{children:[F("APPS.MAIL.YOU"),D("span",{children:["- ",w.to]})]}):w.sender})}),D("div",{className:"date",children:[I("div",{className:"time",children:Vt(w.timestamp)}),I(Yr,{})]})]}),D("div",{className:"item-body",children:[I("div",{className:"subject",children:w.subject}),I("div",{className:"message",children:si(w.message.length>70?w.message.substring(0,70)+"...":w.message)})]})]},C))}),I("div",{className:"mail-footer",children:D("div",{className:"footer-wrapper",children:[I(Kr,{onClick:()=>U()}),D("div",{className:"text",children:[I("div",{className:"title",children:a}),D("div",{className:"count",children:[u.filter(w=>!w.read&&a!==w.sender).length," ",F("APPS.MAIL.UNREAD")]})]}),I(Jr,{onClick:()=>y(!0)})]})})]})}const tt=["http","https","mailto","tel"];function pi(e){const n=(e||"").trim(),t=n.charAt(0);if(t==="#"||t==="/")return n;const r=n.indexOf(":");if(r===-1)return n;let i=-1;for(;++i<tt.length;){const l=tt[i];if(r===l.length&&n.slice(0,l.length).toLowerCase()===l)return n}return i=n.indexOf("?"),i!==-1&&r>i||(i=n.indexOf("#"),i!==-1&&r>i)?n:"javascript:void(0)"}/*!
 * Determine if an object is a Buffer
 *
 * <AUTHOR> Aboukhadijeh <https://feross.org>
 * @license  MIT
 */var hi=function(n){return n!=null&&n.constructor!=null&&typeof n.constructor.isBuffer=="function"&&n.constructor.isBuffer(n)};const Qt=je(hi);function Qe(e){return!e||typeof e!="object"?"":"position"in e||"type"in e?rt(e.position):"start"in e||"end"in e?rt(e):"line"in e||"column"in e?Ln(e):""}function Ln(e){return it(e&&e.line)+":"+it(e&&e.column)}function rt(e){return Ln(e&&e.start)+"-"+Ln(e&&e.end)}function it(e){return e&&typeof e=="number"?e:1}class ce extends Error{constructor(n,t,r){const i=[null,null];let l={start:{line:null,column:null},end:{line:null,column:null}};if(super(),typeof t=="string"&&(r=t,t=void 0),typeof r=="string"){const a=r.indexOf(":");a===-1?i[1]=r:(i[0]=r.slice(0,a),i[1]=r.slice(a+1))}t&&("type"in t||"position"in t?t.position&&(l=t.position):"start"in t||"end"in t?l=t:("line"in t||"column"in t)&&(l.start=t)),this.name=Qe(t)||"1:1",this.message=typeof n=="object"?n.message:n,this.stack="",typeof n=="object"&&n.stack&&(this.stack=n.stack),this.reason=this.message,this.fatal,this.line=l.start.line,this.column=l.start.column,this.position=l,this.source=i[0],this.ruleId=i[1],this.file,this.actual,this.expected,this.url,this.note}}ce.prototype.file="";ce.prototype.name="";ce.prototype.reason="";ce.prototype.message="";ce.prototype.stack="";ce.prototype.fatal=null;ce.prototype.column=null;ce.prototype.line=null;ce.prototype.source=null;ce.prototype.ruleId=null;ce.prototype.position=null;const ye={basename:fi,dirname:mi,extname:di,join:gi,sep:"/"};function fi(e,n){if(n!==void 0&&typeof n!="string")throw new TypeError('"ext" argument must be a string');Xe(e);let t=0,r=-1,i=e.length,l;if(n===void 0||n.length===0||n.length>e.length){for(;i--;)if(e.charCodeAt(i)===47){if(l){t=i+1;break}}else r<0&&(l=!0,r=i+1);return r<0?"":e.slice(t,r)}if(n===e)return"";let a=-1,o=n.length-1;for(;i--;)if(e.charCodeAt(i)===47){if(l){t=i+1;break}}else a<0&&(l=!0,a=i+1),o>-1&&(e.charCodeAt(i)===n.charCodeAt(o--)?o<0&&(r=i):(o=-1,r=a));return t===r?r=a:r<0&&(r=e.length),e.slice(t,r)}function mi(e){if(Xe(e),e.length===0)return".";let n=-1,t=e.length,r;for(;--t;)if(e.charCodeAt(t)===47){if(r){n=t;break}}else r||(r=!0);return n<0?e.charCodeAt(0)===47?"/":".":n===1&&e.charCodeAt(0)===47?"//":e.slice(0,n)}function di(e){Xe(e);let n=e.length,t=-1,r=0,i=-1,l=0,a;for(;n--;){const o=e.charCodeAt(n);if(o===47){if(a){r=n+1;break}continue}t<0&&(a=!0,t=n+1),o===46?i<0?i=n:l!==1&&(l=1):i>-1&&(l=-1)}return i<0||t<0||l===0||l===1&&i===t-1&&i===r+1?"":e.slice(i,t)}function gi(...e){let n=-1,t;for(;++n<e.length;)Xe(e[n]),e[n]&&(t=t===void 0?e[n]:t+"/"+e[n]);return t===void 0?".":yi(t)}function yi(e){Xe(e);const n=e.charCodeAt(0)===47;let t=xi(e,!n);return t.length===0&&!n&&(t="."),t.length>0&&e.charCodeAt(e.length-1)===47&&(t+="/"),n?"/"+t:t}function xi(e,n){let t="",r=0,i=-1,l=0,a=-1,o,u;for(;++a<=e.length;){if(a<e.length)o=e.charCodeAt(a);else{if(o===47)break;o=47}if(o===47){if(!(i===a-1||l===1))if(i!==a-1&&l===2){if(t.length<2||r!==2||t.charCodeAt(t.length-1)!==46||t.charCodeAt(t.length-2)!==46){if(t.length>2){if(u=t.lastIndexOf("/"),u!==t.length-1){u<0?(t="",r=0):(t=t.slice(0,u),r=t.length-1-t.lastIndexOf("/")),i=a,l=0;continue}}else if(t.length>0){t="",r=0,i=a,l=0;continue}}n&&(t=t.length>0?t+"/..":"..",r=2)}else t.length>0?t+="/"+e.slice(i+1,a):t=e.slice(i+1,a),r=a-i-1;i=a,l=0}else o===46&&l>-1?l++:l=-1}return t}function Xe(e){if(typeof e!="string")throw new TypeError("Path must be a string. Received "+JSON.stringify(e))}const ki={cwd:bi};function bi(){return"/"}function vn(e){return e!==null&&typeof e=="object"&&e.href&&e.origin}function wi(e){if(typeof e=="string")e=new URL(e);else if(!vn(e)){const n=new TypeError('The "path" argument must be of type string or an instance of URL. Received `'+e+"`");throw n.code="ERR_INVALID_ARG_TYPE",n}if(e.protocol!=="file:"){const n=new TypeError("The URL must be of scheme file");throw n.code="ERR_INVALID_URL_SCHEME",n}return Si(e)}function Si(e){if(e.hostname!==""){const r=new TypeError('File URL host must be "localhost" or empty on darwin');throw r.code="ERR_INVALID_FILE_URL_HOST",r}const n=e.pathname;let t=-1;for(;++t<n.length;)if(n.charCodeAt(t)===37&&n.charCodeAt(t+1)===50){const r=n.charCodeAt(t+2);if(r===70||r===102){const i=new TypeError("File URL path must not include encoded / characters");throw i.code="ERR_INVALID_FILE_URL_PATH",i}}return decodeURIComponent(n)}const yn=["history","path","basename","stem","extname","dirname"];class Gt{constructor(n){let t;n?typeof n=="string"||Ai(n)?t={value:n}:vn(n)?t={path:n}:t=n:t={},this.data={},this.messages=[],this.history=[],this.cwd=ki.cwd(),this.value,this.stored,this.result,this.map;let r=-1;for(;++r<yn.length;){const l=yn[r];l in t&&t[l]!==void 0&&t[l]!==null&&(this[l]=l==="history"?[...t[l]]:t[l])}let i;for(i in t)yn.includes(i)||(this[i]=t[i])}get path(){return this.history[this.history.length-1]}set path(n){vn(n)&&(n=wi(n)),kn(n,"path"),this.path!==n&&this.history.push(n)}get dirname(){return typeof this.path=="string"?ye.dirname(this.path):void 0}set dirname(n){lt(this.basename,"dirname"),this.path=ye.join(n||"",this.basename)}get basename(){return typeof this.path=="string"?ye.basename(this.path):void 0}set basename(n){kn(n,"basename"),xn(n,"basename"),this.path=ye.join(this.dirname||"",n)}get extname(){return typeof this.path=="string"?ye.extname(this.path):void 0}set extname(n){if(xn(n,"extname"),lt(this.dirname,"extname"),n){if(n.charCodeAt(0)!==46)throw new Error("`extname` must start with `.`");if(n.includes(".",1))throw new Error("`extname` cannot contain multiple dots")}this.path=ye.join(this.dirname,this.stem+(n||""))}get stem(){return typeof this.path=="string"?ye.basename(this.path,this.extname):void 0}set stem(n){kn(n,"stem"),xn(n,"stem"),this.path=ye.join(this.dirname||"",n+(this.extname||""))}toString(n){return(this.value||"").toString(n||void 0)}message(n,t,r){const i=new ce(n,t,r);return this.path&&(i.name=this.path+":"+i.name,i.file=this.path),i.fatal=!1,this.messages.push(i),i}info(n,t,r){const i=this.message(n,t,r);return i.fatal=null,i}fail(n,t,r){const i=this.message(n,t,r);throw i.fatal=!0,i}}function xn(e,n){if(e&&e.includes(ye.sep))throw new Error("`"+n+"` cannot be a path: did not expect `"+ye.sep+"`")}function kn(e,n){if(!e)throw new Error("`"+n+"` cannot be empty")}function lt(e,n){if(!e)throw new Error("Setting `"+n+"` requires `path` to be set too")}function Ai(e){return Qt(e)}function at(e){if(e)throw e}var Ze=Object.prototype.hasOwnProperty,Xt=Object.prototype.toString,ot=Object.defineProperty,ut=Object.getOwnPropertyDescriptor,st=function(n){return typeof Array.isArray=="function"?Array.isArray(n):Xt.call(n)==="[object Array]"},ct=function(n){if(!n||Xt.call(n)!=="[object Object]")return!1;var t=Ze.call(n,"constructor"),r=n.constructor&&n.constructor.prototype&&Ze.call(n.constructor.prototype,"isPrototypeOf");if(n.constructor&&!t&&!r)return!1;var i;for(i in n);return typeof i>"u"||Ze.call(n,i)},pt=function(n,t){ot&&t.name==="__proto__"?ot(n,t.name,{enumerable:!0,configurable:!0,value:t.newValue,writable:!0}):n[t.name]=t.newValue},ht=function(n,t){if(t==="__proto__")if(Ze.call(n,t)){if(ut)return ut(n,t).value}else return;return n[t]},Ei=function e(){var n,t,r,i,l,a,o=arguments[0],u=1,c=arguments.length,p=!1;for(typeof o=="boolean"&&(p=o,o=arguments[1]||{},u=2),(o==null||typeof o!="object"&&typeof o!="function")&&(o={});u<c;++u)if(n=arguments[u],n!=null)for(t in n)r=ht(o,t),i=ht(n,t),o!==i&&(p&&i&&(ct(i)||(l=st(i)))?(l?(l=!1,a=r&&st(r)?r:[]):a=r&&ct(r)?r:{},pt(o,{name:t,newValue:e(p,a,i)})):typeof i<"u"&&pt(o,{name:t,newValue:i}));return o};const ft=je(Ei);function Tn(e){if(typeof e!="object"||e===null)return!1;const n=Object.getPrototypeOf(e);return(n===null||n===Object.prototype||Object.getPrototypeOf(n)===null)&&!(Symbol.toStringTag in e)&&!(Symbol.iterator in e)}function Pi(){const e=[],n={run:t,use:r};return n;function t(...i){let l=-1;const a=i.pop();if(typeof a!="function")throw new TypeError("Expected function as last argument, not "+a);o(null,...i);function o(u,...c){const p=e[++l];let s=-1;if(u){a(u);return}for(;++s<i.length;)(c[s]===null||c[s]===void 0)&&(c[s]=i[s]);i=c,p?Ci(p,o)(...c):a(null,...c)}}function r(i){if(typeof i!="function")throw new TypeError("Expected `middelware` to be a function, not "+i);return e.push(i),n}}function Ci(e,n){let t;return r;function r(...a){const o=e.length>a.length;let u;o&&a.push(i);try{u=e.apply(this,a)}catch(c){const p=c;if(o&&t)throw p;return i(p)}o||(u&&u.then&&typeof u.then=="function"?u.then(l,i):u instanceof Error?i(u):l(u))}function i(a,...o){t||(t=!0,n(a,...o))}function l(a){i(null,a)}}const Ii=Kt().freeze(),Yt={}.hasOwnProperty;function Kt(){const e=Pi(),n=[];let t={},r,i=-1;return l.data=a,l.Parser=void 0,l.Compiler=void 0,l.freeze=o,l.attachers=n,l.use=u,l.parse=c,l.stringify=p,l.run=s,l.runSync=d,l.process=h,l.processSync=b,l;function l(){const y=Kt();let S=-1;for(;++S<n.length;)y.use(...n[S]);return y.data(ft(!0,{},t)),y}function a(y,S){return typeof y=="string"?arguments.length===2?(Sn("data",r),t[y]=S,l):Yt.call(t,y)&&t[y]||null:y?(Sn("data",r),t=y,l):t}function o(){if(r)return l;for(;++i<n.length;){const[y,...S]=n[i];if(S[0]===!1)continue;S[0]===!0&&(S[0]=void 0);const x=y.call(l,...S);typeof x=="function"&&e.use(x)}return r=!0,i=Number.POSITIVE_INFINITY,l}function u(y,...S){let x;if(Sn("use",r),y!=null)if(typeof y=="function")z(y,...S);else if(typeof y=="object")Array.isArray(y)?_(y):P(y);else throw new TypeError("Expected usable value, not `"+y+"`");return x&&(t.settings=Object.assign(t.settings||{},x)),l;function v(g){if(typeof g=="function")z(g);else if(typeof g=="object")if(Array.isArray(g)){const[L,...R]=g;z(L,...R)}else P(g);else throw new TypeError("Expected usable value, not `"+g+"`")}function P(g){_(g.plugins),g.settings&&(x=Object.assign(x||{},g.settings))}function _(g){let L=-1;if(g!=null)if(Array.isArray(g))for(;++L<g.length;){const R=g[L];v(R)}else throw new TypeError("Expected a list of plugins, not `"+g+"`")}function z(g,L){let R=-1,U;for(;++R<n.length;)if(n[R][0]===g){U=n[R];break}U?(Tn(U[1])&&Tn(L)&&(L=ft(!0,U[1],L)),U[1]=L):n.push([...arguments])}}function c(y){l.freeze();const S=We(y),x=l.Parser;return bn("parse",x),mt(x,"parse")?new x(String(S),S).parse():x(String(S),S)}function p(y,S){l.freeze();const x=We(S),v=l.Compiler;return wn("stringify",v),dt(y),mt(v,"compile")?new v(y,x).compile():v(y,x)}function s(y,S,x){if(dt(y),l.freeze(),!x&&typeof S=="function"&&(x=S,S=void 0),!x)return new Promise(v);v(null,x);function v(P,_){e.run(y,We(S),z);function z(g,L,R){L=L||y,g?_(g):P?P(L):x(null,L,R)}}}function d(y,S){let x,v;return l.run(y,S,P),gt("runSync","run",v),x;function P(_,z){at(_),x=z,v=!0}}function h(y,S){if(l.freeze(),bn("process",l.Parser),wn("process",l.Compiler),!S)return new Promise(x);x(null,S);function x(v,P){const _=We(y);l.run(l.parse(_),_,(g,L,R)=>{if(g||!L||!R)z(g);else{const U=l.stringify(L,R);U==null||(Ti(U)?R.value=U:R.result=U),z(g,R)}});function z(g,L){g||!L?P(g):v?v(L):S(null,L)}}}function b(y){let S;l.freeze(),bn("processSync",l.Parser),wn("processSync",l.Compiler);const x=We(y);return l.process(x,v),gt("processSync","process",S),x;function v(P){S=!0,at(P)}}}function mt(e,n){return typeof e=="function"&&e.prototype&&(Li(e.prototype)||n in e.prototype)}function Li(e){let n;for(n in e)if(Yt.call(e,n))return!0;return!1}function bn(e,n){if(typeof n!="function")throw new TypeError("Cannot `"+e+"` without `Parser`")}function wn(e,n){if(typeof n!="function")throw new TypeError("Cannot `"+e+"` without `Compiler`")}function Sn(e,n){if(n)throw new Error("Cannot call `"+e+"` on a frozen processor.\nCreate a new processor first, by calling it: use `processor()` instead of `processor`.")}function dt(e){if(!Tn(e)||typeof e.type!="string")throw new TypeError("Expected node, got `"+e+"`")}function gt(e,n,t){if(!t)throw new Error("`"+e+"` finished async. Use `"+n+"` instead")}function We(e){return vi(e)?e:new Gt(e)}function vi(e){return!!(e&&typeof e=="object"&&"message"in e&&"messages"in e)}function Ti(e){return typeof e=="string"||Qt(e)}const Mi={};function Oi(e,n){const t=n||Mi,r=typeof t.includeImageAlt=="boolean"?t.includeImageAlt:!0,i=typeof t.includeHtml=="boolean"?t.includeHtml:!0;return Jt(e,r,i)}function Jt(e,n,t){if(Fi(e)){if("value"in e)return e.type==="html"&&!t?"":e.value;if(n&&"alt"in e&&e.alt)return e.alt;if("children"in e)return yt(e.children,n,t)}return Array.isArray(e)?yt(e,n,t):""}function yt(e,n,t){const r=[];let i=-1;for(;++i<e.length;)r[i]=Jt(e[i],n,t);return r.join("")}function Fi(e){return!!(e&&typeof e=="object")}function ke(e,n,t,r){const i=e.length;let l=0,a;if(n<0?n=-n>i?0:i+n:n=n>i?i:n,t=t>0?t:0,r.length<1e4)a=Array.from(r),a.unshift(n,t),e.splice(...a);else for(t&&e.splice(n,t);l<r.length;)a=r.slice(l,l+1e4),a.unshift(n,0),e.splice(...a),l+=1e4,n+=1e4}function ue(e,n){return e.length>0?(ke(e,e.length,0,n),e):n}const xt={}.hasOwnProperty;function Ni(e){const n={};let t=-1;for(;++t<e.length;)Di(n,e[t]);return n}function Di(e,n){let t;for(t in n){const i=(xt.call(e,t)?e[t]:void 0)||(e[t]={}),l=n[t];let a;if(l)for(a in l){xt.call(i,a)||(i[a]=[]);const o=l[a];zi(i[a],Array.isArray(o)?o:o?[o]:[])}}}function zi(e,n){let t=-1;const r=[];for(;++t<n.length;)(n[t].add==="after"?e:r).push(n[t]);ke(e,0,0,r)}const Ri=/[!-\/:-@\[-`\{-~\xA1\xA7\xAB\xB6\xB7\xBB\xBF\u037E\u0387\u055A-\u055F\u0589\u058A\u05BE\u05C0\u05C3\u05C6\u05F3\u05F4\u0609\u060A\u060C\u060D\u061B\u061D-\u061F\u066A-\u066D\u06D4\u0700-\u070D\u07F7-\u07F9\u0830-\u083E\u085E\u0964\u0965\u0970\u09FD\u0A76\u0AF0\u0C77\u0C84\u0DF4\u0E4F\u0E5A\u0E5B\u0F04-\u0F12\u0F14\u0F3A-\u0F3D\u0F85\u0FD0-\u0FD4\u0FD9\u0FDA\u104A-\u104F\u10FB\u1360-\u1368\u1400\u166E\u169B\u169C\u16EB-\u16ED\u1735\u1736\u17D4-\u17D6\u17D8-\u17DA\u1800-\u180A\u1944\u1945\u1A1E\u1A1F\u1AA0-\u1AA6\u1AA8-\u1AAD\u1B5A-\u1B60\u1B7D\u1B7E\u1BFC-\u1BFF\u1C3B-\u1C3F\u1C7E\u1C7F\u1CC0-\u1CC7\u1CD3\u2010-\u2027\u2030-\u2043\u2045-\u2051\u2053-\u205E\u207D\u207E\u208D\u208E\u2308-\u230B\u2329\u232A\u2768-\u2775\u27C5\u27C6\u27E6-\u27EF\u2983-\u2998\u29D8-\u29DB\u29FC\u29FD\u2CF9-\u2CFC\u2CFE\u2CFF\u2D70\u2E00-\u2E2E\u2E30-\u2E4F\u2E52-\u2E5D\u3001-\u3003\u3008-\u3011\u3014-\u301F\u3030\u303D\u30A0\u30FB\uA4FE\uA4FF\uA60D-\uA60F\uA673\uA67E\uA6F2-\uA6F7\uA874-\uA877\uA8CE\uA8CF\uA8F8-\uA8FA\uA8FC\uA92E\uA92F\uA95F\uA9C1-\uA9CD\uA9DE\uA9DF\uAA5C-\uAA5F\uAADE\uAADF\uAAF0\uAAF1\uABEB\uFD3E\uFD3F\uFE10-\uFE19\uFE30-\uFE52\uFE54-\uFE61\uFE63\uFE68\uFE6A\uFE6B\uFF01-\uFF03\uFF05-\uFF0A\uFF0C-\uFF0F\uFF1A\uFF1B\uFF1F\uFF20\uFF3B-\uFF3D\uFF3F\uFF5B\uFF5D\uFF5F-\uFF65]/,xe=Le(/[A-Za-z]/),oe=Le(/[\dA-Za-z]/),_i=Le(/[#-'*+\--9=?A-Z^-~]/);function Mn(e){return e!==null&&(e<32||e===127)}const On=Le(/\d/),Bi=Le(/[\dA-Fa-f]/),ji=Le(/[!-/:-@[-`{-~]/);function M(e){return e!==null&&e<-2}function ie(e){return e!==null&&(e<0||e===32)}function V(e){return e===-2||e===-1||e===32}const Ui=Le(Ri),Hi=Le(/\s/);function Le(e){return n;function n(t){return t!==null&&e.test(String.fromCharCode(t))}}function X(e,n,t,r){const i=r?r-1:Number.POSITIVE_INFINITY;let l=0;return a;function a(u){return V(u)?(e.enter(t),o(u)):n(u)}function o(u){return V(u)&&l++<i?(e.consume(u),o):(e.exit(t),n(u))}}const $i={tokenize:Vi};function Vi(e){const n=e.attempt(this.parser.constructs.contentInitial,r,i);let t;return n;function r(o){if(o===null){e.consume(o);return}return e.enter("lineEnding"),e.consume(o),e.exit("lineEnding"),X(e,n,"linePrefix")}function i(o){return e.enter("paragraph"),l(o)}function l(o){const u=e.enter("chunkText",{contentType:"text",previous:t});return t&&(t.next=u),t=u,a(o)}function a(o){if(o===null){e.exit("chunkText"),e.exit("paragraph"),e.consume(o);return}return M(o)?(e.consume(o),e.exit("chunkText"),l):(e.consume(o),a)}}const qi={tokenize:Wi},kt={tokenize:Qi};function Wi(e){const n=this,t=[];let r=0,i,l,a;return o;function o(P){if(r<t.length){const _=t[r];return n.containerState=_[1],e.attempt(_[0].continuation,u,c)(P)}return c(P)}function u(P){if(r++,n.containerState._closeFlow){n.containerState._closeFlow=void 0,i&&v();const _=n.events.length;let z=_,g;for(;z--;)if(n.events[z][0]==="exit"&&n.events[z][1].type==="chunkFlow"){g=n.events[z][1].end;break}x(r);let L=_;for(;L<n.events.length;)n.events[L][1].end=Object.assign({},g),L++;return ke(n.events,z+1,0,n.events.slice(_)),n.events.length=L,c(P)}return o(P)}function c(P){if(r===t.length){if(!i)return d(P);if(i.currentConstruct&&i.currentConstruct.concrete)return b(P);n.interrupt=!!(i.currentConstruct&&!i._gfmTableDynamicInterruptHack)}return n.containerState={},e.check(kt,p,s)(P)}function p(P){return i&&v(),x(r),d(P)}function s(P){return n.parser.lazy[n.now().line]=r!==t.length,a=n.now().offset,b(P)}function d(P){return n.containerState={},e.attempt(kt,h,b)(P)}function h(P){return r++,t.push([n.currentConstruct,n.containerState]),d(P)}function b(P){if(P===null){i&&v(),x(0),e.consume(P);return}return i=i||n.parser.flow(n.now()),e.enter("chunkFlow",{contentType:"flow",previous:l,_tokenizer:i}),y(P)}function y(P){if(P===null){S(e.exit("chunkFlow"),!0),x(0),e.consume(P);return}return M(P)?(e.consume(P),S(e.exit("chunkFlow")),r=0,n.interrupt=void 0,o):(e.consume(P),y)}function S(P,_){const z=n.sliceStream(P);if(_&&z.push(null),P.previous=l,l&&(l.next=P),l=P,i.defineSkip(P.start),i.write(z),n.parser.lazy[P.start.line]){let g=i.events.length;for(;g--;)if(i.events[g][1].start.offset<a&&(!i.events[g][1].end||i.events[g][1].end.offset>a))return;const L=n.events.length;let R=L,U,J;for(;R--;)if(n.events[R][0]==="exit"&&n.events[R][1].type==="chunkFlow"){if(U){J=n.events[R][1].end;break}U=!0}for(x(r),g=L;g<n.events.length;)n.events[g][1].end=Object.assign({},J),g++;ke(n.events,R+1,0,n.events.slice(L)),n.events.length=g}}function x(P){let _=t.length;for(;_-- >P;){const z=t[_];n.containerState=z[1],z[0].exit.call(n,e)}t.length=P}function v(){i.write([null]),l=void 0,i=void 0,n.containerState._closeFlow=void 0}}function Qi(e,n,t){return X(e,e.attempt(this.parser.constructs.document,n,t),"linePrefix",this.parser.constructs.disable.null.includes("codeIndented")?void 0:4)}function bt(e){if(e===null||ie(e)||Hi(e))return 1;if(Ui(e))return 2}function Bn(e,n,t){const r=[];let i=-1;for(;++i<e.length;){const l=e[i].resolveAll;l&&!r.includes(l)&&(n=l(n,t),r.push(l))}return n}const Fn={name:"attention",tokenize:Xi,resolveAll:Gi};function Gi(e,n){let t=-1,r,i,l,a,o,u,c,p;for(;++t<e.length;)if(e[t][0]==="enter"&&e[t][1].type==="attentionSequence"&&e[t][1]._close){for(r=t;r--;)if(e[r][0]==="exit"&&e[r][1].type==="attentionSequence"&&e[r][1]._open&&n.sliceSerialize(e[r][1]).charCodeAt(0)===n.sliceSerialize(e[t][1]).charCodeAt(0)){if((e[r][1]._close||e[t][1]._open)&&(e[t][1].end.offset-e[t][1].start.offset)%3&&!((e[r][1].end.offset-e[r][1].start.offset+e[t][1].end.offset-e[t][1].start.offset)%3))continue;u=e[r][1].end.offset-e[r][1].start.offset>1&&e[t][1].end.offset-e[t][1].start.offset>1?2:1;const s=Object.assign({},e[r][1].end),d=Object.assign({},e[t][1].start);wt(s,-u),wt(d,u),a={type:u>1?"strongSequence":"emphasisSequence",start:s,end:Object.assign({},e[r][1].end)},o={type:u>1?"strongSequence":"emphasisSequence",start:Object.assign({},e[t][1].start),end:d},l={type:u>1?"strongText":"emphasisText",start:Object.assign({},e[r][1].end),end:Object.assign({},e[t][1].start)},i={type:u>1?"strong":"emphasis",start:Object.assign({},a.start),end:Object.assign({},o.end)},e[r][1].end=Object.assign({},a.start),e[t][1].start=Object.assign({},o.end),c=[],e[r][1].end.offset-e[r][1].start.offset&&(c=ue(c,[["enter",e[r][1],n],["exit",e[r][1],n]])),c=ue(c,[["enter",i,n],["enter",a,n],["exit",a,n],["enter",l,n]]),c=ue(c,Bn(n.parser.constructs.insideSpan.null,e.slice(r+1,t),n)),c=ue(c,[["exit",l,n],["enter",o,n],["exit",o,n],["exit",i,n]]),e[t][1].end.offset-e[t][1].start.offset?(p=2,c=ue(c,[["enter",e[t][1],n],["exit",e[t][1],n]])):p=0,ke(e,r-1,t-r+3,c),t=r+c.length-p-2;break}}for(t=-1;++t<e.length;)e[t][1].type==="attentionSequence"&&(e[t][1].type="data");return e}function Xi(e,n){const t=this.parser.constructs.attentionMarkers.null,r=this.previous,i=bt(r);let l;return a;function a(u){return l=u,e.enter("attentionSequence"),o(u)}function o(u){if(u===l)return e.consume(u),o;const c=e.exit("attentionSequence"),p=bt(u),s=!p||p===2&&i||t.includes(u),d=!i||i===2&&p||t.includes(r);return c._open=!!(l===42?s:s&&(i||!d)),c._close=!!(l===42?d:d&&(p||!s)),n(u)}}function wt(e,n){e.column+=n,e.offset+=n,e._bufferIndex+=n}const Yi={name:"autolink",tokenize:Ki};function Ki(e,n,t){let r=0;return i;function i(h){return e.enter("autolink"),e.enter("autolinkMarker"),e.consume(h),e.exit("autolinkMarker"),e.enter("autolinkProtocol"),l}function l(h){return xe(h)?(e.consume(h),a):c(h)}function a(h){return h===43||h===45||h===46||oe(h)?(r=1,o(h)):c(h)}function o(h){return h===58?(e.consume(h),r=0,u):(h===43||h===45||h===46||oe(h))&&r++<32?(e.consume(h),o):(r=0,c(h))}function u(h){return h===62?(e.exit("autolinkProtocol"),e.enter("autolinkMarker"),e.consume(h),e.exit("autolinkMarker"),e.exit("autolink"),n):h===null||h===32||h===60||Mn(h)?t(h):(e.consume(h),u)}function c(h){return h===64?(e.consume(h),p):_i(h)?(e.consume(h),c):t(h)}function p(h){return oe(h)?s(h):t(h)}function s(h){return h===46?(e.consume(h),r=0,p):h===62?(e.exit("autolinkProtocol").type="autolinkEmail",e.enter("autolinkMarker"),e.consume(h),e.exit("autolinkMarker"),e.exit("autolink"),n):d(h)}function d(h){if((h===45||oe(h))&&r++<63){const b=h===45?d:s;return e.consume(h),b}return t(h)}}const tn={tokenize:Ji,partial:!0};function Ji(e,n,t){return r;function r(l){return V(l)?X(e,i,"linePrefix")(l):i(l)}function i(l){return l===null||M(l)?n(l):t(l)}}const Zt={name:"blockQuote",tokenize:Zi,continuation:{tokenize:el},exit:nl};function Zi(e,n,t){const r=this;return i;function i(a){if(a===62){const o=r.containerState;return o.open||(e.enter("blockQuote",{_container:!0}),o.open=!0),e.enter("blockQuotePrefix"),e.enter("blockQuoteMarker"),e.consume(a),e.exit("blockQuoteMarker"),l}return t(a)}function l(a){return V(a)?(e.enter("blockQuotePrefixWhitespace"),e.consume(a),e.exit("blockQuotePrefixWhitespace"),e.exit("blockQuotePrefix"),n):(e.exit("blockQuotePrefix"),n(a))}}function el(e,n,t){const r=this;return i;function i(a){return V(a)?X(e,l,"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(a):l(a)}function l(a){return e.attempt(Zt,n,t)(a)}}function nl(e){e.exit("blockQuote")}const er={name:"characterEscape",tokenize:tl};function tl(e,n,t){return r;function r(l){return e.enter("characterEscape"),e.enter("escapeMarker"),e.consume(l),e.exit("escapeMarker"),i}function i(l){return ji(l)?(e.enter("characterEscapeValue"),e.consume(l),e.exit("characterEscapeValue"),e.exit("characterEscape"),n):t(l)}}const St=document.createElement("i");function jn(e){const n="&"+e+";";St.innerHTML=n;const t=St.textContent;return t.charCodeAt(t.length-1)===59&&e!=="semi"||t===n?!1:t}const nr={name:"characterReference",tokenize:rl};function rl(e,n,t){const r=this;let i=0,l,a;return o;function o(s){return e.enter("characterReference"),e.enter("characterReferenceMarker"),e.consume(s),e.exit("characterReferenceMarker"),u}function u(s){return s===35?(e.enter("characterReferenceMarkerNumeric"),e.consume(s),e.exit("characterReferenceMarkerNumeric"),c):(e.enter("characterReferenceValue"),l=31,a=oe,p(s))}function c(s){return s===88||s===120?(e.enter("characterReferenceMarkerHexadecimal"),e.consume(s),e.exit("characterReferenceMarkerHexadecimal"),e.enter("characterReferenceValue"),l=6,a=Bi,p):(e.enter("characterReferenceValue"),l=7,a=On,p(s))}function p(s){if(s===59&&i){const d=e.exit("characterReferenceValue");return a===oe&&!jn(r.sliceSerialize(d))?t(s):(e.enter("characterReferenceMarker"),e.consume(s),e.exit("characterReferenceMarker"),e.exit("characterReference"),n)}return a(s)&&i++<l?(e.consume(s),p):t(s)}}const At={tokenize:ll,partial:!0},Et={name:"codeFenced",tokenize:il,concrete:!0};function il(e,n,t){const r=this,i={tokenize:z,partial:!0};let l=0,a=0,o;return u;function u(g){return c(g)}function c(g){const L=r.events[r.events.length-1];return l=L&&L[1].type==="linePrefix"?L[2].sliceSerialize(L[1],!0).length:0,o=g,e.enter("codeFenced"),e.enter("codeFencedFence"),e.enter("codeFencedFenceSequence"),p(g)}function p(g){return g===o?(a++,e.consume(g),p):a<3?t(g):(e.exit("codeFencedFenceSequence"),V(g)?X(e,s,"whitespace")(g):s(g))}function s(g){return g===null||M(g)?(e.exit("codeFencedFence"),r.interrupt?n(g):e.check(At,y,_)(g)):(e.enter("codeFencedFenceInfo"),e.enter("chunkString",{contentType:"string"}),d(g))}function d(g){return g===null||M(g)?(e.exit("chunkString"),e.exit("codeFencedFenceInfo"),s(g)):V(g)?(e.exit("chunkString"),e.exit("codeFencedFenceInfo"),X(e,h,"whitespace")(g)):g===96&&g===o?t(g):(e.consume(g),d)}function h(g){return g===null||M(g)?s(g):(e.enter("codeFencedFenceMeta"),e.enter("chunkString",{contentType:"string"}),b(g))}function b(g){return g===null||M(g)?(e.exit("chunkString"),e.exit("codeFencedFenceMeta"),s(g)):g===96&&g===o?t(g):(e.consume(g),b)}function y(g){return e.attempt(i,_,S)(g)}function S(g){return e.enter("lineEnding"),e.consume(g),e.exit("lineEnding"),x}function x(g){return l>0&&V(g)?X(e,v,"linePrefix",l+1)(g):v(g)}function v(g){return g===null||M(g)?e.check(At,y,_)(g):(e.enter("codeFlowValue"),P(g))}function P(g){return g===null||M(g)?(e.exit("codeFlowValue"),v(g)):(e.consume(g),P)}function _(g){return e.exit("codeFenced"),n(g)}function z(g,L,R){let U=0;return J;function J(B){return g.enter("lineEnding"),g.consume(B),g.exit("lineEnding"),w}function w(B){return g.enter("codeFencedFence"),V(B)?X(g,C,"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(B):C(B)}function C(B){return B===o?(g.enter("codeFencedFenceSequence"),H(B)):R(B)}function H(B){return B===o?(U++,g.consume(B),H):U>=a?(g.exit("codeFencedFenceSequence"),V(B)?X(g,j,"whitespace")(B):j(B)):R(B)}function j(B){return B===null||M(B)?(g.exit("codeFencedFence"),L(B)):R(B)}}}function ll(e,n,t){const r=this;return i;function i(a){return a===null?t(a):(e.enter("lineEnding"),e.consume(a),e.exit("lineEnding"),l)}function l(a){return r.parser.lazy[r.now().line]?t(a):n(a)}}const An={name:"codeIndented",tokenize:ol},al={tokenize:ul,partial:!0};function ol(e,n,t){const r=this;return i;function i(c){return e.enter("codeIndented"),X(e,l,"linePrefix",4+1)(c)}function l(c){const p=r.events[r.events.length-1];return p&&p[1].type==="linePrefix"&&p[2].sliceSerialize(p[1],!0).length>=4?a(c):t(c)}function a(c){return c===null?u(c):M(c)?e.attempt(al,a,u)(c):(e.enter("codeFlowValue"),o(c))}function o(c){return c===null||M(c)?(e.exit("codeFlowValue"),a(c)):(e.consume(c),o)}function u(c){return e.exit("codeIndented"),n(c)}}function ul(e,n,t){const r=this;return i;function i(a){return r.parser.lazy[r.now().line]?t(a):M(a)?(e.enter("lineEnding"),e.consume(a),e.exit("lineEnding"),i):X(e,l,"linePrefix",4+1)(a)}function l(a){const o=r.events[r.events.length-1];return o&&o[1].type==="linePrefix"&&o[2].sliceSerialize(o[1],!0).length>=4?n(a):M(a)?i(a):t(a)}}const sl={name:"codeText",tokenize:hl,resolve:cl,previous:pl};function cl(e){let n=e.length-4,t=3,r,i;if((e[t][1].type==="lineEnding"||e[t][1].type==="space")&&(e[n][1].type==="lineEnding"||e[n][1].type==="space")){for(r=t;++r<n;)if(e[r][1].type==="codeTextData"){e[t][1].type="codeTextPadding",e[n][1].type="codeTextPadding",t+=2,n-=2;break}}for(r=t-1,n++;++r<=n;)i===void 0?r!==n&&e[r][1].type!=="lineEnding"&&(i=r):(r===n||e[r][1].type==="lineEnding")&&(e[i][1].type="codeTextData",r!==i+2&&(e[i][1].end=e[r-1][1].end,e.splice(i+2,r-i-2),n-=r-i-2,r=i+2),i=void 0);return e}function pl(e){return e!==96||this.events[this.events.length-1][1].type==="characterEscape"}function hl(e,n,t){let r=0,i,l;return a;function a(s){return e.enter("codeText"),e.enter("codeTextSequence"),o(s)}function o(s){return s===96?(e.consume(s),r++,o):(e.exit("codeTextSequence"),u(s))}function u(s){return s===null?t(s):s===32?(e.enter("space"),e.consume(s),e.exit("space"),u):s===96?(l=e.enter("codeTextSequence"),i=0,p(s)):M(s)?(e.enter("lineEnding"),e.consume(s),e.exit("lineEnding"),u):(e.enter("codeTextData"),c(s))}function c(s){return s===null||s===32||s===96||M(s)?(e.exit("codeTextData"),u(s)):(e.consume(s),c)}function p(s){return s===96?(e.consume(s),i++,p):i===r?(e.exit("codeTextSequence"),e.exit("codeText"),n(s)):(l.type="codeTextData",c(s))}}function tr(e){const n={};let t=-1,r,i,l,a,o,u,c;for(;++t<e.length;){for(;t in n;)t=n[t];if(r=e[t],t&&r[1].type==="chunkFlow"&&e[t-1][1].type==="listItemPrefix"&&(u=r[1]._tokenizer.events,l=0,l<u.length&&u[l][1].type==="lineEndingBlank"&&(l+=2),l<u.length&&u[l][1].type==="content"))for(;++l<u.length&&u[l][1].type!=="content";)u[l][1].type==="chunkText"&&(u[l][1]._isInFirstContentOfListItem=!0,l++);if(r[0]==="enter")r[1].contentType&&(Object.assign(n,fl(e,t)),t=n[t],c=!0);else if(r[1]._container){for(l=t,i=void 0;l--&&(a=e[l],a[1].type==="lineEnding"||a[1].type==="lineEndingBlank");)a[0]==="enter"&&(i&&(e[i][1].type="lineEndingBlank"),a[1].type="lineEnding",i=l);i&&(r[1].end=Object.assign({},e[i][1].start),o=e.slice(i,t),o.unshift(r),ke(e,i,t-i+1,o))}}return!c}function fl(e,n){const t=e[n][1],r=e[n][2];let i=n-1;const l=[],a=t._tokenizer||r.parser[t.contentType](t.start),o=a.events,u=[],c={};let p,s,d=-1,h=t,b=0,y=0;const S=[y];for(;h;){for(;e[++i][1]!==h;);l.push(i),h._tokenizer||(p=r.sliceStream(h),h.next||p.push(null),s&&a.defineSkip(h.start),h._isInFirstContentOfListItem&&(a._gfmTasklistFirstContentOfListItem=!0),a.write(p),h._isInFirstContentOfListItem&&(a._gfmTasklistFirstContentOfListItem=void 0)),s=h,h=h.next}for(h=t;++d<o.length;)o[d][0]==="exit"&&o[d-1][0]==="enter"&&o[d][1].type===o[d-1][1].type&&o[d][1].start.line!==o[d][1].end.line&&(y=d+1,S.push(y),h._tokenizer=void 0,h.previous=void 0,h=h.next);for(a.events=[],h?(h._tokenizer=void 0,h.previous=void 0):S.pop(),d=S.length;d--;){const x=o.slice(S[d],S[d+1]),v=l.pop();u.unshift([v,v+x.length-1]),ke(e,v,2,x)}for(d=-1;++d<u.length;)c[b+u[d][0]]=b+u[d][1],b+=u[d][1]-u[d][0]-1;return c}const ml={tokenize:yl,resolve:gl},dl={tokenize:xl,partial:!0};function gl(e){return tr(e),e}function yl(e,n){let t;return r;function r(o){return e.enter("content"),t=e.enter("chunkContent",{contentType:"content"}),i(o)}function i(o){return o===null?l(o):M(o)?e.check(dl,a,l)(o):(e.consume(o),i)}function l(o){return e.exit("chunkContent"),e.exit("content"),n(o)}function a(o){return e.consume(o),e.exit("chunkContent"),t.next=e.enter("chunkContent",{contentType:"content",previous:t}),t=t.next,i}}function xl(e,n,t){const r=this;return i;function i(a){return e.exit("chunkContent"),e.enter("lineEnding"),e.consume(a),e.exit("lineEnding"),X(e,l,"linePrefix")}function l(a){if(a===null||M(a))return t(a);const o=r.events[r.events.length-1];return!r.parser.constructs.disable.null.includes("codeIndented")&&o&&o[1].type==="linePrefix"&&o[2].sliceSerialize(o[1],!0).length>=4?n(a):e.interrupt(r.parser.constructs.flow,t,n)(a)}}function rr(e,n,t,r,i,l,a,o,u){const c=u||Number.POSITIVE_INFINITY;let p=0;return s;function s(x){return x===60?(e.enter(r),e.enter(i),e.enter(l),e.consume(x),e.exit(l),d):x===null||x===32||x===41||Mn(x)?t(x):(e.enter(r),e.enter(a),e.enter(o),e.enter("chunkString",{contentType:"string"}),y(x))}function d(x){return x===62?(e.enter(l),e.consume(x),e.exit(l),e.exit(i),e.exit(r),n):(e.enter(o),e.enter("chunkString",{contentType:"string"}),h(x))}function h(x){return x===62?(e.exit("chunkString"),e.exit(o),d(x)):x===null||x===60||M(x)?t(x):(e.consume(x),x===92?b:h)}function b(x){return x===60||x===62||x===92?(e.consume(x),h):h(x)}function y(x){return!p&&(x===null||x===41||ie(x))?(e.exit("chunkString"),e.exit(o),e.exit(a),e.exit(r),n(x)):p<c&&x===40?(e.consume(x),p++,y):x===41?(e.consume(x),p--,y):x===null||x===32||x===40||Mn(x)?t(x):(e.consume(x),x===92?S:y)}function S(x){return x===40||x===41||x===92?(e.consume(x),y):y(x)}}function ir(e,n,t,r,i,l){const a=this;let o=0,u;return c;function c(h){return e.enter(r),e.enter(i),e.consume(h),e.exit(i),e.enter(l),p}function p(h){return o>999||h===null||h===91||h===93&&!u||h===94&&!o&&"_hiddenFootnoteSupport"in a.parser.constructs?t(h):h===93?(e.exit(l),e.enter(i),e.consume(h),e.exit(i),e.exit(r),n):M(h)?(e.enter("lineEnding"),e.consume(h),e.exit("lineEnding"),p):(e.enter("chunkString",{contentType:"string"}),s(h))}function s(h){return h===null||h===91||h===93||M(h)||o++>999?(e.exit("chunkString"),p(h)):(e.consume(h),u||(u=!V(h)),h===92?d:s)}function d(h){return h===91||h===92||h===93?(e.consume(h),o++,s):s(h)}}function lr(e,n,t,r,i,l){let a;return o;function o(d){return d===34||d===39||d===40?(e.enter(r),e.enter(i),e.consume(d),e.exit(i),a=d===40?41:d,u):t(d)}function u(d){return d===a?(e.enter(i),e.consume(d),e.exit(i),e.exit(r),n):(e.enter(l),c(d))}function c(d){return d===a?(e.exit(l),u(a)):d===null?t(d):M(d)?(e.enter("lineEnding"),e.consume(d),e.exit("lineEnding"),X(e,c,"linePrefix")):(e.enter("chunkString",{contentType:"string"}),p(d))}function p(d){return d===a||d===null||M(d)?(e.exit("chunkString"),c(d)):(e.consume(d),d===92?s:p)}function s(d){return d===a||d===92?(e.consume(d),p):p(d)}}function Ge(e,n){let t;return r;function r(i){return M(i)?(e.enter("lineEnding"),e.consume(i),e.exit("lineEnding"),t=!0,r):V(i)?X(e,r,t?"linePrefix":"lineSuffix")(i):n(i)}}function Re(e){return e.replace(/[\t\n\r ]+/g," ").replace(/^ | $/g,"").toLowerCase().toUpperCase()}const kl={name:"definition",tokenize:wl},bl={tokenize:Sl,partial:!0};function wl(e,n,t){const r=this;let i;return l;function l(h){return e.enter("definition"),a(h)}function a(h){return ir.call(r,e,o,t,"definitionLabel","definitionLabelMarker","definitionLabelString")(h)}function o(h){return i=Re(r.sliceSerialize(r.events[r.events.length-1][1]).slice(1,-1)),h===58?(e.enter("definitionMarker"),e.consume(h),e.exit("definitionMarker"),u):t(h)}function u(h){return ie(h)?Ge(e,c)(h):c(h)}function c(h){return rr(e,p,t,"definitionDestination","definitionDestinationLiteral","definitionDestinationLiteralMarker","definitionDestinationRaw","definitionDestinationString")(h)}function p(h){return e.attempt(bl,s,s)(h)}function s(h){return V(h)?X(e,d,"whitespace")(h):d(h)}function d(h){return h===null||M(h)?(e.exit("definition"),r.parser.defined.push(i),n(h)):t(h)}}function Sl(e,n,t){return r;function r(o){return ie(o)?Ge(e,i)(o):t(o)}function i(o){return lr(e,l,t,"definitionTitle","definitionTitleMarker","definitionTitleString")(o)}function l(o){return V(o)?X(e,a,"whitespace")(o):a(o)}function a(o){return o===null||M(o)?n(o):t(o)}}const Al={name:"hardBreakEscape",tokenize:El};function El(e,n,t){return r;function r(l){return e.enter("hardBreakEscape"),e.consume(l),i}function i(l){return M(l)?(e.exit("hardBreakEscape"),n(l)):t(l)}}const Pl={name:"headingAtx",tokenize:Il,resolve:Cl};function Cl(e,n){let t=e.length-2,r=3,i,l;return e[r][1].type==="whitespace"&&(r+=2),t-2>r&&e[t][1].type==="whitespace"&&(t-=2),e[t][1].type==="atxHeadingSequence"&&(r===t-1||t-4>r&&e[t-2][1].type==="whitespace")&&(t-=r+1===t?2:4),t>r&&(i={type:"atxHeadingText",start:e[r][1].start,end:e[t][1].end},l={type:"chunkText",start:e[r][1].start,end:e[t][1].end,contentType:"text"},ke(e,r,t-r+1,[["enter",i,n],["enter",l,n],["exit",l,n],["exit",i,n]])),e}function Il(e,n,t){let r=0;return i;function i(p){return e.enter("atxHeading"),l(p)}function l(p){return e.enter("atxHeadingSequence"),a(p)}function a(p){return p===35&&r++<6?(e.consume(p),a):p===null||ie(p)?(e.exit("atxHeadingSequence"),o(p)):t(p)}function o(p){return p===35?(e.enter("atxHeadingSequence"),u(p)):p===null||M(p)?(e.exit("atxHeading"),n(p)):V(p)?X(e,o,"whitespace")(p):(e.enter("atxHeadingText"),c(p))}function u(p){return p===35?(e.consume(p),u):(e.exit("atxHeadingSequence"),o(p))}function c(p){return p===null||p===35||ie(p)?(e.exit("atxHeadingText"),o(p)):(e.consume(p),c)}}const Ll=["address","article","aside","base","basefont","blockquote","body","caption","center","col","colgroup","dd","details","dialog","dir","div","dl","dt","fieldset","figcaption","figure","footer","form","frame","frameset","h1","h2","h3","h4","h5","h6","head","header","hr","html","iframe","legend","li","link","main","menu","menuitem","nav","noframes","ol","optgroup","option","p","param","search","section","summary","table","tbody","td","tfoot","th","thead","title","tr","track","ul"],Pt=["pre","script","style","textarea"],vl={name:"htmlFlow",tokenize:Fl,resolveTo:Ol,concrete:!0},Tl={tokenize:Dl,partial:!0},Ml={tokenize:Nl,partial:!0};function Ol(e){let n=e.length;for(;n--&&!(e[n][0]==="enter"&&e[n][1].type==="htmlFlow"););return n>1&&e[n-2][1].type==="linePrefix"&&(e[n][1].start=e[n-2][1].start,e[n+1][1].start=e[n-2][1].start,e.splice(n-2,2)),e}function Fl(e,n,t){const r=this;let i,l,a,o,u;return c;function c(m){return p(m)}function p(m){return e.enter("htmlFlow"),e.enter("htmlFlowData"),e.consume(m),s}function s(m){return m===33?(e.consume(m),d):m===47?(e.consume(m),l=!0,y):m===63?(e.consume(m),i=3,r.interrupt?n:f):xe(m)?(e.consume(m),a=String.fromCharCode(m),S):t(m)}function d(m){return m===45?(e.consume(m),i=2,h):m===91?(e.consume(m),i=5,o=0,b):xe(m)?(e.consume(m),i=4,r.interrupt?n:f):t(m)}function h(m){return m===45?(e.consume(m),r.interrupt?n:f):t(m)}function b(m){const de="CDATA[";return m===de.charCodeAt(o++)?(e.consume(m),o===de.length?r.interrupt?n:C:b):t(m)}function y(m){return xe(m)?(e.consume(m),a=String.fromCharCode(m),S):t(m)}function S(m){if(m===null||m===47||m===62||ie(m)){const de=m===47,Oe=a.toLowerCase();return!de&&!l&&Pt.includes(Oe)?(i=1,r.interrupt?n(m):C(m)):Ll.includes(a.toLowerCase())?(i=6,de?(e.consume(m),x):r.interrupt?n(m):C(m)):(i=7,r.interrupt&&!r.parser.lazy[r.now().line]?t(m):l?v(m):P(m))}return m===45||oe(m)?(e.consume(m),a+=String.fromCharCode(m),S):t(m)}function x(m){return m===62?(e.consume(m),r.interrupt?n:C):t(m)}function v(m){return V(m)?(e.consume(m),v):J(m)}function P(m){return m===47?(e.consume(m),J):m===58||m===95||xe(m)?(e.consume(m),_):V(m)?(e.consume(m),P):J(m)}function _(m){return m===45||m===46||m===58||m===95||oe(m)?(e.consume(m),_):z(m)}function z(m){return m===61?(e.consume(m),g):V(m)?(e.consume(m),z):P(m)}function g(m){return m===null||m===60||m===61||m===62||m===96?t(m):m===34||m===39?(e.consume(m),u=m,L):V(m)?(e.consume(m),g):R(m)}function L(m){return m===u?(e.consume(m),u=null,U):m===null||M(m)?t(m):(e.consume(m),L)}function R(m){return m===null||m===34||m===39||m===47||m===60||m===61||m===62||m===96||ie(m)?z(m):(e.consume(m),R)}function U(m){return m===47||m===62||V(m)?P(m):t(m)}function J(m){return m===62?(e.consume(m),w):t(m)}function w(m){return m===null||M(m)?C(m):V(m)?(e.consume(m),w):t(m)}function C(m){return m===45&&i===2?(e.consume(m),ee):m===60&&i===1?(e.consume(m),ne):m===62&&i===4?(e.consume(m),me):m===63&&i===3?(e.consume(m),f):m===93&&i===5?(e.consume(m),be):M(m)&&(i===6||i===7)?(e.exit("htmlFlowData"),e.check(Tl,we,H)(m)):m===null||M(m)?(e.exit("htmlFlowData"),H(m)):(e.consume(m),C)}function H(m){return e.check(Ml,j,we)(m)}function j(m){return e.enter("lineEnding"),e.consume(m),e.exit("lineEnding"),B}function B(m){return m===null||M(m)?H(m):(e.enter("htmlFlowData"),C(m))}function ee(m){return m===45?(e.consume(m),f):C(m)}function ne(m){return m===47?(e.consume(m),a="",fe):C(m)}function fe(m){if(m===62){const de=a.toLowerCase();return Pt.includes(de)?(e.consume(m),me):C(m)}return xe(m)&&a.length<8?(e.consume(m),a+=String.fromCharCode(m),fe):C(m)}function be(m){return m===93?(e.consume(m),f):C(m)}function f(m){return m===62?(e.consume(m),me):m===45&&i===2?(e.consume(m),f):C(m)}function me(m){return m===null||M(m)?(e.exit("htmlFlowData"),we(m)):(e.consume(m),me)}function we(m){return e.exit("htmlFlow"),n(m)}}function Nl(e,n,t){const r=this;return i;function i(a){return M(a)?(e.enter("lineEnding"),e.consume(a),e.exit("lineEnding"),l):t(a)}function l(a){return r.parser.lazy[r.now().line]?t(a):n(a)}}function Dl(e,n,t){return r;function r(i){return e.enter("lineEnding"),e.consume(i),e.exit("lineEnding"),e.attempt(tn,n,t)}}const zl={name:"htmlText",tokenize:Rl};function Rl(e,n,t){const r=this;let i,l,a;return o;function o(f){return e.enter("htmlText"),e.enter("htmlTextData"),e.consume(f),u}function u(f){return f===33?(e.consume(f),c):f===47?(e.consume(f),z):f===63?(e.consume(f),P):xe(f)?(e.consume(f),R):t(f)}function c(f){return f===45?(e.consume(f),p):f===91?(e.consume(f),l=0,b):xe(f)?(e.consume(f),v):t(f)}function p(f){return f===45?(e.consume(f),h):t(f)}function s(f){return f===null?t(f):f===45?(e.consume(f),d):M(f)?(a=s,ne(f)):(e.consume(f),s)}function d(f){return f===45?(e.consume(f),h):s(f)}function h(f){return f===62?ee(f):f===45?d(f):s(f)}function b(f){const me="CDATA[";return f===me.charCodeAt(l++)?(e.consume(f),l===me.length?y:b):t(f)}function y(f){return f===null?t(f):f===93?(e.consume(f),S):M(f)?(a=y,ne(f)):(e.consume(f),y)}function S(f){return f===93?(e.consume(f),x):y(f)}function x(f){return f===62?ee(f):f===93?(e.consume(f),x):y(f)}function v(f){return f===null||f===62?ee(f):M(f)?(a=v,ne(f)):(e.consume(f),v)}function P(f){return f===null?t(f):f===63?(e.consume(f),_):M(f)?(a=P,ne(f)):(e.consume(f),P)}function _(f){return f===62?ee(f):P(f)}function z(f){return xe(f)?(e.consume(f),g):t(f)}function g(f){return f===45||oe(f)?(e.consume(f),g):L(f)}function L(f){return M(f)?(a=L,ne(f)):V(f)?(e.consume(f),L):ee(f)}function R(f){return f===45||oe(f)?(e.consume(f),R):f===47||f===62||ie(f)?U(f):t(f)}function U(f){return f===47?(e.consume(f),ee):f===58||f===95||xe(f)?(e.consume(f),J):M(f)?(a=U,ne(f)):V(f)?(e.consume(f),U):ee(f)}function J(f){return f===45||f===46||f===58||f===95||oe(f)?(e.consume(f),J):w(f)}function w(f){return f===61?(e.consume(f),C):M(f)?(a=w,ne(f)):V(f)?(e.consume(f),w):U(f)}function C(f){return f===null||f===60||f===61||f===62||f===96?t(f):f===34||f===39?(e.consume(f),i=f,H):M(f)?(a=C,ne(f)):V(f)?(e.consume(f),C):(e.consume(f),j)}function H(f){return f===i?(e.consume(f),i=void 0,B):f===null?t(f):M(f)?(a=H,ne(f)):(e.consume(f),H)}function j(f){return f===null||f===34||f===39||f===60||f===61||f===96?t(f):f===47||f===62||ie(f)?U(f):(e.consume(f),j)}function B(f){return f===47||f===62||ie(f)?U(f):t(f)}function ee(f){return f===62?(e.consume(f),e.exit("htmlTextData"),e.exit("htmlText"),n):t(f)}function ne(f){return e.exit("htmlTextData"),e.enter("lineEnding"),e.consume(f),e.exit("lineEnding"),fe}function fe(f){return V(f)?X(e,be,"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(f):be(f)}function be(f){return e.enter("htmlTextData"),a(f)}}const Un={name:"labelEnd",tokenize:$l,resolveTo:Hl,resolveAll:Ul},_l={tokenize:Vl},Bl={tokenize:ql},jl={tokenize:Wl};function Ul(e){let n=-1;for(;++n<e.length;){const t=e[n][1];(t.type==="labelImage"||t.type==="labelLink"||t.type==="labelEnd")&&(e.splice(n+1,t.type==="labelImage"?4:2),t.type="data",n++)}return e}function Hl(e,n){let t=e.length,r=0,i,l,a,o;for(;t--;)if(i=e[t][1],l){if(i.type==="link"||i.type==="labelLink"&&i._inactive)break;e[t][0]==="enter"&&i.type==="labelLink"&&(i._inactive=!0)}else if(a){if(e[t][0]==="enter"&&(i.type==="labelImage"||i.type==="labelLink")&&!i._balanced&&(l=t,i.type!=="labelLink")){r=2;break}}else i.type==="labelEnd"&&(a=t);const u={type:e[l][1].type==="labelLink"?"link":"image",start:Object.assign({},e[l][1].start),end:Object.assign({},e[e.length-1][1].end)},c={type:"label",start:Object.assign({},e[l][1].start),end:Object.assign({},e[a][1].end)},p={type:"labelText",start:Object.assign({},e[l+r+2][1].end),end:Object.assign({},e[a-2][1].start)};return o=[["enter",u,n],["enter",c,n]],o=ue(o,e.slice(l+1,l+r+3)),o=ue(o,[["enter",p,n]]),o=ue(o,Bn(n.parser.constructs.insideSpan.null,e.slice(l+r+4,a-3),n)),o=ue(o,[["exit",p,n],e[a-2],e[a-1],["exit",c,n]]),o=ue(o,e.slice(a+1)),o=ue(o,[["exit",u,n]]),ke(e,l,e.length,o),e}function $l(e,n,t){const r=this;let i=r.events.length,l,a;for(;i--;)if((r.events[i][1].type==="labelImage"||r.events[i][1].type==="labelLink")&&!r.events[i][1]._balanced){l=r.events[i][1];break}return o;function o(d){return l?l._inactive?s(d):(a=r.parser.defined.includes(Re(r.sliceSerialize({start:l.end,end:r.now()}))),e.enter("labelEnd"),e.enter("labelMarker"),e.consume(d),e.exit("labelMarker"),e.exit("labelEnd"),u):t(d)}function u(d){return d===40?e.attempt(_l,p,a?p:s)(d):d===91?e.attempt(Bl,p,a?c:s)(d):a?p(d):s(d)}function c(d){return e.attempt(jl,p,s)(d)}function p(d){return n(d)}function s(d){return l._balanced=!0,t(d)}}function Vl(e,n,t){return r;function r(s){return e.enter("resource"),e.enter("resourceMarker"),e.consume(s),e.exit("resourceMarker"),i}function i(s){return ie(s)?Ge(e,l)(s):l(s)}function l(s){return s===41?p(s):rr(e,a,o,"resourceDestination","resourceDestinationLiteral","resourceDestinationLiteralMarker","resourceDestinationRaw","resourceDestinationString",32)(s)}function a(s){return ie(s)?Ge(e,u)(s):p(s)}function o(s){return t(s)}function u(s){return s===34||s===39||s===40?lr(e,c,t,"resourceTitle","resourceTitleMarker","resourceTitleString")(s):p(s)}function c(s){return ie(s)?Ge(e,p)(s):p(s)}function p(s){return s===41?(e.enter("resourceMarker"),e.consume(s),e.exit("resourceMarker"),e.exit("resource"),n):t(s)}}function ql(e,n,t){const r=this;return i;function i(o){return ir.call(r,e,l,a,"reference","referenceMarker","referenceString")(o)}function l(o){return r.parser.defined.includes(Re(r.sliceSerialize(r.events[r.events.length-1][1]).slice(1,-1)))?n(o):t(o)}function a(o){return t(o)}}function Wl(e,n,t){return r;function r(l){return e.enter("reference"),e.enter("referenceMarker"),e.consume(l),e.exit("referenceMarker"),i}function i(l){return l===93?(e.enter("referenceMarker"),e.consume(l),e.exit("referenceMarker"),e.exit("reference"),n):t(l)}}const Ql={name:"labelStartImage",tokenize:Gl,resolveAll:Un.resolveAll};function Gl(e,n,t){const r=this;return i;function i(o){return e.enter("labelImage"),e.enter("labelImageMarker"),e.consume(o),e.exit("labelImageMarker"),l}function l(o){return o===91?(e.enter("labelMarker"),e.consume(o),e.exit("labelMarker"),e.exit("labelImage"),a):t(o)}function a(o){return o===94&&"_hiddenFootnoteSupport"in r.parser.constructs?t(o):n(o)}}const Xl={name:"labelStartLink",tokenize:Yl,resolveAll:Un.resolveAll};function Yl(e,n,t){const r=this;return i;function i(a){return e.enter("labelLink"),e.enter("labelMarker"),e.consume(a),e.exit("labelMarker"),e.exit("labelLink"),l}function l(a){return a===94&&"_hiddenFootnoteSupport"in r.parser.constructs?t(a):n(a)}}const En={name:"lineEnding",tokenize:Kl};function Kl(e,n){return t;function t(r){return e.enter("lineEnding"),e.consume(r),e.exit("lineEnding"),X(e,n,"linePrefix")}}const en={name:"thematicBreak",tokenize:Jl};function Jl(e,n,t){let r=0,i;return l;function l(c){return e.enter("thematicBreak"),a(c)}function a(c){return i=c,o(c)}function o(c){return c===i?(e.enter("thematicBreakSequence"),u(c)):r>=3&&(c===null||M(c))?(e.exit("thematicBreak"),n(c)):t(c)}function u(c){return c===i?(e.consume(c),r++,u):(e.exit("thematicBreakSequence"),V(c)?X(e,o,"whitespace")(c):o(c))}}const re={name:"list",tokenize:na,continuation:{tokenize:ta},exit:ia},Zl={tokenize:la,partial:!0},ea={tokenize:ra,partial:!0};function na(e,n,t){const r=this,i=r.events[r.events.length-1];let l=i&&i[1].type==="linePrefix"?i[2].sliceSerialize(i[1],!0).length:0,a=0;return o;function o(h){const b=r.containerState.type||(h===42||h===43||h===45?"listUnordered":"listOrdered");if(b==="listUnordered"?!r.containerState.marker||h===r.containerState.marker:On(h)){if(r.containerState.type||(r.containerState.type=b,e.enter(b,{_container:!0})),b==="listUnordered")return e.enter("listItemPrefix"),h===42||h===45?e.check(en,t,c)(h):c(h);if(!r.interrupt||h===49)return e.enter("listItemPrefix"),e.enter("listItemValue"),u(h)}return t(h)}function u(h){return On(h)&&++a<10?(e.consume(h),u):(!r.interrupt||a<2)&&(r.containerState.marker?h===r.containerState.marker:h===41||h===46)?(e.exit("listItemValue"),c(h)):t(h)}function c(h){return e.enter("listItemMarker"),e.consume(h),e.exit("listItemMarker"),r.containerState.marker=r.containerState.marker||h,e.check(tn,r.interrupt?t:p,e.attempt(Zl,d,s))}function p(h){return r.containerState.initialBlankLine=!0,l++,d(h)}function s(h){return V(h)?(e.enter("listItemPrefixWhitespace"),e.consume(h),e.exit("listItemPrefixWhitespace"),d):t(h)}function d(h){return r.containerState.size=l+r.sliceSerialize(e.exit("listItemPrefix"),!0).length,n(h)}}function ta(e,n,t){const r=this;return r.containerState._closeFlow=void 0,e.check(tn,i,l);function i(o){return r.containerState.furtherBlankLines=r.containerState.furtherBlankLines||r.containerState.initialBlankLine,X(e,n,"listItemIndent",r.containerState.size+1)(o)}function l(o){return r.containerState.furtherBlankLines||!V(o)?(r.containerState.furtherBlankLines=void 0,r.containerState.initialBlankLine=void 0,a(o)):(r.containerState.furtherBlankLines=void 0,r.containerState.initialBlankLine=void 0,e.attempt(ea,n,a)(o))}function a(o){return r.containerState._closeFlow=!0,r.interrupt=void 0,X(e,e.attempt(re,n,t),"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(o)}}function ra(e,n,t){const r=this;return X(e,i,"listItemIndent",r.containerState.size+1);function i(l){const a=r.events[r.events.length-1];return a&&a[1].type==="listItemIndent"&&a[2].sliceSerialize(a[1],!0).length===r.containerState.size?n(l):t(l)}}function ia(e){e.exit(this.containerState.type)}function la(e,n,t){const r=this;return X(e,i,"listItemPrefixWhitespace",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4+1);function i(l){const a=r.events[r.events.length-1];return!V(l)&&a&&a[1].type==="listItemPrefixWhitespace"?n(l):t(l)}}const Ct={name:"setextUnderline",tokenize:oa,resolveTo:aa};function aa(e,n){let t=e.length,r,i,l;for(;t--;)if(e[t][0]==="enter"){if(e[t][1].type==="content"){r=t;break}e[t][1].type==="paragraph"&&(i=t)}else e[t][1].type==="content"&&e.splice(t,1),!l&&e[t][1].type==="definition"&&(l=t);const a={type:"setextHeading",start:Object.assign({},e[i][1].start),end:Object.assign({},e[e.length-1][1].end)};return e[i][1].type="setextHeadingText",l?(e.splice(i,0,["enter",a,n]),e.splice(l+1,0,["exit",e[r][1],n]),e[r][1].end=Object.assign({},e[l][1].end)):e[r][1]=a,e.push(["exit",a,n]),e}function oa(e,n,t){const r=this;let i;return l;function l(c){let p=r.events.length,s;for(;p--;)if(r.events[p][1].type!=="lineEnding"&&r.events[p][1].type!=="linePrefix"&&r.events[p][1].type!=="content"){s=r.events[p][1].type==="paragraph";break}return!r.parser.lazy[r.now().line]&&(r.interrupt||s)?(e.enter("setextHeadingLine"),i=c,a(c)):t(c)}function a(c){return e.enter("setextHeadingLineSequence"),o(c)}function o(c){return c===i?(e.consume(c),o):(e.exit("setextHeadingLineSequence"),V(c)?X(e,u,"lineSuffix")(c):u(c))}function u(c){return c===null||M(c)?(e.exit("setextHeadingLine"),n(c)):t(c)}}const ua={tokenize:sa};function sa(e){const n=this,t=e.attempt(tn,r,e.attempt(this.parser.constructs.flowInitial,i,X(e,e.attempt(this.parser.constructs.flow,i,e.attempt(ml,i)),"linePrefix")));return t;function r(l){if(l===null){e.consume(l);return}return e.enter("lineEndingBlank"),e.consume(l),e.exit("lineEndingBlank"),n.currentConstruct=void 0,t}function i(l){if(l===null){e.consume(l);return}return e.enter("lineEnding"),e.consume(l),e.exit("lineEnding"),n.currentConstruct=void 0,t}}const ca={resolveAll:or()},pa=ar("string"),ha=ar("text");function ar(e){return{tokenize:n,resolveAll:or(e==="text"?fa:void 0)};function n(t){const r=this,i=this.parser.constructs[e],l=t.attempt(i,a,o);return a;function a(p){return c(p)?l(p):o(p)}function o(p){if(p===null){t.consume(p);return}return t.enter("data"),t.consume(p),u}function u(p){return c(p)?(t.exit("data"),l(p)):(t.consume(p),u)}function c(p){if(p===null)return!0;const s=i[p];let d=-1;if(s)for(;++d<s.length;){const h=s[d];if(!h.previous||h.previous.call(r,r.previous))return!0}return!1}}}function or(e){return n;function n(t,r){let i=-1,l;for(;++i<=t.length;)l===void 0?t[i]&&t[i][1].type==="data"&&(l=i,i++):(!t[i]||t[i][1].type!=="data")&&(i!==l+2&&(t[l][1].end=t[i-1][1].end,t.splice(l+2,i-l-2),i=l+2),l=void 0);return e?e(t,r):t}}function fa(e,n){let t=0;for(;++t<=e.length;)if((t===e.length||e[t][1].type==="lineEnding")&&e[t-1][1].type==="data"){const r=e[t-1][1],i=n.sliceStream(r);let l=i.length,a=-1,o=0,u;for(;l--;){const c=i[l];if(typeof c=="string"){for(a=c.length;c.charCodeAt(a-1)===32;)o++,a--;if(a)break;a=-1}else if(c===-2)u=!0,o++;else if(c!==-1){l++;break}}if(o){const c={type:t===e.length||u||o<2?"lineSuffix":"hardBreakTrailing",start:{line:r.end.line,column:r.end.column-o,offset:r.end.offset-o,_index:r.start._index+l,_bufferIndex:l?a:r.start._bufferIndex+a},end:Object.assign({},r.end)};r.end=Object.assign({},c.start),r.start.offset===r.end.offset?Object.assign(r,c):(e.splice(t,0,["enter",c,n],["exit",c,n]),t+=2)}t++}return e}function ma(e,n,t){let r=Object.assign(t?Object.assign({},t):{line:1,column:1,offset:0},{_index:0,_bufferIndex:-1});const i={},l=[];let a=[],o=[];const u={consume:v,enter:P,exit:_,attempt:L(z),check:L(g),interrupt:L(g,{interrupt:!0})},c={previous:null,code:null,containerState:{},events:[],parser:e,sliceStream:h,sliceSerialize:d,now:b,defineSkip:y,write:s};let p=n.tokenize.call(c,u);return n.resolveAll&&l.push(n),c;function s(w){return a=ue(a,w),S(),a[a.length-1]!==null?[]:(R(n,0),c.events=Bn(l,c.events,c),c.events)}function d(w,C){return ga(h(w),C)}function h(w){return da(a,w)}function b(){const{line:w,column:C,offset:H,_index:j,_bufferIndex:B}=r;return{line:w,column:C,offset:H,_index:j,_bufferIndex:B}}function y(w){i[w.line]=w.column,J()}function S(){let w;for(;r._index<a.length;){const C=a[r._index];if(typeof C=="string")for(w=r._index,r._bufferIndex<0&&(r._bufferIndex=0);r._index===w&&r._bufferIndex<C.length;)x(C.charCodeAt(r._bufferIndex));else x(C)}}function x(w){p=p(w)}function v(w){M(w)?(r.line++,r.column=1,r.offset+=w===-3?2:1,J()):w!==-1&&(r.column++,r.offset++),r._bufferIndex<0?r._index++:(r._bufferIndex++,r._bufferIndex===a[r._index].length&&(r._bufferIndex=-1,r._index++)),c.previous=w}function P(w,C){const H=C||{};return H.type=w,H.start=b(),c.events.push(["enter",H,c]),o.push(H),H}function _(w){const C=o.pop();return C.end=b(),c.events.push(["exit",C,c]),C}function z(w,C){R(w,C.from)}function g(w,C){C.restore()}function L(w,C){return H;function H(j,B,ee){let ne,fe,be,f;return Array.isArray(j)?we(j):"tokenize"in j?we([j]):me(j);function me(te){return Fe;function Fe(Ee){const Ne=Ee!==null&&te[Ee],De=Ee!==null&&te.null,dn=[...Array.isArray(Ne)?Ne:Ne?[Ne]:[],...Array.isArray(De)?De:De?[De]:[]];return we(dn)(Ee)}}function we(te){return ne=te,fe=0,te.length===0?ee:m(te[fe])}function m(te){return Fe;function Fe(Ee){return f=U(),be=te,te.partial||(c.currentConstruct=te),te.name&&c.parser.constructs.disable.null.includes(te.name)?Oe():te.tokenize.call(C?Object.assign(Object.create(c),C):c,u,de,Oe)(Ee)}}function de(te){return w(be,f),B}function Oe(te){return f.restore(),++fe<ne.length?m(ne[fe]):ee}}}function R(w,C){w.resolveAll&&!l.includes(w)&&l.push(w),w.resolve&&ke(c.events,C,c.events.length-C,w.resolve(c.events.slice(C),c)),w.resolveTo&&(c.events=w.resolveTo(c.events,c))}function U(){const w=b(),C=c.previous,H=c.currentConstruct,j=c.events.length,B=Array.from(o);return{restore:ee,from:j};function ee(){r=w,c.previous=C,c.currentConstruct=H,c.events.length=j,o=B,J()}}function J(){r.line in i&&r.column<2&&(r.column=i[r.line],r.offset+=i[r.line]-1)}}function da(e,n){const t=n.start._index,r=n.start._bufferIndex,i=n.end._index,l=n.end._bufferIndex;let a;if(t===i)a=[e[t].slice(r,l)];else{if(a=e.slice(t,i),r>-1){const o=a[0];typeof o=="string"?a[0]=o.slice(r):a.shift()}l>0&&a.push(e[i].slice(0,l))}return a}function ga(e,n){let t=-1;const r=[];let i;for(;++t<e.length;){const l=e[t];let a;if(typeof l=="string")a=l;else switch(l){case-5:{a="\r";break}case-4:{a=`
`;break}case-3:{a=`\r
`;break}case-2:{a=n?" ":"	";break}case-1:{if(!n&&i)continue;a=" ";break}default:a=String.fromCharCode(l)}i=l===-2,r.push(a)}return r.join("")}const ya={42:re,43:re,45:re,48:re,49:re,50:re,51:re,52:re,53:re,54:re,55:re,56:re,57:re,62:Zt},xa={91:kl},ka={[-2]:An,[-1]:An,32:An},ba={35:Pl,42:en,45:[Ct,en],60:vl,61:Ct,95:en,96:Et,126:Et},wa={38:nr,92:er},Sa={[-5]:En,[-4]:En,[-3]:En,33:Ql,38:nr,42:Fn,60:[Yi,zl],91:Xl,92:[Al,er],93:Un,95:Fn,96:sl},Aa={null:[Fn,ca]},Ea={null:[42,95]},Pa={null:[]},Ca=Object.freeze(Object.defineProperty({__proto__:null,attentionMarkers:Ea,contentInitial:xa,disable:Pa,document:ya,flow:ba,flowInitial:ka,insideSpan:Aa,string:wa,text:Sa},Symbol.toStringTag,{value:"Module"}));function Ia(e){const t=Ni([Ca,...(e||{}).extensions||[]]),r={defined:[],lazy:{},constructs:t,content:i($i),document:i(qi),flow:i(ua),string:i(pa),text:i(ha)};return r;function i(l){return a;function a(o){return ma(r,l,o)}}}const It=/[\0\t\n\r]/g;function La(){let e=1,n="",t=!0,r;return i;function i(l,a,o){const u=[];let c,p,s,d,h;for(l=n+l.toString(a),s=0,n="",t&&(l.charCodeAt(0)===65279&&s++,t=void 0);s<l.length;){if(It.lastIndex=s,c=It.exec(l),d=c&&c.index!==void 0?c.index:l.length,h=l.charCodeAt(d),!c){n=l.slice(s);break}if(h===10&&s===d&&r)u.push(-3),r=void 0;else switch(r&&(u.push(-5),r=void 0),s<d&&(u.push(l.slice(s,d)),e+=d-s),h){case 0:{u.push(65533),e++;break}case 9:{for(p=Math.ceil(e/4)*4,u.push(-2);e++<p;)u.push(-1);break}case 10:{u.push(-4),e=1;break}default:r=!0,e=1}s=d+1}return o&&(r&&u.push(-5),n&&u.push(n),u.push(null)),u}}function va(e){for(;!tr(e););return e}function ur(e,n){const t=Number.parseInt(e,n);return t<9||t===11||t>13&&t<32||t>126&&t<160||t>55295&&t<57344||t>64975&&t<65008||(t&65535)===65535||(t&65535)===65534||t>1114111?"�":String.fromCharCode(t)}const Ta=/\\([!-/:-@[-`{-~])|&(#(?:\d{1,7}|x[\da-f]{1,6})|[\da-z]{1,31});/gi;function Ma(e){return e.replace(Ta,Oa)}function Oa(e,n,t){if(n)return n;if(t.charCodeAt(0)===35){const i=t.charCodeAt(1),l=i===120||i===88;return ur(t.slice(l?2:1),l?16:10)}return jn(t)||e}const sr={}.hasOwnProperty,Fa=function(e,n,t){return typeof n!="string"&&(t=n,n=void 0),Na(t)(va(Ia(t).document().write(La()(e,n,!0))))};function Na(e){const n={transforms:[],canContainEols:["emphasis","fragment","heading","paragraph","strong"],enter:{autolink:o(et),autolinkProtocol:w,autolinkEmail:w,atxHeading:o(Kn),blockQuote:o(dn),characterEscape:w,characterReference:w,codeFenced:o(Yn),codeFencedFenceInfo:u,codeFencedFenceMeta:u,codeIndented:o(Yn,u),codeText:o(Nr,u),codeTextData:w,data:w,codeFlowValue:w,definition:o(Dr),definitionDestinationString:u,definitionLabelString:u,definitionTitleString:u,emphasis:o(zr),hardBreakEscape:o(Jn),hardBreakTrailing:o(Jn),htmlFlow:o(Zn,u),htmlFlowData:w,htmlText:o(Zn,u),htmlTextData:w,image:o(Rr),label:u,link:o(et),listItem:o(_r),listItemValue:b,listOrdered:o(nt,h),listUnordered:o(nt),paragraph:o(Br),reference:Oe,referenceString:u,resourceDestinationString:u,resourceTitleString:u,setextHeading:o(Kn),strong:o(jr),thematicBreak:o(Hr)},exit:{atxHeading:p(),atxHeadingSequence:L,autolink:p(),autolinkEmail:De,autolinkProtocol:Ne,blockQuote:p(),characterEscapeValue:C,characterReferenceMarkerHexadecimal:Fe,characterReferenceMarkerNumeric:Fe,characterReferenceValue:Ee,codeFenced:p(v),codeFencedFence:x,codeFencedFenceInfo:y,codeFencedFenceMeta:S,codeFlowValue:C,codeIndented:p(P),codeText:p(ne),codeTextData:C,data:C,definition:p(),definitionDestinationString:g,definitionLabelString:_,definitionTitleString:z,emphasis:p(),hardBreakEscape:p(j),hardBreakTrailing:p(j),htmlFlow:p(B),htmlFlowData:C,htmlText:p(ee),htmlTextData:C,image:p(be),label:me,labelText:f,lineEnding:H,link:p(fe),listItem:p(),listOrdered:p(),listUnordered:p(),paragraph:p(),referenceString:te,resourceDestinationString:we,resourceTitleString:m,resource:de,setextHeading:p(J),setextHeadingLineSequence:U,setextHeadingText:R,strong:p(),thematicBreak:p()}};cr(n,(e||{}).mdastExtensions||[]);const t={};return r;function r(k){let E={type:"root",children:[]};const T={stack:[E],tokenStack:[],config:n,enter:c,exit:s,buffer:u,resume:d,setData:l,getData:a},W=[];let Q=-1;for(;++Q<k.length;)if(k[Q][1].type==="listOrdered"||k[Q][1].type==="listUnordered")if(k[Q][0]==="enter")W.push(Q);else{const ge=W.pop();Q=i(k,ge,Q)}for(Q=-1;++Q<k.length;){const ge=n[k[Q][0]];sr.call(ge,k[Q][1].type)&&ge[k[Q][1].type].call(Object.assign({sliceSerialize:k[Q][2].sliceSerialize},T),k[Q][1])}if(T.tokenStack.length>0){const ge=T.tokenStack[T.tokenStack.length-1];(ge[1]||Lt).call(T,void 0,ge[0])}for(E.position={start:Ce(k.length>0?k[0][1].start:{line:1,column:1,offset:0}),end:Ce(k.length>0?k[k.length-2][1].end:{line:1,column:1,offset:0})},Q=-1;++Q<n.transforms.length;)E=n.transforms[Q](E)||E;return E}function i(k,E,T){let W=E-1,Q=-1,ge=!1,Pe,Se,Ve,qe;for(;++W<=T;){const Y=k[W];if(Y[1].type==="listUnordered"||Y[1].type==="listOrdered"||Y[1].type==="blockQuote"?(Y[0]==="enter"?Q++:Q--,qe=void 0):Y[1].type==="lineEndingBlank"?Y[0]==="enter"&&(Pe&&!qe&&!Q&&!Ve&&(Ve=W),qe=void 0):Y[1].type==="linePrefix"||Y[1].type==="listItemValue"||Y[1].type==="listItemMarker"||Y[1].type==="listItemPrefix"||Y[1].type==="listItemPrefixWhitespace"||(qe=void 0),!Q&&Y[0]==="enter"&&Y[1].type==="listItemPrefix"||Q===-1&&Y[0]==="exit"&&(Y[1].type==="listUnordered"||Y[1].type==="listOrdered")){if(Pe){let gn=W;for(Se=void 0;gn--;){const Ae=k[gn];if(Ae[1].type==="lineEnding"||Ae[1].type==="lineEndingBlank"){if(Ae[0]==="exit")continue;Se&&(k[Se][1].type="lineEndingBlank",ge=!0),Ae[1].type="lineEnding",Se=gn}else if(!(Ae[1].type==="linePrefix"||Ae[1].type==="blockQuotePrefix"||Ae[1].type==="blockQuotePrefixWhitespace"||Ae[1].type==="blockQuoteMarker"||Ae[1].type==="listItemIndent"))break}Ve&&(!Se||Ve<Se)&&(Pe._spread=!0),Pe.end=Object.assign({},Se?k[Se][1].start:Y[1].end),k.splice(Se||W,0,["exit",Pe,Y[2]]),W++,T++}Y[1].type==="listItemPrefix"&&(Pe={type:"listItem",_spread:!1,start:Object.assign({},Y[1].start),end:void 0},k.splice(W,0,["enter",Pe,Y[2]]),W++,T++,Ve=void 0,qe=!0)}}return k[E][1]._spread=ge,T}function l(k,E){t[k]=E}function a(k){return t[k]}function o(k,E){return T;function T(W){c.call(this,k(W),W),E&&E.call(this,W)}}function u(){this.stack.push({type:"fragment",children:[]})}function c(k,E,T){return this.stack[this.stack.length-1].children.push(k),this.stack.push(k),this.tokenStack.push([E,T]),k.position={start:Ce(E.start)},k}function p(k){return E;function E(T){k&&k.call(this,T),s.call(this,T)}}function s(k,E){const T=this.stack.pop(),W=this.tokenStack.pop();if(W)W[0].type!==k.type&&(E?E.call(this,k,W[0]):(W[1]||Lt).call(this,k,W[0]));else throw new Error("Cannot close `"+k.type+"` ("+Qe({start:k.start,end:k.end})+"): it’s not open");return T.position.end=Ce(k.end),T}function d(){return Oi(this.stack.pop())}function h(){l("expectingFirstListItemValue",!0)}function b(k){if(a("expectingFirstListItemValue")){const E=this.stack[this.stack.length-2];E.start=Number.parseInt(this.sliceSerialize(k),10),l("expectingFirstListItemValue")}}function y(){const k=this.resume(),E=this.stack[this.stack.length-1];E.lang=k}function S(){const k=this.resume(),E=this.stack[this.stack.length-1];E.meta=k}function x(){a("flowCodeInside")||(this.buffer(),l("flowCodeInside",!0))}function v(){const k=this.resume(),E=this.stack[this.stack.length-1];E.value=k.replace(/^(\r?\n|\r)|(\r?\n|\r)$/g,""),l("flowCodeInside")}function P(){const k=this.resume(),E=this.stack[this.stack.length-1];E.value=k.replace(/(\r?\n|\r)$/g,"")}function _(k){const E=this.resume(),T=this.stack[this.stack.length-1];T.label=E,T.identifier=Re(this.sliceSerialize(k)).toLowerCase()}function z(){const k=this.resume(),E=this.stack[this.stack.length-1];E.title=k}function g(){const k=this.resume(),E=this.stack[this.stack.length-1];E.url=k}function L(k){const E=this.stack[this.stack.length-1];if(!E.depth){const T=this.sliceSerialize(k).length;E.depth=T}}function R(){l("setextHeadingSlurpLineEnding",!0)}function U(k){const E=this.stack[this.stack.length-1];E.depth=this.sliceSerialize(k).charCodeAt(0)===61?1:2}function J(){l("setextHeadingSlurpLineEnding")}function w(k){const E=this.stack[this.stack.length-1];let T=E.children[E.children.length-1];(!T||T.type!=="text")&&(T=Ur(),T.position={start:Ce(k.start)},E.children.push(T)),this.stack.push(T)}function C(k){const E=this.stack.pop();E.value+=this.sliceSerialize(k),E.position.end=Ce(k.end)}function H(k){const E=this.stack[this.stack.length-1];if(a("atHardBreak")){const T=E.children[E.children.length-1];T.position.end=Ce(k.end),l("atHardBreak");return}!a("setextHeadingSlurpLineEnding")&&n.canContainEols.includes(E.type)&&(w.call(this,k),C.call(this,k))}function j(){l("atHardBreak",!0)}function B(){const k=this.resume(),E=this.stack[this.stack.length-1];E.value=k}function ee(){const k=this.resume(),E=this.stack[this.stack.length-1];E.value=k}function ne(){const k=this.resume(),E=this.stack[this.stack.length-1];E.value=k}function fe(){const k=this.stack[this.stack.length-1];if(a("inReference")){const E=a("referenceType")||"shortcut";k.type+="Reference",k.referenceType=E,delete k.url,delete k.title}else delete k.identifier,delete k.label;l("referenceType")}function be(){const k=this.stack[this.stack.length-1];if(a("inReference")){const E=a("referenceType")||"shortcut";k.type+="Reference",k.referenceType=E,delete k.url,delete k.title}else delete k.identifier,delete k.label;l("referenceType")}function f(k){const E=this.sliceSerialize(k),T=this.stack[this.stack.length-2];T.label=Ma(E),T.identifier=Re(E).toLowerCase()}function me(){const k=this.stack[this.stack.length-1],E=this.resume(),T=this.stack[this.stack.length-1];if(l("inReference",!0),T.type==="link"){const W=k.children;T.children=W}else T.alt=E}function we(){const k=this.resume(),E=this.stack[this.stack.length-1];E.url=k}function m(){const k=this.resume(),E=this.stack[this.stack.length-1];E.title=k}function de(){l("inReference")}function Oe(){l("referenceType","collapsed")}function te(k){const E=this.resume(),T=this.stack[this.stack.length-1];T.label=E,T.identifier=Re(this.sliceSerialize(k)).toLowerCase(),l("referenceType","full")}function Fe(k){l("characterReferenceType",k.type)}function Ee(k){const E=this.sliceSerialize(k),T=a("characterReferenceType");let W;T?(W=ur(E,T==="characterReferenceMarkerNumeric"?10:16),l("characterReferenceType")):W=jn(E);const Q=this.stack.pop();Q.value+=W,Q.position.end=Ce(k.end)}function Ne(k){C.call(this,k);const E=this.stack[this.stack.length-1];E.url=this.sliceSerialize(k)}function De(k){C.call(this,k);const E=this.stack[this.stack.length-1];E.url="mailto:"+this.sliceSerialize(k)}function dn(){return{type:"blockquote",children:[]}}function Yn(){return{type:"code",lang:null,meta:null,value:""}}function Nr(){return{type:"inlineCode",value:""}}function Dr(){return{type:"definition",identifier:"",label:null,title:null,url:""}}function zr(){return{type:"emphasis",children:[]}}function Kn(){return{type:"heading",depth:void 0,children:[]}}function Jn(){return{type:"break"}}function Zn(){return{type:"html",value:""}}function Rr(){return{type:"image",title:null,url:"",alt:null}}function et(){return{type:"link",title:null,url:"",children:[]}}function nt(k){return{type:"list",ordered:k.type==="listOrdered",start:null,spread:k._spread,children:[]}}function _r(k){return{type:"listItem",spread:k._spread,checked:null,children:[]}}function Br(){return{type:"paragraph",children:[]}}function jr(){return{type:"strong",children:[]}}function Ur(){return{type:"text",value:""}}function Hr(){return{type:"thematicBreak"}}}function Ce(e){return{line:e.line,column:e.column,offset:e.offset}}function cr(e,n){let t=-1;for(;++t<n.length;){const r=n[t];Array.isArray(r)?cr(e,r):Da(e,r)}}function Da(e,n){let t;for(t in n)if(sr.call(n,t)){if(t==="canContainEols"){const r=n[t];r&&e[t].push(...r)}else if(t==="transforms"){const r=n[t];r&&e[t].push(...r)}else if(t==="enter"||t==="exit"){const r=n[t];r&&Object.assign(e[t],r)}}}function Lt(e,n){throw e?new Error("Cannot close `"+e.type+"` ("+Qe({start:e.start,end:e.end})+"): a different token (`"+n.type+"`, "+Qe({start:n.start,end:n.end})+") is open"):new Error("Cannot close document, a token (`"+n.type+"`, "+Qe({start:n.start,end:n.end})+") is still open")}function za(e){Object.assign(this,{Parser:t=>{const r=this.data("settings");return Fa(t,Object.assign({},r,e,{extensions:this.data("micromarkExtensions")||[],mdastExtensions:this.data("fromMarkdownExtensions")||[]}))}})}function Ra(e,n){const t={type:"element",tagName:"blockquote",properties:{},children:e.wrap(e.all(n),!0)};return e.patch(n,t),e.applyData(n,t)}function _a(e,n){const t={type:"element",tagName:"br",properties:{},children:[]};return e.patch(n,t),[e.applyData(n,t),{type:"text",value:`
`}]}function Ba(e,n){const t=n.value?n.value+`
`:"",r=n.lang?n.lang.match(/^[^ \t]+(?=[ \t]|$)/):null,i={};r&&(i.className=["language-"+r]);let l={type:"element",tagName:"code",properties:i,children:[{type:"text",value:t}]};return n.meta&&(l.data={meta:n.meta}),e.patch(n,l),l=e.applyData(n,l),l={type:"element",tagName:"pre",properties:{},children:[l]},e.patch(n,l),l}function ja(e,n){const t={type:"element",tagName:"del",properties:{},children:e.all(n)};return e.patch(n,t),e.applyData(n,t)}function Ua(e,n){const t={type:"element",tagName:"em",properties:{},children:e.all(n)};return e.patch(n,t),e.applyData(n,t)}function Ue(e){const n=[];let t=-1,r=0,i=0;for(;++t<e.length;){const l=e.charCodeAt(t);let a="";if(l===37&&oe(e.charCodeAt(t+1))&&oe(e.charCodeAt(t+2)))i=2;else if(l<128)/[!#$&-;=?-Z_a-z~]/.test(String.fromCharCode(l))||(a=String.fromCharCode(l));else if(l>55295&&l<57344){const o=e.charCodeAt(t+1);l<56320&&o>56319&&o<57344?(a=String.fromCharCode(l,o),i=1):a="�"}else a=String.fromCharCode(l);a&&(n.push(e.slice(r,t),encodeURIComponent(a)),r=t+i+1,a=""),i&&(t+=i,i=0)}return n.join("")+e.slice(r)}function pr(e,n){const t=String(n.identifier).toUpperCase(),r=Ue(t.toLowerCase()),i=e.footnoteOrder.indexOf(t);let l;i===-1?(e.footnoteOrder.push(t),e.footnoteCounts[t]=1,l=e.footnoteOrder.length):(e.footnoteCounts[t]++,l=i+1);const a=e.footnoteCounts[t],o={type:"element",tagName:"a",properties:{href:"#"+e.clobberPrefix+"fn-"+r,id:e.clobberPrefix+"fnref-"+r+(a>1?"-"+a:""),dataFootnoteRef:!0,ariaDescribedBy:["footnote-label"]},children:[{type:"text",value:String(l)}]};e.patch(n,o);const u={type:"element",tagName:"sup",properties:{},children:[o]};return e.patch(n,u),e.applyData(n,u)}function Ha(e,n){const t=e.footnoteById;let r=1;for(;r in t;)r++;const i=String(r);return t[i]={type:"footnoteDefinition",identifier:i,children:[{type:"paragraph",children:n.children}],position:n.position},pr(e,{type:"footnoteReference",identifier:i,position:n.position})}function $a(e,n){const t={type:"element",tagName:"h"+n.depth,properties:{},children:e.all(n)};return e.patch(n,t),e.applyData(n,t)}function Va(e,n){if(e.dangerous){const t={type:"raw",value:n.value};return e.patch(n,t),e.applyData(n,t)}return null}function hr(e,n){const t=n.referenceType;let r="]";if(t==="collapsed"?r+="[]":t==="full"&&(r+="["+(n.label||n.identifier)+"]"),n.type==="imageReference")return{type:"text",value:"!["+n.alt+r};const i=e.all(n),l=i[0];l&&l.type==="text"?l.value="["+l.value:i.unshift({type:"text",value:"["});const a=i[i.length-1];return a&&a.type==="text"?a.value+=r:i.push({type:"text",value:r}),i}function qa(e,n){const t=e.definition(n.identifier);if(!t)return hr(e,n);const r={src:Ue(t.url||""),alt:n.alt};t.title!==null&&t.title!==void 0&&(r.title=t.title);const i={type:"element",tagName:"img",properties:r,children:[]};return e.patch(n,i),e.applyData(n,i)}function Wa(e,n){const t={src:Ue(n.url)};n.alt!==null&&n.alt!==void 0&&(t.alt=n.alt),n.title!==null&&n.title!==void 0&&(t.title=n.title);const r={type:"element",tagName:"img",properties:t,children:[]};return e.patch(n,r),e.applyData(n,r)}function Qa(e,n){const t={type:"text",value:n.value.replace(/\r?\n|\r/g," ")};e.patch(n,t);const r={type:"element",tagName:"code",properties:{},children:[t]};return e.patch(n,r),e.applyData(n,r)}function Ga(e,n){const t=e.definition(n.identifier);if(!t)return hr(e,n);const r={href:Ue(t.url||"")};t.title!==null&&t.title!==void 0&&(r.title=t.title);const i={type:"element",tagName:"a",properties:r,children:e.all(n)};return e.patch(n,i),e.applyData(n,i)}function Xa(e,n){const t={href:Ue(n.url)};n.title!==null&&n.title!==void 0&&(t.title=n.title);const r={type:"element",tagName:"a",properties:t,children:e.all(n)};return e.patch(n,r),e.applyData(n,r)}function Ya(e,n,t){const r=e.all(n),i=t?Ka(t):fr(n),l={},a=[];if(typeof n.checked=="boolean"){const p=r[0];let s;p&&p.type==="element"&&p.tagName==="p"?s=p:(s={type:"element",tagName:"p",properties:{},children:[]},r.unshift(s)),s.children.length>0&&s.children.unshift({type:"text",value:" "}),s.children.unshift({type:"element",tagName:"input",properties:{type:"checkbox",checked:n.checked,disabled:!0},children:[]}),l.className=["task-list-item"]}let o=-1;for(;++o<r.length;){const p=r[o];(i||o!==0||p.type!=="element"||p.tagName!=="p")&&a.push({type:"text",value:`
`}),p.type==="element"&&p.tagName==="p"&&!i?a.push(...p.children):a.push(p)}const u=r[r.length-1];u&&(i||u.type!=="element"||u.tagName!=="p")&&a.push({type:"text",value:`
`});const c={type:"element",tagName:"li",properties:l,children:a};return e.patch(n,c),e.applyData(n,c)}function Ka(e){let n=!1;if(e.type==="list"){n=e.spread||!1;const t=e.children;let r=-1;for(;!n&&++r<t.length;)n=fr(t[r])}return n}function fr(e){const n=e.spread;return n??e.children.length>1}function Ja(e,n){const t={},r=e.all(n);let i=-1;for(typeof n.start=="number"&&n.start!==1&&(t.start=n.start);++i<r.length;){const a=r[i];if(a.type==="element"&&a.tagName==="li"&&a.properties&&Array.isArray(a.properties.className)&&a.properties.className.includes("task-list-item")){t.className=["contains-task-list"];break}}const l={type:"element",tagName:n.ordered?"ol":"ul",properties:t,children:e.wrap(r,!0)};return e.patch(n,l),e.applyData(n,l)}function Za(e,n){const t={type:"element",tagName:"p",properties:{},children:e.all(n)};return e.patch(n,t),e.applyData(n,t)}function eo(e,n){const t={type:"root",children:e.wrap(e.all(n))};return e.patch(n,t),e.applyData(n,t)}function no(e,n){const t={type:"element",tagName:"strong",properties:{},children:e.all(n)};return e.patch(n,t),e.applyData(n,t)}const Hn=mr("start"),$n=mr("end");function to(e){return{start:Hn(e),end:$n(e)}}function mr(e){return n;function n(t){const r=t&&t.position&&t.position[e]||{};return{line:r.line||null,column:r.column||null,offset:r.offset>-1?r.offset:null}}}function ro(e,n){const t=e.all(n),r=t.shift(),i=[];if(r){const a={type:"element",tagName:"thead",properties:{},children:e.wrap([r],!0)};e.patch(n.children[0],a),i.push(a)}if(t.length>0){const a={type:"element",tagName:"tbody",properties:{},children:e.wrap(t,!0)},o=Hn(n.children[1]),u=$n(n.children[n.children.length-1]);o.line&&u.line&&(a.position={start:o,end:u}),i.push(a)}const l={type:"element",tagName:"table",properties:{},children:e.wrap(i,!0)};return e.patch(n,l),e.applyData(n,l)}function io(e,n,t){const r=t?t.children:void 0,l=(r?r.indexOf(n):1)===0?"th":"td",a=t&&t.type==="table"?t.align:void 0,o=a?a.length:n.children.length;let u=-1;const c=[];for(;++u<o;){const s=n.children[u],d={},h=a?a[u]:void 0;h&&(d.align=h);let b={type:"element",tagName:l,properties:d,children:[]};s&&(b.children=e.all(s),e.patch(s,b),b=e.applyData(n,b)),c.push(b)}const p={type:"element",tagName:"tr",properties:{},children:e.wrap(c,!0)};return e.patch(n,p),e.applyData(n,p)}function lo(e,n){const t={type:"element",tagName:"td",properties:{},children:e.all(n)};return e.patch(n,t),e.applyData(n,t)}const vt=9,Tt=32;function ao(e){const n=String(e),t=/\r?\n|\r/g;let r=t.exec(n),i=0;const l=[];for(;r;)l.push(Mt(n.slice(i,r.index),i>0,!0),r[0]),i=r.index+r[0].length,r=t.exec(n);return l.push(Mt(n.slice(i),i>0,!1)),l.join("")}function Mt(e,n,t){let r=0,i=e.length;if(n){let l=e.codePointAt(r);for(;l===vt||l===Tt;)r++,l=e.codePointAt(r)}if(t){let l=e.codePointAt(i-1);for(;l===vt||l===Tt;)i--,l=e.codePointAt(i-1)}return i>r?e.slice(r,i):""}function oo(e,n){const t={type:"text",value:ao(String(n.value))};return e.patch(n,t),e.applyData(n,t)}function uo(e,n){const t={type:"element",tagName:"hr",properties:{},children:[]};return e.patch(n,t),e.applyData(n,t)}const so={blockquote:Ra,break:_a,code:Ba,delete:ja,emphasis:Ua,footnoteReference:pr,footnote:Ha,heading:$a,html:Va,imageReference:qa,image:Wa,inlineCode:Qa,linkReference:Ga,link:Xa,listItem:Ya,list:Ja,paragraph:Za,root:eo,strong:no,table:ro,tableCell:lo,tableRow:io,text:oo,thematicBreak:uo,toml:Ke,yaml:Ke,definition:Ke,footnoteDefinition:Ke};function Ke(){return null}const dr=function(e){if(e==null)return fo;if(typeof e=="string")return ho(e);if(typeof e=="object")return Array.isArray(e)?co(e):po(e);if(typeof e=="function")return rn(e);throw new Error("Expected function, string, or object as test")};function co(e){const n=[];let t=-1;for(;++t<e.length;)n[t]=dr(e[t]);return rn(r);function r(...i){let l=-1;for(;++l<n.length;)if(n[l].call(this,...i))return!0;return!1}}function po(e){return rn(n);function n(t){let r;for(r in e)if(t[r]!==e[r])return!1;return!0}}function ho(e){return rn(n);function n(t){return t&&t.type===e}}function rn(e){return n;function n(t,...r){return!!(t&&typeof t=="object"&&"type"in t&&e.call(this,t,...r))}}function fo(){return!0}const mo=!0,Ot=!1,go="skip",yo=function(e,n,t,r){typeof n=="function"&&typeof t!="function"&&(r=t,t=n,n=null);const i=dr(n),l=r?-1:1;a(e,void 0,[])();function a(o,u,c){const p=o&&typeof o=="object"?o:{};if(typeof p.type=="string"){const d=typeof p.tagName=="string"?p.tagName:typeof p.name=="string"?p.name:void 0;Object.defineProperty(s,"name",{value:"node ("+(o.type+(d?"<"+d+">":""))+")"})}return s;function s(){let d=[],h,b,y;if((!n||i(o,u,c[c.length-1]||null))&&(d=xo(t(o,c)),d[0]===Ot))return d;if(o.children&&d[0]!==go)for(b=(r?o.children.length:-1)+l,y=c.concat(o);b>-1&&b<o.children.length;){if(h=a(o.children[b],b,y)(),h[0]===Ot)return h;b=typeof h[1]=="number"?h[1]:b+l}return d}}};function xo(e){return Array.isArray(e)?e:typeof e=="number"?[mo,e]:[e]}const Vn=function(e,n,t,r){typeof n=="function"&&typeof t!="function"&&(r=t,t=n,n=null),yo(e,n,i,r);function i(l,a){const o=a[a.length-1];return t(l,o?o.children.indexOf(l):null,o)}};function ko(e){return!e||!e.position||!e.position.start||!e.position.start.line||!e.position.start.column||!e.position.end||!e.position.end.line||!e.position.end.column}const Ft={}.hasOwnProperty;function bo(e){const n=Object.create(null);if(!e||!e.type)throw new Error("mdast-util-definitions expected node");return Vn(e,"definition",r=>{const i=Nt(r.identifier);i&&!Ft.call(n,i)&&(n[i]=r)}),t;function t(r){const i=Nt(r);return i&&Ft.call(n,i)?n[i]:null}}function Nt(e){return String(e||"").toUpperCase()}const nn={}.hasOwnProperty;function wo(e,n){const t=n||{},r=t.allowDangerousHtml||!1,i={};return a.dangerous=r,a.clobberPrefix=t.clobberPrefix===void 0||t.clobberPrefix===null?"user-content-":t.clobberPrefix,a.footnoteLabel=t.footnoteLabel||"Footnotes",a.footnoteLabelTagName=t.footnoteLabelTagName||"h2",a.footnoteLabelProperties=t.footnoteLabelProperties||{className:["sr-only"]},a.footnoteBackLabel=t.footnoteBackLabel||"Back to content",a.unknownHandler=t.unknownHandler,a.passThrough=t.passThrough,a.handlers={...so,...t.handlers},a.definition=bo(e),a.footnoteById=i,a.footnoteOrder=[],a.footnoteCounts={},a.patch=So,a.applyData=Ao,a.one=o,a.all=u,a.wrap=Po,a.augment=l,Vn(e,"footnoteDefinition",c=>{const p=String(c.identifier).toUpperCase();nn.call(i,p)||(i[p]=c)}),a;function l(c,p){if(c&&"data"in c&&c.data){const s=c.data;s.hName&&(p.type!=="element"&&(p={type:"element",tagName:"",properties:{},children:[]}),p.tagName=s.hName),p.type==="element"&&s.hProperties&&(p.properties={...p.properties,...s.hProperties}),"children"in p&&p.children&&s.hChildren&&(p.children=s.hChildren)}if(c){const s="type"in c?c:{position:c};ko(s)||(p.position={start:Hn(s),end:$n(s)})}return p}function a(c,p,s,d){return Array.isArray(s)&&(d=s,s={}),l(c,{type:"element",tagName:p,properties:s||{},children:d||[]})}function o(c,p){return gr(a,c,p)}function u(c){return qn(a,c)}}function So(e,n){e.position&&(n.position=to(e))}function Ao(e,n){let t=n;if(e&&e.data){const r=e.data.hName,i=e.data.hChildren,l=e.data.hProperties;typeof r=="string"&&(t.type==="element"?t.tagName=r:t={type:"element",tagName:r,properties:{},children:[]}),t.type==="element"&&l&&(t.properties={...t.properties,...l}),"children"in t&&t.children&&i!==null&&i!==void 0&&(t.children=i)}return t}function gr(e,n,t){const r=n&&n.type;if(!r)throw new Error("Expected node, got `"+n+"`");return nn.call(e.handlers,r)?e.handlers[r](e,n,t):e.passThrough&&e.passThrough.includes(r)?"children"in n?{...n,children:qn(e,n)}:n:e.unknownHandler?e.unknownHandler(e,n,t):Eo(e,n)}function qn(e,n){const t=[];if("children"in n){const r=n.children;let i=-1;for(;++i<r.length;){const l=gr(e,r[i],n);if(l){if(i&&r[i-1].type==="break"&&(!Array.isArray(l)&&l.type==="text"&&(l.value=l.value.replace(/^\s+/,"")),!Array.isArray(l)&&l.type==="element")){const a=l.children[0];a&&a.type==="text"&&(a.value=a.value.replace(/^\s+/,""))}Array.isArray(l)?t.push(...l):t.push(l)}}}return t}function Eo(e,n){const t=n.data||{},r="value"in n&&!(nn.call(t,"hProperties")||nn.call(t,"hChildren"))?{type:"text",value:n.value}:{type:"element",tagName:"div",properties:{},children:qn(e,n)};return e.patch(n,r),e.applyData(n,r)}function Po(e,n){const t=[];let r=-1;for(n&&t.push({type:"text",value:`
`});++r<e.length;)r&&t.push({type:"text",value:`
`}),t.push(e[r]);return n&&e.length>0&&t.push({type:"text",value:`
`}),t}function Co(e){const n=[];let t=-1;for(;++t<e.footnoteOrder.length;){const r=e.footnoteById[e.footnoteOrder[t]];if(!r)continue;const i=e.all(r),l=String(r.identifier).toUpperCase(),a=Ue(l.toLowerCase());let o=0;const u=[];for(;++o<=e.footnoteCounts[l];){const s={type:"element",tagName:"a",properties:{href:"#"+e.clobberPrefix+"fnref-"+a+(o>1?"-"+o:""),dataFootnoteBackref:!0,className:["data-footnote-backref"],ariaLabel:e.footnoteBackLabel},children:[{type:"text",value:"↩"}]};o>1&&s.children.push({type:"element",tagName:"sup",children:[{type:"text",value:String(o)}]}),u.length>0&&u.push({type:"text",value:" "}),u.push(s)}const c=i[i.length-1];if(c&&c.type==="element"&&c.tagName==="p"){const s=c.children[c.children.length-1];s&&s.type==="text"?s.value+=" ":c.children.push({type:"text",value:" "}),c.children.push(...u)}else i.push(...u);const p={type:"element",tagName:"li",properties:{id:e.clobberPrefix+"fn-"+a},children:e.wrap(i,!0)};e.patch(r,p),n.push(p)}if(n.length!==0)return{type:"element",tagName:"section",properties:{dataFootnotes:!0,className:["footnotes"]},children:[{type:"element",tagName:e.footnoteLabelTagName,properties:{...JSON.parse(JSON.stringify(e.footnoteLabelProperties)),id:"footnote-label"},children:[{type:"text",value:e.footnoteLabel}]},{type:"text",value:`
`},{type:"element",tagName:"ol",properties:{},children:e.wrap(n,!0)},{type:"text",value:`
`}]}}function yr(e,n){const t=wo(e,n),r=t.one(e,null),i=Co(t);return i&&r.children.push({type:"text",value:`
`},i),Array.isArray(r)?{type:"root",children:r}:r}const Io=function(e,n){return e&&"run"in e?vo(e,n):To(e||n)},Lo=Io;function vo(e,n){return(t,r,i)=>{e.run(yr(t,n),r,l=>{i(l)})}}function To(e){return n=>yr(n,e)}var xr={exports:{}},Mo="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED",Oo=Mo,Fo=Oo;function kr(){}function br(){}br.resetWarningCache=kr;var No=function(){function e(r,i,l,a,o,u){if(u!==Fo){var c=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw c.name="Invariant Violation",c}}e.isRequired=e;function n(){return e}var t={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:n,element:e,elementType:e,instanceOf:n,node:e,objectOf:n,oneOf:n,oneOfType:n,shape:n,exact:n,checkPropTypes:br,resetWarningCache:kr};return t.PropTypes=t,t};xr.exports=No();var Do=xr.exports;const O=je(Do);class Ye{constructor(n,t,r){this.property=n,this.normal=t,r&&(this.space=r)}}Ye.prototype.property={};Ye.prototype.normal={};Ye.prototype.space=null;function wr(e,n){const t={},r={};let i=-1;for(;++i<e.length;)Object.assign(t,e[i].property),Object.assign(r,e[i].normal);return new Ye(t,r,n)}function Nn(e){return e.toLowerCase()}class pe{constructor(n,t){this.property=n,this.attribute=t}}pe.prototype.space=null;pe.prototype.boolean=!1;pe.prototype.booleanish=!1;pe.prototype.overloadedBoolean=!1;pe.prototype.number=!1;pe.prototype.commaSeparated=!1;pe.prototype.spaceSeparated=!1;pe.prototype.commaOrSpaceSeparated=!1;pe.prototype.mustUseProperty=!1;pe.prototype.defined=!1;let zo=0;const N=Me(),Z=Me(),Sr=Me(),A=Me(),G=Me(),_e=Me(),le=Me();function Me(){return 2**++zo}const Dn=Object.freeze(Object.defineProperty({__proto__:null,boolean:N,booleanish:Z,commaOrSpaceSeparated:le,commaSeparated:_e,number:A,overloadedBoolean:Sr,spaceSeparated:G},Symbol.toStringTag,{value:"Module"})),Pn=Object.keys(Dn);class Wn extends pe{constructor(n,t,r,i){let l=-1;if(super(n,t),Dt(this,"space",i),typeof r=="number")for(;++l<Pn.length;){const a=Pn[l];Dt(this,Pn[l],(r&Dn[a])===Dn[a])}}}Wn.prototype.defined=!0;function Dt(e,n,t){t&&(e[n]=t)}const Ro={}.hasOwnProperty;function He(e){const n={},t={};let r;for(r in e.properties)if(Ro.call(e.properties,r)){const i=e.properties[r],l=new Wn(r,e.transform(e.attributes||{},r),i,e.space);e.mustUseProperty&&e.mustUseProperty.includes(r)&&(l.mustUseProperty=!0),n[r]=l,t[Nn(r)]=r,t[Nn(l.attribute)]=r}return new Ye(n,t,e.space)}const Ar=He({space:"xlink",transform(e,n){return"xlink:"+n.slice(5).toLowerCase()},properties:{xLinkActuate:null,xLinkArcRole:null,xLinkHref:null,xLinkRole:null,xLinkShow:null,xLinkTitle:null,xLinkType:null}}),Er=He({space:"xml",transform(e,n){return"xml:"+n.slice(3).toLowerCase()},properties:{xmlLang:null,xmlBase:null,xmlSpace:null}});function Pr(e,n){return n in e?e[n]:n}function Cr(e,n){return Pr(e,n.toLowerCase())}const Ir=He({space:"xmlns",attributes:{xmlnsxlink:"xmlns:xlink"},transform:Cr,properties:{xmlns:null,xmlnsXLink:null}}),Lr=He({transform(e,n){return n==="role"?n:"aria-"+n.slice(4).toLowerCase()},properties:{ariaActiveDescendant:null,ariaAtomic:Z,ariaAutoComplete:null,ariaBusy:Z,ariaChecked:Z,ariaColCount:A,ariaColIndex:A,ariaColSpan:A,ariaControls:G,ariaCurrent:null,ariaDescribedBy:G,ariaDetails:null,ariaDisabled:Z,ariaDropEffect:G,ariaErrorMessage:null,ariaExpanded:Z,ariaFlowTo:G,ariaGrabbed:Z,ariaHasPopup:null,ariaHidden:Z,ariaInvalid:null,ariaKeyShortcuts:null,ariaLabel:null,ariaLabelledBy:G,ariaLevel:A,ariaLive:null,ariaModal:Z,ariaMultiLine:Z,ariaMultiSelectable:Z,ariaOrientation:null,ariaOwns:G,ariaPlaceholder:null,ariaPosInSet:A,ariaPressed:Z,ariaReadOnly:Z,ariaRelevant:null,ariaRequired:Z,ariaRoleDescription:G,ariaRowCount:A,ariaRowIndex:A,ariaRowSpan:A,ariaSelected:Z,ariaSetSize:A,ariaSort:null,ariaValueMax:A,ariaValueMin:A,ariaValueNow:A,ariaValueText:null,role:null}}),_o=He({space:"html",attributes:{acceptcharset:"accept-charset",classname:"class",htmlfor:"for",httpequiv:"http-equiv"},transform:Cr,mustUseProperty:["checked","multiple","muted","selected"],properties:{abbr:null,accept:_e,acceptCharset:G,accessKey:G,action:null,allow:null,allowFullScreen:N,allowPaymentRequest:N,allowUserMedia:N,alt:null,as:null,async:N,autoCapitalize:null,autoComplete:G,autoFocus:N,autoPlay:N,blocking:G,capture:null,charSet:null,checked:N,cite:null,className:G,cols:A,colSpan:null,content:null,contentEditable:Z,controls:N,controlsList:G,coords:A|_e,crossOrigin:null,data:null,dateTime:null,decoding:null,default:N,defer:N,dir:null,dirName:null,disabled:N,download:Sr,draggable:Z,encType:null,enterKeyHint:null,fetchPriority:null,form:null,formAction:null,formEncType:null,formMethod:null,formNoValidate:N,formTarget:null,headers:G,height:A,hidden:N,high:A,href:null,hrefLang:null,htmlFor:G,httpEquiv:G,id:null,imageSizes:null,imageSrcSet:null,inert:N,inputMode:null,integrity:null,is:null,isMap:N,itemId:null,itemProp:G,itemRef:G,itemScope:N,itemType:G,kind:null,label:null,lang:null,language:null,list:null,loading:null,loop:N,low:A,manifest:null,max:null,maxLength:A,media:null,method:null,min:null,minLength:A,multiple:N,muted:N,name:null,nonce:null,noModule:N,noValidate:N,onAbort:null,onAfterPrint:null,onAuxClick:null,onBeforeMatch:null,onBeforePrint:null,onBeforeToggle:null,onBeforeUnload:null,onBlur:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onContextLost:null,onContextMenu:null,onContextRestored:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnded:null,onError:null,onFocus:null,onFormData:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLanguageChange:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadEnd:null,onLoadStart:null,onMessage:null,onMessageError:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRejectionHandled:null,onReset:null,onResize:null,onScroll:null,onScrollEnd:null,onSecurityPolicyViolation:null,onSeeked:null,onSeeking:null,onSelect:null,onSlotChange:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnhandledRejection:null,onUnload:null,onVolumeChange:null,onWaiting:null,onWheel:null,open:N,optimum:A,pattern:null,ping:G,placeholder:null,playsInline:N,popover:null,popoverTarget:null,popoverTargetAction:null,poster:null,preload:null,readOnly:N,referrerPolicy:null,rel:G,required:N,reversed:N,rows:A,rowSpan:A,sandbox:G,scope:null,scoped:N,seamless:N,selected:N,shadowRootClonable:N,shadowRootDelegatesFocus:N,shadowRootMode:null,shape:null,size:A,sizes:null,slot:null,span:A,spellCheck:Z,src:null,srcDoc:null,srcLang:null,srcSet:null,start:A,step:null,style:null,tabIndex:A,target:null,title:null,translate:null,type:null,typeMustMatch:N,useMap:null,value:Z,width:A,wrap:null,writingSuggestions:null,align:null,aLink:null,archive:G,axis:null,background:null,bgColor:null,border:A,borderColor:null,bottomMargin:A,cellPadding:null,cellSpacing:null,char:null,charOff:null,classId:null,clear:null,code:null,codeBase:null,codeType:null,color:null,compact:N,declare:N,event:null,face:null,frame:null,frameBorder:null,hSpace:A,leftMargin:A,link:null,longDesc:null,lowSrc:null,marginHeight:A,marginWidth:A,noResize:N,noHref:N,noShade:N,noWrap:N,object:null,profile:null,prompt:null,rev:null,rightMargin:A,rules:null,scheme:null,scrolling:Z,standby:null,summary:null,text:null,topMargin:A,valueType:null,version:null,vAlign:null,vLink:null,vSpace:A,allowTransparency:null,autoCorrect:null,autoSave:null,disablePictureInPicture:N,disableRemotePlayback:N,prefix:null,property:null,results:A,security:null,unselectable:null}}),Bo=He({space:"svg",attributes:{accentHeight:"accent-height",alignmentBaseline:"alignment-baseline",arabicForm:"arabic-form",baselineShift:"baseline-shift",capHeight:"cap-height",className:"class",clipPath:"clip-path",clipRule:"clip-rule",colorInterpolation:"color-interpolation",colorInterpolationFilters:"color-interpolation-filters",colorProfile:"color-profile",colorRendering:"color-rendering",crossOrigin:"crossorigin",dataType:"datatype",dominantBaseline:"dominant-baseline",enableBackground:"enable-background",fillOpacity:"fill-opacity",fillRule:"fill-rule",floodColor:"flood-color",floodOpacity:"flood-opacity",fontFamily:"font-family",fontSize:"font-size",fontSizeAdjust:"font-size-adjust",fontStretch:"font-stretch",fontStyle:"font-style",fontVariant:"font-variant",fontWeight:"font-weight",glyphName:"glyph-name",glyphOrientationHorizontal:"glyph-orientation-horizontal",glyphOrientationVertical:"glyph-orientation-vertical",hrefLang:"hreflang",horizAdvX:"horiz-adv-x",horizOriginX:"horiz-origin-x",horizOriginY:"horiz-origin-y",imageRendering:"image-rendering",letterSpacing:"letter-spacing",lightingColor:"lighting-color",markerEnd:"marker-end",markerMid:"marker-mid",markerStart:"marker-start",navDown:"nav-down",navDownLeft:"nav-down-left",navDownRight:"nav-down-right",navLeft:"nav-left",navNext:"nav-next",navPrev:"nav-prev",navRight:"nav-right",navUp:"nav-up",navUpLeft:"nav-up-left",navUpRight:"nav-up-right",onAbort:"onabort",onActivate:"onactivate",onAfterPrint:"onafterprint",onBeforePrint:"onbeforeprint",onBegin:"onbegin",onCancel:"oncancel",onCanPlay:"oncanplay",onCanPlayThrough:"oncanplaythrough",onChange:"onchange",onClick:"onclick",onClose:"onclose",onCopy:"oncopy",onCueChange:"oncuechange",onCut:"oncut",onDblClick:"ondblclick",onDrag:"ondrag",onDragEnd:"ondragend",onDragEnter:"ondragenter",onDragExit:"ondragexit",onDragLeave:"ondragleave",onDragOver:"ondragover",onDragStart:"ondragstart",onDrop:"ondrop",onDurationChange:"ondurationchange",onEmptied:"onemptied",onEnd:"onend",onEnded:"onended",onError:"onerror",onFocus:"onfocus",onFocusIn:"onfocusin",onFocusOut:"onfocusout",onHashChange:"onhashchange",onInput:"oninput",onInvalid:"oninvalid",onKeyDown:"onkeydown",onKeyPress:"onkeypress",onKeyUp:"onkeyup",onLoad:"onload",onLoadedData:"onloadeddata",onLoadedMetadata:"onloadedmetadata",onLoadStart:"onloadstart",onMessage:"onmessage",onMouseDown:"onmousedown",onMouseEnter:"onmouseenter",onMouseLeave:"onmouseleave",onMouseMove:"onmousemove",onMouseOut:"onmouseout",onMouseOver:"onmouseover",onMouseUp:"onmouseup",onMouseWheel:"onmousewheel",onOffline:"onoffline",onOnline:"ononline",onPageHide:"onpagehide",onPageShow:"onpageshow",onPaste:"onpaste",onPause:"onpause",onPlay:"onplay",onPlaying:"onplaying",onPopState:"onpopstate",onProgress:"onprogress",onRateChange:"onratechange",onRepeat:"onrepeat",onReset:"onreset",onResize:"onresize",onScroll:"onscroll",onSeeked:"onseeked",onSeeking:"onseeking",onSelect:"onselect",onShow:"onshow",onStalled:"onstalled",onStorage:"onstorage",onSubmit:"onsubmit",onSuspend:"onsuspend",onTimeUpdate:"ontimeupdate",onToggle:"ontoggle",onUnload:"onunload",onVolumeChange:"onvolumechange",onWaiting:"onwaiting",onZoom:"onzoom",overlinePosition:"overline-position",overlineThickness:"overline-thickness",paintOrder:"paint-order",panose1:"panose-1",pointerEvents:"pointer-events",referrerPolicy:"referrerpolicy",renderingIntent:"rendering-intent",shapeRendering:"shape-rendering",stopColor:"stop-color",stopOpacity:"stop-opacity",strikethroughPosition:"strikethrough-position",strikethroughThickness:"strikethrough-thickness",strokeDashArray:"stroke-dasharray",strokeDashOffset:"stroke-dashoffset",strokeLineCap:"stroke-linecap",strokeLineJoin:"stroke-linejoin",strokeMiterLimit:"stroke-miterlimit",strokeOpacity:"stroke-opacity",strokeWidth:"stroke-width",tabIndex:"tabindex",textAnchor:"text-anchor",textDecoration:"text-decoration",textRendering:"text-rendering",transformOrigin:"transform-origin",typeOf:"typeof",underlinePosition:"underline-position",underlineThickness:"underline-thickness",unicodeBidi:"unicode-bidi",unicodeRange:"unicode-range",unitsPerEm:"units-per-em",vAlphabetic:"v-alphabetic",vHanging:"v-hanging",vIdeographic:"v-ideographic",vMathematical:"v-mathematical",vectorEffect:"vector-effect",vertAdvY:"vert-adv-y",vertOriginX:"vert-origin-x",vertOriginY:"vert-origin-y",wordSpacing:"word-spacing",writingMode:"writing-mode",xHeight:"x-height",playbackOrder:"playbackorder",timelineBegin:"timelinebegin"},transform:Pr,properties:{about:le,accentHeight:A,accumulate:null,additive:null,alignmentBaseline:null,alphabetic:A,amplitude:A,arabicForm:null,ascent:A,attributeName:null,attributeType:null,azimuth:A,bandwidth:null,baselineShift:null,baseFrequency:null,baseProfile:null,bbox:null,begin:null,bias:A,by:null,calcMode:null,capHeight:A,className:G,clip:null,clipPath:null,clipPathUnits:null,clipRule:null,color:null,colorInterpolation:null,colorInterpolationFilters:null,colorProfile:null,colorRendering:null,content:null,contentScriptType:null,contentStyleType:null,crossOrigin:null,cursor:null,cx:null,cy:null,d:null,dataType:null,defaultAction:null,descent:A,diffuseConstant:A,direction:null,display:null,dur:null,divisor:A,dominantBaseline:null,download:N,dx:null,dy:null,edgeMode:null,editable:null,elevation:A,enableBackground:null,end:null,event:null,exponent:A,externalResourcesRequired:null,fill:null,fillOpacity:A,fillRule:null,filter:null,filterRes:null,filterUnits:null,floodColor:null,floodOpacity:null,focusable:null,focusHighlight:null,fontFamily:null,fontSize:null,fontSizeAdjust:null,fontStretch:null,fontStyle:null,fontVariant:null,fontWeight:null,format:null,fr:null,from:null,fx:null,fy:null,g1:_e,g2:_e,glyphName:_e,glyphOrientationHorizontal:null,glyphOrientationVertical:null,glyphRef:null,gradientTransform:null,gradientUnits:null,handler:null,hanging:A,hatchContentUnits:null,hatchUnits:null,height:null,href:null,hrefLang:null,horizAdvX:A,horizOriginX:A,horizOriginY:A,id:null,ideographic:A,imageRendering:null,initialVisibility:null,in:null,in2:null,intercept:A,k:A,k1:A,k2:A,k3:A,k4:A,kernelMatrix:le,kernelUnitLength:null,keyPoints:null,keySplines:null,keyTimes:null,kerning:null,lang:null,lengthAdjust:null,letterSpacing:null,lightingColor:null,limitingConeAngle:A,local:null,markerEnd:null,markerMid:null,markerStart:null,markerHeight:null,markerUnits:null,markerWidth:null,mask:null,maskContentUnits:null,maskUnits:null,mathematical:null,max:null,media:null,mediaCharacterEncoding:null,mediaContentEncodings:null,mediaSize:A,mediaTime:null,method:null,min:null,mode:null,name:null,navDown:null,navDownLeft:null,navDownRight:null,navLeft:null,navNext:null,navPrev:null,navRight:null,navUp:null,navUpLeft:null,navUpRight:null,numOctaves:null,observer:null,offset:null,onAbort:null,onActivate:null,onAfterPrint:null,onBeforePrint:null,onBegin:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnd:null,onEnded:null,onError:null,onFocus:null,onFocusIn:null,onFocusOut:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadStart:null,onMessage:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onMouseWheel:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRepeat:null,onReset:null,onResize:null,onScroll:null,onSeeked:null,onSeeking:null,onSelect:null,onShow:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnload:null,onVolumeChange:null,onWaiting:null,onZoom:null,opacity:null,operator:null,order:null,orient:null,orientation:null,origin:null,overflow:null,overlay:null,overlinePosition:A,overlineThickness:A,paintOrder:null,panose1:null,path:null,pathLength:A,patternContentUnits:null,patternTransform:null,patternUnits:null,phase:null,ping:G,pitch:null,playbackOrder:null,pointerEvents:null,points:null,pointsAtX:A,pointsAtY:A,pointsAtZ:A,preserveAlpha:null,preserveAspectRatio:null,primitiveUnits:null,propagate:null,property:le,r:null,radius:null,referrerPolicy:null,refX:null,refY:null,rel:le,rev:le,renderingIntent:null,repeatCount:null,repeatDur:null,requiredExtensions:le,requiredFeatures:le,requiredFonts:le,requiredFormats:le,resource:null,restart:null,result:null,rotate:null,rx:null,ry:null,scale:null,seed:null,shapeRendering:null,side:null,slope:null,snapshotTime:null,specularConstant:A,specularExponent:A,spreadMethod:null,spacing:null,startOffset:null,stdDeviation:null,stemh:null,stemv:null,stitchTiles:null,stopColor:null,stopOpacity:null,strikethroughPosition:A,strikethroughThickness:A,string:null,stroke:null,strokeDashArray:le,strokeDashOffset:null,strokeLineCap:null,strokeLineJoin:null,strokeMiterLimit:A,strokeOpacity:A,strokeWidth:null,style:null,surfaceScale:A,syncBehavior:null,syncBehaviorDefault:null,syncMaster:null,syncTolerance:null,syncToleranceDefault:null,systemLanguage:le,tabIndex:A,tableValues:null,target:null,targetX:A,targetY:A,textAnchor:null,textDecoration:null,textRendering:null,textLength:null,timelineBegin:null,title:null,transformBehavior:null,type:null,typeOf:le,to:null,transform:null,transformOrigin:null,u1:null,u2:null,underlinePosition:A,underlineThickness:A,unicode:null,unicodeBidi:null,unicodeRange:null,unitsPerEm:A,values:null,vAlphabetic:A,vMathematical:A,vectorEffect:null,vHanging:A,vIdeographic:A,version:null,vertAdvY:A,vertOriginX:A,vertOriginY:A,viewBox:null,viewTarget:null,visibility:null,width:null,widths:null,wordSpacing:null,writingMode:null,x:null,x1:null,x2:null,xChannelSelector:null,xHeight:A,y:null,y1:null,y2:null,yChannelSelector:null,z:null,zoomAndPan:null}}),jo=/^data[-\w.:]+$/i,zt=/-[a-z]/g,Uo=/[A-Z]/g;function Ho(e,n){const t=Nn(n);let r=n,i=pe;if(t in e.normal)return e.property[e.normal[t]];if(t.length>4&&t.slice(0,4)==="data"&&jo.test(n)){if(n.charAt(4)==="-"){const l=n.slice(5).replace(zt,Vo);r="data"+l.charAt(0).toUpperCase()+l.slice(1)}else{const l=n.slice(4);if(!zt.test(l)){let a=l.replace(Uo,$o);a.charAt(0)!=="-"&&(a="-"+a),n="data"+a}}i=Wn}return new i(r,n)}function $o(e){return"-"+e.toLowerCase()}function Vo(e){return e.charAt(1).toUpperCase()}const Rt={classId:"classID",dataType:"datatype",itemId:"itemID",strokeDashArray:"strokeDasharray",strokeDashOffset:"strokeDashoffset",strokeLineCap:"strokeLinecap",strokeLineJoin:"strokeLinejoin",strokeMiterLimit:"strokeMiterlimit",typeOf:"typeof",xLinkActuate:"xlinkActuate",xLinkArcRole:"xlinkArcrole",xLinkHref:"xlinkHref",xLinkRole:"xlinkRole",xLinkShow:"xlinkShow",xLinkTitle:"xlinkTitle",xLinkType:"xlinkType",xmlnsXLink:"xmlnsXlink"},qo=wr([Er,Ar,Ir,Lr,_o],"html"),Wo=wr([Er,Ar,Ir,Lr,Bo],"svg");function Qo(e){if(e.allowedElements&&e.disallowedElements)throw new TypeError("Only one of `allowedElements` and `disallowedElements` should be defined");if(e.allowedElements||e.disallowedElements||e.allowElement)return n=>{Vn(n,"element",(t,r,i)=>{const l=i;let a;if(e.allowedElements?a=!e.allowedElements.includes(t.tagName):e.disallowedElements&&(a=e.disallowedElements.includes(t.tagName)),!a&&e.allowElement&&typeof r=="number"&&(a=!e.allowElement(t,r,l)),a&&typeof r=="number")return e.unwrapDisallowed&&t.children?l.children.splice(r,1,...t.children):l.children.splice(r,1),r})}}var vr={exports:{}},q={};/**
 * @license React
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Qn=Symbol.for("react.element"),Gn=Symbol.for("react.portal"),ln=Symbol.for("react.fragment"),an=Symbol.for("react.strict_mode"),on=Symbol.for("react.profiler"),un=Symbol.for("react.provider"),sn=Symbol.for("react.context"),Go=Symbol.for("react.server_context"),cn=Symbol.for("react.forward_ref"),pn=Symbol.for("react.suspense"),hn=Symbol.for("react.suspense_list"),fn=Symbol.for("react.memo"),mn=Symbol.for("react.lazy"),Xo=Symbol.for("react.offscreen"),Tr;Tr=Symbol.for("react.module.reference");function he(e){if(typeof e=="object"&&e!==null){var n=e.$$typeof;switch(n){case Qn:switch(e=e.type,e){case ln:case on:case an:case pn:case hn:return e;default:switch(e=e&&e.$$typeof,e){case Go:case sn:case cn:case mn:case fn:case un:return e;default:return n}}case Gn:return n}}}q.ContextConsumer=sn;q.ContextProvider=un;q.Element=Qn;q.ForwardRef=cn;q.Fragment=ln;q.Lazy=mn;q.Memo=fn;q.Portal=Gn;q.Profiler=on;q.StrictMode=an;q.Suspense=pn;q.SuspenseList=hn;q.isAsyncMode=function(){return!1};q.isConcurrentMode=function(){return!1};q.isContextConsumer=function(e){return he(e)===sn};q.isContextProvider=function(e){return he(e)===un};q.isElement=function(e){return typeof e=="object"&&e!==null&&e.$$typeof===Qn};q.isForwardRef=function(e){return he(e)===cn};q.isFragment=function(e){return he(e)===ln};q.isLazy=function(e){return he(e)===mn};q.isMemo=function(e){return he(e)===fn};q.isPortal=function(e){return he(e)===Gn};q.isProfiler=function(e){return he(e)===on};q.isStrictMode=function(e){return he(e)===an};q.isSuspense=function(e){return he(e)===pn};q.isSuspenseList=function(e){return he(e)===hn};q.isValidElementType=function(e){return typeof e=="string"||typeof e=="function"||e===ln||e===on||e===an||e===pn||e===hn||e===Xo||typeof e=="object"&&e!==null&&(e.$$typeof===mn||e.$$typeof===fn||e.$$typeof===un||e.$$typeof===sn||e.$$typeof===cn||e.$$typeof===Tr||e.getModuleId!==void 0)};q.typeOf=he;vr.exports=q;var Yo=vr.exports;const Ko=je(Yo);function Jo(e){const n=e&&typeof e=="object"&&e.type==="text"?e.value||"":e;return typeof n=="string"&&n.replace(/[ \t\n\f\r]/g,"")===""}function Zo(e){return e.join(" ").trim()}function eu(e,n){const t=n||{};return(e[e.length-1]===""?[...e,""]:e).join((t.padRight?" ":"")+","+(t.padLeft===!1?"":" ")).trim()}var Xn={exports:{}},_t=/\/\*[^*]*\*+([^/*][^*]*\*+)*\//g,nu=/\n/g,tu=/^\s*/,ru=/^(\*?[-#/*\\\w]+(\[[0-9a-z_-]+\])?)\s*/,iu=/^:\s*/,lu=/^((?:'(?:\\'|.)*?'|"(?:\\"|.)*?"|\([^)]*?\)|[^};])+)/,au=/^[;\s]*/,ou=/^\s+|\s+$/g,uu=`
`,Bt="/",jt="*",ve="",su="comment",cu="declaration",pu=function(e,n){if(typeof e!="string")throw new TypeError("First argument must be a string");if(!e)return[];n=n||{};var t=1,r=1;function i(b){var y=b.match(nu);y&&(t+=y.length);var S=b.lastIndexOf(uu);r=~S?b.length-S:r+b.length}function l(){var b={line:t,column:r};return function(y){return y.position=new a(b),c(),y}}function a(b){this.start=b,this.end={line:t,column:r},this.source=n.source}a.prototype.content=e;function o(b){var y=new Error(n.source+":"+t+":"+r+": "+b);if(y.reason=b,y.filename=n.source,y.line=t,y.column=r,y.source=e,!n.silent)throw y}function u(b){var y=b.exec(e);if(y){var S=y[0];return i(S),e=e.slice(S.length),y}}function c(){u(tu)}function p(b){var y;for(b=b||[];y=s();)y!==!1&&b.push(y);return b}function s(){var b=l();if(!(Bt!=e.charAt(0)||jt!=e.charAt(1))){for(var y=2;ve!=e.charAt(y)&&(jt!=e.charAt(y)||Bt!=e.charAt(y+1));)++y;if(y+=2,ve===e.charAt(y-1))return o("End of comment missing");var S=e.slice(2,y-2);return r+=2,i(S),e=e.slice(y),r+=2,b({type:su,comment:S})}}function d(){var b=l(),y=u(ru);if(y){if(s(),!u(iu))return o("property missing ':'");var S=u(lu),x=b({type:cu,property:Ut(y[0].replace(_t,ve)),value:S?Ut(S[0].replace(_t,ve)):ve});return u(au),x}}function h(){var b=[];p(b);for(var y;y=d();)y!==!1&&(b.push(y),p(b));return b}return c(),h()};function Ut(e){return e?e.replace(ou,ve):ve}var hu=pu;function Mr(e,n){var t=null;if(!e||typeof e!="string")return t;for(var r,i=hu(e),l=typeof n=="function",a,o,u=0,c=i.length;u<c;u++)r=i[u],a=r.property,o=r.value,l?n(a,o,r):o&&(t||(t={}),t[a]=o);return t}Xn.exports=Mr;Xn.exports.default=Mr;var fu=Xn.exports;const mu=je(fu),zn={}.hasOwnProperty,du=new Set(["table","thead","tbody","tfoot","tr"]);function Or(e,n){const t=[];let r=-1,i;for(;++r<n.children.length;)i=n.children[r],i.type==="element"?t.push(gu(e,i,r,n)):i.type==="text"?(n.type!=="element"||!du.has(n.tagName)||!Jo(i))&&t.push(i.value):i.type==="raw"&&!e.options.skipHtml&&t.push(i.value);return t}function gu(e,n,t,r){const i=e.options,l=i.transformLinkUri===void 0?pi:i.transformLinkUri,a=e.schema,o=n.tagName,u={};let c=a,p;if(a.space==="html"&&o==="svg"&&(c=Wo,e.schema=c),n.properties)for(p in n.properties)zn.call(n.properties,p)&&xu(u,p,n.properties[p],e);(o==="ol"||o==="ul")&&e.listDepth++;const s=Or(e,n);(o==="ol"||o==="ul")&&e.listDepth--,e.schema=a;const d=n.position||{start:{line:null,column:null,offset:null},end:{line:null,column:null,offset:null}},h=i.components&&zn.call(i.components,o)?i.components[o]:o,b=typeof h=="string"||h===ze.Fragment;if(!Ko.isValidElementType(h))throw new TypeError(`Component for name \`${o}\` not defined or is not renderable`);if(u.key=t,o==="a"&&i.linkTarget&&(u.target=typeof i.linkTarget=="function"?i.linkTarget(String(u.href||""),n.children,typeof u.title=="string"?u.title:null):i.linkTarget),o==="a"&&l&&(u.href=l(String(u.href||""),n.children,typeof u.title=="string"?u.title:null)),!b&&o==="code"&&r.type==="element"&&r.tagName!=="pre"&&(u.inline=!0),!b&&(o==="h1"||o==="h2"||o==="h3"||o==="h4"||o==="h5"||o==="h6")&&(u.level=Number.parseInt(o.charAt(1),10)),o==="img"&&i.transformImageUri&&(u.src=i.transformImageUri(String(u.src||""),String(u.alt||""),typeof u.title=="string"?u.title:null)),!b&&o==="li"&&r.type==="element"){const y=yu(n);u.checked=y&&y.properties?!!y.properties.checked:null,u.index=Cn(r,n),u.ordered=r.tagName==="ol"}return!b&&(o==="ol"||o==="ul")&&(u.ordered=o==="ol",u.depth=e.listDepth),(o==="td"||o==="th")&&(u.align&&(u.style||(u.style={}),u.style.textAlign=u.align,delete u.align),b||(u.isHeader=o==="th")),!b&&o==="tr"&&r.type==="element"&&(u.isHeader=r.tagName==="thead"),i.sourcePos&&(u["data-sourcepos"]=wu(d)),!b&&i.rawSourcePos&&(u.sourcePosition=n.position),!b&&i.includeElementIndex&&(u.index=Cn(r,n),u.siblingCount=Cn(r)),b||(u.node=n),s.length>0?ze.createElement(h,u,s):ze.createElement(h,u)}function yu(e){let n=-1;for(;++n<e.children.length;){const t=e.children[n];if(t.type==="element"&&t.tagName==="input")return t}return null}function Cn(e,n){let t=-1,r=0;for(;++t<e.children.length&&e.children[t]!==n;)e.children[t].type==="element"&&r++;return r}function xu(e,n,t,r){const i=Ho(r.schema,n);let l=t;l==null||l!==l||(Array.isArray(l)&&(l=i.commaSeparated?eu(l):Zo(l)),i.property==="style"&&typeof l=="string"&&(l=ku(l)),i.space&&i.property?e[zn.call(Rt,i.property)?Rt[i.property]:i.property]=l:i.attribute&&(e[i.attribute]=l))}function ku(e){const n={};try{mu(e,t)}catch{}return n;function t(r,i){const l=r.slice(0,4)==="-ms-"?`ms-${r.slice(4)}`:r;n[l.replace(/-([a-z])/g,bu)]=i}}function bu(e,n){return n.toUpperCase()}function wu(e){return[e.start.line,":",e.start.column,"-",e.end.line,":",e.end.column].map(String).join("")}const Ht={}.hasOwnProperty,Su="https://github.com/remarkjs/react-markdown/blob/main/changelog.md",Je={plugins:{to:"remarkPlugins",id:"change-plugins-to-remarkplugins"},renderers:{to:"components",id:"change-renderers-to-components"},astPlugins:{id:"remove-buggy-html-in-markdown-parser"},allowDangerousHtml:{id:"remove-buggy-html-in-markdown-parser"},escapeHtml:{id:"remove-buggy-html-in-markdown-parser"},source:{to:"children",id:"change-source-to-children"},allowNode:{to:"allowElement",id:"replace-allownode-allowedtypes-and-disallowedtypes"},allowedTypes:{to:"allowedElements",id:"replace-allownode-allowedtypes-and-disallowedtypes"},disallowedTypes:{to:"disallowedElements",id:"replace-allownode-allowedtypes-and-disallowedtypes"},includeNodeIndex:{to:"includeElementIndex",id:"change-includenodeindex-to-includeelementindex"}};function Fr(e){for(const l in Je)if(Ht.call(Je,l)&&Ht.call(e,l)){const a=Je[l];console.warn(`[react-markdown] Warning: please ${a.to?`use \`${a.to}\` instead of`:"remove"} \`${l}\` (see <${Su}#${a.id}> for more info)`),delete Je[l]}const n=Ii().use(za).use(e.remarkPlugins||[]).use(Lo,{...e.remarkRehypeOptions,allowDangerousHtml:!0}).use(e.rehypePlugins||[]).use(Qo,e),t=new Gt;typeof e.children=="string"?t.value=e.children:e.children!==void 0&&e.children!==null&&console.warn(`[react-markdown] Warning: please pass a string as \`children\` (not: \`${e.children}\`)`);const r=n.runSync(n.parse(t),t);if(r.type!=="root")throw new TypeError("Expected a `root` node");let i=ze.createElement(ze.Fragment,{},Or({options:e,schema:qo,listDepth:0},r));return e.className&&(i=ze.createElement("div",{className:e.className},i)),i}Fr.propTypes={children:O.string,className:O.string,allowElement:O.func,allowedElements:O.arrayOf(O.string),disallowedElements:O.arrayOf(O.string),unwrapDisallowed:O.bool,remarkPlugins:O.arrayOf(O.oneOfType([O.object,O.func,O.arrayOf(O.oneOfType([O.bool,O.string,O.object,O.func,O.arrayOf(O.any)]))])),rehypePlugins:O.arrayOf(O.oneOfType([O.object,O.func,O.arrayOf(O.oneOfType([O.bool,O.string,O.object,O.func,O.arrayOf(O.any)]))])),sourcePos:O.bool,rawSourcePos:O.bool,skipHtml:O.bool,includeElementIndex:O.bool,transformLinkUri:O.oneOfType([O.func,O.bool]),linkTarget:O.oneOfType([O.func,O.string]),transformImageUri:O.func,components:O.object};function Au(){const{Email:e,View:n,SelectedMail:t,ShowNewMail:r}=$.useContext($e),i=Rn(Be),[l,a]=n,[o]=e,[u]=t,[c,p]=r;return D(_n.div,{...$t("right","mail"),className:"animation-container",children:[I("div",{className:"mail-header",children:D("div",{className:"left",onClick:()=>a("home"),children:[I(ei,{}),F("APPS.MAIL.INBOX")]})}),D("div",{className:"mail-wrapper",children:[D("div",{className:"item-header",children:[D("div",{className:"user",children:[I("div",{className:"name",children:u.sender}),D("div",{className:"to",children:[D("span",{children:[F("APPS.MAIL.TO"),":"]})," ",u.to]})]}),I("div",{className:"date",children:Vt(u.timestamp)})]}),I("div",{className:"item subject",children:u.subject}),I("div",{className:"item message",children:I("div",{className:"message",children:I(Fr,{children:u.message})})}),u.attachments&&u.attachments.length>0&&I("div",{className:"item attachments",children:u.attachments.map((s,d)=>I("div",{className:"attachment",onClick:()=>{Ie.FullscreenImage.set(s)},children:I(qt,{src:s,blur:!0})},d))}),u.actions&&I("div",{className:"item actions",children:u.actions.map((s,d)=>I("div",{className:"action",onClick:()=>{ae("Mail",{action:"action",id:u.id,actionId:d})},children:s.label},d))})]}),I("div",{className:"mail-footer",children:D("div",{className:"footer-wrapper",children:[i.DeleteMail?I(ni,{onClick:()=>{Ie.PopUp.set({title:F("APPS.MAIL.DELETE_MAIL_POPUP.TITLE"),description:F("APPS.MAIL.DELETE_MAIL_POPUP.DESCRIPTION"),buttons:[{title:F("APPS.MAIL.DELETE_MAIL_POPUP.CANCEL")},{title:F("APPS.MAIL.DELETE_MAIL_POPUP.DELETE"),color:"red",cb:()=>{ae("Mail",{action:"deleteMail",id:u.id},!0).then(s=>{if(!s)return se("warning","Failed to delete mail");K.APPS.MAIL.recentmails.set(K.APPS.MAIL.recentmails.value.filter(d=>d.id!==u.id)),a("home")})}}]})}}):I("span",{}),I(ti,{onClick:()=>{p({id:null,sender:o,to:u.sender===o?u.to:u.sender,subject:F("APPS.MAIL.RE").format({subject:u.subject}),message:"",attachments:[],timestamp:Date.now()})}})]})})]})}function Eu(){const{Email:e,Mails:n,ShowNewMail:t}=$.useContext($e),[r]=e,[i,l]=n,[a,o]=t,u=$.useRef(null),[c,p]=$.useState(!1),[s,d]=$.useState({to:"",subject:"",message:"",attachments:[]});$.useEffect(()=>{a&&typeof a=="object"&&d({...s,to:a==null?void 0:a.to,subject:a==null?void 0:a.subject})},[a]);const h=()=>{if(c){if(!s)return se("warning","Form is null");if(s.to===""||s.subject===""||s.message==="")return se("warning","Form is empty");ae("Mail",{action:"sendMail",data:s},Math.floor(Math.random()*1e3)).then(b=>{if(!b){se("warning","Failed to send mail"),Ie.PopUp.set({title:F("APPS.MAIL.FAILED_SEND.TITLE"),description:F("APPS.MAIL.FAILED_SEND.DESCRIPTION"),buttons:[{title:F("APPS.MAIL.FAILED_SEND.OK")}]});return}let y={...s,id:b,read:!0,sender:r,timestamp:Date.now()};if(l([y,...i]),!K.APPS.MAIL.recentmails.value)return;let S=K.APPS.MAIL.recentmails.value;S.length>=10&&S.pop(),S.unshift(y),K.APPS.MAIL.recentmails.set(S),o(null)})}};return $.useEffect(()=>{if(!s)return p(!1);const b=s.to.match(/^([\w.%+-]+)@([\w-]+\.)+([\w]{2,})$/i);s.to!==""&&b&&s.subject!==""&&s.message!==""&&r!==s.to?p(!0):p(!1)},[s]),D(_n.div,{className:"new-mail-container",initial:{opacity:0,y:"100%"},animate:{opacity:1,y:0},exit:{opacity:0,y:"100%"},transition:{duration:.3,ease:"easeInOut"},children:[D("div",{className:"mail-header",children:[I("div",{className:"cancel",onClick:()=>o(!1),children:F("APPS.MAIL.CANCEL")}),I("div",{className:"title",children:F("APPS.MAIL.NEW_MESSAGE")}),I("div",{className:"send","data-disabled":!c,onClick:()=>h(),children:F("APPS.MAIL.SEND")})]}),D("div",{className:"mail-options",children:[D("div",{className:"option",children:[D("div",{className:"title",children:[F("APPS.MAIL.TO"),":"]}),I(Te,{className:"blue",maxLength:60,defaultValue:s==null?void 0:s.to,onChange:b=>{d({...s,to:b.target.value})}})]}),D("div",{className:"option",children:[D("div",{className:"title",children:[F("APPS.MAIL.FROM"),":"]}),I(Te,{type:"text",value:r,disabled:!0})]}),D("div",{className:"option",children:[D("div",{className:"title",children:[F("APPS.MAIL.SUBJECT"),":"]}),I(Te,{type:"text",maxLength:50,defaultValue:s==null?void 0:s.subject,onChange:b=>{d({...s,subject:b.target.value})}})]}),D("div",{className:"option textarea",children:[D("div",{className:"title",children:[F("APPS.MAIL.MESSAGE"),":"]}),I(oi,{type:"text",ref:u,onChange:b=>{u.current.style.height="auto",u.current.style.height=u.current.scrollHeight+"px",d({...s,message:b.target.value})}}),I("div",{className:"attachments",children:s.attachments.map((b,y)=>D("div",{className:"attachment",children:[I(qt,{src:b,onClick:()=>{Ie.FullscreenImage.set(b)}},y),I(ri,{onClick:S=>{S.stopPropagation();const x=s==null?void 0:s.attachments;x.splice(y,1),d({...s,attachments:x})}})]},y))})]})]}),I("div",{className:"mail-footer",children:I("div",{className:"footer-wrapper",children:I(ii,{onClick:()=>{var b,y,S;Ie.Gallery.set({onSelect:x=>d({...s,attachments:[...s.attachments,x.src]}),allowExternal:(S=(y=(b=Be)==null?void 0:b.value)==null?void 0:y.AllowExternal)==null?void 0:S.Mail})}})})})]})}function Pu(){const{Email:e,View:n}=$.useContext($e),[t,r]=e,[i,l]=n,[a,o]=$.useState(null),[u,c]=$.useState({email:"",password:""}),p=()=>{o(null),ae("Mail",{action:"login",data:u},{success:!0}).then(s=>{if(s&&s.success)r(u.email),K.APPS.MAIL.address.set(u.email),K.APPS.MAIL.recentmails.set(null),l("home");else if(s&&s.error)return o(s.error)})};return D("div",{className:"mail-form",children:[D("div",{className:"item",children:[I("div",{className:"title",children:F("APPS.MAIL.EMAIL")}),I("div",{className:"input",children:I(Te,{type:"text",placeholder:`mail@${Be.value.EmailDomain}`,maxLength:50,onChange:s=>{c({...u,email:s.target.value})}})}),a==="Invalid address"&&I("div",{className:"error",children:F("APPS.MAIL.EMAIL_INVALID")})]}),D("div",{className:"item",children:[I("div",{className:"title",children:F("APPS.MAIL.PASSWORD")}),I("div",{className:"input",children:I(Te,{type:"password",placeholder:"••••••••••",onChange:s=>{c({...u,password:s.target.value})}})}),a==="Invalid password"&&I("div",{className:"error",children:F("APPS.MAIL.WRONG_PASSWORD")})]}),I("div",{className:"button",onClick:()=>p(),children:F("APPS.MAIL.LOGIN")}),D("div",{className:"footer",children:[F("APPS.MAIL.NO_EMAIL")," ",I("span",{onClick:()=>l("signup"),children:F("APPS.MAIL.CREATE_ONE")})]})]})}function Cu(){var d;const{Email:e,View:n}=$.useContext($e),[t,r]=e,[i,l]=n,a=Rn(Be),[o,u]=$.useState(null),[c,p]=$.useState({email:"",password:""}),s=()=>{if(c.email===""||c.password==="")return se("warning","Email or password is empty");ae("Mail",{action:"createMail",data:c},{success:!0}).then(h=>{if(!h)return se("warning","Mail response is null");if(h&&h.success){const b=`${c.email}@${a.EmailDomain}`;r(b),K.APPS.MAIL.address.set(b),K.APPS.MAIL.recentmails.set(null),l("home")}else if(h&&h.error){se("warning",h.error),u(h.error);return}})};return D("div",{className:"mail-form",children:[D("div",{className:"item",children:[I("div",{className:"title",children:F("APPS.MAIL.EMAIL")}),D("div",{className:"input",children:[I(Te,{type:"text",placeholder:F("APPS.MAIL.EMAIL").toLowerCase(),maxLength:50-((d=a.EmailDomain)==null?void 0:d.length),onChange:h=>{h.target.value.match(/^[a-zA-Z0-9._-]+$/)&&p({...c,email:h.target.value})}}),D("div",{className:"suffix",children:["@",a.EmailDomain]})]}),o==="Address already exists"&&I("div",{className:"error",children:F("APPS.MAIL.EMAIL_EXISTS")})]}),D("div",{className:"item",children:[I("div",{className:"title",children:F("APPS.MAIL.PASSWORD")}),I("div",{className:"input",children:I(Te,{type:"password",placeholder:"••••••••••",onChange:h=>{p({...c,password:h.target.value})}})})]}),I("div",{className:"button",onClick:()=>s(),children:F("APPS.MAIL.CREATE_MAIL")}),D("div",{className:"footer",children:[F("APPS.MAIL.ALREADY_HAVE")," ",I("span",{onClick:()=>l("login"),children:F("APPS.MAIL.LOGIN")})]})]})}const $e=$.createContext(null),vu=Wt(!1),Tu=Wt("signup");function Mu(){const[e,n]=$.useState(null),[t,r]=$.useState("signup"),[i,l]=$.useState([]),[a,o]=$.useState(null),[u,c]=$.useState(null),[p,s]=$.useState(!1),[d,h]=$.useState(!0),b=S=>{S&&ae("Mail",{action:"getMail",id:S.id},In.mails.find(x=>x.id===S.id)).then(x=>{if(!x)return;o(x),r("mail");let v=K.APPS.MAIL.recentmails.value;v=v==null?void 0:v.map(P=>(P.id===S.id&&(P.read=!0),P)),K.APPS.MAIL.recentmails.set(v)})},y={home:I(ci,{}),mail:I(Au,{}),signup:I(Cu,{}),login:I(Pu,{})};return $.useEffect(()=>{if(K.APPS.MAIL.address.value){n(K.APPS.MAIL.address.value),h(!1),r("home");return}ae("Mail",{action:"isLoggedIn"},!1).then(S=>{S&&(n(S),r("home")),h(!1),K.APPS.MAIL.address.set(S)})},[]),$.useEffect(()=>{if(t==="refresh")return r("home")},[t]),li("mail:logout",S=>{if(!S)return se("warning","No email provided to logout");if(!p)return se("info","User is not logged in, cannot log out");if(S!==e)return se("warning","Email provided does not match current email, not logging out");n(null),s(!1),r("login")}),I("div",{className:"mail-container",children:D($e.Provider,{value:{Email:[e,n],View:[t,r],Mails:[i,l],SelectedMail:[a,o],ShowNewMail:[u,c],LoggedIn:[p,s],fetchAndSetMail:b},children:[I(ai,{initial:!1,mode:"wait",children:u&&I(Eu,{})}),y[t]]})})}export{vu as LoggedIn,$e as MailContext,Tu as View,Mu as default};
