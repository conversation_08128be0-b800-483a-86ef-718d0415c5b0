shared_scripts { '@FiniAC/fini_events.js', '@FiniAC/fini_events.lua' }

fx_version 'cerulean'
game 'gta5'

name 'BDX-Hoverboard'
description 'The Future is here.'
author 'Bodhix'
version '1.0.1'

lua54 'yes'

shared_scripts {
  'config.lua',
}

client_scripts {
  'client/*.lua',
}

server_scripts {
  'server/*.lua',
}

files {
  'nui/fonts/*.ttf',
  'nui/index.html',
  'nui/Main.css',
  'nui/sounds/*.MP3',
  'nui/customs/*.png',
  'stream/Hoverboards/Retro/*.yft',
  'stream/Hoverboards/Ultra/*.yft',
  'stream/Anims/*.ycd',
  'server/version.json',
}

ui_page 'nui/index.html'
nui_page 'nui/index.html'

data_file "DLC_ITYP_REQUEST" "stream/Hoverboards/retro_hoverboards.ytyp"
data_file "DLC_ITYP_REQUEST" "stream/Hoverboards/ultra_hoverboards.ytyp"

escrow_ignore {
  'config.lua',
  'server/hover-shops-sv.lua',
  'client/hover-shops-cl.lua',
}
dependency '/assetpacks'