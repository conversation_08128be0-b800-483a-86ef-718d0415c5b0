-----------------------
----   Variables   ----
-----------------------
ESX = exports['es_extended']:getSharedObject()
local scenes = {}

-----------------------
----   Threads     ----
-----------------------

CreateThread(function()
    UpdateAllScenes()
    while true do
        DeleteExpiredScenes()
        Wait(Config.AuditInterval)
    end
end)

-----------------------
---- Server Events ----
-----------------------

ESX.RegisterServerCallback('esx-scenes:server:GetScenes', function(source, cb)
    cb(scenes)
end)

-----------------------
----   Functions   ----
-----------------------

function UpdateAllScenes()
    scenes = {}
    MySQL.Async.fetchAll('SELECT * FROM scenes', {}, function(result)
        if result[1] then
            for _, v in pairs(result) do
                local newCoords = json.decode(v.coords)
                scenes[#scenes+1] = {
                    id = v.id,
                    text = v.text,
                    color = v.color,
                    viewdistance = v.viewdistance,
                    expiration = v.expiration,
                    fontsize = v.fontsize,
                    fontstyle = v.fontstyle,
                    coords = vector3(newCoords.x, newCoords.y, newCoords.z),
                }
            end
        end
        TriggerClientEvent('esx-scenes:client:UpdateAllScenes', -1, scenes)
    end)
end

function DeleteExpiredScenes()
    MySQL.Async.execute('DELETE FROM scenes WHERE date_deletion < NOW()', {}, function(result)
        if result > 0 then
            print('Deleted '..result..' expired scenes from the database.')
            UpdateAllScenes()
        end
    end)
end

RegisterNetEvent('esx-scenes:server:DeleteScene', function(id)
    MySQL.Async.execute('DELETE FROM scenes WHERE id = ?', {id}, function()
        UpdateAllScenes()
    end)
end)

RegisterNetEvent('esx-scenes:server:CreateScene', function(sceneData)
    local src = source
    local xPlayer = ESX.GetPlayerFromId(src)

    -- Discord Role Checks
    local hasSilver = exports.zdiscord:isRolePresent(src, '1347445310766452803')
    local hasGold = exports.zdiscord:isRolePresent(src, '1347445310766452805')
    local hasPlatinum = exports.zdiscord:isRolePresent(src, '1347445310766452806')
    local hasDiamond = exports.zdiscord:isRolePresent(src, '1347445310766452807')
    if hasSilver or hasGold or hasPlatinum or hasDiamond then
        TriggerEvent("NukeXD-Logs:CreateLog", "scenes", "New Scene Created", "red", "**".. GetPlayerName(source) .. "** (identifier: *"..xPlayer.identifier.."* | id: *"..source.."*) created a new scene; scene: **"..sceneData.text.."**, where: **" .. sceneData.coords .. "**")

        MySQL.Async.insert('INSERT INTO scenes (creator, text, color, viewdistance, expiration, fontsize, fontstyle, coords, date_creation, date_deletion) VALUES (? ,?, ?, ?, ?, ?, ?, ?, NOW(), DATE_ADD(NOW(), INTERVAL ? HOUR))', {
            xPlayer.identifier,
            sceneData.text,
            sceneData.color,
            sceneData.viewdistance,
            sceneData.expiration,
            sceneData.fontsize,
            sceneData.fontstyle,
            json.encode(sceneData.coords),
            sceneData.expiration
        }, function()
            UpdateAllScenes()
        end)
    else
        notifData = {
            title = 'HollywoodHills Subscriptions',
            description = 'You need at least HollywoodHills Silver! Check out https://hollywoodhills.tebex.io/',
            type = 'error'
        }
        TriggerClientEvent('ox_lib:notify', src, notifData)
    end
end)
