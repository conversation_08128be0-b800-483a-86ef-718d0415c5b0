Config = {}

Config.minimumSpeed = 1.0 -- km/h
Config.groundHeight = 0.5 -- minimum height to perform and autocancel the superman animation.
Config.EnablePeds = true
Config.ShowScore = false
Config.ActiveWhitelist = false

Config.allowedVehicles = {
    [`bmx`] = true,
}

BMX = {
    BeachParkCoords = { x = -1375.1957, y = -1420.2004,  z = 2.5767,  heading = 25.9502 },     
    ParkCoords = { x = -942.3220,   y = -801.9327,   z = 14.9212,  heading = 227.3334 },    
} 

BMXSpawn = {
    BMXPeds = {
        {
            Position = vector4(BMX.BeachParkCoords.x, BMX.BeachParkCoords.y, BMX.BeachParkCoords.z, BMX.BeachParkCoords.heading),
            Model = `a_m_m_skater_01`,
            Scenario = 'WORLD_HUMAN_YOGA'
        },
        {
            Position = vector4(BMX.ParkCoords.x, BMX.ParkCoords.y, BMX.ParkCoords.z, BMX.ParkCoords.heading),
            Model = `a_m_m_skater_01`,
            Scenario = 'WORLD_HUMAN_MUSCLE_FLEX'
        },
    }
}
