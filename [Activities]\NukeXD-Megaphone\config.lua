Config = {}

------------------------------------------- MEGA PHONE -----------------------------------

Config.MicrophoneZones = {
    [1] = {
        name = "vinewood_bowl1", 
        coords = vector3(683.37, 569.31, 130.46),
        length = 3.4,
        width = 3.6,
        spawnProp = true, -- if set to true, it will let you spawn the prop at location
        data = {
            debugPoly = false,
            heading = 340,
            minZ = 127.86,
            maxZ = 131.86,
            data = {
                range = 60.0 -- range for the voice
            }
        }
    },
	[2] = {
        name = "gabz_townhall_main", 
        coords = vector3(-572.526, -207.757, 38.227),
        length = 1.5,
        width = 1.5,
        spawnProp = false, -- if set to true, it will let you spawn the prop at location
        data = {
            debugPoly = false,
            heading = 123.55,
            minZ = 38.0,
            maxZ = 39.0,
            data = {
                range = 30.0 -- range for the voice
            }
        }
    },

    [3] = {
        name = "doj_court_1", 
        coords = vector3(-577.085, -210.426, 38.266),
        length = 1.5,
        width = 2,
        spawnProp = false, -- if set to true, it will let you spawn the prop at location
        data = {
            debugPoly = false,
            heading = 299.99,
            minZ = 38.0,
            maxZ = 39.0,
            data = {
                range = 40.0 -- range for the voice
            }
        }
    },

    [4] = {
        name = "doj_court_2", 
        coords = vector3(-572.526, -207.844, 38.227),
        length = 1.5,
        width = 2,
        spawnProp = false, -- if set to true, it will let you spawn the prop at location
        data = {
            debugPoly = false,
            heading = 299.99,
            minZ = 38.0,
            maxZ = 39.0,
            data = {
                range = 40.0 -- range for the voice
            }
        }
    },

    [5] = {
        name = "doj_court_3", 
        coords = vector3(-569.268, -207.941, 38.687),
        length = 1.5,
        width = 2,
        spawnProp = false, -- if set to true, it will let you spawn the prop at location
        data = {
            debugPoly = false,
            heading = 299.99,
            minZ = 38.0,
            maxZ = 39.0,
            data = {
                range = 40.0 -- range for the voice
            }
        }
    },

    [6] = {
        name = "doj_court_4", 
        coords = vector3(-570.804, -204.947, 38.681),
        length = 1.5,
        width = 2,
        spawnProp = false, -- if set to true, it will let you spawn the prop at location
        data = {
            debugPoly = false,
            heading = 299.99,
            minZ = 38.0,
            maxZ = 39.0,
            data = {
                range = 40.0 -- range for the voice
            }
        }
    },

    [7] = {
        name = "doj_court_5", 
        coords = vector3(-578.152, -208.562, 39.04),
        length = 1.5,
        width = 2,
        spawnProp = false, -- if set to true, it will let you spawn the prop at location
        data = {
            debugPoly = false,
            heading = 299.99,
            minZ = 38.0,
            maxZ = 39.0,
            data = {
                range = 40.0 -- range for the voice
            }
        }
    },

    [8] = {
        name = "doj_court_6", 
        coords = vector3(-576.078, -212.26, 39.04),
        length = 1.5,
        width = 2,
        spawnProp = false, -- if set to true, it will let you spawn the prop at location
        data = {
            debugPoly = false,
            heading = 299.99,
            minZ = 38.0,
            maxZ = 39.0,
            data = {
                range = 40.0 -- range for the voice
            }
        }
    },
}