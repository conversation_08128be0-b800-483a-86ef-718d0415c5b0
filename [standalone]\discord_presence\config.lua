Config = {}

Config.AppId = '1417420900554510386'

Config.LargeAsset = {
    image = 'hwhl',
    text = 'Hollywood Hills Roleplay'
}

--[[ Config.SmallAsset = {
    image = 'hocs',
    text = 'TEST'
} ]]

Config.Buttons = {
    { label = "Join Discord", url = "https://discord.gg/hollywoodhills" },
    { label = "Store", url = "https://hollywoodhills.tebex.io/" }
}

Config.Options = {
    showPlayerName = true,
    showStreetName = true,
    updateInterval = 30000
}
