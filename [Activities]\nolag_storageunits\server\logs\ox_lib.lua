local sharedConfig = lib.load('config.shared')
if sharedConfig.logs ~= 'ox_lib' then return end

Logs = {
    CreateStorage = function(playerIdentifier, storageId, storageLabel, storagePrice)
        lib.logger(playerIdentifier, "CreateStorage", locale("logs_created_storage", playerIdentifier, storageId, storageLabel, storagePrice))
    end,
    RentStorage = function(playerIdentifier, storageId, storageLabel, rentalDays, storagePrice)
        lib.logger(playerIdentifier, "RentStorage", locale("logs_rented_storage", playerIdentifier, storageId, storageLabel, rentalDays, storagePrice))
    end,
    ChangePassword = function(playerIdentifier, storageId, storageLabel)
        lib.logger(playerIdentifier, "ChangePassword", locale("logs_changed_password", playerIdentifier, storageId, storageLabel))
    end,
    CancelSubscription = function(playerIdentifier, storageId, storageLabel)
        lib.logger(playerIdentifier, "CancelSubscription", locale("logs_canceled_subscription", playerIdentifier, storageId, storageLabel))
    end,
    TransferStorage = function(playerIdentifier, targetplayerIdentifier, storageId, storageLabel)
        lib.logger(playerIdentifier, "TransferStorage", locale("logs_transferred_storage", playerIdentifier, targetplayerIdentifier, storageId, storageLabel))
    end,
    DeleteStorage = function(playerIdentifier, storageId, storageLabel)
        lib.logger(playerIdentifier, "DeleteStorage", locale("logs_deleted_storage", playerIdentifier, storageId, storageLabel))
    end,
    ExpiredStorage = function(storageId, storageLabel)
        lib.logger("server", "ExpiredStorage", locale("logs_expired_storage", storageId, storageLabel))
    end,
    ---@param playerIdentifier string
    ---@param storageId number
    ---@param paymentType string
    ---@param amount number
    HandledPayment = function(playerIdentifier, storageId, paymentType, amount)
        lib.logger(playerIdentifier, "HandledPayment", locale("logs_handled_payment", playerIdentifier, storageId, paymentType, amount))
    end,
    ---@param playerIdentifier string
    ---@param storageId number
    ---@param paymentType string
    ---@param amount number
    FailedPayment = function(playerIdentifier, storageId, paymentType, amount)
        lib.logger(playerIdentifier, "FailedPayment", locale("logs_failed_payment", playerIdentifier, storageId, paymentType, amount))
    end,
    ---@param playerIdentifier string
    ---@param storageId number
    ---@param storageLabel string
    ---@param storagePrice number
    BuyStorage = function(playerIdentifier, storageId, storageLabel, storagePrice)
        lib.logger(playerIdentifier, "BuyStorage", locale("logs_bought_storage", playerIdentifier, storageId, storageLabel, storagePrice))
    end,
    ---@param playerIdentifier string
    ---@param storageId number
    ---@param storageLabel string
    RaidStorage = function(playerIdentifier, storageId, storageLabel)
        lib.logger(playerIdentifier, "RaidStorage", locale("logs_raided_storage", playerIdentifier, storageId, storageLabel))
    end,
}
