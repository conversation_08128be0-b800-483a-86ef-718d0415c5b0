
--------------------------MENU FUNCTION----------------------------
function useSmokingItem(item)
    local data = Config.SmokingItems[item]
    local m = Config.Menu
    local t = Translations['MENU']
    local dead = false
    CreateMainSmokingProp(item)

        Citizen.CreateThread(function()
            while STATE.SELECT_ITEM == item do
                if (not STATE.USING) then
---LIGHTER MENU ---
            if STATE.ACT == 'LIGHTER' then
        
                    if not STATE.MENU then
                    Config.CustomMenu(t['LIGHT'].tag ..t['LIGHT'].label.. ' ' ..t['HIDE'].tag ..t['HIDE'].label, m['MAIN'])
                    DrawSmokeMenu(t['OPTIONS'].tag.. t['OPTIONS'].label, m['OPTIONS'])
          
                    end
            end
--
--SMOKING Menu--
             if STATE.ACT == 'SMOKING' then

            if not STATE.MENU then
                if data.Type == 'NEED_LIGHTER' then
                Config.CustomMenu(t['USE'].tag.. t['USE'].label.. ' ' ..t['THROW'].tag.. t['THROW'].label, m['MAIN'])
                DrawSmokeMenu(t['OPTIONS'].tag.. t['OPTIONS'].label, m['OPTIONS'])
                else
                    if STATE.SIZE <= 0 then
                        if data.Type == 'BONG' then
                    Config.CustomMenu(t['CHARGE'].tag.. t['CHARGE'].label.. ' ' ..t['HIDE'].tag.. t['HIDE'].label, m['MAIN'])
                    DrawSmokeMenu(t['OPTIONS'].tag.. t['OPTIONS'].label, m['OPTIONS'])
                        elseif data.Type == 'VAPE' then
                            Config.CustomMenu(t['CHARGE'].tag.. t['CHARGE'].label.. ' ' ..t['HIDE'].tag.. t['HIDE'].label, m['MAIN'])
                            DrawSmokeMenu(t['OPTIONS'].tag.. t['OPTIONS'].label, m['OPTIONS'])
                    end
                else
                    if data.Type == 'BONG' then
                    Config.CustomMenu(t['LIGHT'].tag.. t['LIGHT'].label.. ' ' ..t['HIDE'].tag.. t['HIDE'].label, m['MAIN'])
                    DrawSmokeMenu(t['OPTIONS'].tag.. t['OPTIONS'].label, m['OPTIONS'])
                        elseif data.Type == 'VAPE' then
                            Config.CustomMenu(t['USE'].tag.. t['USE'].label.. ' '  ..t['HIDE'].tag.. t['HIDE'].label, m['MAIN'])
                            DrawSmokeMenu(t['OPTIONS'].tag.. t['OPTIONS'].label, m['OPTIONS'])
                        elseif data.Type == 'VAPE-PUFF' then
                            Config.CustomMenu(t['USE'].tag.. t['USE'].label.. ' '  ..t['HIDE'].tag.. t['HIDE'].label, m['MAIN'])
                            DrawSmokeMenu(t['OPTIONS'].tag.. t['OPTIONS'].label, m['OPTIONS'])
                    end
                end
            end
        end
                if STATE.SIZE > 0 then
                    if data.Type == 'NEED_LIGHTER' then
                
                        local percent = STATE.SIZE / data.Setting.Size * 100
                        local getColor = GetTextColor(item)
                        
                        DrawSmokeMenu(getColor ..round(percent)..  '~w~%', m['STATUS'])
                    end

                    if data.Type == 'VAPE' then
                        DrawSmokeMenu(round_number(STATE.SIZE, 2).. ' / ' ..data.Setting.Size, m['STATUS'])
                    end
                    if data.Type == 'VAPE-PUFF' then
                        DrawSmokeMenu(round_number(STATE.SIZE, 2).. ' / ' ..data.Setting.Size, m['STATUS'])
                    end
                end
        end
------
        end

        if Config.Settings.DisableCombatButtons then
            for i=140, 143 do
                DisableControlAction(0, i, true)
            end
                DisableControlAction(0, 6, true)
                DisableControlAction(0, 13, true)
                DisableControlAction(0, 25, true)
                DisableControlAction(0, 68, true)
                DisableControlAction(0, 70, true)
                DisableControlAction(0, 66, true)
        end

        if STATE.SIZE <= 0 then
            STATE.SIZE = 0
            if data.Type == 'VAPE-PUFF' or data.Type == 'NEED_LIGHTER' then
                StopSmoking('THROW')
            break

            end
             if data.Type == 'BONG' or data.Type == 'VAPE' then
                STATE.CONTENT = nil
             end
        end


        if IsPedSwimming(PlayerPedId()) then
            StopSmoking('THROW')
        end

        if GetCurrentPedWeapon(PlayerPedId()) then
            StopSmoking('THROW')
        end

        if IsPedDeadOrDying(PlayerPedId(), false) and not dead then
            dead = true
            StopSmoking()
            if STATE.OTHER_PROP then
            for k, v in pairs(STATE.OTHER_PROP) do
                if k then
                    DeleteOtherItemProp(k)
                end
            end
        end
            if (STATE.HIGH.EFFECT ~= 0.0) then
                stopHighEffect()
            end
            break
        end

        Citizen.Wait(0)
    end
    end)
end



--------------------------CONTROL PANEL FUNCTION----------------------------

function ControlPanel(item)
	local data = Config.SmokingItems[item]
        Citizen.CreateThread(function()
            while STATE.SELECT_ITEM == item do
                if (not STATE.USING) then

                    if STATE.SIZE > 0 and STATE.ACT == 'SMOKING' and data.Type ~= 'BONG' and IsControlJustPressed(0, Config.Buttons.Use) then
                        if data.AfterExhale.Status['HIGH'] then
                            if STATE.HIGH.EFFECT <= Config.HighSystem.MaxHigh then
                            OnSmoking(item)
                            else
                                Config.Notify(Translations['TEXT']['max_high'], 'error')
                            end
                        else
                            OnSmoking(item)
                        end
                    end

                    if data.Type == 'BONG' and STATE.ACT == 'SMOKING' and IsControlJustPressed(0, Config.Buttons.Light) then
                            if STATE.CONTENT and STATE.SIZE > 0 then
                                if STATE.HIGH.EFFECT <= Config.HighSystem.MaxHigh then
                                GetLighter(item)
                            else
                                Config.Notify(Translations['TEXT']['max_high'], 'error')
                                end
                            else
                                Config.Notify(Translations['TEXT']['not_filled'], 'error')
                            end
                    end

                    if STATE.ACT == 'SMOKING' and data.Type == 'NEED_LIGHTER' and IsControlJustPressed(0, Config.Buttons.Throw) then
                        if IsPedInAnyVehicle(PlayerPedId(), false) then
                            StopSmoking()
                        else
                        if STATE.POS ~= 'MOUTH' then
                                PlayAnim('THROW', item, function()
                                    
                                    STATE.USING = false
                                    
                                    StopSmoking('THROW')
                            end)
                        else
          
                            STATE.USING = false
                                    
                            StopSmoking('THROW')
                        end
                    end
                end

                    if IsControlJustPressed(0, Config.Buttons.Options) then
                        OpenOptionsMenu(item)
                    end
                    


                    if STATE.POS == 'HAND' and STATE.ACT == 'SMOKING' and data.Type ~= 'NEED_LIGHTER' and IsControlJustPressed(0, Config.Buttons.Hide) then
                        PlayAnim('TAKEOUT', item, function()
                                    
                            STATE.USING = false
    
                            StopSmoking()
                     end)
                    end

                    if data.Type ~= 'NEED_LIGHTER' and STATE.ACT == 'SMOKING' and STATE.SIZE <= 0 and IsControlJustPressed(0, Config.Buttons.Charge) then
                        if data.Type == 'VAPE' or data.Type == 'BONG' then
                        STATE.USING = true
                        OpenChargeMenu(item)
                        end
                    end



            end
            Citizen.Wait(0)
        end
    end)
end


-------------------------- CONTROL FUNCTION FOR ITEM TYPE NEED_LIGHTER BEFORE LIGHT ----------------------------

function LighterPanel(item)
    local data = Config.SmokingItems[item]
        Citizen.CreateThread(function()
            while STATE.ACT == 'LIGHTER' do
                if (not STATE.USING) then



                    if IsControlJustPressed(0, Config.Buttons.Light) then
                        GetLighter(item)
                    end
                    
                    if STATE.ACT == 'LIGHTER' and  IsControlJustPressed(0, Config.Buttons.Options) then
                    OpenOptionsMenu(item)
                    end



                if IsControlJustPressed(0, Config.Buttons.Hide) then
                    if STATE.POS == 'HAND' then
                    PlayAnim('TAKEOUT', item, function()
                                
                        STATE.USING = false

                        StopSmoking()
                 end)
                elseif STATE.POS == 'MOUTH' then
                    ChangePosition('HAND', item)
                    PlayAnim('TAKEOUT', item, function()
                                
                        STATE.USING = false

                        StopSmoking()
                 end)

                end
                
                end
            end
            Citizen.Wait(0)
        end
    end)
end




---------------------------------------OTHER FUNCTIONS-----------------------------------------------------------

local shadow = false
local intensity = 0

function OnSmoking(item)
    local ped = PlayerPedId()
    STATE.USING = true
        if STATE.POS ~= 'MOUTH' then

            PlayAnim('USE', item, function()
                    if Config.SmokingItems[item].AfterExhale.Status['HIGH'] ~= nil then
                        RefreshHighEffect(item)
                    else
                      
                        SmokeDarkEffect()
                        if Config.SmokingItems[item].AfterExhale.ShakeCam then
                            ShakeGameplayCam(Config.SmokingItems[item].AfterExhale.ShakeCam.effect, Config.SmokingItems[item].AfterExhale.ShakeCam.value)
                          end
                    end
                

                TriggerServerEvent(GetCurrentResourceName()..':server:startExhaleEff', PedToNet(ped), item, 'Exhale')
                if Config.SmokingItems[item].Anims['COUGH'] and Config.SmokingItems[item].AfterExhale.CoughChance then
                    local random = math.random(1, Config.SmokingItems[item].AfterExhale.CoughChance)
                    if random == Config.SmokingItems[item].AfterExhale.CoughChance then
                    PlayAnim('COUGH', item, function()
                    
                    end)
                end
            end
            Wait(100)
            addStatus(item)
        end)
        
    elseif STATE.POS ~= 'HAND' then

                if Config.SmokingItems[item].AfterExhale.Status['HIGH'] ~= nil  then
                    RefreshHighEffect(item)
                else
               
                    SmokeDarkEffect()
                    if Config.SmokingItems[item].AfterExhale.ShakeCam then
                        ShakeGameplayCam(Config.SmokingItems[item].AfterExhale.ShakeCam.effect, Config.SmokingItems[item].AfterExhale.ShakeCam.value)
                      end
            end 
            TriggerServerEvent(GetCurrentResourceName()..':server:startExhaleEff', PedToNet(ped), item, 'Exhale')
            if Config.SmokingItems[item].Anims['COUGH'] and Config.SmokingItems[item].AfterExhale.CoughChance then
                local random = math.random(1, Config.SmokingItems[item].AfterExhale.CoughChance)
                if random == Config.SmokingItems[item].AfterExhale.CoughChance then
                PlayAnim('COUGH', item, function()
                end)
            end
        end
        Wait(100)
        addStatus(item)
    end
end


function DarkScreen(intensity)
    local width, height = GetActiveScreenResolution()

    DrawRect(0, 0, width * 0.1, height, 0, 0, 0, intensity)

    DrawRect(width * 0.9, 0, width * 0.1, height, 0, 0, 0, intensity)

    DrawRect(0, 0, width, height * 0.1, 0, 0, 0, intensity)

    DrawRect(0, height * 0.9, width, height * 0.1, 0, 0, 0, intensity)
end


function SmokeDarkEffect()
    if STATE.SELECT_ITEM then
    DarkEffectStart()
    if Config.SmokingItems[STATE.SELECT_ITEM].AfterExhale.ExhaleEffect.effect then
    ShakeGameplayCam('VIBRATE_SHAKE', 2.5 )
    AnimpostfxPlay(Config.SmokingItems[STATE.SELECT_ITEM].AfterExhale.ExhaleEffect.effect, 2000, false)
    DarkEffectStart()
    end
end
end

function DarkEffectStart()
    shadow = true
    Citizen.CreateThread(function()
        if STATE.SELECT_ITEM then
        local max_intensity = Config.SmokingItems[STATE.SELECT_ITEM].AfterExhale.ExhaleEffect.intensity
    while true do
        if shadow then
                if intensity <= max_intensity then
                    intensity = intensity + 1
                end
                if intensity >= max_intensity then
                    shadow = false
                end
                
            else
                if intensity > 0 then
                    
                    intensity = intensity - 1
                end
                if intensity <= 0 then
                    intensity = 0
                    shadow = true
                    break
                end
            end

                DarkScreen(intensity)
                Citizen.Wait(5)
            end
        end
    end)
end


function ChangePosition(pos, item) -- FUNCTION FOR

        if 'MOUTH' == pos and STATE.ACT == 'LIGHTER' then
            STATE.POS = 'MOUTH'
            PlayAnim('MOUTH', item, function()

                CreateMainSmokingProp(item)
                
             STATE.USING = false
         end)
        elseif 'HAND' == pos and STATE.ACT == 'LIGHTER' then
            STATE.POS = 'HAND'
            PlayAnim('MOUTH', item, function()
                CreateMainSmokingProp(item)
               
             STATE.USING = false
         end)
        elseif 'EAR' == pos and STATE.ACT == 'LIGHTER' then
            STATE.POS = 'EAR'
            PlayAnim('EAR', item, function()

                CreateOtherItemProp(item, 'EAR')
                setPosition(item)
         end)
        elseif 'HAND' == pos and STATE.ACT == 'SMOKING' then
            STATE.POS = 'HAND'
            PlayAnim('MOUTH', item, function()

                CreateMainSmokingProp(item)
                if STATE.ACT == 'SMOKING' then
                TriggerServerEvent(GetCurrentResourceName()..':server:startParticles', STATE.SELECT_ITEM, ObjToNet(STATE.CURRENT_PROP), 'Prop')
                end
             STATE.USING = false
         end)
        elseif 'MOUTH' == pos and STATE.ACT == 'SMOKING' then
            STATE.POS = 'MOUTH'
            PlayAnim('MOUTH', item, function()

                CreateMainSmokingProp(item)
                if STATE.ACT == 'SMOKING' then
                TriggerServerEvent(GetCurrentResourceName()..':server:startParticles', STATE.SELECT_ITEM, ObjToNet(STATE.CURRENT_PROP), 'Prop')
                end
             STATE.USING = false
         end)
        end
end


function sizeReduction(item)
    Citizen.CreateThread(function()
        while true do
            if (not STATE.USING) then
                local data = Config.SmokingItems[item]
                if STATE.ACT == 'SMOKING' and data.Type == 'NEED_LIGHTER' then
                    STATE.SIZE = STATE.SIZE - data.Setting.Remove
                else
                    break
                end
        end
        local data = Config.SmokingItems[item]
            Citizen.Wait(data.Setting.Time * 1000)
        end
    end)
end


function StopSmoking(act)
        if (STATE.SELECT_ITEM) then
            if act == 'THROW' then
                local data = Config.SmokingItems[STATE.SELECT_ITEM]
                if data.Type == 'NEED_LIGHTER' then
                TriggerServerEvent(GetCurrentResourceName()..':server:stopParticles', ObjToNet(STATE.CURRENT_PROP))
                end
                TriggerServerEvent(GetCurrentResourceName()..':server:stopSmoking', STATE.SELECT_ITEM, STATE)
                throwItem(STATE.SELECT_ITEM)
            elseif act == 'GIVE' then
                STATE.ACT = nil
                STATE.SIZE = nil
                STATE.CONTENT = nil
                STATE.SELECT_ITEM = nil
                DeleteItemProp()
            else
                TriggerServerEvent(GetCurrentResourceName()..':server:stopSmoking', STATE.SELECT_ITEM, STATE)
                STATE.ACT = nil
                STATE.SIZE = nil
                STATE.CONTENT = nil
                STATE.SELECT_ITEM = nil
                DeleteItemProp()
            end
        end
    end



function setPosition(item)
    if STATE.OTHER_PROP['EAR'] then
        STATE.USING = false
        TriggerServerEvent(GetCurrentResourceName()..':server:setPosition', item)
        StopSmoking()
        Config.Notify(Translations['TEXT']['save_position'])
    else
        Config.Notify(Translations['TEXT']['already_ear'])
    end
end



function addStatus(item)
    local data = Config.SmokingItems[item]
    if (STATE.SELECT_ITEM) then
        if (STATE.USING) then

            local value = data.AfterExhale.RemoveAfterExhale

            STATE.SIZE = round_number(STATE.SIZE - value, 2)
            

            for k, v in pairs(data.AfterExhale.Status) do
                Config.Status[k](data)
            end
        end
    end
end

function GiveOnHandItem(item)
    local m = Config.Menu


    Citizen.CreateThread(function()
        while STATE.SELECT_ITEM == item do
            local closestPlayer, closestDistance = GetClosePlayer()
            if closestPlayer ~= -1 and closestDistance <= 2.5 then
                Config.CustomMenu(Translations['MENU']['ACCEPT'].tag.. Translations['MENU']['ACCEPT'].label.. ' '  ..Translations['MENU']['BACK'].tag.. Translations['MENU']['BACK'].label, m['MAIN'])
                target_id = GetPlayerPed(closestPlayer)
                playerX, playerY, playerZ = table.unpack(GetEntityCoords(target_id))
                DrawMarker(0, playerX, playerY, playerZ+1.5, 0.0, 0.0, 0.0, 0, 0.0, 0.0, 0.6, 0.6, 0.6, 10, 255, 0, 100, true, true, 2, true, false, false, false)

                if IsControlJustPressed(0, Config.Buttons.Accept) then
                    if closestPlayer ~= -1 and closestDistance <= 2.5 then
                        if STATE.POS == 'MOUTH' then
                            ChangePosition('HAND', item)
                            Citizen.Wait(1600)
                        end
                        TriggerServerEvent(GetCurrentResourceName()..':server:Receiver', GetPlayerServerId(closestPlayer), item, STATE)
                        break
                        end
                    end
                else
                    Config.CustomMenu(Translations['MENU']['BACK'].tag.. Translations['MENU']['BACK'].label, m['MAIN'])
                end
                    if IsControlJustPressed(0, Config.Buttons.Back) then
                        STATE.USING = false
                        break
                    end
                
            Citizen.Wait(0)
        end
    end)
end


