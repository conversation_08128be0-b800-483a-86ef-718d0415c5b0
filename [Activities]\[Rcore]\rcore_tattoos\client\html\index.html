<!DOCTYPE html>
<html lang="en">

<head>
    <!-- Required meta tags -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <script src="./js/jquery-3.6.0.min.js"></script>

    <!-- Color themes
    // Available themes: dark, light, darkred, darkgold, darkgreen, pink
    // To change a theme, simply replace "dark" in the line below with any style above and restart your script. -->

    <link rel="stylesheet" href="./style/themes/dark.css">

    <!-- ////////////////////////////////////////////// -->    
    
    <link rel="stylesheet" type="text/css" href="./style/fonts/fonts.css">
    <link rel="stylesheet" type="text/css" href="./style/main.css">
    <link rel="stylesheet" type="text/css" href="./style/business_main.css" media="screen" />
    <link rel="stylesheet" type="text/css" href="./style/helpKeys.css" media="screen" />
    <link rel="stylesheet" type="text/css" href="./style/minigame.css" media="screen" />
</head>

<body>
    <section class="notif-box js-notif"></section>
    <div id="app" v-if="visible">
        <section class="menu__parts" v-if="partsMenuVisible">
            <div class="menu__parts-wrap">
                <div v-for="(partData, partName) in parts" v-on:click="openTattooMenu(partName)"
                    :class="{
                        [`menu__part menu__part--${partName}`]: true,
                        'menu__part--empty': !tattoos[partName].length,
                    }"
                >
                    <div class="menu__part-bg"></div>
                    <h1 class="menu__part-title">{{ partData.label }}</h1>
                    <span class="menu__part-desc">{{ tattoos[partName].length }} {{ texts.tattoos }}</span>
                </div>
            </div>
        </section>
        <section class="menu__tats js-tats-menu" style="display: none">
            <div class="menu__tats-wrap">
                <div class="menu__tats-box">
                    <div class="menu__tats-logo">
                        <img src="img/tattoologo.png" alt="Logo">
                    </div>
                    <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 1440 320" class="menu__tats-wave">
                        <path d="M1 256c203-10.7 385-104.2 484-126.2 174-33 317-16.5 484 1.1 159 15.4 311 80.3 391 115.5l80 35.2V0H0Z" />
                    </svg>
                    <div class="menu__tats-content">
                        <div class="menu__tats-header">
                            <h1 class="menu__tats-title">{{ texts.list_of_tattoos }}</h1>
                            <span v-if="tattoosMenuVisible" class="menu__tats-count">{{ listIndexLabel }}</span>
                        </div>
                        <div :class="{'menu__search':true, 'menu__search--active': search.focus}">
                            <svg class="menu__search-icon" xmlns="http://www.w3.org/2000/svg" fill="currentColor" xml:space="preserve" viewBox="0 0 122.879 119.799"><path d="M49.988 0h.016v.007C63.803.011 76.298 5.608 85.34 14.652c9.027 9.031 14.619 21.515 14.628 35.303h.007V50.028h-.007a49.932 49.932 0 0 1-3.471 18.301v.007a50.011 50.011 0 0 1-5.547 10.307l29.082 26.139.018.016.157.146.011.011a8.602 8.602 0 0 1 2.649 5.78 8.611 8.611 0 0 1-1.979 5.971l-.011.016-.175.203-.035.035-.146.16-.016.021a8.594 8.594 0 0 1-5.78 2.646 8.602 8.602 0 0 1-5.971-1.978l-.015-.011-.204-.175-.029-.024-29.745-26.734c-.88.62-1.778 1.209-2.687 1.765a50.063 50.063 0 0 1-3.813 2.115c-6.699 3.342-14.269 5.222-22.272 5.222v.007h-.016v-.007c-13.799-.004-26.296-5.601-35.338-14.645C5.605 76.291.016 63.805.007 50.021H0v-.049h.007c.004-13.799 5.601-26.296 14.645-35.338C23.683 5.608 36.167.016 49.955.007V0h.033zm.016 11.21v.007h-.049v-.007c-10.686.007-20.372 4.35-27.384 11.359-7.011 7.009-11.358 16.705-11.361 27.404h.007v.049h-.007c.007 10.686 4.347 20.367 11.359 27.381 7.009 7.012 16.705 11.359 27.403 11.361v-.007h.049v.007c10.686-.007 20.368-4.348 27.382-11.359 7.011-7.009 11.358-16.702 11.36-27.4h-.006v-.049h.006c-.006-10.686-4.35-20.372-11.358-27.384-7.009-7.012-16.702-11.359-27.401-11.362z"/></svg>
                            <input class="menu__search-input" type="text"
                                @focus="search.focus = true"
                                @blur="search.focus = false"
                                v-model="search.value"
                            >
                        </div>
                        <ul 
                            :class="{
                                'menu__tats-list': true,
                                'menu__tats-list--extend': !opacity.enabled,
                            }"
                            ref="tattooList"
                        >
                            <li 
                                v-if="tattoosMenuVisible && tattoos[currentCategory].length && isMatchingSearch(getTattooName(tattoo))"
                                v-for="(tattoo, index) in tattoos[currentCategory]"
                                v-on:click="setCurrentTattooIndex($event, tattoo)"
                                :class="[tattoo == tattoos[currentCategory][0] ? 'tat-selected' : '' , 'js-tattoo-item']" 
                                :data-index="index"
                            >
                                <span>
                                    <template v-if="tattoo.Permissions">
                                        {{ tattoo.Permissions.jobs && tattoo.Permissions.jobs.length ? '💼 ' : '' }}
                                        {{ tattoo.Permissions.identifiers && tattoo.Permissions.identifiers.length ? '⭐ ' : '' }}
                                    </template>
                                    {{ getTattooName(tattoo) }}
                                </span>
                                <strong class="js-tattoo-price" v-if="!tattoo.Owned">
                                    {{ getTattooPriceLabel(tattoo.Price) }}
                                </strong>
                                <span class="js-tattoo-price" v-if="tattoo.Owned">
                                    ✔
                                </span>
                            </li>
                        </ul>

                        <div v-if="opacity.enabled" class="menu__tats-opacity">
                            <label for="opacity">
                                <span>{{ texts.opacity }}</span>
                                <span>{{ opacity.level }}</span>
                            </label>
                            <input name="opacity.level" v-model="opacity.level" @input="handlePreview()" type="range" min="1" max="5">
                        </div>

                        <div class="menu__tats-henna">
                            <label class="tooltip__wrap">
                                <input name="useHenna" type="checkbox" v-model="useHenna">
                                {{ texts.use_henna }}
                                <span class="tooltip">
                                    <svg class="tooltip__icon" xmlns="http://www.w3.org/2000/svg" id="tooltip" fill="currentColor" viewBox="0 0 24 24"><path d="M12 0a12 12 0 1 0 12 12A12.013 12.013 0 0 0 12 0Zm0 22a10 10 0 1 1 10-10 10.011 10.011 0 0 1-10 10Z"/><path d="M12.717 5.063A4 4 0 0 0 8 9a1 1 0 0 0 2 0 2 2 0 0 1 2.371-1.967 2.024 2.024 0 0 1 1.6 1.595 2 2 0 0 1-1 2.125A3.954 3.954 0 0 0 11 14.257V15a1 1 0 0 0 2 0v-.743a1.982 1.982 0 0 1 .93-1.752 4 4 0 0 0-1.213-7.442Z"/><rect x="11" y="17" width="2" height="2" rx="1"/></svg>
                                    <span class="tooltip__info tooltip__info--top">{{ texts.henna_info }}</span>
                                </span>
                            </label>
                        </div>
                        
                        <div class="menu__tats-controls">
                            <button class="menu__tats-btn menu__tats-btn--icon" v-if="!onlySpecificPart" v-on:click="menuGoBackClick">
                                <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" xml:space="preserve" viewBox="0 0 30.725 30.725">
                                    <path d="M24.078 26.457a2.5 2.5 0 0 1-1.77 4.267 2.497 2.497 0 0 1-1.768-.731L5.914 15.362 20.543.732a2.499 2.499 0 1 1 3.535 3.536L12.984 15.362l11.094 11.095z" />
                                </svg>
                            </button>
                            <button class="menu__tats-btn" :disabled="search.focus" v-if="getIsBuyRemoveBtnVisible()" v-on:click="buyOrRemoveWithButton">
                                <span v-if="!tattoos[currentCategory][currentTattooIndex].Owned">{{ texts.buy_tattoo }}</span>
                                <span v-else>{{ texts.remove_tattoo }}</span>
                            </button>
                        </div>
                    </div>
                
                    <transition name="fade">
                        <div
                            v-if="waitForEmployee"
                            class="menu__dialog menu__dialog--anim"
                            ref="waitForEmployeeDialog"
                        >
                            {{ texts.wait_for_employee }}
                        </div>
                    </transition>
                </div>
            </div>
            
            <div class="menu__slider-wrap">
                <input type="range" min="-180" max="180" value="0" step="2" class="menu__slider js-cam-slider">
            </div>
        </section>
    </div>

    <!-- ---------------------- -->
    <!-- BUSINESS MENUs SECTION -->
    <!-- ---------------------- -->

    <!-- Business margin menu -->
    <div id="marginMenu">
        <div class="menu__container" v-bind:class="[position, float]" v-if="visible">
            <div class="menu__title">{{ titleMenu }}</div>

            <div class="menu__input-wrap">
                <input class="menu__input" :min="marginData.min" :max="marginData.max" v-model="marginData.current" type="range">
                <div class="menu__input-label" v-html="`${marginData.current}%`"></div>

                <div class="menu__input-label">
                    <strong>{{ texts.example }}</strong>
                    <div :style="{textAlign: 'left', marginTop: '0.5vw'}" :set="tempPrice = 300">
                        <div :set="tempNewPrice = Math.floor(tempPrice * (1 + parseInt(marginData.current || 0) / 100))">
                            <strong>{{ texts.price }}:</strong> {{tempPrice}}{{ texts.currency }}<br>
                            <strong>{{ texts.newPrice }}:</strong> {{ tempNewPrice }}{{ texts.currency }}<br>
                            <strong>{{ texts.profit }}:</strong> {{tempNewPrice - tempPrice}}{{ texts.currency }}
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="menu__btns-wrap">
                <div class="menu__tats-btn" @click="Choose()">{{ ChooseText }}</div>
                <div class="menu__tats-btn" @click="Close()">{{ CloseText }}</div>
            </div>
        </div>
    </div>

    <!-- Withdraw/Deposit menu -->
    <div id="inputMenu">
        <div class="menu__container" v-bind:class="[position, float]" v-if="visible">
            <div class="menu__title">{{ titleMenu }}</div>

            <div class="menu__input-wrap">
                <div class="menu__input-label">
                    <strong>{{ texts.title }}</strong>: {{ description }}
                </div>
                <input ref="input" class="menu__input menu__input--text" @input="validate" v-model="value" type="number">
            </div>
            
            <div class="menu__btns-wrap">
                <div class="menu__tats-btn" @click="Choose()">{{ ChooseText }}</div>
                <div class="menu__tats-btn" @click="Close()">{{ CloseText }}</div>
            </div>
        </div>
    </div>

    <!-- Bossmenu -->
    <div id="menu">
        <div class="menu__container" v-bind:class="[position, float]" v-if="visible">
            <h1 class="menu__title">
                {{ titleMenu }}
                <span class="menu__subtitle">{{ businessMoney }}</span>
            </h1>
            <div class="menu__body menu__scroll" id="scrolldiv">
                <div v-for="item, index in menu" class="menu__item" v-bind:class="{ 'active': item.active}">
                    <span v-html="item.label"></span>
                </div>
            </div>
            <div class="menu__bottom"></div>
        </div>
    </div>

    <!-- HELP KEYS -->
    <div id="helpKeysApp" v-if="visible">
        <div :class="classList" :style="styleList" v-if="helpKeys.length">
            <div class="help-keys__key" v-for="key in helpKeys">
                <span class="help-keys__label" v-html="key.label"></span>
                <kbd class="help-keys__button" v-html="key.keyName"></kbd>
            </div>
        </div>
    </div>

    <!-- MINIGAME -->
    <div id="minigameApp" v-if="visible">
        <div class="minigame" ref="minigame">
            <div v-if="showConfirmDialog" class="minigame__dialog">
                <div class="minigame__dialog-content">
                    <h1 class="minigame__dialog-title">{{ texts.confirm_title }}</h1>
                    <p class="minigame__dialog-desc">{{ texts.confirm_desc }}</p>
                </div>
                <div class="minigame__dialog-line minigame__dialog-line--red" ref="confirmLine"></div>
            </div>

            <template v-else>
                <div class="minigame__area" ref="balanceArea"></div>
                <div class="minigame__fill-area" ref="fillArea"></div>
                <div class="minigame__cursor" ref="cursor"></div>

                <div class="minigame__dialog">
                    <div class="minigame__dialog-content">
                        <h1 class="minigame__dialog-title">{{ texts.tutorial_title }}</h1>
                        <p class="minigame__dialog-desc">{{ texts.tutorial_desc }}</p>
                    </div>
                    <div class="minigame__dialog-line" ref="tutorialLine"></div>
                </div>
            </template>
        </div>
    </div>

    <!-- Scripts -->
    <script src="./js/vue.min.js" type="text/javascript"></script>
    <script src="./js/business.js" type="module"></script>
    <script src="./js/helpKeys.js" type="text/javascript"></script>
    <script src="./js/minigame.js" type="module"></script>
    <script src="./js/script.js" type="module"></script>
</body>

</html>