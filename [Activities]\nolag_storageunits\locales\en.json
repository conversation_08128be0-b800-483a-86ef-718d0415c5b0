{"expired_storage": "This storage is locked because it was not paid for.", "not_enough_money": "You do not have enough money", "owned_storage": "This storage belongs to someone else.", "rented_storage": "You have successfully rented a storage for %s.", "day_singular": "1 day", "day_plural": "%s days", "rent_canceled": "You have successfully canceled the rent", "rent_storage": "Rent a storage", "rent_storage_description": "The storage belongs to no one, would you like to rent it?  \n Payments are made only through bank transfer!  \n The rent is $%s per %s days.  \n Inventory slots: %s  \n Inventory weight: %skg", "cancel_subscription": "Cancel rent", "cancel_description": "After canceling the rent, everything from the storage will be removed and you will not get your money back.", "open_storage": "Open Storage", "storage": "Storage", "storages": "Storages", "options": "Options", "change_password": "Change password", "new_password": "New password", "enter_password": "Enter password", "wrong_password": "Wrong password", "changed_password": "You have successfully changed the storage password.", "no_players_nearby": "No players nearby", "transfer_storage": "Transfer storage", "select_player": "Select person", "storage_transferred": "The storage has been transferred", "something_went_wrong": "Something went wrong", "transfer_storage_description": "%s wants to transfer their storage to you. Press the button below to confirm that you want to receive it.", "player_did_not_accept": "The player did not accept", "storage_received": "You have received storage", "create_storage": "Create storage", "storage_created_title": "Storage Created", "storage_created_description": "You have successfully created a storage.", "storage_created_error_title": "Storage Creation Error", "storage_created_error_description": "There is a problem with creating a storage.", "raid_storage": "Raid storage", "storage_raid_success": "You have successfully raided the storage", "storage_raid_fail": "You have failed to raid the storage", "not_authorized": "You are not authorized to do this", "edit_storage": "Edit storage", "delete_storage": "Delete storage", "delete_storage_description": "Are you sure you want to delete this storage? This action cannot be undone. All items will be lost.", "label": "Label", "label_description": "The name of the storage", "price": "Price", "price_description": "The price of the storage", "can_buy_forever": "Can buy forever", "blip": "Blip", "rental_days": "Rental days", "storage_edited": "You have successfully edited the storage", "storage_deleted": "You have successfully deleted the storage", "set_waypoint": "Set waypoint", "waypoint_set": "Waypoint set", "lock_storage": "Lock storage", "storage_locked": "Storage locked", "unlock_storage": "Unlock storage", "storage_unlocked": "Storage unlocked", "renter": "Renter", "expired": "Expired", "rented": "Rented", "remove_renter": "Remove renter", "renter_removed": "Renter removed", "point_help": "`LMB` - Set point coords", "rented_until": "Rented until", "storages_not_loaded": "Storages are not loaded", "keybind_description": "Open the storage menu", "keybind_open": "[%s] Storage", "inventory_slots": "Inventory slots", "inventory_slots_description": "The number of slots in the storage", "inventory_weightlimit": "Inventory weight limit", "inventory_weightlimit_description": "The weight limit of the storage", "buy_storage": "Buy storage", "buy_storage_description": "The storage belongs to no one, would you like to buy it?  \n Payments are made only through bank transfer!  \n The price is $%s.  \n Inventory slots: %s  \n Inventory weight: %skg", "storage_bought": "You have successfully bought the storage", "bought": "Bought", "logs_created_storage": "Player %s created a storage. Storage ID: %s, Storage Label: %s, Storage Price: %s", "logs_rented_storage": "Player %s rented a storage. Storage ID: %s, Storage Label: %s, Rental Days: %s, Storage Price: %s", "logs_changed_password": "Player %s changed the password of the storage. Storage ID: %s, Storage Label: %s", "logs_canceled_subscription": "Player %s canceled the rent of the storage. Storage ID: %s, Storage Label: %s", "logs_transferred_storage": "Player %s transferred the storage to %s. Storage ID: %s, Storage Label: %s", "logs_deleted_storage": "Player %s deleted the storage. Storage ID: %s, Storage Label: %s", "logs_expired_storage": "Storage ID: %s, Storage Label: %s", "logs_handled_payment": "Player %s paid for the storage. Storage ID: %s, Payment Type: %s, Storage Price: %s", "logs_failed_payment": "Player %s failed to pay for the storage. Storage ID: %s, Payment Type: %s, Storage Price: %s", "logs_bought_storage": "Player %s bought the storage. Storage ID: %s, Storage Label: %s, Storage Price: %s", "logs_raided_storage": "Player %s raided the storage. Storage ID: %s, Storage Label: %s"}