
-- Learning Method, Dont touch if you dont know what you doing, if your a Dev, here is where you can modify or implement the learning method.

local notificationShown = false
local asleep2 = 2000

Citizen.CreateThread(function()
    while true do
        Citizen.Wait(asleep2) 
        local playerPed = PlayerPedId()

        local distance = #(GetEntityCoords(playerPed) - vector3(Config.spawnCoords.x, Config.spawnCoords.y, Config.spawnCoords.z))
        local distance2 = #(GetEntityCoords(playerPed) - vector3(Config.spawnCoords2.x, Config.spawnCoords2.y, Config.spawnCoords2.z))
        local distance3 = #(GetEntityCoords(playerPed) - vector3(Config.spawnCoords3.x, Config.spawnCoords3.y, Config.spawnCoords3.z))
        local Fighting1 = exports['BDX-Fighting']:IsFighting1()
        local Fighting2 = exports['BDX-Fighting']:IsFighting2()
        local Fighting3 = exports['BDX-Fighting']:IsFighting3()

        if distance <= stances.notificationRadius or distance2 <= stances.notificationRadius or distance3 <= stances.notificationRadius then

            asleep2 = 10
            if not notificationShown then
                if distance <= stances.notificationRadius then
                    if Fighting1 then
                        ShowHelpNotification("Press E to unlearn Fight Style")
                    else
                        ShowHelpNotification("Press E to learn Fight Style")
                    end
                    notificationShown = true
                elseif distance2 <= stances.notificationRadius then
                    if Fighting3 then
                        ShowHelpNotification("Press E to unlearn Fight Style")
                    else
                        ShowHelpNotification("Press E to learn Fight Style")
                    end
                    notificationShown = true
                elseif distance3 <= stances.notificationRadius then
                    if Fighting2 then
                        ShowHelpNotification("Press E to unlearn Fight Style")
                    else
                        ShowHelpNotification("Press E to learn Fight Style")
                    end
                    notificationShown = true
                end
            end

            if IsControlJustPressed(0, 38) then -- 38 corresponds to the "E" key
                if distance <= stances.notificationRadius then
                    if Fighting1 then
                        TriggerEvent('UnlearnFight1')
                    else
                        TriggerEvent('LearnFight1')
                    end
                elseif distance2 <= stances.notificationRadius then
                    if Fighting3 then
                        TriggerEvent('UnlearnFight3')
                    else
                        TriggerEvent('LearnFight3')
                    end
                elseif distance3 <= stances.notificationRadius then
                    if Fighting2 then
                        TriggerEvent('UnlearnFight2')
                    else
                        TriggerEvent('LearnFight2')
                    end
                end
            end
        else
            notificationShown = false
            asleep2 = 2000
        end
    end
end)

--Dont Touch if youre not a Dev.
--This is an Export if you want to the fights can be disable by another resource otherwise leave it like this.

exported = {
    resource = 'BDX-Fighting'
}

---ADD TO NEW RECOURSE---
local DisableFight = false

exports('CanFight', function()
    return DisableFight
end)