-- server.lua

-- Whitelist | Steam | Discord | FiveM

local FightsWhitelist = {
    "discord:1174154534449332396", -- Creator Discord ID.
    "discord:1174154741354352640", -- Creator Discord ID.
    "discord:1174154857708531743", -- Creator Discord ID.
    "discord:1174154783674871919", -- Creator Discord ID.
    "steam:110000112345678", -- Your ID.
    "fivem:17703310",   -- Add your Clients!
}

--- Function to check if an identifier is whitelisted
function isPlayerWhitelisted(identifier)
    for _, id in ipairs(FightsWhitelist) do
        if id == identifier then
            return true
        end
    end
    return false
end

-- Event to check if the player is whitelisted
RegisterNetEvent('CheckFightsWhitelist')
AddEventHandler('CheckFightsWhitelist', function()
    local _source = source
    local identifiers = GetPlayerIdentifiers(_source)
    local isWhitelisted = false

    for _, identifier in ipairs(identifiers) do
        if isPlayerWhitelisted(identifier) then
            isWhitelisted = true
            break
        end
    end

    -- Notify the client about the whitelist status
    TriggerClientEvent('ReceiveFightsWhitelistStatus', _source, isWhitelisted)
end)
