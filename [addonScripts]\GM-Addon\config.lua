Config = {}

-- Starter Pack
Config.Reward = {
    { name = 'weapon_pepperspray', count = 1 },
    { name = 'dirtymoney', count = 5000 },
    { name = 'money', count = 5000 },
    { name = 'WEAPON_HATCHET', count = 1 },

}

-- Advertisement System 
Config.Advertisement = {
    enabled = true,                   
    cost = 2500,                       
    cooldown = 300,                   -- (5 minutes)
    maxLength = 250,                
    minLength = 10,                   

    jobRestricted = true,            
    allowedJobs = {                   
        'catcafe',  
        'hotbox',
        'cloud9',
        'venguardautos',
        'club77',
        'dragonautos',
        'vanguardautos',
        'drivelinecustoms',
        'gpub',
        'lifeinvader',
        'hotbox',
        'vividvisions',
        'bahamamamas',
        'lustresort',
        'mommymm',
        'speedcustoms',
        'vanillaunicorn',
        'verspuccibeachbar',
        'whitewidow',
    },

    notification = {
        enabled = true,              
        title = "📢 Advertisement",
        duration = 15000,             -- (15 seconds)
    },

    notifications = {
        success = "Advertisement posted successfully! Cost: $%s",
        insufficient_funds = "Insufficient funds! You need $%s to post an advertisement.",
        cooldown = "You must wait %s more seconds before posting another advertisement.",
        too_short = "Advertisement message is too short! Minimum %s characters required.",
        too_long = "Advertisement message is too long! Maximum %s characters allowed.",
        invalid_message = "Please provide a valid advertisement message.",
        job_restricted = "You do not have permission to use advertisements. Only certain jobs can post ads."
    }
}

-- Blip System
Config.Blips = {
    enabled = false,  --- Disable the whole system
    regularBlips = {
        --[[ {
            name = "Pink Trap",
            coords = vector3(-1240.305, -1239.947, 14.9),
            sprite = 51,             
            color = 8,              
            scale = 0.8,             
            shortRange = true,        
            enabled = true            
        }, ]]
    },
    radiusBlips = {
        --[[ {
            name = "Test Radius",
            coords = vector3(0.0, 0.0, 0.0),
            radius = 10,  
            color = 1, 
            alpha = 100, 
            enabled = false           
        }, ]]
    }
}

return Config
