if GetResourceState('qbx_core') ~= 'started' then
    return
end

lib.print.debug('Loading QBox Framework')
local sharedConfig = require 'config.shared'

Framework = {
    GetPlayerFromId = function(source)
        local xPlayer = {}
        local qbPlayer = exports.qbx_core:GetPlayer(source)
        ---------
        if not qbPlayer then
            return nil
        end
        xPlayer.source = qbPlayer.PlayerData.source
        ---------
        xPlayer.identifier = qbPlayer.PlayerData.citizenid
        ---------
        xPlayer.license = qbPlayer.PlayerData.license
        ---------
        local gradeName = "none"
        local gradeLevel = -1

        if qbPlayer.PlayerData.job.grade then
            gradeName = qbPlayer.PlayerData.job.grade.name
            gradeLevel = qbPlayer.PlayerData.job.grade.level
        end

        xPlayer.job = {
            name = qbPlayer.PlayerData.job.name,
            label = qbPlayer.PlayerData.job.label,
            grade = {
                name = gradeName,
                level = gradeLevel
            }
        }
        ---------
        xPlayer.playerData = qbPlayer.PlayerData
        ---------
        xPlayer.getJob = function()
            if not qbPlayer.PlayerData.job or not qbPlayer.PlayerData.job.grade then
                return {
                    grade = 0,
                    grade_name = nil,
                    name = nil
                }
            end

            local gradeName = "none"
            local gradeLevel = -1

            if qbPlayer.PlayerData.job.grade then
                gradeName = qbPlayer.PlayerData.job.grade.name
                gradeLevel = qbPlayer.PlayerData.job.grade.level
            end

            return {
                grade = gradeLevel,
                grade_name = gradeName,
                name = qbPlayer.PlayerData.job.name or "none"
            }
        end
        ---------
        xPlayer.getName = function()
            if qbPlayer.PlayerData.charinfo and qbPlayer.PlayerData.charinfo.firstname then
                return qbPlayer.PlayerData.charinfo.firstname
            else
                return qbPlayer.PlayerData.name
            end
        end
        ---------
        xPlayer.getAccount = function(type)
            return {
                money = qbPlayer.Functions.GetMoney(type) or 0
            }
        end
        ---------
        xPlayer.removeAccountMoney = function(type, money, reason)
            qbPlayer.Functions.RemoveMoney(type, money, reason)
        end
        return xPlayer
    end,

    GetPlayerFromIdentifier = function(playerIdentifier)
        local xPlayer = {}
        local qbPlayer = exports.qbx_core:GetPlayerByCitizenId(playerIdentifier)
        ---------
        if not qbPlayer then
            return nil
        end
        xPlayer.source = qbPlayer.PlayerData.source
        ---------
        xPlayer.identifier = qbPlayer.PlayerData.citizenid
        ---------
        xPlayer.license = qbPlayer.PlayerData.license
        ---------
        local gradeName = "none"
        local gradeLevel = -1

        if qbPlayer.PlayerData.job.grade then
            gradeName = qbPlayer.PlayerData.job.grade.name
            gradeLevel = qbPlayer.PlayerData.job.grade.level
        end

        xPlayer.job = {
            name = qbPlayer.PlayerData.job.name,
            label = qbPlayer.PlayerData.job.label,
            grade = {
                name = gradeName,
                level = gradeLevel
            }
        }
        ---------
        xPlayer.getJob = function()
            if not qbPlayer.PlayerData.job or not qbPlayer.PlayerData.job.grade then
                return {
                    grade = 0,
                    grade_name = nil,
                    name = nil
                }
            end

            local gradeName = "none"
            local gradeLevel = -1

            if qbPlayer.PlayerData.job.grade then
                gradeName = qbPlayer.PlayerData.job.grade.name
                gradeLevel = qbPlayer.PlayerData.job.grade.level
            end

            return {
                grade = gradeLevel,
                grade_name = gradeName,
                name = qbPlayer.PlayerData.job.name or "none"
            }
        end
        ---------
        xPlayer.getName = function()
            if qbPlayer.PlayerData.charinfo and qbPlayer.PlayerData.charinfo.firstname and qbPlayer.PlayerData.charinfo.lastname then
                return qbPlayer.PlayerData.charinfo.firstname .. " " .. qbPlayer.PlayerData.charinfo.lastname
            else
                return qbPlayer.PlayerData.name
            end
        end
        ---------
        xPlayer.removeAccountMoney = function(type, money, reason)
            qbPlayer.Functions.RemoveMoney(type, money, reason)
        end
        ---------
        xPlayer.getAccount = function(type)
            return {
                money = qbPlayer.Functions.GetMoney(type) or 0
            }
        end
        return xPlayer
    end,

    IsPlayerAuthorized = function(player)
        for jobName, grade in pairs(sharedConfig.realEstateJobs) do
            if jobName == player.job.name and player.job.grade.level >= grade then
                return true
            end
        end
        return false
    end,

    IsPlayerAuthorizedToRaid = function(player)
        for jobName, grade in pairs(sharedConfig.policeRaid.jobs) do
            if jobName == player.job.name and player.job.grade.level >= grade then
                return true
            end
        end
        return false
    end,

    IsPlayerAuthorizedToDeleteStorage = function(player)
        return Framework.IsPlayerAuthorized(player)
    end,

    IsPlayerAuthorizedToLockdown = function(player)
        return Framework.IsPlayerAuthorizedToRaid(player)
    end,

    IsPlayerAuthorizedToManageStorages = function(player)
        return Framework.IsPlayerAuthorized(player)
    end
}

GetOfflinePlayer = function(playerIdentifier)
    local xPlayer = {}
    local qbPlayer = exports.qbx_core:GetOfflinePlayer(playerIdentifier)
    ---------
    if not qbPlayer then
        return nil
    end
    xPlayer.identifier = qbPlayer.PlayerData.citizenid
    ---------
    xPlayer.license = qbPlayer.PlayerData.license
    ---------
    xPlayer.getName = function()
        if qbPlayer.PlayerData.charinfo and qbPlayer.PlayerData.charinfo.firstname and qbPlayer.PlayerData.charinfo.lastname then
            return qbPlayer.PlayerData.charinfo.firstname .. " " .. qbPlayer.PlayerData.charinfo.lastname
        else
            return qbPlayer.PlayerData.name
        end
    end
    ---------
    xPlayer.removeAccountMoney = function(type, money, reason)
        qbPlayer.Functions.RemoveMoney(type, money, reason)
    end
    ---------
    xPlayer.getAccount = function(type)
        return {
            money = qbPlayer.Functions.GetMoney(type) or 0
        }
    end
    return xPlayer
end
