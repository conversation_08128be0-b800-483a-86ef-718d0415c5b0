shared_scripts { '@FiniAC/fini_events.lua' }

fx_version "cerulean"
game "gta5"
author "Pickle Mods"
version "v1.1.3"

ui_page "nui/index.html"

files {
	"nui/index.html",
	"nui/assets/*.*",
    'stream/*.ytyp'
}

-- Data files
data_file 'DLC_ITYP_REQUEST' 'stream/picklemods_solargas_props.ytyp'

shared_scripts {
	"@ox_lib/init.lua",
	"config.lua",
	"locales/locale.lua",
    "locales/translations/*.lua",
	"modules/**/shared.lua",
    "core/shared.lua"
}

client_scripts {
	"bridge/**/**/client.lua",
	"modules/**/client.lua",
    "core/client.lua"
}

server_scripts {
	"@mysql-async/lib/MySQL.lua",
	"bridge/**/**/server.lua",
	"modules/**/server.lua",
}

lua54 'yes'

escrow_ignore {
	"config.lua",
	"__INSTALL/**/*.*",
	"bridge/**/**/*.*",
	"bridge/**/*.*",
    "locales/locale.lua",
    "locales/translations/*.lua",
	-- Comment below if source --
	"modules/**/*.*",
	"core/*.*",
}
dependency '/assetpacks'