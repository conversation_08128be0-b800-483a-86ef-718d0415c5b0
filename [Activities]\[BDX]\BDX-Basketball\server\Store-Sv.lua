
local spawnedShops, netIdTableS = {}, {}
local menuLock = nil -- Stores the ID of the player who has the menu open

AddEventHandler('onResourceStart', function(resourceName)
    if (GetCurrentResourceName() ~= resourceName) then return end
    Shop.CreateShopPeds()
end)

AddEventHandler('onResourceStop', function(resourceName)
    if (GetCurrentResourceName() ~= resourceName) then return end
    Shop.DeleteEntities() 
end)
RegisterServerEvent("BDX-Basketball:SyncPeds")
AddEventHandler("BDX-Basketball:SyncPeds", function()
    TriggerClientEvent('Basketball-Shop:pedHandlers', source, netIdTableS)
end)
Shop = {
    CreateShopPeds = function()
        for i = 1, #Config.Shops.ShopPeds do
            local model = Config.Shops.ShopPeds[i].Model
            local coords = Config.Shops.ShopPeds[i].Position

            spawnedShops[i] = CreatePed(4, model, coords.x, coords.y, coords.z, coords.w, true, true)

            while not DoesEntityExist(spawnedShops[i]) do Wait(5) end

            FreezeEntityPosition(spawnedShops[i], true)
            netIdTableS[i] = NetworkGetNetworkIdFromEntity(spawnedShops[i])

            if Config.Debug then
            print("^2[INFO] Spawned NPC " .. i .. " with NetID: " .. tostring(netIdTableS[i]) .. "^0")
            end
        end
    end,
    DeleteEntities = function()
        for i = 1, #spawnedShops do
            if DoesEntityExist(spawnedShops[i]) then
                DeleteEntity(spawnedShops[i])
            end
            spawnedShops[i] = nil
        end
    end
}
RegisterNetEvent("bodhix-Basketball:purchaseItem")
AddEventHandler("bodhix-Basketball:purchaseItem", function(itemType, itemId)
    local src = source
    local price = nil
    local xPlayer = nil
    local hasMoney = false

	if itemType == "Ball" then
		price = Config.DesignPrice  
	else
        if Config.Debug then
		print("^1[ERROR] Invalid purchase type!^0")
        end

		cb("error")
		return
	end

    if Config.Debug then
        print("Server: Purchasing " .. itemType .. " Model: " .. itemId)
    end
    -- Framework detection based on config
    if Config.Framework == "qb" then
        local QBCore = exports[Config.FrameworkResourceName or "qb-core"]:GetCoreObject()
        xPlayer = QBCore.Functions.GetPlayer(source)
        if xPlayer then
            hasMoney = xPlayer.Functions.RemoveMoney("cash", price)
        end
        if Config.Debug then
            print('QB: Player has been Charged for ' .. price)
        end
    elseif Config.Framework == "esx" then
        local ESX = exports[Config.FrameworkResourceName or "es_extended"]:getSharedObject()
        xPlayer = ESX.GetPlayerFromId(source)
        if xPlayer and xPlayer.getMoney() >= price then
            xPlayer.removeMoney(price)
            hasMoney = true
        end
        if Config.Debug then
            print('ESX: Player has been Charged for ' .. price)
        end
    elseif Config.Framework == "vrp" then
        local user_id = vRP.getUserId({src})
        if user_id then
            hasMoney = vRP.tryPayment({user_id, price}) -- Tries to remove cash
            if Config.Debug then
                if hasMoney then
                    print("vRP: Charged user_id " .. user_id .. " $" .. price)
                else
                    print("vRP: Player " .. user_id .. " does not have enough money.")
                end
            end
        end    
    elseif Config.Framework == "custom" and Config.CustomFrameworkFunctions then
        -- Calls the custom money check & deduction functions set in config
        xPlayer = Config.CustomFrameworkFunctions.GetPlayer(source)
        if xPlayer then
            hasMoney = Config.CustomFrameworkFunctions.RemoveMoney(source, price)
        end
        if Config.Debug then
            print('Custom: Player has been Charged for ' .. price)
        end
    end

    -- If player was not found or framework isn't set properly
    if not xPlayer then
        if Config.Debug then
        print("^1[ERROR] Player not found! Check Config.Framework settings.^0")
        end
        return
    end

    -- If player has enough money, process the purchase
    if hasMoney then
        local modelName = nil
        if itemType == "Ball" then
            modelName = "prop_bskball_" .. tostring(itemId)
            if Config.Debug then
                print('Ball model: ' .. modelName)
            end
            TriggerClientEvent('bodhix-Basketball:purchase', src, itemType, modelName)
        else
            print("^1[ERROR] Invalid purchase type!^0")
            cb("error")
            return
        end
        if Config.Debug then
        print("^2[INFO] Player " .. src .. " purchased " .. modelName .. " for $" .. price .. "^0")
        end
    else
        TriggerClientEvent('Basketball-Store:PurchaseFailed', src)
        if Config.Debug then
            print("^1[ERROR] Player " .. src .. " does not have enough money!^0")
        end
    end
end)
