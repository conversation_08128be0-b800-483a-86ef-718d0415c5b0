local preCategory = {
        label = "Collectores",
        pageValue = "collectores-menu",
        categoryType = "item",
        items = {
            {
                id = 1,
                label = "Ks Diaperbag 01",
                itemName = "ks_diaperbag_01",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Bags Backpacks",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_diaperbag_01.png",
            },
            {
                id = 2,
                label = "Ks Diaperbag 02",
                itemName = "ks_diaperbag_02",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Bags Backpacks",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_diaperbag_02.png",
            },
            {
                id = 3,
                label = "Ks Diaperbag 03",
                itemName = "ks_diaperbag_03",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Bags Backpacks",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_diaperbag_03.png",
            },
            {
                id = 4,
                label = "Ks Diaperbag 04",
                itemName = "ks_diaperbag_04",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Bags Backpacks",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_diaperbag_04.png",
            },
            {
                id = 5,
                label = "Ks Diaperbag 05",
                itemName = "ks_diaperbag_05",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Bags Backpacks",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_diaperbag_05.png",
            },
            {
                id = 6,
                label = "Ks Goblinhat 1",
                itemName = "ks_goblinhat_01",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Headwear Hats",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_goblinhat_01.png",
            },

            {
                id = 7,
                label = "Ks Headflower 1",
                itemName = "ks_headflower_01",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Headwear Hats",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_headflower_01.png",
            },

            {
                id = 8,
                label = "Ks Headflower 2",
                itemName = "ks_headflower_02",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Headwear Hats",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_headflower_02.png",
            },

            {
                id = 9,
                label = "Ks Headflower 3",
                itemName = "ks_headflower_03",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Headwear Hats",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_headflower_03.png",
            },

            {
                id = 10,
                label = "Ks Headflower 4",
                itemName = "ks_headflower_04",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Headwear Hats",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_headflower_04.png",
            },

            {
                id = 11,
                label = "Ks Headflower 5",
                itemName = "ks_headflower_05",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Headwear Hats",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_headflower_05.png",
            },

            {
                id = 12,
                label = "Ks Headflower 6",
                itemName = "ks_headflower_06",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Headwear Hats",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_headflower_06.png",
            },

            {
                id = 13,
                label = "Ks Polarbearhat 1",
                itemName = "ks_polarbearhat_01",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Headwear Hats",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_polarbearhat_01.png",
            },

            {
                id = 14,
                label = "Ks Poweruphat 1",
                itemName = "ks_poweruphat_01",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Headwear Hats",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_poweruphat_01.png",
            },

            {
                id = 15,
                label = "Ks Poweruphat 2",
                itemName = "ks_poweruphat_02",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Headwear Hats",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_poweruphat_02.png",
            },

            {
                id = 16,
                label = "Ks Poweruphat 3",
                itemName = "ks_poweruphat_03",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Headwear Hats",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_poweruphat_03.png",
            },

            {
                id = 17,
                label = "Ks Poweruphat 4",
                itemName = "ks_poweruphat_04",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Headwear Hats",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_poweruphat_04.png",
            },

            {
                id = 18,
                label = "Ks Poweruphat 5",
                itemName = "ks_poweruphat_05",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Headwear Hats",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_poweruphat_05.png",
            },

            {
                id = 19,
                label = "Ks Poweruphat 6",
                itemName = "ks_poweruphat_06",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Headwear Hats",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_poweruphat_06.png",
            },

            {
                id = 20,
                label = "Ks Poweruphat 7",
                itemName = "ks_poweruphat_07",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Headwear Hats",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_poweruphat_07.png",
            },

            {
                id = 21,
                label = "Ks Renhat 1",
                itemName = "ks_renhat_01",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Headwear Hats",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_renhat_01.png",
            },

           

            {
                id = 34,
                label = "Ks Santaclaushat 1",
                itemName = "ks_santaclaushat_01",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Headwear Hats",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_santaclaushat_01.png",
            },

            {
                id = 35,
                label = "Ks Santahat 1",
                itemName = "ks_santahat_01",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Headwear Hats",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_santahat_01.png",
            },

            {
                id = 36,
                label = "Ks Santahat 2",
                itemName = "ks_santahat_02",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Headwear Hats",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_santahat_02.png",
            },

            {
                id = 37,
                label = "Ks Santahat 3",
                itemName = "ks_santahat_03",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Headwear Hats",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_santahat_03.png",
            },

            {
                id = 38,
                label = "Ks Santahat 4",
                itemName = "ks_santahat_04",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Headwear Hats",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_santahat_04.png",
            },

            {
                id = 39,
                label = "Ks Santahat 5",
                itemName = "ks_santahat_05",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Headwear Hats",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_santahat_05.png",
            },

            {
                id = 40,
                label = "Ks Santahat 6",
                itemName = "ks_santahat_06",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Headwear Hats",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_santahat_06.png",
            },

            {
                id = 41,
                label = "Ks Snowmanhat 1",
                itemName = "ks_snowmanhat_01",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Headwear Hats",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_snowmanhat_01.png",
            },

            {
                id = 42,
                label = "Ks Sunhat 1",
                itemName = "ks_sunhat_01",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Headwear Hats",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_sunhat_01.png",
            },

            {
                id = 43,
                label = "Ks Sunhat 2",
                itemName = "ks_sunhat_02",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Headwear Hats",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_sunhat_02.png",
            },

            {
                id = 44,
                label = "Ks Sunhat 3",
                itemName = "ks_sunhat_03",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Headwear Hats",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_sunhat_03.png",
            },

            {
                id = 45,
                label = "Ks Sunhat 4",
                itemName = "ks_sunhat_04",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Headwear Hats",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_sunhat_04.png",
            },

            {
                id = 46,
                label = "Ks Sunhat 5",
                itemName = "ks_sunhat_05",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Headwear Hats",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_sunhat_05.png",
            },

            {
                id = 47,
                label = "Ks Sunhat 6",
                itemName = "ks_sunhat_06",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Headwear Hats",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_sunhat_06.png",
            },

            {
                id = 48,
                label = "Ks Winterhat 1",
                itemName = "ks_winterhat_01",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Headwear Hats",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_winterhat_01.png",
            },

            {
                id = 49,
                label = "Ks Witchhat 1",
                itemName = "ks_witchhat_01",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Headwear Hats",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_witchhat_01.png",
            },

            {
                id = 50,
                label = "Ks Witchhat 2",
                itemName = "ks_witchhat_02",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Headwear Hats",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_witchhat_02.png",
            },

            {
                id = 51,
                label = "Ks Witchhat 3",
                itemName = "ks_witchhat_03",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Headwear Hats",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_witchhat_03.png",
            },

            {
                id = 52,
                label = "Ks Witchhat 4",
                itemName = "ks_witchhat_04",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Headwear Hats",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_witchhat_04.png",
            },

            {
                id = 53,
                label = "Ks Witchhat 5",
                itemName = "ks_witchhat_05",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Headwear Hats",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_witchhat_05.png",
            },

            {
                id = 54,
                label = "Ks Witchhat 6",
                itemName = "ks_witchhat_06",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Headwear Hats",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_witchhat_06.png",
            },
            {
                id = 55,
                label = "Ks Cathood 01",
                itemName = "ks_cathood_01",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Masks_Hoods",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_cathood_01.png",
            },

            {
                id = 56,
                label = "Ks Cathood 02",
                itemName = "ks_cathood_02",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Masks_Hoods",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_cathood_02.png",
            },

            {
                id = 57,
                label = "Ks Cathood 03",
                itemName = "ks_cathood_03",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Masks_Hoods",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_cathood_03.png",
            },

            {
                id = 58,
                label = "Ks Cathood 04",
                itemName = "ks_cathood_04",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Masks_Hoods",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_cathood_04.png",
            },

            {
                id = 59,
                label = "Ks Cathood 05",
                itemName = "ks_cathood_05",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Masks_Hoods",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_cathood_05.png",
            },

            {
                id = 60,
                label = "Ks Cathood 06",
                itemName = "ks_cathood_06",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Masks_Hoods",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_cathood_06.png",
            },
            {
                id = 61,
                label = "Bearharness 01",
                itemName = "ks_bearharness_01",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Clothing Wearables",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_bearharness_01.png",
            },
            {
                id = 62,
                label = "Bearharness 02",
                itemName = "ks_bearharness_02",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Clothing Wearables",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_bearharness_02.png",
            },
            {
                id = 63,
                label = "Bearharness 03",
                itemName = "ks_bearharness_03",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Clothing Wearables",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_bearharness_03.png",
            },
            {
                id = 64,
                label = "Bearharness 04",
                itemName = "ks_bearharness_04",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Clothing Wearables",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_bearharness_04.png",
            },
            {
                id = 65,
                label = "Bearharness 05",
                itemName = "ks_bearharness_05",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Clothing Wearables",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_bearharness_05.png",
            },
            {
                id = 66,
                label = "Bearscarf 01",
                itemName = "ks_bearscarf_01",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Clothing Wearables",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_bearscarf_01.png",
            },
            {
                id = 67,
                label = "Bearscarf 02",
                itemName = "ks_bearscarf_02",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Clothing Wearables",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_bearscarf_02.png",
            },
            {
                id = 68,
                label = "Bearscarf 03",
                itemName = "ks_bearscarf_03",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Clothing Wearables",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_bearscarf_03.png",
            },
            {
                id = 69,
                label = "Bearscarf 04",
                itemName = "ks_bearscarf_04",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Clothing Wearables",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_bearscarf_04.png",
            },
            {
                id = 70,
                label = "Bearscarf 05",
                itemName = "ks_bearscarf_05",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Clothing Wearables",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_bearscarf_05.png",
            },
            {
                id = 71,
                label = "Bearscarf 06",
                itemName = "ks_bearscarf_06",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Clothing Wearables",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_bearscarf_06.png",
            },
            {
                id = 72,
                label = "Bunnyharness 01",
                itemName = "ks_bunnyharness_01",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Clothing Wearables",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_bunnyharness_01.png",
            },
            {
                id = 73,
                label = "Bunnyharness 02",
                itemName = "ks_bunnyharness_02",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Clothing Wearables",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_bunnyharness_02.png",
            },
            {
                id = 74,
                label = "Bunnyharness 03",
                itemName = "ks_bunnyharness_03",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Clothing Wearables",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_bunnyharness_03.png",
            },
            {
                id = 75,
                label = "Bunnyharness 04",
                itemName = "ks_bunnyharness_04",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Clothing Wearables",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_bunnyharness_04.png",
            },
            {
                id = 76,
                label = "Bunnyharness 05",
                itemName = "ks_bunnyharness_05",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Clothing Wearables",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_bunnyharness_05.png",
            },
            {
                id = 77,
                label = "Bearhb 01",
                itemName = "ks_bearhb_01",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Jewelry Accessories",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_bearhb_01.png",
            },

            {
                id = 78,
                label = "Betweenus 01",
                itemName = "ks_betweenus_01",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Jewelry Accessories",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_betweenus_01.png",
            },

            {
                id = 79,
                label = "Betweenus 02",
                itemName = "ks_betweenus_02",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Jewelry Accessories",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_betweenus_02.png",
            },

            {
                id = 80,
                label = "Betweenus 03",
                itemName = "ks_betweenus_03",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Jewelry Accessories",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_betweenus_03.png",
            },

            {
                id = 81,
                label = "Betweenus 04",
                itemName = "ks_betweenus_04",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Jewelry Accessories",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_betweenus_04.png",
            },

            {
                id = 82,
                label = "Betweenus 05",
                itemName = "ks_betweenus_05",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Jewelry Accessories",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_betweenus_05.png",
            },

            {
                id = 83,
                label = "Betweenus 06",
                itemName = "ks_betweenus_06",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Jewelry Accessories",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_betweenus_06.png",
            },

            {
                id = 84,
                label = "Betweenus 07",
                itemName = "ks_betweenus_07",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Jewelry Accessories",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_betweenus_07.png",
            },

            {
                id = 85,
                label = "Betweenus 08",
                itemName = "ks_betweenus_08",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Jewelry Accessories",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_betweenus_08.png",
            },

            {
                id = 86,
                label = "Betweenus 09",
                itemName = "ks_betweenus_09",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Jewelry Accessories",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_betweenus_09.png",
            },

            {
                id = 87,
                label = "Betweenus 10",
                itemName = "ks_betweenus_10",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Jewelry Accessories",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_betweenus_10.png",
            },

            {
                id = 88,
                label = "Betweenus 11",
                itemName = "ks_betweenus_11",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Jewelry Accessories",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_betweenus_11.png",
            },

            {
                id = 89,
                label = "Dad 01",
                itemName = "ks_dad_01",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Jewelry Accessories",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_dad_01.png",
            },

           
           
            {
                id = 111,
                label = "Pinehb 01",
                itemName = "ks_pinehb_01",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Jewelry Accessories",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_pinehb_01.png",
            },

            

            {
                id = 118,
                label = "Strawberryflowers 01",
                itemName = "ks_strawberryflowers_01",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Jewelry Accessories",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_strawberryflowers_01.png",
            },

            {
                id = 119,
                label = "Strawberryflowers 02",
                itemName = "ks_strawberryflowers_02",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Jewelry Accessories",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_strawberryflowers_02.png",
            },

            {
                id = 120,
                label = "Strawberryflowers 03",
                itemName = "ks_strawberryflowers_03",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Jewelry Accessories",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_strawberryflowers_03.png",
            },

            {
                id = 121,
                label = "Strawberryflowers 04",
                itemName = "ks_strawberryflowers_04",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Jewelry Accessories",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_strawberryflowers_04.png",
            },

            {
                id = 122,
                label = "Strawberryflowers 05",
                itemName = "ks_strawberryflowers_05",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Jewelry Accessories",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_strawberryflowers_05.png",
            },

           
            {
                id = 133,
                label = "Winneraward 01",
                itemName = "ks_winneraward_01",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Jewelry Accessories",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_winneraward_01.png",
            },

            {
                id = 134,
                label = "Winneraward 02",
                itemName = "ks_winneraward_02",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Jewelry Accessories",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_winneraward_02.png",
            },

            {
                id = 135,
                label = "Winneraward 03",
                itemName = "ks_winneraward_03",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Jewelry Accessories",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_winneraward_03.png",
            },

            {
                id = 136,
                label = "Winneraward 04",
                itemName = "ks_winneraward_04",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Jewelry Accessories",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_winneraward_04.png",
            },

            {
                id = 137,
                label = "Winneraward 05",
                itemName = "ks_winneraward_05",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Jewelry Accessories",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_winneraward_05.png",
            },

            {
                id = 138,
                label = "Winneraward 06",
                itemName = "ks_winneraward_06",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Jewelry Accessories",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_winneraward_06.png",
            },

            {
                id = 139,
                label = "Winneraward 07",
                itemName = "ks_winneraward_07",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Jewelry Accessories",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_winneraward_07.png",
            },

            {
                id = 140,
                label = "Winneraward 08",
                itemName = "ks_winneraward_08",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Jewelry Accessories",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_winneraward_08.png",
            },

            {
                id = 141,
                label = "Winneraward 09",
                itemName = "ks_winneraward_09",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Jewelry Accessories",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_winneraward_09.png",
            },
            {
                id = 142,
                label = "Angelwingc",
                itemName = "angelwingc",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Wings_Halos",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/angelwingc.png",
            },
            {
                id = 143,
                label = "Angelwingp",
                itemName = "angelwingp",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Wings_Halos",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/angelwingp.png",
            },
            {
                id = 144,
                label = "Angelwingw",
                itemName = "angelwingw",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Wings_Halos",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/angelwingw.png",
            },
            {
                id = 145,
                label = "Angelwingy",
                itemName = "angelwingy",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Wings_Halos",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/angelwingy.png",
            },
            {
                id = 146,
                label = "Batwingsb",
                itemName = "batwingsb",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Wings_Halos",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/batwingsb.png",
            },
            {
                id = 147,
                label = "Batwingsp",
                itemName = "batwingsp",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Wings_Halos",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/batwingsp.png",
            },
            {
                id = 148,
                label = "Batwingspk",
                itemName = "batwingspk",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Wings_Halos",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/batwingspk.png",
            },
            {
                id = 149,
                label = "Batwingsr",
                itemName = "batwingsr",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Wings_Halos",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/batwingsr.png",
            },
            {
                id = 150,
                label = "Batwingsw",
                itemName = "batwingsw",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Wings_Halos",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/batwingsw.png",
            },
            {
                id = 151,
                label = "Halob",
                itemName = "halob",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Wings_Halos",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/halob.png",
            },
            {
                id = 152,
                label = "Haloc",
                itemName = "haloc",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Wings_Halos",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/haloc.png",
            },
            {
                id = 153,
                label = "Halor",
                itemName = "halor",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Wings_Halos",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/halor.png",
            },
            {
                id = 154,
                label = "Halow",
                itemName = "halow",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Wings_Halos",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/halow.png",
            },
            {
                id = 155,
                label = "Haloy",
                itemName = "haloy",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Wings_Halos",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/haloy.png",
            },
            {
                id = 224,
                label = "Canastaoso G",
                itemName = "canastaoso-g",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Containers Boxes",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/canastaoso-g.png",
            },
            {
                id = 225,
                label = "Canastaoso R",
                itemName = "canastaoso-r",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Containers Boxes",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/canastaoso-r.png",
            },

            {
                id = 226,
                label = "Canastaoso Ro",
                itemName = "canastaoso-ro",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Containers Boxes",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/canastaoso-ro.png",
            },

            {
                id = 227,
                label = "Ks Cauldron 01",
                itemName = "ks_cauldron_01",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Containers Boxes",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_cauldron_01.png",
            },

            {
                id = 228,
                label = "Ks Cauldron 02",
                itemName = "ks_cauldron_02",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Containers Boxes",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_cauldron_02.png",
            },

            {
                id = 229,
                label = "Ks Cauldron 03",
                itemName = "ks_cauldron_03",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Containers Boxes",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_cauldron_03.png",
            },

            {
                id = 230,
                label = "Ks Cauldron 04",
                itemName = "ks_cauldron_04",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Containers Boxes",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_cauldron_04.png",
            },

            {
                id = 231,
                label = "Ks Cauldron 05",
                itemName = "ks_cauldron_05",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Containers Boxes",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_cauldron_05.png",
            },

            {
                id = 232,
                label = "Ks Cauldron 06",
                itemName = "ks_cauldron_06",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Containers Boxes",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_cauldron_06.png",
            },

            {
                id = 233,
                label = "Ks Ramobox",
                itemName = "ks_ramobox",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Containers Boxes",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_ramobox.png",
            },

            {
                id = 234,
                label = "Ks Ramobox G",
                itemName = "ks_ramobox_g",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Containers Boxes",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_ramobox_g.png",
            },

            {
                id = 235,
                label = "Ks Ramobox Hp",
                itemName = "ks_ramobox_hp",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Containers Boxes",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_ramobox_hp.png",
            },

            {
                id = 236,
                label = "Ks Ramobox R",
                itemName = "ks_ramobox_r",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Containers Boxes",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_ramobox_r.png",
            },

            {
                id = 237,
                label = "Ks Ramobox Rb",
                itemName = "ks_ramobox_rb",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Containers Boxes",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_ramobox_rb.png",
            },

            {
                id = 238,
                label = "Ks Ramobox W",
                itemName = "ks_ramobox_w",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Containers Boxes",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_ramobox_w.png",
            },

            {
                id = 239,
                label = "Ludboxb",
                itemName = "ludboxb",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Containers Boxes",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ludboxb.png",
            },

            {
                id = 240,
                label = "Ludboxbl",
                itemName = "ludboxbl",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Containers Boxes",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ludboxbl.png",
            },

            {
                id = 241,
                label = "Ludboxhp",
                itemName = "ludboxhp",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Containers Boxes",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ludboxhp.png",
            },

            {
                id = 242,
                label = "Ludboxp",
                itemName = "ludboxp",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Containers Boxes",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ludboxp.png",
            },

            {
                id = 243,
                label = "Ludboxr",
                itemName = "ludboxr",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Containers Boxes",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ludboxr.png",
            },

            {
                id = 244,
                label = "Millk B",
                itemName = "millk-b",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Containers Boxes",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/millk-b.png",
            },

            {
                id = 245,
                label = "Millk Be",
                itemName = "millk-be",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Containers Boxes",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/millk-be.png",
            },

            {
                id = 246,
                label = "Millk P",
                itemName = "millk-p",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Containers Boxes",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/millk-p.png",
            },

            {
                id = 247,
                label = "Millk Pu",
                itemName = "millk-pu",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Containers Boxes",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/millk-pu.png",
            },

            {
                id = 248,
                label = "Millk R",
                itemName = "millk-r",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Containers Boxes",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/millk-r.png",
            },

            {
                id = 249,
                label = "Osocaja Azu",
                itemName = "osocaja-azu",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Containers Boxes",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/osocaja-azu.png",
            },

            {
                id = 250,
                label = "Osocaja Roj",
                itemName = "osocaja-roj",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Containers Boxes",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/osocaja-roj.png",
            },

            {
                id = 251,
                label = "Osocaja Ros",
                itemName = "osocaja-ros",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Containers Boxes",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/osocaja-ros.png",
            },

            {
                id = 252,
                label = "Ramobilletes N",
                itemName = "ramobilletes-n",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Containers Boxes",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ramobilletes-n.png",
            },

            {
                id = 253,
                label = "Ramobilletes R",
                itemName = "ramobilletes-r",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Containers Boxes",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ramobilletes-r.png",
            },

            {
                id = 254,
                label = "Ramobilletes Ro",
                itemName = "ramobilletes-ro",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Containers Boxes",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ramobilletes-ro.png",
            },

            {
                id = 255,
                label = "Ramobilletes Rp",
                itemName = "ramobilletes-rp",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Containers Boxes",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ramobilletes-rp.png",
            },


            {
                id = 286,
                label = "Ks Armfloat 02",
                itemName = "ks_armfloat_02",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Sports Recreation",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_armfloat_02.png",
            },

            {
                id = 287,
                label = "Ks Armfloat 03",
                itemName = "ks_armfloat_03",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Sports Recreation",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_armfloat_03.png",
            },

            {
                id = 288,
                label = "Ks Armfloat 04",
                itemName = "ks_armfloat_04",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Sports Recreation",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_armfloat_04.png",
            },

            {
                id = 289,
                label = "Ks Armfloat 05",
                itemName = "ks_armfloat_05",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Sports Recreation",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_armfloat_05.png",
            },

            {
                id = 290,
                label = "Ks Armfloat 06",
                itemName = "ks_armfloat_06",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Sports Recreation",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_armfloat_06.png",
            },

            {
                id = 291,
                label = "Ks Surfboard 01",
                itemName = "ks_surfboard_01",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Sports Recreation",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_surfboard_01.png",
            },

            {
                id = 292,
                label = "Ks Surfboard 02",
                itemName = "ks_surfboard_02",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Sports Recreation",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_surfboard_02.png",
            },

            {
                id = 293,
                label = "Ks Surfboard 03",
                itemName = "ks_surfboard_03",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Sports Recreation",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_surfboard_03.png",
            },

            {
                id = 294,
                label = "Ks Surfboard 04",
                itemName = "ks_surfboard_04",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Sports Recreation",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_surfboard_04.png",
            },

            {
                id = 295,
                label = "Ks Surfboard 05",
                itemName = "ks_surfboard_05",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Sports Recreation",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_surfboard_05.png",
            },

            {
                id = 296,
                label = "Ks Swimfloat 01",
                itemName = "ks_swimfloat_01",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Sports Recreation",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_swimfloat_01.png",
            },

            {
                id = 297,
                label = "Ks Swimfloat 02",
                itemName = "ks_swimfloat_02",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Sports Recreation",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_swimfloat_02.png",
            },

            {
                id = 298,
                label = "Ks Swimfloat 03",
                itemName = "ks_swimfloat_03",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Sports Recreation",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_swimfloat_03.png",
            },

            {
                id = 299,
                label = "Ks Swimfloat 04",
                itemName = "ks_swimfloat_04",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Sports Recreation",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_swimfloat_04.png",
            },

            {
                id = 300,
                label = "Ks Swimfloat 05",
                itemName = "ks_swimfloat_05",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Sports Recreation",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_swimfloat_05.png",
            },
            {
                id = 301,
                label = "Camera 01",
                itemName = "ks_camera_01",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Electronics_Gadgets",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_camera_01.png",
            },

            {
                id = 302,
                label = "Camera 02",
                itemName = "ks_camera_02",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Electronics_Gadgets",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_camera_02.png",
            },

            {
                id = 303,
                label = "Camera 03",
                itemName = "ks_camera_03",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Electronics_Gadgets",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_camera_03.png",
            },

            {
                id = 304,
                label = "Camera 04",
                itemName = "ks_camera_04",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Electronics_Gadgets",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_camera_04.png",
            },

            {
                id = 305,
                label = "Camera 05",
                itemName = "ks_camera_05",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Electronics_Gadgets",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_camera_05.png",
            },

            {
                id = 306,
                label = "Camera 06",
                itemName = "ks_camera_06",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Electronics_Gadgets",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_camera_06.png",
            },

            {
                id = 307,
                label = "Camera 07",
                itemName = "ks_camera_07",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Electronics_Gadgets",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_camera_07.png",
            },

            {
                id = 308,
                label = "Camera 08",
                itemName = "ks_camera_08",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Electronics_Gadgets",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_camera_08.png",
            },

            {
                id = 309,
                label = "Goggles 01",
                itemName = "ks_goggles_01",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Electronics_Gadgets",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_goggles_01.png",
            },

            {
                id = 310,
                label = "Goggles 02",
                itemName = "ks_goggles_02",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Electronics_Gadgets",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_goggles_02.png",
            },

            {
                id = 311,
                label = "Goggles 03",
                itemName = "ks_goggles_03",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Electronics_Gadgets",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_goggles_03.png",
            },

            {
                id = 312,
                label = "Goggles 04",
                itemName = "ks_goggles_04",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Electronics_Gadgets",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_goggles_04.png",
            },

            {
                id = 313,
                label = "Goggles 05",
                itemName = "ks_goggles_05",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Electronics_Gadgets",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_goggles_05.png",
            },

            {
                id = 314,
                label = "Goggles 06",
                itemName = "ks_goggles_06",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Electronics_Gadgets",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_goggles_06.png",
            },

            {
                id = 315,
                label = "Prismorb 01",
                itemName = "ks_prismorb_01",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Electronics_Gadgets",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_prismorb_04.png",
            },

            {
                id = 316,
                label = "Prismorb 02",
                itemName = "ks_prismorb_02",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Electronics_Gadgets",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_prismorb_02.png",
            },

            {
                id = 317,
                label = "Prismorb 03",
                itemName = "ks_prismorb_03",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Electronics_Gadgets",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_prismorb_03.png",
            },

            {
                id = 318,
                label = "Prismorb 04",
                itemName = "ks_prismorb_04",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Electronics_Gadgets",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_prismorb_01.png",
            },
            {
                id = 319,
                label = "Banana",
                itemName = "banana",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Food",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/banana.png",
            },

            {
                id = 320,
                label = "Cacahuate",
                itemName = "cacahuate",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Food",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/cacahuate.png",
            },

            {
                id = 321,
                label = "Chicharo",
                itemName = "chicharo",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Food",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/chicharo.png",
            },

            {
                id = 322,
                label = "Donutcookie 01",
                itemName = "ks_donutcookie_01",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Food",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_donutcookie_01.png",
            },

            {
                id = 323,
                label = "Giftcookie 01",
                itemName = "ks_giftcookie_01",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Food",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_giftcookie_01.png",
            },

            {
                id = 324,
                label = "Gingercookie 01",
                itemName = "ks_gingercookie_01",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Food",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_gingercookie_01.png",
            },

            {
                id = 325,
                label = "Pinecookie 01",
                itemName = "ks_pinecookie_01",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Food",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_pinecookie_01.png",
            },

            {
                id = 326,
                label = "Popsicles 01",
                itemName = "ks_popsicles_01",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Food",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_popsicles_01.png",
            },

            {
                id = 327,
                label = "Popsicles 02",
                itemName = "ks_popsicles_02",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Food",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_popsicles_02.png",
            },

            {
                id = 328,
                label = "Popsicles 03",
                itemName = "ks_popsicles_03",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Food",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_popsicles_03.png",
            },

            {
                id = 329,
                label = "Popsicles 04",
                itemName = "ks_popsicles_04",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Food",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_popsicles_04.png",
            },

            {
                id = 330,
                label = "Popsicles 05",
                itemName = "ks_popsicles_05",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Food",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_popsicles_05.png",
            },

            {
                id = 331,
                label = "Santacookie 01",
                itemName = "ks_santacookie_01",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Food",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_santacookie_01.png",
            },
            {
                id = 332,
                label = "Ahba",
                itemName = "ahba",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Characters Mascots",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ahba.png",
            },

            {
                id = 333,
                label = "Ahbaz",
                itemName = "ahbaz",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Characters Mascots",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ahbaz.png",
            },

            {
                id = 334,
                label = "Ahbm",
                itemName = "ahbm",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Characters Mascots",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ahbm.png",
            },

            {
                id = 335,
                label = "Ahbn",
                itemName = "ahbn",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Characters Mascots",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ahbn.png",
            },

            {
                id = 336,
                label = "Ahbne",
                itemName = "ahbne",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Characters Mascots",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ahbne.png",
            },

            {
                id = 337,
                label = "Ahbr",
                itemName = "ahbr",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Characters Mascots",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ahbr.png",
            },

            {
                id = 338,
                label = "Ahbro",
                itemName = "ahbro",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Characters Mascots",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ahbro.png",
            },

            {
                id = 339,
                label = "Ahbv",
                itemName = "ahbv",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Characters Mascots",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ahbv.png",
            },

            {
                id = 340,
                label = "Bearb1",
                itemName = "bearb1",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Characters Mascots",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/bearb1.png",
            },

            {
                id = 341,
                label = "Bearb2",
                itemName = "bearb2",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Characters Mascots",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/bearb2.png",
            },

            {
                id = 342,
                label = "Bearb3",
                itemName = "bearb3",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Characters Mascots",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/bearb3.png",
            },

            {
                id = 343,
                label = "Bearb4",
                itemName = "bearb4",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Characters Mascots",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/bearb4.png",
            },

            {
                id = 344,
                label = "Bearb5",
                itemName = "bearb5",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Characters Mascots",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/bearb5.png",
            },

            {
                id = 345,
                label = "Cathsb",
                itemName = "cathsb",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Characters Mascots",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/cathsb.png",
            },

            {
                id = 346,
                label = "Cathsgn",
                itemName = "cathsgn",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Characters Mascots",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/cathsgn.png",
            },

            {
                id = 347,
                label = "Cathsp",
                itemName = "cathsp",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Characters Mascots",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/cathsp.png",
            },

            {
                id = 348,
                label = "Cathspk",
                itemName = "cathspk",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Characters Mascots",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/cathspk.png",
            },

            {
                id = 349,
                label = "Cathsr",
                itemName = "cathsr",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Characters Mascots",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/cathsr.png",
            },

            {
                id = 350,
                label = "Corgib",
                itemName = "corgib",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Characters Mascots",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/corgib.png",
            },

            {
                id = 351,
                label = "Corgic",
                itemName = "corgic",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Characters Mascots",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/corgic.png",
            },

            {
                id = 352,
                label = "Corgig",
                itemName = "corgig",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Characters Mascots",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/corgig.png",
            },

            {
                id = 353,
                label = "Corgigd",
                itemName = "corgigd",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Characters Mascots",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/corgigd.png",
            },

            {
                id = 354,
                label = "Corgip",
                itemName = "corgip",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Characters Mascots",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/corgip.png",
            },

            {
                id = 355,
                label = "Corgir",
                itemName = "corgir",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Characters Mascots",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/corgir.png",
            },


            {
                id = 357,
                label = "Kitsune G",
                itemName = "kitsune-g",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Characters Mascots",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/kitsune-g.png",
            },

            {
                id = 358,
                label = "Kitsune P",
                itemName = "kitsune-p",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Characters Mascots",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/kitsune-p.png",
            },

            {
                id = 359,
                label = "Kitsune R",
                itemName = "kitsune-r",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Characters Mascots",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/kitsune-r.png",
            },

            {
                id = 360,
                label = "Kitsuneside G",
                itemName = "kitsuneside-g",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Characters Mascots",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/kitsuneside-g.png",
            },

            {
                id = 361,
                label = "Kitsuneside P",
                itemName = "kitsuneside-p",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Characters Mascots",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/kitsuneside-p.png",
            },

            {
                id = 362,
                label = "Kitsuneside R",
                itemName = "kitsuneside-r",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Characters Mascots",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/kitsuneside-r.png",
            },

            {
                id = 363,
                label = "Kitsuneside V",
                itemName = "kitsuneside-v",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Characters Mascots",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/kitsuneside-v.png",
            },

            {
                id = 364,
                label = "Kitsune V",
                itemName = "kitsune-v",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Characters Mascots",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/kitsune-v.png",
            },

            

            {
                id = 366,
                label = "Cutepenguin 01",
                itemName = "ks_cutepenguin_01",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Characters Mascots",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_cutepenguin_01.png",
            },

            {
                id = 367,
                label = "Cuterein 01",
                itemName = "ks_cuterein_01",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Characters Mascots",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_cuterein_01.png",
            },

            {
                id = 368,
                label = "Cutereindeer 01",
                itemName = "ks_cutereindeer_01",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Characters Mascots",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_cutereindeer_01.png",
            },

            {
                id = 369,
                label = "Cutexmasbird 01",
                itemName = "ks_cutexmasbird_01",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Characters Mascots",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_cutexmasbird_01.png",
            },

            {
                id = 370,
                label = "Evilcat 01",
                itemName = "ks_evilcat_01",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Characters Mascots",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_evilcat_01.png",
            },

            {
                id = 371,
                label = "Ghostcat 01",
                itemName = "ks_ghostcat_01",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Characters Mascots",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_ghostcat_01.png",
            },

            {
                id = 372,
                label = "Killerbunny 01",
                itemName = "ks_killerbunny_01",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Characters Mascots",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_killerbunny_01.png",
            },

            {
                id = 373,
                label = "Pumpkincat 01",
                itemName = "ks_pumpkincat_01",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Characters Mascots",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_pumpkincat_01.png",
            },

            {
                id = 374,
                label = "Pumpkincat 02",
                itemName = "ks_pumpkincat_02",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Characters Mascots",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_pumpkincat_02.png",
            },

            {
                id = 375,
                label = "Pumpkincat 03",
                itemName = "ks_pumpkincat_03",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Characters Mascots",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_pumpkincat_03.png",
            },

            {
                id = 376,
                label = "Pumpkincat 04",
                itemName = "ks_pumpkincat_04",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Characters Mascots",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_pumpkincat_04.png",
            },

            {
                id = 377,
                label = "Pumpkincat 05",
                itemName = "ks_pumpkincat_05",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Characters Mascots",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_pumpkincat_05.png",
            },

            {
                id = 378,
                label = "Pumpkincat 06",
                itemName = "ks_pumpkincat_06",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Characters Mascots",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_pumpkincat_06.png",
            },

            {
                id = 379,
                label = "Reaper 01",
                itemName = "ks_reaper_01",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Characters Mascots",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_reaper_01.png",
            },

            {
                id = 380,
                label = "Reindeerhb 01",
                itemName = "ks_reindeerhb_01",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Characters Mascots",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_reindeerhb_01.png",
            },

            {
                id = 381,
                label = "Reinmouse 01",
                itemName = "ks_reinmouse_01",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Characters Mascots",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_reinmouse_01.png",
            },

            {
                id = 382,
                label = "Sadghost 01",
                itemName = "ks_sadghost_01",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Characters Mascots",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_sadghost_01.png",
            },

            {
                id = 383,
                label = "Sadghost 02",
                itemName = "ks_sadghost_02",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Characters Mascots",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_sadghost_02.png",
            },

            {
                id = 384,
                label = "Sadghost 03",
                itemName = "ks_sadghost_03",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Characters Mascots",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_sadghost_03.png",
            },

            {
                id = 385,
                label = "Sadghost 04",
                itemName = "ks_sadghost_04",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Characters Mascots",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_sadghost_04.png",
            },

            {
                id = 386,
                label = "Sadghost 05",
                itemName = "ks_sadghost_05",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Characters Mascots",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_sadghost_05.png",
            },

            {
                id = 387,
                label = "Sadghost 06",
                itemName = "ks_sadghost_06",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Characters Mascots",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_sadghost_06.png",
            },

            {
                id = 388,
                label = "Sadghost 07",
                itemName = "ks_sadghost_07",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Characters Mascots",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_sadghost_07.png",
            },

            {
                id = 389,
                label = "Scarybat 01",
                itemName = "ks_scarybat_01",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Characters Mascots",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_scarybat_01.png",
            },

            {
                id = 390,
                label = "Scarymummy 01",
                itemName = "ks_scarymummy_01",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Characters Mascots",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_scarymummy_01.png",
            },

            {
                id = 391,
                label = "Scarypumpkin 01",
                itemName = "ks_scarypumpkin_01",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Characters Mascots",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_scarypumpkin_01.png",
            },

            {
                id = 392,
                label = "Scaryreaper 01",
                itemName = "ks_scaryreaper_01",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Characters Mascots",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_scaryreaper_01.png",
            },

            {
                id = 393,
                label = "Skeletonbird 01",
                itemName = "ks_skeletonbird_01",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Characters Mascots",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_skeletonbird_01.png",
            },

            {
                id = 394,
                label = "Snowmanhb 01",
                itemName = "ks_snowmanhb_01",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Characters Mascots",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_snowmanhb_01.png",
            },

            {
                id = 395,
                label = "Snowrabbit 01",
                itemName = "ks_snowrabbit_01",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Characters Mascots",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_snowrabbit_01.png",
            },

            {
                id = 396,
                label = "Snowsr 01",
                itemName = "ks_snowsr_01",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Characters Mascots",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_snowsr_01.png",
            },

            {
                id = 397,
                label = "Spiderwitch 01",
                itemName = "ks_spiderwitch_01",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Characters Mascots",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_spiderwitch_01.png",
            },

            {
                id = 398,
                label = "Winterspider 01",
                itemName = "ks_winterspider_01",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Characters Mascots",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_winterspider_01.png",
            },

            {
                id = 399,
                label = "Xmasbear 01",
                itemName = "ks_xmasbear_01",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Characters Mascots",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_xmasbear_01.png",
            },

            {
                id = 400,
                label = "Xmasdog 01",
                itemName = "ks_xmasdog_01",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Characters Mascots",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_xmasdog_01.png",
            },

            

          
           
           

            

         
            {
                id = 411,
                label = "Osoconglobo",
                itemName = "osoconglobo",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Characters Mascots",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/osoconglobo.png",
            },

            {
                id = 412,
                label = "Osoconglobo D",
                itemName = "osoconglobo-d",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Characters Mascots",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/osoconglobo-d.png",
            },

            {
                id = 413,
                label = "Osoconglobo R",
                itemName = "osoconglobo-r",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Characters Mascots",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/osoconglobo-r.png",
            },

            {
                id = 414,
                label = "Osogrande B",
                itemName = "osogrande-b",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Characters Mascots",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/osogrande-b.png",
            },

            {
                id = 415,
                label = "Osogrande C",
                itemName = "osogrande-c",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Characters Mascots",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/osogrande-c.png",
            },

            {
                id = 416,
                label = "Osogrande G",
                itemName = "osogrande-g",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Characters Mascots",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/osogrande-g.png",
            },

            {
                id = 417,
                label = "Pusheen",
                itemName = "pusheen",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Characters Mascots",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/pusheen.png",
            },

            {
                id = 418,
                label = "Pushenicorn",
                itemName = "pushenicorn",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Characters Mascots",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/pushenicorn.png",
            },

            {
                id = 419,
                label = "Pushmermaid",
                itemName = "pushmermaid",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Characters Mascots",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/pushmermaid.png",
            },

            
            {
                id = 427,
                label = "Ks Adminsstaff 01",
                itemName = "ks_adminsstaff_01",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Tools_Weapons_Magic",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_adminsstaff_01.png",
            },
            {
                id = 428,
                label = "Ks Adminsstaff 02",
                itemName = "ks_adminsstaff_02",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Tools_Weapons_Magic",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_adminsstaff_02.png",
            },
            {
                id = 429,
                label = "Ks Adminsstaff 03",
                itemName = "ks_adminsstaff_03",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Tools_Weapons_Magic",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_adminsstaff_03.png",
            },
            {
                id = 430,
                label = "Ks Adminsstaff 04",
                itemName = "ks_adminsstaff_04",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Tools_Weapons_Magic",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_adminsstaff_04.png",
            },
            {
                id = 431,
                label = "Ks Adminsstaff 05",
                itemName = "ks_adminsstaff_05",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Tools_Weapons_Magic",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_adminsstaff_05.png",
            },
            {
                id = 432,
                label = "Ks Adminsstaff 06",
                itemName = "ks_adminsstaff_06",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Tools_Weapons_Magic",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_adminsstaff_06.png",
            },
            {
                id = 433,
                label = "Ks Adminsstaff 07",
                itemName = "ks_adminsstaff_07",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Tools_Weapons_Magic",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_adminsstaff_07.png",
            },
            {
                id = 434,
                label = "Ks Adminsstaff 08",
                itemName = "ks_adminsstaff_08",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Tools_Weapons_Magic",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_adminsstaff_08.png",
            },
            {
                id = 435,
                label = "Ks Adminsstaff 09",
                itemName = "ks_adminsstaff_09",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Tools_Weapons_Magic",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_adminsstaff_09.png",
            },
            {
                id = 436,
                label = "Ks Adminsstaff 010",
                itemName = "ks_adminsstaff_010",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Tools_Weapons_Magic",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_adminsstaff_010.png",
            },
            {
                id = 437,
                label = "Ks Adminsstaff 011",
                itemName = "ks_adminsstaff_011",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Tools_Weapons_Magic",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_adminsstaff_011.png",
            },
            {
                id = 438,
                label = "Ks Adminsstaff 012",
                itemName = "ks_adminsstaff_012",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Tools_Weapons_Magic",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_adminsstaff_012.png",
            },
            {
                id = 439,
                label = "Ks Adminsstaff 013",
                itemName = "ks_adminsstaff_013",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Tools_Weapons_Magic",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_adminsstaff_013.png",
            },
            {
                id = 440,
                label = "Ks Spellbook 01",
                itemName = "ks_spellbook_01",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Tools_Weapons_Magic",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_spellbook_01.png",
            },
            {
                id = 441,
                label = "Ks Spellbook 02",
                itemName = "ks_spellbook_02",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Tools_Weapons_Magic",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_spellbook_02.png",
            },
            {
                id = 442,
                label = "Ks Spellbook 03",
                itemName = "ks_spellbook_03",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Tools_Weapons_Magic",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_spellbook_03.png",
            },
            {
                id = 443,
                label = "Ks Spellbook 04",
                itemName = "ks_spellbook_04",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Tools_Weapons_Magic",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_spellbook_04.png",
            },
            {
                id = 444,
                label = "Ks Spellbook 05",
                itemName = "ks_spellbook_05",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Tools_Weapons_Magic",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_spellbook_05.png",
            },
            {
                id = 445,
                label = "Ks Spellbook 06",
                itemName = "ks_spellbook_06",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Tools_Weapons_Magic",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_spellbook_06.png",
            },
            {
                id = 446,
                label = "Ks Witchstick 01",
                itemName = "ks_witchstick_01",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Tools_Weapons_Magic",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_witchstick_01.png",
            },
            {
                id = 447,
                label = "Ks Witchstick 02",
                itemName = "ks_witchstick_02",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Tools_Weapons_Magic",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_witchstick_02.png",
            },
            {
                id = 448,
                label = "Ks Witchstick 03",
                itemName = "ks_witchstick_03",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Tools_Weapons_Magic",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_witchstick_03.png",
            },
            {
                id = 449,
                label = "Ks Witchstick 04",
                itemName = "ks_witchstick_04",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Tools_Weapons_Magic",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_witchstick_04.png",
            },
            {
                id = 450,
                label = "Ks Witchstick 05",
                itemName = "ks_witchstick_05",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Tools_Weapons_Magic",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/ks_witchstick_05.png",
            },
            {
                id = 451,
                label = "Babe Party Ballons",
                itemName = "babe_party_ballons",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Party Decorative",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/Babe_Party_Ballons.png",
            },

            {
                id = 452,
                label = "Cir Blue",
                itemName = "cir-blue",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Party Decorative",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/cir-blue.png",
            },

            {
                id = 453,
                label = "Cir Green",
                itemName = "cir-green",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Party Decorative",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/cir-green.png",
            },

            {
                id = 454,
                label = "Cir Red",
                itemName = "cir-red",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Party Decorative",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/cir-red.png",
            },

            {
                id = 455,
                label = "Cir Yellow",
                itemName = "cir-yellow",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Party Decorative",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/cir-yellow.png",
            },

            {
                id = 456,
                label = "Cir Yingyang",
                itemName = "cir-yingyang",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Party Decorative",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/cir-yingyang.png",
            },

            {
                id = 457,
                label = "Combovalentine Roj",
                itemName = "combovalentine-roj",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Party Decorative",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/combovalentine-roj.png",
            },

            {
                id = 458,
                label = "Combovalentine Ros",
                itemName = "combovalentine-ros",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Party Decorative",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/combovalentine-ros.png",
            },

            {
                id = 459,
                label = "Doll Party Ballons",
                itemName = "doll_party_ballons",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Party Decorative",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/doll_party_ballons.png",
            },

            {
                id = 460,
                label = "Lover Party Balloons",
                itemName = "lover_party_balloons",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Party Decorative",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/Lover_Party_Balloons.png",
            },

            {
                id = 461,
                label = "Queen Party Ballons",
                itemName = "queen_party_ballons",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Party Decorative",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/queen_party_ballons.png",
            },

            {
                id = 462,
                label = "Valentines Balloos2 Drv",
                itemName = "valentines_ballons2_drv",
                itemType = "item",
                itemCount = 1,
                price = 50,
                filter = "Party Decorative",
                description = " ",
                discount = {
                    state = false,
                    newPrice = 50,
                },
                itemImg = "./itemImages/Valentines_Ballons2_DRV.png",
            },

        },
    },

    Citizen.Wait(10)

if Settings.Debug then
    Settings.DebugPrint("collectores Category Preloaded", json.encode(preCategory))
end

table.insert(AK4Y.Categories, preCategory)
