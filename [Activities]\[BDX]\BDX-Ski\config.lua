--------------------------------------
--<!>--     BODHIX | STUDIO    --<!>--
--------------------------------------
--------------------------------------
--<!>--      SKI | CAREER      --<!>--
--------------------------------------

-- Support & Feedback: https://discord.gg/PjN7AWqkpF

Config = {}

Config.Debug = true -- True / False for Debug System

Config.Framework = "esx" -- Pick your framework: "qb" or "esx" - Default: "qb"
Config.Target = "none"
Config.ResourceName = nil

-- Settings
Config.ItemName = 'skis'
Config.MaxSpeedKmh = 200 -- This does not really change that much unless you get a boost somehow.
Config.maxJumpHeigh = 5.0 -- We suggest not to mess to much with this (And yes, you can jump very high).
Config.MaxFallHeight = 35.0
Config.LoseConnectionDistance = 2.0 -- This is the distance from you to the skateboard (Don't mess with this, unless you know, what you are doing).
Config.MinimumSkateSpeed = 2.0
Config.MinGroundHeight = 1.0
Config.ShowScore = true
Config.SnowNeeded = true
Config.TextFont = 4

Config.DesignCount = 15
Config.DesignPrice = 1500

Config.PickupKey = 38
Config.ConnectPlayer = 113

--This are the Grab Keys that you need to Press while holding a Flip Key (Left Contrl, Left Shift or Q)
Config.TailGrab = 23   --F
Config.Rocketair = 45  --R
Config.WeddleGrab = 73 --x
Config.GenieGrab = 79  --C
Config.BluntGrab = 217 --Caps

Config.AvailableWeatherTypes = {
    'SNOW', 
    'BLIZZARD', 
    'SNOWLIGHT', 
    'XMAS', 
}

Config.Language = {
    Info = {
        ['controls'] = 'Press E to Pickup | Press G to Ride',
        ['warning'] = 'The Workshop is currently in use by another player.',
        ['purchase'] = 'You have successfully purchased this item!',
        ['failed'] = 'You dont have enough money.',
        ['error'] = 'You already own this Item.'
    },
    Store = {
        ['target'] = 'Open Skate Shop.',
        ['text'] = '[E] Open Skate Shop.'
    },
    Menu = {
        ["equipment"] = "Equipment",
        ["gear"] = "GEAR",
        ["whats_new"] = "WHAT'S NEW",
        ["skis"] = "Skis",
        ["design"] = "Design",
        ["purchase"] = "Purchase"
    }
}

Config.Coords = {
    Peds = {
        MountChilliad = { x = 464.8428, y = 5572.7905, z = 782.3241, heading = 90.4061 },              
    },
    Skis = {
        MountChilliad = { x = 461.7858, y = 5569.9062, z = 782.3198, heading = 90.0 },          
    },
    Camera = {
        MountChilliad = { x = 461.7201, y = 5571.4639, z = 783.3444, heading = 180.6092 },             
    },
} 

Config.Shops = {
    ShopPeds = {
        {
            Position = vector4(Config.Coords.Peds.MountChilliad.x, Config.Coords.Peds.MountChilliad.y, Config.Coords.Peds.MountChilliad.z, Config.Coords.Peds.MountChilliad.heading),
            Model = `a_m_y_motox_02`,
            Scenarios = 'WORLD_HUMAN_AA_COFFEE',
            StoreName = "MountChilliad", -- Unique store ID
        },
    },
}
