(self["webpackChunksh_lights"]=self["webpackChunksh_lights"]||[]).push([[736],{7518:e=>{e.exports=function(e,t,n){const r=void 0!==e.__vccOpts?e.__vccOpts:e,o=r[t];if(void 0===o)r[t]=n;else for(const i in n)void 0===o[i]&&(o[i]=n[i])}},1959:(e,t,n)=>{"use strict";n.d(t,{Bj:()=>a,qq:()=>x,Fl:()=>De,X3:()=>Ae,PG:()=>Ee,dq:()=>Be,Xl:()=>Te,Jd:()=>C,WL:()=>He,qj:()=>ke,iH:()=>$e,lk:()=>E,Um:()=>Se,XI:()=>Ie,IU:()=>qe,j:()=>R,X$:()=>T,SU:()=>Ve});var r=n(2323);let o;const i=[];class a{constructor(e=!1){this.active=!0,this.effects=[],this.cleanups=[],!e&&o&&(this.parent=o,this.index=(o.scopes||(o.scopes=[])).push(this)-1)}run(e){if(this.active)try{return this.on(),e()}finally{this.off()}else 0}on(){this.active&&(i.push(this),o=this)}off(){this.active&&(i.pop(),o=i[i.length-1])}stop(e){if(this.active){if(this.effects.forEach((e=>e.stop())),this.cleanups.forEach((e=>e())),this.scopes&&this.scopes.forEach((e=>e.stop(!0))),this.parent&&!e){const e=this.parent.scopes.pop();e&&e!==this&&(this.parent.scopes[this.index]=e,e.index=this.index)}this.active=!1}}}function l(e,t){t=t||o,t&&t.active&&t.effects.push(e)}const s=e=>{const t=new Set(e);return t.w=0,t.n=0,t},u=e=>(e.w&h)>0,c=e=>(e.n&h)>0,d=({deps:e})=>{if(e.length)for(let t=0;t<e.length;t++)e[t].w|=h},f=e=>{const{deps:t}=e;if(t.length){let n=0;for(let r=0;r<t.length;r++){const o=t[r];u(o)&&!c(o)?o.delete(e):t[n++]=o,o.w&=~h,o.n&=~h}t.length=n}},p=new WeakMap;let v=0,h=1;const g=30,m=[];let b;const y=Symbol(""),w=Symbol("");class x{constructor(e,t=null,n){this.fn=e,this.scheduler=t,this.active=!0,this.deps=[],l(this,n)}run(){if(!this.active)return this.fn();if(!m.includes(this))try{return m.push(b=this),F(),h=1<<++v,v<=g?d(this):_(this),this.fn()}finally{v<=g&&f(this),h=1<<--v,E(),m.pop();const e=m.length;b=e>0?m[e-1]:void 0}}stop(){this.active&&(_(this),this.onStop&&this.onStop(),this.active=!1)}}function _(e){const{deps:t}=e;if(t.length){for(let n=0;n<t.length;n++)t[n].delete(e);t.length=0}}let k=!0;const S=[];function C(){S.push(k),k=!1}function F(){S.push(k),k=!0}function E(){const e=S.pop();k=void 0===e||e}function R(e,t,n){if(!A())return;let r=p.get(e);r||p.set(e,r=new Map);let o=r.get(n);o||r.set(n,o=s());const i=void 0;q(o,i)}function A(){return k&&void 0!==b}function q(e,t){let n=!1;v<=g?c(e)||(e.n|=h,n=!u(e)):n=!e.has(b),n&&(e.add(b),b.deps.push(e))}function T(e,t,n,o,i,a){const l=p.get(e);if(!l)return;let u=[];if("clear"===t)u=[...l.values()];else if("length"===n&&(0,r.kJ)(e))l.forEach(((e,t)=>{("length"===t||t>=o)&&u.push(e)}));else switch(void 0!==n&&u.push(l.get(n)),t){case"add":(0,r.kJ)(e)?(0,r.S0)(n)&&u.push(l.get("length")):(u.push(l.get(y)),(0,r._N)(e)&&u.push(l.get(w)));break;case"delete":(0,r.kJ)(e)||(u.push(l.get(y)),(0,r._N)(e)&&u.push(l.get(w)));break;case"set":(0,r._N)(e)&&u.push(l.get(y));break}if(1===u.length)u[0]&&L(u[0]);else{const e=[];for(const t of u)t&&e.push(...t);L(s(e))}}function L(e,t){for(const n of(0,r.kJ)(e)?e:[...e])(n!==b||n.allowRecurse)&&(n.scheduler?n.scheduler():n.run())}const P=(0,r.fY)("__proto__,__v_isRef,__isVue"),O=new Set(Object.getOwnPropertyNames(Symbol).map((e=>Symbol[e])).filter(r.yk)),M=N(),B=N(!1,!0),$=N(!0),I=j();function j(){const e={};return["includes","indexOf","lastIndexOf"].forEach((t=>{e[t]=function(...e){const n=qe(this);for(let t=0,o=this.length;t<o;t++)R(n,"get",t+"");const r=n[t](...e);return-1===r||!1===r?n[t](...e.map(qe)):r}})),["push","pop","shift","unshift","splice"].forEach((t=>{e[t]=function(...e){C();const n=qe(this)[t].apply(this,e);return E(),n}})),e}function N(e=!1,t=!1){return function(n,o,i){if("__v_isReactive"===o)return!e;if("__v_isReadonly"===o)return e;if("__v_raw"===o&&i===(e?t?we:ye:t?be:me).get(n))return n;const a=(0,r.kJ)(n);if(!e&&a&&(0,r.RI)(I,o))return Reflect.get(I,o,i);const l=Reflect.get(n,o,i);if((0,r.yk)(o)?O.has(o):P(o))return l;if(e||R(n,"get",o),t)return l;if(Be(l)){const e=!a||!(0,r.S0)(o);return e?l.value:l}return(0,r.Kn)(l)?e?Ce(l):ke(l):l}}const V=H(),U=H(!0);function H(e=!1){return function(t,n,o,i){let a=t[n];if(!e&&!Re(o)&&(o=qe(o),a=qe(a),!(0,r.kJ)(t)&&Be(a)&&!Be(o)))return a.value=o,!0;const l=(0,r.kJ)(t)&&(0,r.S0)(n)?Number(n)<t.length:(0,r.RI)(t,n),s=Reflect.set(t,n,o,i);return t===qe(i)&&(l?(0,r.aU)(o,a)&&T(t,"set",n,o,a):T(t,"add",n,o)),s}}function z(e,t){const n=(0,r.RI)(e,t),o=e[t],i=Reflect.deleteProperty(e,t);return i&&n&&T(e,"delete",t,void 0,o),i}function D(e,t){const n=Reflect.has(e,t);return(0,r.yk)(t)&&O.has(t)||R(e,"has",t),n}function Z(e){return R(e,"iterate",(0,r.kJ)(e)?"length":y),Reflect.ownKeys(e)}const W={get:M,set:V,deleteProperty:z,has:D,ownKeys:Z},Y={get:$,set(e,t){return!0},deleteProperty(e,t){return!0}},J=(0,r.l7)({},W,{get:B,set:U}),K=e=>e,X=e=>Reflect.getPrototypeOf(e);function G(e,t,n=!1,r=!1){e=e["__v_raw"];const o=qe(e),i=qe(t);t!==i&&!n&&R(o,"get",t),!n&&R(o,"get",i);const{has:a}=X(o),l=r?K:n?Pe:Le;return a.call(o,t)?l(e.get(t)):a.call(o,i)?l(e.get(i)):void(e!==o&&e.get(t))}function Q(e,t=!1){const n=this["__v_raw"],r=qe(n),o=qe(e);return e!==o&&!t&&R(r,"has",e),!t&&R(r,"has",o),e===o?n.has(e):n.has(e)||n.has(o)}function ee(e,t=!1){return e=e["__v_raw"],!t&&R(qe(e),"iterate",y),Reflect.get(e,"size",e)}function te(e){e=qe(e);const t=qe(this),n=X(t),r=n.has.call(t,e);return r||(t.add(e),T(t,"add",e,e)),this}function ne(e,t){t=qe(t);const n=qe(this),{has:o,get:i}=X(n);let a=o.call(n,e);a||(e=qe(e),a=o.call(n,e));const l=i.call(n,e);return n.set(e,t),a?(0,r.aU)(t,l)&&T(n,"set",e,t,l):T(n,"add",e,t),this}function re(e){const t=qe(this),{has:n,get:r}=X(t);let o=n.call(t,e);o||(e=qe(e),o=n.call(t,e));const i=r?r.call(t,e):void 0,a=t.delete(e);return o&&T(t,"delete",e,void 0,i),a}function oe(){const e=qe(this),t=0!==e.size,n=void 0,r=e.clear();return t&&T(e,"clear",void 0,void 0,n),r}function ie(e,t){return function(n,r){const o=this,i=o["__v_raw"],a=qe(i),l=t?K:e?Pe:Le;return!e&&R(a,"iterate",y),i.forEach(((e,t)=>n.call(r,l(e),l(t),o)))}}function ae(e,t,n){return function(...o){const i=this["__v_raw"],a=qe(i),l=(0,r._N)(a),s="entries"===e||e===Symbol.iterator&&l,u="keys"===e&&l,c=i[e](...o),d=n?K:t?Pe:Le;return!t&&R(a,"iterate",u?w:y),{next(){const{value:e,done:t}=c.next();return t?{value:e,done:t}:{value:s?[d(e[0]),d(e[1])]:d(e),done:t}},[Symbol.iterator](){return this}}}}function le(e){return function(...t){return"delete"!==e&&this}}function se(){const e={get(e){return G(this,e)},get size(){return ee(this)},has:Q,add:te,set:ne,delete:re,clear:oe,forEach:ie(!1,!1)},t={get(e){return G(this,e,!1,!0)},get size(){return ee(this)},has:Q,add:te,set:ne,delete:re,clear:oe,forEach:ie(!1,!0)},n={get(e){return G(this,e,!0)},get size(){return ee(this,!0)},has(e){return Q.call(this,e,!0)},add:le("add"),set:le("set"),delete:le("delete"),clear:le("clear"),forEach:ie(!0,!1)},r={get(e){return G(this,e,!0,!0)},get size(){return ee(this,!0)},has(e){return Q.call(this,e,!0)},add:le("add"),set:le("set"),delete:le("delete"),clear:le("clear"),forEach:ie(!0,!0)},o=["keys","values","entries",Symbol.iterator];return o.forEach((o=>{e[o]=ae(o,!1,!1),n[o]=ae(o,!0,!1),t[o]=ae(o,!1,!0),r[o]=ae(o,!0,!0)})),[e,n,t,r]}const[ue,ce,de,fe]=se();function pe(e,t){const n=t?e?fe:de:e?ce:ue;return(t,o,i)=>"__v_isReactive"===o?!e:"__v_isReadonly"===o?e:"__v_raw"===o?t:Reflect.get((0,r.RI)(n,o)&&o in t?n:t,o,i)}const ve={get:pe(!1,!1)},he={get:pe(!1,!0)},ge={get:pe(!0,!1)};const me=new WeakMap,be=new WeakMap,ye=new WeakMap,we=new WeakMap;function xe(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function _e(e){return e["__v_skip"]||!Object.isExtensible(e)?0:xe((0,r.W7)(e))}function ke(e){return e&&e["__v_isReadonly"]?e:Fe(e,!1,W,ve,me)}function Se(e){return Fe(e,!1,J,he,be)}function Ce(e){return Fe(e,!0,Y,ge,ye)}function Fe(e,t,n,o,i){if(!(0,r.Kn)(e))return e;if(e["__v_raw"]&&(!t||!e["__v_isReactive"]))return e;const a=i.get(e);if(a)return a;const l=_e(e);if(0===l)return e;const s=new Proxy(e,2===l?o:n);return i.set(e,s),s}function Ee(e){return Re(e)?Ee(e["__v_raw"]):!(!e||!e["__v_isReactive"])}function Re(e){return!(!e||!e["__v_isReadonly"])}function Ae(e){return Ee(e)||Re(e)}function qe(e){const t=e&&e["__v_raw"];return t?qe(t):e}function Te(e){return(0,r.Nj)(e,"__v_skip",!0),e}const Le=e=>(0,r.Kn)(e)?ke(e):e,Pe=e=>(0,r.Kn)(e)?Ce(e):e;function Oe(e){A()&&(e=qe(e),e.dep||(e.dep=s()),q(e.dep))}function Me(e,t){e=qe(e),e.dep&&L(e.dep)}function Be(e){return Boolean(e&&!0===e.__v_isRef)}function $e(e){return je(e,!1)}function Ie(e){return je(e,!0)}function je(e,t){return Be(e)?e:new Ne(e,t)}class Ne{constructor(e,t){this._shallow=t,this.dep=void 0,this.__v_isRef=!0,this._rawValue=t?e:qe(e),this._value=t?e:Le(e)}get value(){return Oe(this),this._value}set value(e){e=this._shallow?e:qe(e),(0,r.aU)(e,this._rawValue)&&(this._rawValue=e,this._value=this._shallow?e:Le(e),Me(this,e))}}function Ve(e){return Be(e)?e.value:e}const Ue={get:(e,t,n)=>Ve(Reflect.get(e,t,n)),set:(e,t,n,r)=>{const o=e[t];return Be(o)&&!Be(n)?(o.value=n,!0):Reflect.set(e,t,n,r)}};function He(e){return Ee(e)?e:new Proxy(e,Ue)}class ze{constructor(e,t,n,r){this._setter=t,this.dep=void 0,this._dirty=!0,this.__v_isRef=!0,this.effect=new x(e,(()=>{this._dirty||(this._dirty=!0,Me(this))})),this.effect.active=!r,this["__v_isReadonly"]=n}get value(){const e=qe(this);return Oe(e),e._dirty&&(e._dirty=!1,e._value=e.effect.run()),e._value}set value(e){this._setter(e)}}function De(e,t,n=!1){let o,i;const a=(0,r.mf)(e);a?(o=e,i=r.dG):(o=e.get,i=e.set);const l=new ze(o,i,a||!i,n);return l}Promise.resolve()},3673:(e,t,n)=>{"use strict";n.d(t,{P$:()=>ae,HY:()=>jt,Ob:()=>me,lR:()=>Pt,$d:()=>a,Fl:()=>$n,iD:()=>Kt,_:()=>nn,Us:()=>xt,Uk:()=>sn,Wm:()=>rn,aZ:()=>pe,FN:()=>xn,Q6:()=>fe,h:()=>In,f3:()=>K,Y3:()=>_,dl:()=>ye,Jd:()=>Te,Xn:()=>Ae,bv:()=>Re,Ah:()=>Le,ic:()=>qe,wg:()=>Dt,JJ:()=>J,up:()=>Mt,U2:()=>se,nK:()=>de,Y8:()=>re,YP:()=>G,w5:()=>N,wy:()=>pt});var r=n(1959),o=n(2323);function i(e,t,n,r){let o;try{o=r?e(...r):e()}catch(i){l(i,t,n)}return o}function a(e,t,n,r){if((0,o.mf)(e)){const a=i(e,t,n,r);return a&&(0,o.tI)(a)&&a.catch((e=>{l(e,t,n)})),a}const s=[];for(let o=0;o<e.length;o++)s.push(a(e[o],t,n,r));return s}function l(e,t,n,r=!0){const o=t?t.vnode:null;if(t){let r=t.parent;const o=t.proxy,a=n;while(r){const t=r.ec;if(t)for(let n=0;n<t.length;n++)if(!1===t[n](e,o,a))return;r=r.parent}const l=t.appContext.config.errorHandler;if(l)return void i(l,null,10,[e,o,a])}s(e,n,o,r)}function s(e,t,n,r=!0){console.error(e)}let u=!1,c=!1;const d=[];let f=0;const p=[];let v=null,h=0;const g=[];let m=null,b=0;const y=Promise.resolve();let w=null,x=null;function _(e){const t=w||y;return e?t.then(this?e.bind(this):e):t}function k(e){let t=f+1,n=d.length;while(t<n){const r=t+n>>>1,o=L(d[r]);o<e?t=r+1:n=r}return t}function S(e){d.length&&d.includes(e,u&&e.allowRecurse?f+1:f)||e===x||(null==e.id?d.push(e):d.splice(k(e.id),0,e),C())}function C(){u||c||(c=!0,w=y.then(P))}function F(e){const t=d.indexOf(e);t>f&&d.splice(t,1)}function E(e,t,n,r){(0,o.kJ)(e)?n.push(...e):t&&t.includes(e,e.allowRecurse?r+1:r)||n.push(e),C()}function R(e){E(e,v,p,h)}function A(e){E(e,m,g,b)}function q(e,t=null){if(p.length){for(x=t,v=[...new Set(p)],p.length=0,h=0;h<v.length;h++)v[h]();v=null,h=0,x=null,q(e,t)}}function T(e){if(g.length){const e=[...new Set(g)];if(g.length=0,m)return void m.push(...e);for(m=e,m.sort(((e,t)=>L(e)-L(t))),b=0;b<m.length;b++)m[b]();m=null,b=0}}const L=e=>null==e.id?1/0:e.id;function P(e){c=!1,u=!0,q(e),d.sort(((e,t)=>L(e)-L(t)));o.dG;try{for(f=0;f<d.length;f++){const e=d[f];e&&!1!==e.active&&i(e,null,14)}}finally{f=0,d.length=0,T(e),u=!1,w=null,(d.length||p.length||g.length)&&P(e)}}new Set;new Map;function O(e,t,...n){const r=e.vnode.props||o.kT;let i=n;const l=t.startsWith("update:"),s=l&&t.slice(7);if(s&&s in r){const e=`${"modelValue"===s?"model":s}Modifiers`,{number:t,trim:a}=r[e]||o.kT;a?i=n.map((e=>e.trim())):t&&(i=n.map(o.He))}let u;let c=r[u=(0,o.hR)(t)]||r[u=(0,o.hR)((0,o._A)(t))];!c&&l&&(c=r[u=(0,o.hR)((0,o.rs)(t))]),c&&a(c,e,6,i);const d=r[u+"Once"];if(d){if(e.emitted){if(e.emitted[u])return}else e.emitted={};e.emitted[u]=!0,a(d,e,6,i)}}function M(e,t,n=!1){const r=t.emitsCache,i=r.get(e);if(void 0!==i)return i;const a=e.emits;let l={},s=!1;if(!(0,o.mf)(e)){const r=e=>{const n=M(e,t,!0);n&&(s=!0,(0,o.l7)(l,n))};!n&&t.mixins.length&&t.mixins.forEach(r),e.extends&&r(e.extends),e.mixins&&e.mixins.forEach(r)}return a||s?((0,o.kJ)(a)?a.forEach((e=>l[e]=null)):(0,o.l7)(l,a),r.set(e,l),l):(r.set(e,null),null)}function B(e,t){return!(!e||!(0,o.F7)(t))&&(t=t.slice(2).replace(/Once$/,""),(0,o.RI)(e,t[0].toLowerCase()+t.slice(1))||(0,o.RI)(e,(0,o.rs)(t))||(0,o.RI)(e,t))}let $=null,I=null;function j(e){const t=$;return $=e,I=e&&e.type.__scopeId||null,t}function N(e,t=$,n){if(!t)return e;if(e._n)return e;const r=(...n)=>{r._d&&Yt(-1);const o=j(t),i=e(...n);return j(o),r._d&&Yt(1),i};return r._n=!0,r._c=!0,r._d=!0,r}function V(e){const{type:t,vnode:n,proxy:r,withProxy:i,props:a,propsOptions:[s],slots:u,attrs:c,emit:d,render:f,renderCache:p,data:v,setupState:h,ctx:g,inheritAttrs:m}=e;let b,y;const w=j(e);try{if(4&n.shapeFlag){const e=i||r;b=un(f.call(e,e,p,a,h,v,g)),y=c}else{const e=t;0,b=un(e.length>1?e(a,{attrs:c,slots:u,emit:d}):e(a,null)),y=t.props?c:U(c)}}catch(_){Ht.length=0,l(_,e,1),b=rn(Vt)}let x=b;if(y&&!1!==m){const e=Object.keys(y),{shapeFlag:t}=x;e.length&&7&t&&(s&&e.some(o.tR)&&(y=H(y,s)),x=ln(x,y))}return n.dirs&&(x.dirs=x.dirs?x.dirs.concat(n.dirs):n.dirs),n.transition&&(x.transition=n.transition),b=x,j(w),b}const U=e=>{let t;for(const n in e)("class"===n||"style"===n||(0,o.F7)(n))&&((t||(t={}))[n]=e[n]);return t},H=(e,t)=>{const n={};for(const r in e)(0,o.tR)(r)&&r.slice(9)in t||(n[r]=e[r]);return n};function z(e,t,n){const{props:r,children:o,component:i}=e,{props:a,children:l,patchFlag:s}=t,u=i.emitsOptions;if(t.dirs||t.transition)return!0;if(!(n&&s>=0))return!(!o&&!l||l&&l.$stable)||r!==a&&(r?!a||D(r,a,u):!!a);if(1024&s)return!0;if(16&s)return r?D(r,a,u):!!a;if(8&s){const e=t.dynamicProps;for(let t=0;t<e.length;t++){const n=e[t];if(a[n]!==r[n]&&!B(u,n))return!0}}return!1}function D(e,t,n){const r=Object.keys(t);if(r.length!==Object.keys(e).length)return!0;for(let o=0;o<r.length;o++){const i=r[o];if(t[i]!==e[i]&&!B(n,i))return!0}return!1}function Z({vnode:e,parent:t},n){while(t&&t.subTree===e)(e=t.vnode).el=n,t=t.parent}const W=e=>e.__isSuspense;function Y(e,t){t&&t.pendingBranch?(0,o.kJ)(e)?t.effects.push(...e):t.effects.push(e):A(e)}function J(e,t){if(wn){let n=wn.provides;const r=wn.parent&&wn.parent.provides;r===n&&(n=wn.provides=Object.create(r)),n[e]=t}else 0}function K(e,t,n=!1){const r=wn||$;if(r){const i=null==r.parent?r.vnode.appContext&&r.vnode.appContext.provides:r.parent.provides;if(i&&e in i)return i[e];if(arguments.length>1)return n&&(0,o.mf)(t)?t.call(r.proxy):t}else 0}const X={};function G(e,t,n){return Q(e,t,n)}function Q(e,t,{immediate:n,deep:l,flush:s,onTrack:u,onTrigger:c}=o.kT){const d=wn;let f,p,v=!1,h=!1;if((0,r.dq)(e)?(f=()=>e.value,v=!!e._shallow):(0,r.PG)(e)?(f=()=>e,l=!0):(0,o.kJ)(e)?(h=!0,v=e.some(r.PG),f=()=>e.map((e=>(0,r.dq)(e)?e.value:(0,r.PG)(e)?ne(e):(0,o.mf)(e)?i(e,d,2):void 0))):f=(0,o.mf)(e)?t?()=>i(e,d,2):()=>{if(!d||!d.isUnmounted)return p&&p(),a(e,d,3,[g])}:o.dG,t&&l){const e=f;f=()=>ne(e())}let g=e=>{p=w.onStop=()=>{i(e,d,4)}};if(En)return g=o.dG,t?n&&a(t,d,3,[f(),h?[]:void 0,g]):f(),o.dG;let m=h?[]:X;const b=()=>{if(w.active)if(t){const e=w.run();(l||v||(h?e.some(((e,t)=>(0,o.aU)(e,m[t]))):(0,o.aU)(e,m)))&&(p&&p(),a(t,d,3,[e,m===X?void 0:m,g]),m=e)}else w.run()};let y;b.allowRecurse=!!t,y="sync"===s?b:"post"===s?()=>wt(b,d&&d.suspense):()=>{!d||d.isMounted?R(b):b()};const w=new r.qq(f,y);return t?n?b():m=w.run():"post"===s?wt(w.run.bind(w),d&&d.suspense):w.run(),()=>{w.stop(),d&&d.scope&&(0,o.Od)(d.scope.effects,w)}}function ee(e,t,n){const r=this.proxy,i=(0,o.HD)(e)?e.includes(".")?te(r,e):()=>r[e]:e.bind(r,r);let a;(0,o.mf)(t)?a=t:(a=t.handler,n=t);const l=wn;_n(this);const s=Q(i,a.bind(r),n);return l?_n(l):kn(),s}function te(e,t){const n=t.split(".");return()=>{let t=e;for(let e=0;e<n.length&&t;e++)t=t[n[e]];return t}}function ne(e,t){if(!(0,o.Kn)(e)||e["__v_skip"])return e;if(t=t||new Set,t.has(e))return e;if(t.add(e),(0,r.dq)(e))ne(e.value,t);else if((0,o.kJ)(e))for(let n=0;n<e.length;n++)ne(e[n],t);else if((0,o.DM)(e)||(0,o._N)(e))e.forEach((e=>{ne(e,t)}));else if((0,o.PO)(e))for(const n in e)ne(e[n],t);return e}function re(){const e={isMounted:!1,isLeaving:!1,isUnmounting:!1,leavingVNodes:new Map};return Re((()=>{e.isMounted=!0})),Te((()=>{e.isUnmounting=!0})),e}const oe=[Function,Array],ie={name:"BaseTransition",props:{mode:String,appear:Boolean,persisted:Boolean,onBeforeEnter:oe,onEnter:oe,onAfterEnter:oe,onEnterCancelled:oe,onBeforeLeave:oe,onLeave:oe,onAfterLeave:oe,onLeaveCancelled:oe,onBeforeAppear:oe,onAppear:oe,onAfterAppear:oe,onAppearCancelled:oe},setup(e,{slots:t}){const n=xn(),o=re();let i;return()=>{const a=t.default&&fe(t.default(),!0);if(!a||!a.length)return;const l=(0,r.IU)(e),{mode:s}=l;const u=a[0];if(o.isLeaving)return ue(u);const c=ce(u);if(!c)return ue(u);const d=se(c,l,o,n);de(c,d);const f=n.subTree,p=f&&ce(f);let v=!1;const{getTransitionKey:h}=c.type;if(h){const e=h();void 0===i?i=e:e!==i&&(i=e,v=!0)}if(p&&p.type!==Vt&&(!Gt(c,p)||v)){const e=se(p,l,o,n);if(de(p,e),"out-in"===s)return o.isLeaving=!0,e.afterLeave=()=>{o.isLeaving=!1,n.update()},ue(u);"in-out"===s&&c.type!==Vt&&(e.delayLeave=(e,t,n)=>{const r=le(o,p);r[String(p.key)]=p,e._leaveCb=()=>{t(),e._leaveCb=void 0,delete d.delayedLeave},d.delayedLeave=n})}return u}}},ae=ie;function le(e,t){const{leavingVNodes:n}=e;let r=n.get(t.type);return r||(r=Object.create(null),n.set(t.type,r)),r}function se(e,t,n,r){const{appear:o,mode:i,persisted:l=!1,onBeforeEnter:s,onEnter:u,onAfterEnter:c,onEnterCancelled:d,onBeforeLeave:f,onLeave:p,onAfterLeave:v,onLeaveCancelled:h,onBeforeAppear:g,onAppear:m,onAfterAppear:b,onAppearCancelled:y}=t,w=String(e.key),x=le(n,e),_=(e,t)=>{e&&a(e,r,9,t)},k={mode:i,persisted:l,beforeEnter(t){let r=s;if(!n.isMounted){if(!o)return;r=g||s}t._leaveCb&&t._leaveCb(!0);const i=x[w];i&&Gt(e,i)&&i.el._leaveCb&&i.el._leaveCb(),_(r,[t])},enter(e){let t=u,r=c,i=d;if(!n.isMounted){if(!o)return;t=m||u,r=b||c,i=y||d}let a=!1;const l=e._enterCb=t=>{a||(a=!0,_(t?i:r,[e]),k.delayedLeave&&k.delayedLeave(),e._enterCb=void 0)};t?(t(e,l),t.length<=1&&l()):l()},leave(t,r){const o=String(e.key);if(t._enterCb&&t._enterCb(!0),n.isUnmounting)return r();_(f,[t]);let i=!1;const a=t._leaveCb=n=>{i||(i=!0,r(),_(n?h:v,[t]),t._leaveCb=void 0,x[o]===e&&delete x[o])};x[o]=e,p?(p(t,a),p.length<=1&&a()):a()},clone(e){return se(e,t,n,r)}};return k}function ue(e){if(he(e))return e=ln(e),e.children=null,e}function ce(e){return he(e)?e.children?e.children[0]:void 0:e}function de(e,t){6&e.shapeFlag&&e.component?de(e.component.subTree,t):128&e.shapeFlag?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}function fe(e,t=!1){let n=[],r=0;for(let o=0;o<e.length;o++){const i=e[o];i.type===jt?(128&i.patchFlag&&r++,n=n.concat(fe(i.children,t))):(t||i.type!==Vt)&&n.push(i)}if(r>1)for(let o=0;o<n.length;o++)n[o].patchFlag=-2;return n}function pe(e){return(0,o.mf)(e)?{setup:e,name:e.name}:e}const ve=e=>!!e.type.__asyncLoader;const he=e=>e.type.__isKeepAlive,ge={name:"KeepAlive",__isKeepAlive:!0,props:{include:[String,RegExp,Array],exclude:[String,RegExp,Array],max:[String,Number]},setup(e,{slots:t}){const n=xn(),r=n.ctx;if(!r.renderer)return t.default;const i=new Map,a=new Set;let l=null;const s=n.suspense,{renderer:{p:u,m:c,um:d,o:{createElement:f}}}=r,p=f("div");function v(e){ke(e),d(e,n,s,!0)}function h(e){i.forEach(((t,n)=>{const r=Mn(t.type);!r||e&&e(r)||g(n)}))}function g(e){const t=i.get(e);l&&t.type===l.type?l&&ke(l):v(t),i.delete(e),a.delete(e)}r.activate=(e,t,n,r,i)=>{const a=e.component;c(e,t,n,0,s),u(a.vnode,e,t,n,a,s,r,e.slotScopeIds,i),wt((()=>{a.isDeactivated=!1,a.a&&(0,o.ir)(a.a);const t=e.props&&e.props.onVnodeMounted;t&&pn(t,a.parent,e)}),s)},r.deactivate=e=>{const t=e.component;c(e,p,null,1,s),wt((()=>{t.da&&(0,o.ir)(t.da);const n=e.props&&e.props.onVnodeUnmounted;n&&pn(n,t.parent,e),t.isDeactivated=!0}),s)},G((()=>[e.include,e.exclude]),(([e,t])=>{e&&h((t=>be(e,t))),t&&h((e=>!be(t,e)))}),{flush:"post",deep:!0});let m=null;const b=()=>{null!=m&&i.set(m,Se(n.subTree))};return Re(b),qe(b),Te((()=>{i.forEach((e=>{const{subTree:t,suspense:r}=n,o=Se(t);if(e.type!==o.type)v(e);else{ke(o);const e=o.component.da;e&&wt(e,r)}}))})),()=>{if(m=null,!t.default)return null;const n=t.default(),r=n[0];if(n.length>1)return l=null,n;if(!Xt(r)||!(4&r.shapeFlag)&&!(128&r.shapeFlag))return l=null,r;let o=Se(r);const s=o.type,u=Mn(ve(o)?o.type.__asyncResolved||{}:s),{include:c,exclude:d,max:f}=e;if(c&&(!u||!be(c,u))||d&&u&&be(d,u))return l=o,r;const p=null==o.key?s:o.key,v=i.get(p);return o.el&&(o=ln(o),128&r.shapeFlag&&(r.ssContent=o)),m=p,v?(o.el=v.el,o.component=v.component,o.transition&&de(o,o.transition),o.shapeFlag|=512,a.delete(p),a.add(p)):(a.add(p),f&&a.size>parseInt(f,10)&&g(a.values().next().value)),o.shapeFlag|=256,l=o,r}}},me=ge;function be(e,t){return(0,o.kJ)(e)?e.some((e=>be(e,t))):(0,o.HD)(e)?e.split(",").indexOf(t)>-1:!!e.test&&e.test(t)}function ye(e,t){xe(e,"a",t)}function we(e,t){xe(e,"da",t)}function xe(e,t,n=wn){const r=e.__wdc||(e.__wdc=()=>{let t=n;while(t){if(t.isDeactivated)return;t=t.parent}return e()});if(Ce(t,r,n),n){let e=n.parent;while(e&&e.parent)he(e.parent.vnode)&&_e(r,t,n,e),e=e.parent}}function _e(e,t,n,r){const i=Ce(t,e,r,!0);Le((()=>{(0,o.Od)(r[t],i)}),n)}function ke(e){let t=e.shapeFlag;256&t&&(t-=256),512&t&&(t-=512),e.shapeFlag=t}function Se(e){return 128&e.shapeFlag?e.ssContent:e}function Ce(e,t,n=wn,o=!1){if(n){const i=n[e]||(n[e]=[]),l=t.__weh||(t.__weh=(...o)=>{if(n.isUnmounted)return;(0,r.Jd)(),_n(n);const i=a(t,n,e,o);return kn(),(0,r.lk)(),i});return o?i.unshift(l):i.push(l),l}}const Fe=e=>(t,n=wn)=>(!En||"sp"===e)&&Ce(e,t,n),Ee=Fe("bm"),Re=Fe("m"),Ae=Fe("bu"),qe=Fe("u"),Te=Fe("bum"),Le=Fe("um"),Pe=Fe("sp"),Oe=Fe("rtg"),Me=Fe("rtc");function Be(e,t=wn){Ce("ec",e,t)}let $e=!0;function Ie(e){const t=Ue(e),n=e.proxy,i=e.ctx;$e=!1,t.beforeCreate&&Ne(t.beforeCreate,e,"bc");const{data:a,computed:l,methods:s,watch:u,provide:c,inject:d,created:f,beforeMount:p,mounted:v,beforeUpdate:h,updated:g,activated:m,deactivated:b,beforeDestroy:y,beforeUnmount:w,destroyed:x,unmounted:_,render:k,renderTracked:S,renderTriggered:C,errorCaptured:F,serverPrefetch:E,expose:R,inheritAttrs:A,components:q,directives:T,filters:L}=t,P=null;if(d&&je(d,i,P,e.appContext.config.unwrapInjectedRef),s)for(const r in s){const e=s[r];(0,o.mf)(e)&&(i[r]=e.bind(n))}if(a){0;const t=a.call(n,n);0,(0,o.Kn)(t)&&(e.data=(0,r.qj)(t))}if($e=!0,l)for(const M in l){const e=l[M],t=(0,o.mf)(e)?e.bind(n,n):(0,o.mf)(e.get)?e.get.bind(n,n):o.dG;0;const a=!(0,o.mf)(e)&&(0,o.mf)(e.set)?e.set.bind(n):o.dG,s=(0,r.Fl)({get:t,set:a});Object.defineProperty(i,M,{enumerable:!0,configurable:!0,get:()=>s.value,set:e=>s.value=e})}if(u)for(const r in u)Ve(u[r],i,n,r);if(c){const e=(0,o.mf)(c)?c.call(n):c;Reflect.ownKeys(e).forEach((t=>{J(t,e[t])}))}function O(e,t){(0,o.kJ)(t)?t.forEach((t=>e(t.bind(n)))):t&&e(t.bind(n))}if(f&&Ne(f,e,"c"),O(Ee,p),O(Re,v),O(Ae,h),O(qe,g),O(ye,m),O(we,b),O(Be,F),O(Me,S),O(Oe,C),O(Te,w),O(Le,_),O(Pe,E),(0,o.kJ)(R))if(R.length){const t=e.exposed||(e.exposed={});R.forEach((e=>{Object.defineProperty(t,e,{get:()=>n[e],set:t=>n[e]=t})}))}else e.exposed||(e.exposed={});k&&e.render===o.dG&&(e.render=k),null!=A&&(e.inheritAttrs=A),q&&(e.components=q),T&&(e.directives=T)}function je(e,t,n=o.dG,i=!1){(0,o.kJ)(e)&&(e=We(e));for(const a in e){const n=e[a];let l;l=(0,o.Kn)(n)?"default"in n?K(n.from||a,n.default,!0):K(n.from||a):K(n),(0,r.dq)(l)&&i?Object.defineProperty(t,a,{enumerable:!0,configurable:!0,get:()=>l.value,set:e=>l.value=e}):t[a]=l}}function Ne(e,t,n){a((0,o.kJ)(e)?e.map((e=>e.bind(t.proxy))):e.bind(t.proxy),t,n)}function Ve(e,t,n,r){const i=r.includes(".")?te(n,r):()=>n[r];if((0,o.HD)(e)){const n=t[e];(0,o.mf)(n)&&G(i,n)}else if((0,o.mf)(e))G(i,e.bind(n));else if((0,o.Kn)(e))if((0,o.kJ)(e))e.forEach((e=>Ve(e,t,n,r)));else{const r=(0,o.mf)(e.handler)?e.handler.bind(n):t[e.handler];(0,o.mf)(r)&&G(i,r,e)}else 0}function Ue(e){const t=e.type,{mixins:n,extends:r}=t,{mixins:o,optionsCache:i,config:{optionMergeStrategies:a}}=e.appContext,l=i.get(t);let s;return l?s=l:o.length||n||r?(s={},o.length&&o.forEach((e=>He(s,e,a,!0))),He(s,t,a)):s=t,i.set(t,s),s}function He(e,t,n,r=!1){const{mixins:o,extends:i}=t;i&&He(e,i,n,!0),o&&o.forEach((t=>He(e,t,n,!0)));for(const a in t)if(r&&"expose"===a);else{const r=ze[a]||n&&n[a];e[a]=r?r(e[a],t[a]):t[a]}return e}const ze={data:De,props:Je,emits:Je,methods:Je,computed:Je,beforeCreate:Ye,created:Ye,beforeMount:Ye,mounted:Ye,beforeUpdate:Ye,updated:Ye,beforeDestroy:Ye,beforeUnmount:Ye,destroyed:Ye,unmounted:Ye,activated:Ye,deactivated:Ye,errorCaptured:Ye,serverPrefetch:Ye,components:Je,directives:Je,watch:Ke,provide:De,inject:Ze};function De(e,t){return t?e?function(){return(0,o.l7)((0,o.mf)(e)?e.call(this,this):e,(0,o.mf)(t)?t.call(this,this):t)}:t:e}function Ze(e,t){return Je(We(e),We(t))}function We(e){if((0,o.kJ)(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function Ye(e,t){return e?[...new Set([].concat(e,t))]:t}function Je(e,t){return e?(0,o.l7)((0,o.l7)(Object.create(null),e),t):t}function Ke(e,t){if(!e)return t;if(!t)return e;const n=(0,o.l7)(Object.create(null),e);for(const r in t)n[r]=Ye(e[r],t[r]);return n}function Xe(e,t,n,i=!1){const a={},l={};(0,o.Nj)(l,Qt,1),e.propsDefaults=Object.create(null),Qe(e,t,a,l);for(const r in e.propsOptions[0])r in a||(a[r]=void 0);n?e.props=i?a:(0,r.Um)(a):e.type.props?e.props=a:e.props=l,e.attrs=l}function Ge(e,t,n,i){const{props:a,attrs:l,vnode:{patchFlag:s}}=e,u=(0,r.IU)(a),[c]=e.propsOptions;let d=!1;if(!(i||s>0)||16&s){let r;Qe(e,t,a,l)&&(d=!0);for(const i in u)t&&((0,o.RI)(t,i)||(r=(0,o.rs)(i))!==i&&(0,o.RI)(t,r))||(c?!n||void 0===n[i]&&void 0===n[r]||(a[i]=et(c,u,i,void 0,e,!0)):delete a[i]);if(l!==u)for(const e in l)t&&(0,o.RI)(t,e)||(delete l[e],d=!0)}else if(8&s){const n=e.vnode.dynamicProps;for(let r=0;r<n.length;r++){let i=n[r];const s=t[i];if(c)if((0,o.RI)(l,i))s!==l[i]&&(l[i]=s,d=!0);else{const t=(0,o._A)(i);a[t]=et(c,u,t,s,e,!1)}else s!==l[i]&&(l[i]=s,d=!0)}}d&&(0,r.X$)(e,"set","$attrs")}function Qe(e,t,n,i){const[a,l]=e.propsOptions;let s,u=!1;if(t)for(let r in t){if((0,o.Gg)(r))continue;const c=t[r];let d;a&&(0,o.RI)(a,d=(0,o._A)(r))?l&&l.includes(d)?(s||(s={}))[d]=c:n[d]=c:B(e.emitsOptions,r)||r in i&&c===i[r]||(i[r]=c,u=!0)}if(l){const t=(0,r.IU)(n),i=s||o.kT;for(let r=0;r<l.length;r++){const s=l[r];n[s]=et(a,t,s,i[s],e,!(0,o.RI)(i,s))}}return u}function et(e,t,n,r,i,a){const l=e[n];if(null!=l){const e=(0,o.RI)(l,"default");if(e&&void 0===r){const e=l.default;if(l.type!==Function&&(0,o.mf)(e)){const{propsDefaults:o}=i;n in o?r=o[n]:(_n(i),r=o[n]=e.call(null,t),kn())}else r=e}l[0]&&(a&&!e?r=!1:!l[1]||""!==r&&r!==(0,o.rs)(n)||(r=!0))}return r}function tt(e,t,n=!1){const r=t.propsCache,i=r.get(e);if(i)return i;const a=e.props,l={},s=[];let u=!1;if(!(0,o.mf)(e)){const r=e=>{u=!0;const[n,r]=tt(e,t,!0);(0,o.l7)(l,n),r&&s.push(...r)};!n&&t.mixins.length&&t.mixins.forEach(r),e.extends&&r(e.extends),e.mixins&&e.mixins.forEach(r)}if(!a&&!u)return r.set(e,o.Z6),o.Z6;if((0,o.kJ)(a))for(let d=0;d<a.length;d++){0;const e=(0,o._A)(a[d]);nt(e)&&(l[e]=o.kT)}else if(a){0;for(const e in a){const t=(0,o._A)(e);if(nt(t)){const n=a[e],r=l[t]=(0,o.kJ)(n)||(0,o.mf)(n)?{type:n}:n;if(r){const e=it(Boolean,r.type),n=it(String,r.type);r[0]=e>-1,r[1]=n<0||e<n,(e>-1||(0,o.RI)(r,"default"))&&s.push(t)}}}}const c=[l,s];return r.set(e,c),c}function nt(e){return"$"!==e[0]}function rt(e){const t=e&&e.toString().match(/^\s*function (\w+)/);return t?t[1]:null===e?"null":""}function ot(e,t){return rt(e)===rt(t)}function it(e,t){return(0,o.kJ)(t)?t.findIndex((t=>ot(t,e))):(0,o.mf)(t)&&ot(t,e)?0:-1}const at=e=>"_"===e[0]||"$stable"===e,lt=e=>(0,o.kJ)(e)?e.map(un):[un(e)],st=(e,t,n)=>{const r=N(((...e)=>lt(t(...e))),n);return r._c=!1,r},ut=(e,t,n)=>{const r=e._ctx;for(const i in e){if(at(i))continue;const n=e[i];if((0,o.mf)(n))t[i]=st(i,n,r);else if(null!=n){0;const e=lt(n);t[i]=()=>e}}},ct=(e,t)=>{const n=lt(t);e.slots.default=()=>n},dt=(e,t)=>{if(32&e.vnode.shapeFlag){const n=t._;n?(e.slots=(0,r.IU)(t),(0,o.Nj)(t,"_",n)):ut(t,e.slots={})}else e.slots={},t&&ct(e,t);(0,o.Nj)(e.slots,Qt,1)},ft=(e,t,n)=>{const{vnode:r,slots:i}=e;let a=!0,l=o.kT;if(32&r.shapeFlag){const e=t._;e?n&&1===e?a=!1:((0,o.l7)(i,t),n||1!==e||delete i._):(a=!t.$stable,ut(t,i)),l=t}else t&&(ct(e,t),l={default:1});if(a)for(const o in i)at(o)||o in l||delete i[o]};function pt(e,t){const n=$;if(null===n)return e;const r=n.proxy,i=e.dirs||(e.dirs=[]);for(let a=0;a<t.length;a++){let[e,n,l,s=o.kT]=t[a];(0,o.mf)(e)&&(e={mounted:e,updated:e}),e.deep&&ne(n),i.push({dir:e,instance:r,value:n,oldValue:void 0,arg:l,modifiers:s})}return e}function vt(e,t,n,o){const i=e.dirs,l=t&&t.dirs;for(let s=0;s<i.length;s++){const u=i[s];l&&(u.oldValue=l[s].value);let c=u.dir[o];c&&((0,r.Jd)(),a(c,n,8,[e.el,u,e,t]),(0,r.lk)())}}function ht(){return{app:null,config:{isNativeTag:o.NO,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let gt=0;function mt(e,t){return function(n,r=null){null==r||(0,o.Kn)(r)||(r=null);const i=ht(),a=new Set;let l=!1;const s=i.app={_uid:gt++,_component:n,_props:r,_container:null,_context:i,_instance:null,version:jn,get config(){return i.config},set config(e){0},use(e,...t){return a.has(e)||(e&&(0,o.mf)(e.install)?(a.add(e),e.install(s,...t)):(0,o.mf)(e)&&(a.add(e),e(s,...t))),s},mixin(e){return i.mixins.includes(e)||i.mixins.push(e),s},component(e,t){return t?(i.components[e]=t,s):i.components[e]},directive(e,t){return t?(i.directives[e]=t,s):i.directives[e]},mount(o,a,u){if(!l){const c=rn(n,r);return c.appContext=i,a&&t?t(c,o):e(c,o,u),l=!0,s._container=o,o.__vue_app__=s,On(c.component)||c.component.proxy}},unmount(){l&&(e(null,s._container),delete s._container.__vue_app__)},provide(e,t){return i.provides[e]=t,s}};return s}}function bt(e,t,n,a,l=!1){if((0,o.kJ)(e))return void e.forEach(((e,r)=>bt(e,t&&((0,o.kJ)(t)?t[r]:t),n,a,l)));if(ve(a)&&!l)return;const s=4&a.shapeFlag?On(a.component)||a.component.proxy:a.el,u=l?null:s,{i:c,r:d}=e;const f=t&&t.r,p=c.refs===o.kT?c.refs={}:c.refs,v=c.setupState;if(null!=f&&f!==d&&((0,o.HD)(f)?(p[f]=null,(0,o.RI)(v,f)&&(v[f]=null)):(0,r.dq)(f)&&(f.value=null)),(0,o.mf)(d))i(d,c,12,[u,p]);else{const t=(0,o.HD)(d),i=(0,r.dq)(d);if(t||i){const i=()=>{if(e.f){const n=t?p[d]:d.value;l?(0,o.kJ)(n)&&(0,o.Od)(n,s):(0,o.kJ)(n)?n.includes(s)||n.push(s):t?p[d]=[s]:(d.value=[s],e.k&&(p[e.k]=d.value))}else t?(p[d]=u,(0,o.RI)(v,d)&&(v[d]=u)):(0,r.dq)(d)&&(d.value=u,e.k&&(p[e.k]=u))};u?(i.id=-1,wt(i,n)):i()}else 0}}function yt(){}const wt=Y;function xt(e){return _t(e)}function _t(e,t){yt();const n=(0,o.E9)();n.__VUE__=!0;const{insert:i,remove:a,patchProp:l,createElement:s,createText:u,createComment:c,setText:d,setElementText:f,parentNode:p,nextSibling:v,setScopeId:h=o.dG,cloneNode:g,insertStaticContent:m}=e,b=(e,t,n,r=null,o=null,i=null,a=!1,l=null,s=!!t.dynamicChildren)=>{if(e===t)return;e&&!Gt(e,t)&&(r=Q(e),Y(e,o,i,!0),e=null),-2===t.patchFlag&&(s=!1,t.dynamicChildren=null);const{type:u,ref:c,shapeFlag:d}=t;switch(u){case Nt:y(e,t,n,r);break;case Vt:w(e,t,n,r);break;case Ut:null==e&&x(t,n,r,a);break;case jt:M(e,t,n,r,o,i,a,l,s);break;default:1&d?C(e,t,n,r,o,i,a,l,s):6&d?B(e,t,n,r,o,i,a,l,s):(64&d||128&d)&&u.process(e,t,n,r,o,i,a,l,s,te)}null!=c&&o&&bt(c,e&&e.ref,i,t||e,!t)},y=(e,t,n,r)=>{if(null==e)i(t.el=u(t.children),n,r);else{const n=t.el=e.el;t.children!==e.children&&d(n,t.children)}},w=(e,t,n,r)=>{null==e?i(t.el=c(t.children||""),n,r):t.el=e.el},x=(e,t,n,r)=>{[e.el,e.anchor]=m(e.children,t,n,r,e.el,e.anchor)},_=({el:e,anchor:t},n,r)=>{let o;while(e&&e!==t)o=v(e),i(e,n,r),e=o;i(t,n,r)},k=({el:e,anchor:t})=>{let n;while(e&&e!==t)n=v(e),a(e),e=n;a(t)},C=(e,t,n,r,o,i,a,l,s)=>{a=a||"svg"===t.type,null==e?E(t,n,r,o,i,a,l,s):L(e,t,o,i,a,l,s)},E=(e,t,n,r,a,u,c,d)=>{let p,v;const{type:h,props:m,shapeFlag:b,transition:y,patchFlag:w,dirs:x}=e;if(e.el&&void 0!==g&&-1===w)p=e.el=g(e.el);else{if(p=e.el=s(e.type,u,m&&m.is,m),8&b?f(p,e.children):16&b&&A(e.children,p,null,r,a,u&&"foreignObject"!==h,c,d),x&&vt(e,null,r,"created"),m){for(const t in m)"value"===t||(0,o.Gg)(t)||l(p,t,null,m[t],u,e.children,r,a,G);"value"in m&&l(p,"value",null,m.value),(v=m.onVnodeBeforeMount)&&pn(v,r,e)}R(p,e,e.scopeId,c,r)}x&&vt(e,null,r,"beforeMount");const _=(!a||a&&!a.pendingBranch)&&y&&!y.persisted;_&&y.beforeEnter(p),i(p,t,n),((v=m&&m.onVnodeMounted)||_||x)&&wt((()=>{v&&pn(v,r,e),_&&y.enter(p),x&&vt(e,null,r,"mounted")}),a)},R=(e,t,n,r,o)=>{if(n&&h(e,n),r)for(let i=0;i<r.length;i++)h(e,r[i]);if(o){let n=o.subTree;if(t===n){const t=o.vnode;R(e,t,t.scopeId,t.slotScopeIds,o.parent)}}},A=(e,t,n,r,o,i,a,l,s=0)=>{for(let u=s;u<e.length;u++){const s=e[u]=l?cn(e[u]):un(e[u]);b(null,s,t,n,r,o,i,a,l)}},L=(e,t,n,r,i,a,s)=>{const u=t.el=e.el;let{patchFlag:c,dynamicChildren:d,dirs:p}=t;c|=16&e.patchFlag;const v=e.props||o.kT,h=t.props||o.kT;let g;n&&kt(n,!1),(g=h.onVnodeBeforeUpdate)&&pn(g,n,t,e),p&&vt(t,e,n,"beforeUpdate"),n&&kt(n,!0);const m=i&&"foreignObject"!==t.type;if(d?P(e.dynamicChildren,d,u,n,r,m,a):s||U(e,t,u,null,n,r,m,a,!1),c>0){if(16&c)O(u,t,v,h,n,r,i);else if(2&c&&v.class!==h.class&&l(u,"class",null,h.class,i),4&c&&l(u,"style",v.style,h.style,i),8&c){const o=t.dynamicProps;for(let t=0;t<o.length;t++){const a=o[t],s=v[a],c=h[a];c===s&&"value"!==a||l(u,a,s,c,i,e.children,n,r,G)}}1&c&&e.children!==t.children&&f(u,t.children)}else s||null!=d||O(u,t,v,h,n,r,i);((g=h.onVnodeUpdated)||p)&&wt((()=>{g&&pn(g,n,t,e),p&&vt(t,e,n,"updated")}),r)},P=(e,t,n,r,o,i,a)=>{for(let l=0;l<t.length;l++){const s=e[l],u=t[l],c=s.el&&(s.type===jt||!Gt(s,u)||70&s.shapeFlag)?p(s.el):n;b(s,u,c,null,r,o,i,a,!0)}},O=(e,t,n,r,i,a,s)=>{if(n!==r){for(const u in r){if((0,o.Gg)(u))continue;const c=r[u],d=n[u];c!==d&&"value"!==u&&l(e,u,d,c,s,t.children,i,a,G)}if(n!==o.kT)for(const u in n)(0,o.Gg)(u)||u in r||l(e,u,n[u],null,s,t.children,i,a,G);"value"in r&&l(e,"value",n.value,r.value)}},M=(e,t,n,r,o,a,l,s,c)=>{const d=t.el=e?e.el:u(""),f=t.anchor=e?e.anchor:u("");let{patchFlag:p,dynamicChildren:v,slotScopeIds:h}=t;h&&(s=s?s.concat(h):h),null==e?(i(d,n,r),i(f,n,r),A(t.children,n,f,o,a,l,s,c)):p>0&&64&p&&v&&e.dynamicChildren?(P(e.dynamicChildren,v,n,o,a,l,s),(null!=t.key||o&&t===o.subTree)&&St(e,t,!0)):U(e,t,n,f,o,a,l,s,c)},B=(e,t,n,r,o,i,a,l,s)=>{t.slotScopeIds=l,null==e?512&t.shapeFlag?o.ctx.activate(t,n,r,a,s):$(t,n,r,o,i,a,s):I(e,t,s)},$=(e,t,n,r,o,i,a)=>{const l=e.component=yn(e,r,o);if(he(e)&&(l.ctx.renderer=te),Rn(l),l.asyncDep){if(o&&o.registerDep(l,j),!e.el){const e=l.subTree=rn(Vt);w(null,e,t,n)}}else j(l,e,t,n,o,i,a)},I=(e,t,n)=>{const r=t.component=e.component;if(z(e,t,n)){if(r.asyncDep&&!r.asyncResolved)return void N(r,t,n);r.next=t,F(r.update),r.update()}else t.component=e.component,t.el=e.el,r.vnode=t},j=(e,t,n,i,a,l,s)=>{const u=()=>{if(e.isMounted){let t,{next:n,bu:r,u:i,parent:u,vnode:c}=e,d=n;0,kt(e,!1),n?(n.el=c.el,N(e,n,s)):n=c,r&&(0,o.ir)(r),(t=n.props&&n.props.onVnodeBeforeUpdate)&&pn(t,u,n,c),kt(e,!0);const f=V(e);0;const v=e.subTree;e.subTree=f,b(v,f,p(v.el),Q(v),e,a,l),n.el=f.el,null===d&&Z(e,f.el),i&&wt(i,a),(t=n.props&&n.props.onVnodeUpdated)&&wt((()=>pn(t,u,n,c)),a)}else{let r;const{el:s,props:u}=t,{bm:c,m:d,parent:f}=e,p=ve(t);if(kt(e,!1),c&&(0,o.ir)(c),!p&&(r=u&&u.onVnodeBeforeMount)&&pn(r,f,t),kt(e,!0),s&&re){const n=()=>{e.subTree=V(e),re(s,e.subTree,e,a,null)};p?t.type.__asyncLoader().then((()=>!e.isUnmounted&&n())):n()}else{0;const r=e.subTree=V(e);0,b(null,r,n,i,e,a,l),t.el=r.el}if(d&&wt(d,a),!p&&(r=u&&u.onVnodeMounted)){const e=t;wt((()=>pn(r,f,e)),a)}256&t.shapeFlag&&e.a&&wt(e.a,a),e.isMounted=!0,t=n=i=null}},c=e.effect=new r.qq(u,(()=>S(e.update)),e.scope),d=e.update=c.run.bind(c);d.id=e.uid,kt(e,!0),d()},N=(e,t,n)=>{t.component=e;const o=e.vnode.props;e.vnode=t,e.next=null,Ge(e,t.props,o,n),ft(e,t.children,n),(0,r.Jd)(),q(void 0,e.update),(0,r.lk)()},U=(e,t,n,r,o,i,a,l,s=!1)=>{const u=e&&e.children,c=e?e.shapeFlag:0,d=t.children,{patchFlag:p,shapeFlag:v}=t;if(p>0){if(128&p)return void D(u,d,n,r,o,i,a,l,s);if(256&p)return void H(u,d,n,r,o,i,a,l,s)}8&v?(16&c&&G(u,o,i),d!==u&&f(n,d)):16&c?16&v?D(u,d,n,r,o,i,a,l,s):G(u,o,i,!0):(8&c&&f(n,""),16&v&&A(d,n,r,o,i,a,l,s))},H=(e,t,n,r,i,a,l,s,u)=>{e=e||o.Z6,t=t||o.Z6;const c=e.length,d=t.length,f=Math.min(c,d);let p;for(p=0;p<f;p++){const r=t[p]=u?cn(t[p]):un(t[p]);b(e[p],r,n,null,i,a,l,s,u)}c>d?G(e,i,a,!0,!1,f):A(t,n,r,i,a,l,s,u,f)},D=(e,t,n,r,i,a,l,s,u)=>{let c=0;const d=t.length;let f=e.length-1,p=d-1;while(c<=f&&c<=p){const r=e[c],o=t[c]=u?cn(t[c]):un(t[c]);if(!Gt(r,o))break;b(r,o,n,null,i,a,l,s,u),c++}while(c<=f&&c<=p){const r=e[f],o=t[p]=u?cn(t[p]):un(t[p]);if(!Gt(r,o))break;b(r,o,n,null,i,a,l,s,u),f--,p--}if(c>f){if(c<=p){const e=p+1,o=e<d?t[e].el:r;while(c<=p)b(null,t[c]=u?cn(t[c]):un(t[c]),n,o,i,a,l,s,u),c++}}else if(c>p)while(c<=f)Y(e[c],i,a,!0),c++;else{const v=c,h=c,g=new Map;for(c=h;c<=p;c++){const e=t[c]=u?cn(t[c]):un(t[c]);null!=e.key&&g.set(e.key,c)}let m,y=0;const w=p-h+1;let x=!1,_=0;const k=new Array(w);for(c=0;c<w;c++)k[c]=0;for(c=v;c<=f;c++){const r=e[c];if(y>=w){Y(r,i,a,!0);continue}let o;if(null!=r.key)o=g.get(r.key);else for(m=h;m<=p;m++)if(0===k[m-h]&&Gt(r,t[m])){o=m;break}void 0===o?Y(r,i,a,!0):(k[o-h]=c+1,o>=_?_=o:x=!0,b(r,t[o],n,null,i,a,l,s,u),y++)}const S=x?Ct(k):o.Z6;for(m=S.length-1,c=w-1;c>=0;c--){const e=h+c,o=t[e],f=e+1<d?t[e+1].el:r;0===k[c]?b(null,o,n,f,i,a,l,s,u):x&&(m<0||c!==S[m]?W(o,n,f,2):m--)}}},W=(e,t,n,r,o=null)=>{const{el:a,type:l,transition:s,children:u,shapeFlag:c}=e;if(6&c)return void W(e.component.subTree,t,n,r);if(128&c)return void e.suspense.move(t,n,r);if(64&c)return void l.move(e,t,n,te);if(l===jt){i(a,t,n);for(let e=0;e<u.length;e++)W(u[e],t,n,r);return void i(e.anchor,t,n)}if(l===Ut)return void _(e,t,n);const d=2!==r&&1&c&&s;if(d)if(0===r)s.beforeEnter(a),i(a,t,n),wt((()=>s.enter(a)),o);else{const{leave:e,delayLeave:r,afterLeave:o}=s,l=()=>i(a,t,n),u=()=>{e(a,(()=>{l(),o&&o()}))};r?r(a,l,u):u()}else i(a,t,n)},Y=(e,t,n,r=!1,o=!1)=>{const{type:i,props:a,ref:l,children:s,dynamicChildren:u,shapeFlag:c,patchFlag:d,dirs:f}=e;if(null!=l&&bt(l,null,n,e,!0),256&c)return void t.ctx.deactivate(e);const p=1&c&&f,v=!ve(e);let h;if(v&&(h=a&&a.onVnodeBeforeUnmount)&&pn(h,t,e),6&c)X(e.component,n,r);else{if(128&c)return void e.suspense.unmount(n,r);p&&vt(e,null,t,"beforeUnmount"),64&c?e.type.remove(e,t,n,o,te,r):u&&(i!==jt||d>0&&64&d)?G(u,t,n,!1,!0):(i===jt&&384&d||!o&&16&c)&&G(s,t,n),r&&J(e)}(v&&(h=a&&a.onVnodeUnmounted)||p)&&wt((()=>{h&&pn(h,t,e),p&&vt(e,null,t,"unmounted")}),n)},J=e=>{const{type:t,el:n,anchor:r,transition:o}=e;if(t===jt)return void K(n,r);if(t===Ut)return void k(e);const i=()=>{a(n),o&&!o.persisted&&o.afterLeave&&o.afterLeave()};if(1&e.shapeFlag&&o&&!o.persisted){const{leave:t,delayLeave:r}=o,a=()=>t(n,i);r?r(e.el,i,a):a()}else i()},K=(e,t)=>{let n;while(e!==t)n=v(e),a(e),e=n;a(t)},X=(e,t,n)=>{const{bum:r,scope:i,update:a,subTree:l,um:s}=e;r&&(0,o.ir)(r),i.stop(),a&&(a.active=!1,Y(l,e,t,n)),s&&wt(s,t),wt((()=>{e.isUnmounted=!0}),t),t&&t.pendingBranch&&!t.isUnmounted&&e.asyncDep&&!e.asyncResolved&&e.suspenseId===t.pendingId&&(t.deps--,0===t.deps&&t.resolve())},G=(e,t,n,r=!1,o=!1,i=0)=>{for(let a=i;a<e.length;a++)Y(e[a],t,n,r,o)},Q=e=>6&e.shapeFlag?Q(e.component.subTree):128&e.shapeFlag?e.suspense.next():v(e.anchor||e.el),ee=(e,t,n)=>{null==e?t._vnode&&Y(t._vnode,null,null,!0):b(t._vnode||null,e,t,null,null,null,n),T(),t._vnode=e},te={p:b,um:Y,m:W,r:J,mt:$,mc:A,pc:U,pbc:P,n:Q,o:e};let ne,re;return t&&([ne,re]=t(te)),{render:ee,hydrate:ne,createApp:mt(ee,ne)}}function kt({effect:e,update:t},n){e.allowRecurse=t.allowRecurse=n}function St(e,t,n=!1){const r=e.children,i=t.children;if((0,o.kJ)(r)&&(0,o.kJ)(i))for(let o=0;o<r.length;o++){const e=r[o];let t=i[o];1&t.shapeFlag&&!t.dynamicChildren&&((t.patchFlag<=0||32===t.patchFlag)&&(t=i[o]=cn(i[o]),t.el=e.el),n||St(e,t))}}function Ct(e){const t=e.slice(),n=[0];let r,o,i,a,l;const s=e.length;for(r=0;r<s;r++){const s=e[r];if(0!==s){if(o=n[n.length-1],e[o]<s){t[r]=o,n.push(r);continue}i=0,a=n.length-1;while(i<a)l=i+a>>1,e[n[l]]<s?i=l+1:a=l;s<e[n[i]]&&(i>0&&(t[r]=n[i-1]),n[i]=r)}}i=n.length,a=n[i-1];while(i-- >0)n[i]=a,a=t[a];return n}const Ft=e=>e.__isTeleport,Et=e=>e&&(e.disabled||""===e.disabled),Rt=e=>"undefined"!==typeof SVGElement&&e instanceof SVGElement,At=(e,t)=>{const n=e&&e.to;if((0,o.HD)(n)){if(t){const e=t(n);return e}return null}return n},qt={__isTeleport:!0,process(e,t,n,r,o,i,a,l,s,u){const{mc:c,pc:d,pbc:f,o:{insert:p,querySelector:v,createText:h,createComment:g}}=u,m=Et(t.props);let{shapeFlag:b,children:y,dynamicChildren:w}=t;if(null==e){const e=t.el=h(""),u=t.anchor=h("");p(e,n,r),p(u,n,r);const d=t.target=At(t.props,v),f=t.targetAnchor=h("");d&&(p(f,d),a=a||Rt(d));const g=(e,t)=>{16&b&&c(y,e,t,o,i,a,l,s)};m?g(n,u):d&&g(d,f)}else{t.el=e.el;const r=t.anchor=e.anchor,c=t.target=e.target,p=t.targetAnchor=e.targetAnchor,h=Et(e.props),g=h?n:c,b=h?r:p;if(a=a||Rt(c),w?(f(e.dynamicChildren,w,g,o,i,a,l),St(e,t,!0)):s||d(e,t,g,b,o,i,a,l,!1),m)h||Tt(t,n,r,u,1);else if((t.props&&t.props.to)!==(e.props&&e.props.to)){const e=t.target=At(t.props,v);e&&Tt(t,e,null,u,0)}else h&&Tt(t,c,p,u,1)}},remove(e,t,n,r,{um:o,o:{remove:i}},a){const{shapeFlag:l,children:s,anchor:u,targetAnchor:c,target:d,props:f}=e;if(d&&i(c),(a||!Et(f))&&(i(u),16&l))for(let p=0;p<s.length;p++){const e=s[p];o(e,t,n,!0,!!e.dynamicChildren)}},move:Tt,hydrate:Lt};function Tt(e,t,n,{o:{insert:r},m:o},i=2){0===i&&r(e.targetAnchor,t,n);const{el:a,anchor:l,shapeFlag:s,children:u,props:c}=e,d=2===i;if(d&&r(a,t,n),(!d||Et(c))&&16&s)for(let f=0;f<u.length;f++)o(u[f],t,n,2);d&&r(l,t,n)}function Lt(e,t,n,r,o,i,{o:{nextSibling:a,parentNode:l,querySelector:s}},u){const c=t.target=At(t.props,s);if(c){const s=c._lpa||c.firstChild;16&t.shapeFlag&&(Et(t.props)?(t.anchor=u(a(e),t,l(e),n,r,o,i),t.targetAnchor=s):(t.anchor=a(e),t.targetAnchor=u(s,t,c,n,r,o,i)),c._lpa=t.targetAnchor&&a(t.targetAnchor))}return t.anchor&&a(t.anchor)}const Pt=qt,Ot="components";function Mt(e,t){return $t(Ot,e,!0,t)||e}const Bt=Symbol();function $t(e,t,n=!0,r=!1){const i=$||wn;if(i){const n=i.type;if(e===Ot){const e=Mn(n);if(e&&(e===t||e===(0,o._A)(t)||e===(0,o.kC)((0,o._A)(t))))return n}const a=It(i[e]||n[e],t)||It(i.appContext[e],t);return!a&&r?n:a}}function It(e,t){return e&&(e[t]||e[(0,o._A)(t)]||e[(0,o.kC)((0,o._A)(t))])}const jt=Symbol(void 0),Nt=Symbol(void 0),Vt=Symbol(void 0),Ut=Symbol(void 0),Ht=[];let zt=null;function Dt(e=!1){Ht.push(zt=e?null:[])}function Zt(){Ht.pop(),zt=Ht[Ht.length-1]||null}let Wt=1;function Yt(e){Wt+=e}function Jt(e){return e.dynamicChildren=Wt>0?zt||o.Z6:null,Zt(),Wt>0&&zt&&zt.push(e),e}function Kt(e,t,n,r,o,i){return Jt(nn(e,t,n,r,o,i,!0))}function Xt(e){return!!e&&!0===e.__v_isVNode}function Gt(e,t){return e.type===t.type&&e.key===t.key}const Qt="__vInternal",en=({key:e})=>null!=e?e:null,tn=({ref:e,ref_key:t,ref_for:n})=>null!=e?(0,o.HD)(e)||(0,r.dq)(e)||(0,o.mf)(e)?{i:$,r:e,k:t,f:!!n}:e:null;function nn(e,t=null,n=null,r=0,i=null,a=(e===jt?0:1),l=!1,s=!1){const u={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&en(t),ref:t&&tn(t),scopeId:I,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetAnchor:null,staticCount:0,shapeFlag:a,patchFlag:r,dynamicProps:i,dynamicChildren:null,appContext:null};return s?(dn(u,n),128&a&&e.normalize(u)):n&&(u.shapeFlag|=(0,o.HD)(n)?8:16),Wt>0&&!l&&zt&&(u.patchFlag>0||6&a)&&32!==u.patchFlag&&zt.push(u),u}const rn=on;function on(e,t=null,n=null,i=0,a=null,l=!1){if(e&&e!==Bt||(e=Vt),Xt(e)){const r=ln(e,t,!0);return n&&dn(r,n),r}if(Bn(e)&&(e=e.__vccOpts),t){t=an(t);let{class:e,style:n}=t;e&&!(0,o.HD)(e)&&(t.class=(0,o.C_)(e)),(0,o.Kn)(n)&&((0,r.X3)(n)&&!(0,o.kJ)(n)&&(n=(0,o.l7)({},n)),t.style=(0,o.j5)(n))}const s=(0,o.HD)(e)?1:W(e)?128:Ft(e)?64:(0,o.Kn)(e)?4:(0,o.mf)(e)?2:0;return nn(e,t,n,i,a,s,l,!0)}function an(e){return e?(0,r.X3)(e)||Qt in e?(0,o.l7)({},e):e:null}function ln(e,t,n=!1){const{props:r,ref:i,patchFlag:a,children:l}=e,s=t?fn(r||{},t):r,u={__v_isVNode:!0,__v_skip:!0,type:e.type,props:s,key:s&&en(s),ref:t&&t.ref?n&&i?(0,o.kJ)(i)?i.concat(tn(t)):[i,tn(t)]:tn(t):i,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:l,target:e.target,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==jt?-1===a?16:16|a:a,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:e.transition,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&ln(e.ssContent),ssFallback:e.ssFallback&&ln(e.ssFallback),el:e.el,anchor:e.anchor};return u}function sn(e=" ",t=0){return rn(Nt,null,e,t)}function un(e){return null==e||"boolean"===typeof e?rn(Vt):(0,o.kJ)(e)?rn(jt,null,e.slice()):"object"===typeof e?cn(e):rn(Nt,null,String(e))}function cn(e){return null===e.el||e.memo?e:ln(e)}function dn(e,t){let n=0;const{shapeFlag:r}=e;if(null==t)t=null;else if((0,o.kJ)(t))n=16;else if("object"===typeof t){if(65&r){const n=t.default;return void(n&&(n._c&&(n._d=!1),dn(e,n()),n._c&&(n._d=!0)))}{n=32;const r=t._;r||Qt in t?3===r&&$&&(1===$.slots._?t._=1:(t._=2,e.patchFlag|=1024)):t._ctx=$}}else(0,o.mf)(t)?(t={default:t,_ctx:$},n=32):(t=String(t),64&r?(n=16,t=[sn(t)]):n=8);e.children=t,e.shapeFlag|=n}function fn(...e){const t={};for(let n=0;n<e.length;n++){const r=e[n];for(const e in r)if("class"===e)t.class!==r.class&&(t.class=(0,o.C_)([t.class,r.class]));else if("style"===e)t.style=(0,o.j5)([t.style,r.style]);else if((0,o.F7)(e)){const n=t[e],i=r[e];n===i||(0,o.kJ)(n)&&n.includes(i)||(t[e]=n?[].concat(n,i):i)}else""!==e&&(t[e]=r[e])}return t}function pn(e,t,n,r=null){a(e,t,7,[n,r])}const vn=e=>e?Sn(e)?On(e)||e.proxy:vn(e.parent):null,hn=(0,o.l7)(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>vn(e.parent),$root:e=>vn(e.root),$emit:e=>e.emit,$options:e=>Ue(e),$forceUpdate:e=>()=>S(e.update),$nextTick:e=>_.bind(e.proxy),$watch:e=>ee.bind(e)}),gn={get({_:e},t){const{ctx:n,setupState:i,data:a,props:l,accessCache:s,type:u,appContext:c}=e;let d;if("$"!==t[0]){const r=s[t];if(void 0!==r)switch(r){case 1:return i[t];case 2:return a[t];case 4:return n[t];case 3:return l[t]}else{if(i!==o.kT&&(0,o.RI)(i,t))return s[t]=1,i[t];if(a!==o.kT&&(0,o.RI)(a,t))return s[t]=2,a[t];if((d=e.propsOptions[0])&&(0,o.RI)(d,t))return s[t]=3,l[t];if(n!==o.kT&&(0,o.RI)(n,t))return s[t]=4,n[t];$e&&(s[t]=0)}}const f=hn[t];let p,v;return f?("$attrs"===t&&(0,r.j)(e,"get",t),f(e)):(p=u.__cssModules)&&(p=p[t])?p:n!==o.kT&&(0,o.RI)(n,t)?(s[t]=4,n[t]):(v=c.config.globalProperties,(0,o.RI)(v,t)?v[t]:void 0)},set({_:e},t,n){const{data:r,setupState:i,ctx:a}=e;if(i!==o.kT&&(0,o.RI)(i,t))i[t]=n;else if(r!==o.kT&&(0,o.RI)(r,t))r[t]=n;else if((0,o.RI)(e.props,t))return!1;return("$"!==t[0]||!(t.slice(1)in e))&&(a[t]=n,!0)},has({_:{data:e,setupState:t,accessCache:n,ctx:r,appContext:i,propsOptions:a}},l){let s;return!!n[l]||e!==o.kT&&(0,o.RI)(e,l)||t!==o.kT&&(0,o.RI)(t,l)||(s=a[0])&&(0,o.RI)(s,l)||(0,o.RI)(r,l)||(0,o.RI)(hn,l)||(0,o.RI)(i.config.globalProperties,l)}};const mn=ht();let bn=0;function yn(e,t,n){const i=e.type,a=(t?t.appContext:e.appContext)||mn,l={uid:bn++,vnode:e,type:i,parent:t,appContext:a,root:null,next:null,subTree:null,effect:null,update:null,scope:new r.Bj(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(a.provides),accessCache:null,renderCache:[],components:null,directives:null,propsOptions:tt(i,a),emitsOptions:M(i,a),emit:null,emitted:null,propsDefaults:o.kT,inheritAttrs:i.inheritAttrs,ctx:o.kT,data:o.kT,props:o.kT,attrs:o.kT,slots:o.kT,refs:o.kT,setupState:o.kT,setupContext:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return l.ctx={_:l},l.root=t?t.root:l,l.emit=O.bind(null,l),e.ce&&e.ce(l),l}let wn=null;const xn=()=>wn||$,_n=e=>{wn=e,e.scope.on()},kn=()=>{wn&&wn.scope.off(),wn=null};function Sn(e){return 4&e.vnode.shapeFlag}let Cn,Fn,En=!1;function Rn(e,t=!1){En=t;const{props:n,children:r}=e.vnode,o=Sn(e);Xe(e,n,o,t),dt(e,r);const i=o?An(e,t):void 0;return En=!1,i}function An(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=(0,r.Xl)(new Proxy(e.ctx,gn));const{setup:a}=n;if(a){const n=e.setupContext=a.length>1?Pn(e):null;_n(e),(0,r.Jd)();const s=i(a,e,0,[e.props,n]);if((0,r.lk)(),kn(),(0,o.tI)(s)){if(s.then(kn,kn),t)return s.then((n=>{qn(e,n,t)})).catch((t=>{l(t,e,0)}));e.asyncDep=s}else qn(e,s,t)}else Tn(e,t)}function qn(e,t,n){(0,o.mf)(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:(0,o.Kn)(t)&&(e.setupState=(0,r.WL)(t)),Tn(e,n)}function Tn(e,t,n){const i=e.type;if(!e.render){if(!t&&Cn&&!i.render){const t=i.template;if(t){0;const{isCustomElement:n,compilerOptions:r}=e.appContext.config,{delimiters:a,compilerOptions:l}=i,s=(0,o.l7)((0,o.l7)({isCustomElement:n,delimiters:a},r),l);i.render=Cn(t,s)}}e.render=i.render||o.dG,Fn&&Fn(e)}_n(e),(0,r.Jd)(),Ie(e),(0,r.lk)(),kn()}function Ln(e){return new Proxy(e.attrs,{get(t,n){return(0,r.j)(e,"get","$attrs"),t[n]}})}function Pn(e){const t=t=>{e.exposed=t||{}};let n;return{get attrs(){return n||(n=Ln(e))},slots:e.slots,emit:e.emit,expose:t}}function On(e){if(e.exposed)return e.exposeProxy||(e.exposeProxy=new Proxy((0,r.WL)((0,r.Xl)(e.exposed)),{get(t,n){return n in t?t[n]:n in hn?hn[n](e):void 0}}))}function Mn(e){return(0,o.mf)(e)&&e.displayName||e.name}function Bn(e){return(0,o.mf)(e)&&"__vccOpts"in e}const $n=(e,t)=>(0,r.Fl)(e,t,En);function In(e,t,n){const r=arguments.length;return 2===r?(0,o.Kn)(t)&&!(0,o.kJ)(t)?Xt(t)?rn(e,null,[t]):rn(e,t):rn(e,null,t):(r>3?n=Array.prototype.slice.call(arguments,2):3===r&&Xt(n)&&(n=[n]),rn(e,t,n))}Symbol("");const jn="3.2.27"},8880:(e,t,n)=>{"use strict";n.d(t,{uT:()=>$,ri:()=>ne});var r=n(2323),o=n(3673);n(1959);const i="http://www.w3.org/2000/svg",a="undefined"!==typeof document?document:null,l=a&&a.createElement("template"),s={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,r)=>{const o=t?a.createElementNS(i,e):a.createElement(e,n?{is:n}:void 0);return"select"===e&&r&&null!=r.multiple&&o.setAttribute("multiple",r.multiple),o},createText:e=>a.createTextNode(e),createComment:e=>a.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>a.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},cloneNode(e){const t=e.cloneNode(!0);return"_value"in e&&(t._value=e._value),t},insertStaticContent(e,t,n,r,o,i){const a=n?n.previousSibling:t.lastChild;if(o&&i){while(1)if(t.insertBefore(o.cloneNode(!0),n),o===i||!(o=o.nextSibling))break}else{l.innerHTML=r?`<svg>${e}</svg>`:e;const o=l.content;if(r){const e=o.firstChild;while(e.firstChild)o.appendChild(e.firstChild);o.removeChild(e)}t.insertBefore(o,n)}return[a?a.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}};function u(e,t,n){const r=e._vtc;r&&(t=(t?[t,...r]:[...r]).join(" ")),null==t?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}function c(e,t,n){const o=e.style,i=(0,r.HD)(n);if(n&&!i){for(const e in n)f(o,e,n[e]);if(t&&!(0,r.HD)(t))for(const e in t)null==n[e]&&f(o,e,"")}else{const r=o.display;i?t!==n&&(o.cssText=n):t&&e.removeAttribute("style"),"_vod"in e&&(o.display=r)}}const d=/\s*!important$/;function f(e,t,n){if((0,r.kJ)(n))n.forEach((n=>f(e,t,n)));else if(t.startsWith("--"))e.setProperty(t,n);else{const o=h(e,t);d.test(n)?e.setProperty((0,r.rs)(o),n.replace(d,""),"important"):e[o]=n}}const p=["Webkit","Moz","ms"],v={};function h(e,t){const n=v[t];if(n)return n;let o=(0,r._A)(t);if("filter"!==o&&o in e)return v[t]=o;o=(0,r.kC)(o);for(let r=0;r<p.length;r++){const n=p[r]+o;if(n in e)return v[t]=n}return t}const g="http://www.w3.org/1999/xlink";function m(e,t,n,o,i){if(o&&t.startsWith("xlink:"))null==n?e.removeAttributeNS(g,t.slice(6,t.length)):e.setAttributeNS(g,t,n);else{const o=(0,r.Pq)(t);null==n||o&&!(0,r.yA)(n)?e.removeAttribute(t):e.setAttribute(t,o?"":n)}}function b(e,t,n,o,i,a,l){if("innerHTML"===t||"textContent"===t)return o&&l(o,i,a),void(e[t]=null==n?"":n);if("value"===t&&"PROGRESS"!==e.tagName&&!e.tagName.includes("-")){e._value=n;const r=null==n?"":n;return e.value===r&&"OPTION"!==e.tagName||(e.value=r),void(null==n&&e.removeAttribute(t))}if(""===n||null==n){const o=typeof e[t];if("boolean"===o)return void(e[t]=(0,r.yA)(n));if(null==n&&"string"===o)return e[t]="",void e.removeAttribute(t);if("number"===o){try{e[t]=0}catch(s){}return void e.removeAttribute(t)}}try{e[t]=n}catch(u){0}}let y=Date.now,w=!1;if("undefined"!==typeof window){y()>document.createEvent("Event").timeStamp&&(y=()=>performance.now());const e=navigator.userAgent.match(/firefox\/(\d+)/i);w=!!(e&&Number(e[1])<=53)}let x=0;const _=Promise.resolve(),k=()=>{x=0},S=()=>x||(_.then(k),x=y());function C(e,t,n,r){e.addEventListener(t,n,r)}function F(e,t,n,r){e.removeEventListener(t,n,r)}function E(e,t,n,r,o=null){const i=e._vei||(e._vei={}),a=i[t];if(r&&a)a.value=r;else{const[n,l]=A(t);if(r){const a=i[t]=q(r,o);C(e,n,a,l)}else a&&(F(e,n,a,l),i[t]=void 0)}}const R=/(?:Once|Passive|Capture)$/;function A(e){let t;if(R.test(e)){let n;t={};while(n=e.match(R))e=e.slice(0,e.length-n[0].length),t[n[0].toLowerCase()]=!0}return[(0,r.rs)(e.slice(2)),t]}function q(e,t){const n=e=>{const r=e.timeStamp||y();(w||r>=n.attached-1)&&(0,o.$d)(T(e,n.value),t,5,[e])};return n.value=e,n.attached=S(),n}function T(e,t){if((0,r.kJ)(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map((e=>t=>!t._stopped&&e(t)))}return t}const L=/^on[a-z]/,P=(e,t,n,o,i=!1,a,l,s,d)=>{"class"===t?u(e,o,i):"style"===t?c(e,n,o):(0,r.F7)(t)?(0,r.tR)(t)||E(e,t,n,o,l):("."===t[0]?(t=t.slice(1),1):"^"===t[0]?(t=t.slice(1),0):O(e,t,o,i))?b(e,t,o,a,l,s,d):("true-value"===t?e._trueValue=o:"false-value"===t&&(e._falseValue=o),m(e,t,o,i))};function O(e,t,n,o){return o?"innerHTML"===t||"textContent"===t||!!(t in e&&L.test(t)&&(0,r.mf)(n)):"spellcheck"!==t&&"draggable"!==t&&("form"!==t&&(("list"!==t||"INPUT"!==e.tagName)&&(("type"!==t||"TEXTAREA"!==e.tagName)&&((!L.test(t)||!(0,r.HD)(n))&&t in e))))}"undefined"!==typeof HTMLElement&&HTMLElement;const M="transition",B="animation",$=(e,{slots:t})=>(0,o.h)(o.P$,V(e),t);$.displayName="Transition";const I={name:String,type:String,css:{type:Boolean,default:!0},duration:[String,Number,Object],enterFromClass:String,enterActiveClass:String,enterToClass:String,appearFromClass:String,appearActiveClass:String,appearToClass:String,leaveFromClass:String,leaveActiveClass:String,leaveToClass:String},j=($.props=(0,r.l7)({},o.P$.props,I),(e,t=[])=>{(0,r.kJ)(e)?e.forEach((e=>e(...t))):e&&e(...t)}),N=e=>!!e&&((0,r.kJ)(e)?e.some((e=>e.length>1)):e.length>1);function V(e){const t={};for(const r in e)r in I||(t[r]=e[r]);if(!1===e.css)return t;const{name:n="v",type:o,duration:i,enterFromClass:a=`${n}-enter-from`,enterActiveClass:l=`${n}-enter-active`,enterToClass:s=`${n}-enter-to`,appearFromClass:u=a,appearActiveClass:c=l,appearToClass:d=s,leaveFromClass:f=`${n}-leave-from`,leaveActiveClass:p=`${n}-leave-active`,leaveToClass:v=`${n}-leave-to`}=e,h=U(i),g=h&&h[0],m=h&&h[1],{onBeforeEnter:b,onEnter:y,onEnterCancelled:w,onLeave:x,onLeaveCancelled:_,onBeforeAppear:k=b,onAppear:S=y,onAppearCancelled:C=w}=t,F=(e,t,n)=>{D(e,t?d:s),D(e,t?c:l),n&&n()},E=(e,t)=>{D(e,v),D(e,p),t&&t()},R=e=>(t,n)=>{const r=e?S:y,i=()=>F(t,e,n);j(r,[t,i]),Z((()=>{D(t,e?u:a),z(t,e?d:s),N(r)||Y(t,o,g,i)}))};return(0,r.l7)(t,{onBeforeEnter(e){j(b,[e]),z(e,a),z(e,l)},onBeforeAppear(e){j(k,[e]),z(e,u),z(e,c)},onEnter:R(!1),onAppear:R(!0),onLeave(e,t){const n=()=>E(e,t);z(e,f),G(),z(e,p),Z((()=>{D(e,f),z(e,v),N(x)||Y(e,o,m,n)})),j(x,[e,n])},onEnterCancelled(e){F(e,!1),j(w,[e])},onAppearCancelled(e){F(e,!0),j(C,[e])},onLeaveCancelled(e){E(e),j(_,[e])}})}function U(e){if(null==e)return null;if((0,r.Kn)(e))return[H(e.enter),H(e.leave)];{const t=H(e);return[t,t]}}function H(e){const t=(0,r.He)(e);return t}function z(e,t){t.split(/\s+/).forEach((t=>t&&e.classList.add(t))),(e._vtc||(e._vtc=new Set)).add(t)}function D(e,t){t.split(/\s+/).forEach((t=>t&&e.classList.remove(t)));const{_vtc:n}=e;n&&(n.delete(t),n.size||(e._vtc=void 0))}function Z(e){requestAnimationFrame((()=>{requestAnimationFrame(e)}))}let W=0;function Y(e,t,n,r){const o=e._endId=++W,i=()=>{o===e._endId&&r()};if(n)return setTimeout(i,n);const{type:a,timeout:l,propCount:s}=J(e,t);if(!a)return r();const u=a+"end";let c=0;const d=()=>{e.removeEventListener(u,f),i()},f=t=>{t.target===e&&++c>=s&&d()};setTimeout((()=>{c<s&&d()}),l+1),e.addEventListener(u,f)}function J(e,t){const n=window.getComputedStyle(e),r=e=>(n[e]||"").split(", "),o=r(M+"Delay"),i=r(M+"Duration"),a=K(o,i),l=r(B+"Delay"),s=r(B+"Duration"),u=K(l,s);let c=null,d=0,f=0;t===M?a>0&&(c=M,d=a,f=i.length):t===B?u>0&&(c=B,d=u,f=s.length):(d=Math.max(a,u),c=d>0?a>u?M:B:null,f=c?c===M?i.length:s.length:0);const p=c===M&&/\b(transform|all)(,|$)/.test(n[M+"Property"]);return{type:c,timeout:d,propCount:f,hasTransform:p}}function K(e,t){while(e.length<t.length)e=e.concat(e);return Math.max(...t.map(((t,n)=>X(t)+X(e[n]))))}function X(e){return 1e3*Number(e.slice(0,-1).replace(",","."))}function G(){return document.body.offsetHeight}new WeakMap,new WeakMap;const Q=(0,r.l7)({patchProp:P},s);let ee;function te(){return ee||(ee=(0,o.Us)(Q))}const ne=(...e)=>{const t=te().createApp(...e);const{mount:n}=t;return t.mount=e=>{const o=re(e);if(!o)return;const i=t._component;(0,r.mf)(i)||i.render||i.template||(i.template=o.innerHTML),o.innerHTML="";const a=n(o,!1,o instanceof SVGElement);return o instanceof Element&&(o.removeAttribute("v-cloak"),o.setAttribute("data-v-app","")),a},t};function re(e){if((0,r.HD)(e)){const t=document.querySelector(e);return t}return e}},2323:(e,t,n)=>{"use strict";function r(e,t){const n=Object.create(null),r=e.split(",");for(let o=0;o<r.length;o++)n[r[o]]=!0;return t?e=>!!n[e.toLowerCase()]:e=>!!n[e]}n.d(t,{Z6:()=>w,kT:()=>y,NO:()=>_,dG:()=>x,_A:()=>W,kC:()=>K,Nj:()=>ee,l7:()=>F,E9:()=>re,aU:()=>G,RI:()=>A,rs:()=>J,yA:()=>s,ir:()=>Q,kJ:()=>q,mf:()=>O,e1:()=>i,S0:()=>H,_N:()=>T,tR:()=>C,Kn:()=>$,F7:()=>S,PO:()=>U,tI:()=>I,Gg:()=>z,DM:()=>L,Pq:()=>l,HD:()=>M,yk:()=>B,WV:()=>h,hq:()=>g,fY:()=>r,C_:()=>p,j5:()=>u,Od:()=>E,zw:()=>m,hR:()=>X,He:()=>te,W7:()=>V});const o="Infinity,undefined,NaN,isFinite,isNaN,parseFloat,parseInt,decodeURI,decodeURIComponent,encodeURI,encodeURIComponent,Math,Number,Date,Array,Object,Boolean,String,RegExp,Map,Set,JSON,Intl,BigInt",i=r(o);const a="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",l=r(a);function s(e){return!!e||""===e}function u(e){if(q(e)){const t={};for(let n=0;n<e.length;n++){const r=e[n],o=M(r)?f(r):u(r);if(o)for(const e in o)t[e]=o[e]}return t}return M(e)||$(e)?e:void 0}const c=/;(?![^(]*\))/g,d=/:(.+)/;function f(e){const t={};return e.split(c).forEach((e=>{if(e){const n=e.split(d);n.length>1&&(t[n[0].trim()]=n[1].trim())}})),t}function p(e){let t="";if(M(e))t=e;else if(q(e))for(let n=0;n<e.length;n++){const r=p(e[n]);r&&(t+=r+" ")}else if($(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}function v(e,t){if(e.length!==t.length)return!1;let n=!0;for(let r=0;n&&r<e.length;r++)n=h(e[r],t[r]);return n}function h(e,t){if(e===t)return!0;let n=P(e),r=P(t);if(n||r)return!(!n||!r)&&e.getTime()===t.getTime();if(n=q(e),r=q(t),n||r)return!(!n||!r)&&v(e,t);if(n=$(e),r=$(t),n||r){if(!n||!r)return!1;const o=Object.keys(e).length,i=Object.keys(t).length;if(o!==i)return!1;for(const n in e){const r=e.hasOwnProperty(n),o=t.hasOwnProperty(n);if(r&&!o||!r&&o||!h(e[n],t[n]))return!1}}return String(e)===String(t)}function g(e,t){return e.findIndex((e=>h(e,t)))}const m=e=>null==e?"":q(e)||$(e)&&(e.toString===j||!O(e.toString))?JSON.stringify(e,b,2):String(e),b=(e,t)=>t&&t.__v_isRef?b(e,t.value):T(t)?{[`Map(${t.size})`]:[...t.entries()].reduce(((e,[t,n])=>(e[`${t} =>`]=n,e)),{})}:L(t)?{[`Set(${t.size})`]:[...t.values()]}:!$(t)||q(t)||U(t)?t:String(t),y={},w=[],x=()=>{},_=()=>!1,k=/^on[^a-z]/,S=e=>k.test(e),C=e=>e.startsWith("onUpdate:"),F=Object.assign,E=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},R=Object.prototype.hasOwnProperty,A=(e,t)=>R.call(e,t),q=Array.isArray,T=e=>"[object Map]"===N(e),L=e=>"[object Set]"===N(e),P=e=>e instanceof Date,O=e=>"function"===typeof e,M=e=>"string"===typeof e,B=e=>"symbol"===typeof e,$=e=>null!==e&&"object"===typeof e,I=e=>$(e)&&O(e.then)&&O(e.catch),j=Object.prototype.toString,N=e=>j.call(e),V=e=>N(e).slice(8,-1),U=e=>"[object Object]"===N(e),H=e=>M(e)&&"NaN"!==e&&"-"!==e[0]&&""+parseInt(e,10)===e,z=r(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),D=e=>{const t=Object.create(null);return n=>{const r=t[n];return r||(t[n]=e(n))}},Z=/-(\w)/g,W=D((e=>e.replace(Z,((e,t)=>t?t.toUpperCase():"")))),Y=/\B([A-Z])/g,J=D((e=>e.replace(Y,"-$1").toLowerCase())),K=D((e=>e.charAt(0).toUpperCase()+e.slice(1))),X=D((e=>e?`on${K(e)}`:"")),G=(e,t)=>!Object.is(e,t),Q=(e,t)=>{for(let n=0;n<e.length;n++)e[n](t)},ee=(e,t,n)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,value:n})},te=e=>{const t=parseFloat(e);return isNaN(t)?e:t};let ne;const re=()=>ne||(ne="undefined"!==typeof globalThis?globalThis:"undefined"!==typeof self?self:"undefined"!==typeof window?window:"undefined"!==typeof n.g?n.g:{})},4607:(e,t,n)=>{"use strict";n.d(t,{Z:()=>A});var r=n(3673),o=n(1959),i=n(8880),a=n(4554),l=n(9754),s=n(860);n(6245);const u={left:"start",center:"center",right:"end",between:"between",around:"around",evenly:"evenly",stretch:"stretch"},c=Object.keys(u),d={align:{type:String,validator:e=>c.includes(e)}};function f(e){return(0,r.Fl)((()=>{const t=void 0===e.align?!0===e.vertical?"stretch":"left":e.align;return`${!0===e.vertical?"items":"justify"}-${u[t]}`}))}var p=n(2417),v=n(7277);const h={none:0,xs:4,sm:8,md:16,lg:24,xl:32},g={xs:8,sm:10,md:14,lg:20,xl:24},m=["button","submit","reset"],b=/[^\s]\/[^\s]/,y={...p.LU,...v.$,type:{type:String,default:"button"},label:[Number,String],icon:String,iconRight:String,round:Boolean,outline:Boolean,flat:Boolean,unelevated:Boolean,rounded:Boolean,push:Boolean,glossy:Boolean,size:String,fab:Boolean,fabMini:Boolean,padding:String,color:String,textColor:String,noCaps:Boolean,noWrap:Boolean,dense:Boolean,tabindex:[Number,String],ripple:{type:[Boolean,Object],default:!0},align:{...d.align,default:"center"},stack:Boolean,stretch:Boolean,loading:{type:Boolean,default:null},disable:Boolean};function w(e){const t=(0,p.ZP)(e,g),n=f(e),{hasRouterLink:o,hasLink:i,linkTag:a,linkProps:l,navigateToRouterLink:s}=(0,v.Z)("button"),u=(0,r.Fl)((()=>{const n=!1===e.fab&&!1===e.fabMini?t.value:{};return void 0!==e.padding?Object.assign({},n,{padding:e.padding.split(/\s+/).map((e=>e in h?h[e]+"px":e)).join(" "),minWidth:"0",minHeight:"0"}):n})),c=(0,r.Fl)((()=>!0===e.rounded||!0===e.fab||!0===e.fabMini)),d=(0,r.Fl)((()=>!0!==e.disable&&!0!==e.loading)),y=(0,r.Fl)((()=>!0===d.value?e.tabindex||0:-1)),w=(0,r.Fl)((()=>!0===e.flat?"flat":!0===e.outline?"outline":!0===e.push?"push":!0===e.unelevated?"unelevated":"standard")),x=(0,r.Fl)((()=>{const t={tabindex:y.value};return!0===i.value?Object.assign(t,l.value):!0===m.includes(e.type)&&(t.type=e.type),"a"===a.value?(!0===e.disable?t["aria-disabled"]="true":void 0===t.href&&(t.role="button"),!0!==o.value&&!0===b.test(e.type)&&(t.type=e.type)):!0===e.disable&&(t.disabled="",t["aria-disabled"]="true"),!0===e.loading&&void 0!==e.percentage&&Object.assign(t,{role:"progressbar","aria-valuemin":0,"aria-valuemax":100,"aria-valuenow":e.percentage}),t})),_=(0,r.Fl)((()=>{let t;return void 0!==e.color?t=!0===e.flat||!0===e.outline?`text-${e.textColor||e.color}`:`bg-${e.color} text-${e.textColor||"white"}`:e.textColor&&(t=`text-${e.textColor}`),`q-btn--${w.value} q-btn--`+(!0===e.round?"round":"rectangle"+(!0===c.value?" q-btn--rounded":""))+(void 0!==t?" "+t:"")+(!0===d.value?" q-btn--actionable q-focusable q-hoverable":!0===e.disable?" disabled":"")+(!0===e.fab?" q-btn--fab":!0===e.fabMini?" q-btn--fab-mini":"")+(!0===e.noCaps?" q-btn--no-uppercase":"")+(!0===e.dense?" q-btn--dense":"")+(!0===e.stretch?" no-border-radius self-stretch":"")+(!0===e.glossy?" glossy":"")})),k=(0,r.Fl)((()=>n.value+(!0===e.stack?" column":" row")+(!0===e.noWrap?" no-wrap text-no-wrap":"")+(!0===e.loading?" q-btn__content--hidden":"")));return{classes:_,style:u,innerClasses:k,attributes:x,hasRouterLink:o,hasLink:i,linkTag:a,navigateToRouterLink:s,isActionable:d}}var x=n(908),_=n(7657),k=n(4716),S=n(1436);const{passiveCapture:C}=k.rU;let F=null,E=null,R=null;const A=(0,x.L)({name:"QBtn",props:{...y,percentage:Number,darkPercentage:Boolean},emits:["click","keydown","touchstart","mousedown","keyup"],setup(e,{slots:t,emit:n}){const{proxy:u}=(0,r.FN)(),{classes:c,style:d,innerClasses:f,attributes:p,hasRouterLink:v,hasLink:h,linkTag:g,navigateToRouterLink:m,isActionable:b}=w(e),y=(0,o.iH)(null),x=(0,o.iH)(null);let A,q,T=null;const L=(0,r.Fl)((()=>void 0!==e.label&&null!==e.label&&""!==e.label)),P=(0,r.Fl)((()=>!0!==e.disable&&!1!==e.ripple&&{keyCodes:!0===h.value?[13,32]:[13],...!0===e.ripple?{}:e.ripple})),O=(0,r.Fl)((()=>({center:e.round}))),M=(0,r.Fl)((()=>{const t=Math.max(0,Math.min(100,e.percentage));return t>0?{transition:"transform 0.6s",transform:`translateX(${t-100}%)`}:{}})),B=(0,r.Fl)((()=>!0===e.loading?{onMousedown:z,onTouchstartPassive:z,onClick:z,onKeydown:z,onKeyup:z}:!0===b.value?{onClick:I,onKeydown:j,onMousedown:V,onTouchstartPassive:N}:{onClick:k.NS})),$=(0,r.Fl)((()=>({ref:y,class:"q-btn q-btn-item non-selectable no-outline "+c.value,style:d.value,...p.value,...B.value})));function I(t){if(null!==y.value){if(void 0!==t){if(!0===t.defaultPrevented)return;const n=document.activeElement;if("submit"===e.type&&n!==document.body&&!1===y.value.contains(n)&&!1===n.contains(y.value)){y.value.focus();const e=()=>{document.removeEventListener("keydown",k.NS,!0),document.removeEventListener("keyup",e,C),null!==y.value&&y.value.removeEventListener("blur",e,C)};document.addEventListener("keydown",k.NS,!0),document.addEventListener("keyup",e,C),y.value.addEventListener("blur",e,C)}}if(!0===v.value){const e=()=>{t.__qNavigate=!0,m(t)};n("click",t,e),!0!==t.defaultPrevented&&e()}else n("click",t)}}function j(e){null!==y.value&&(!0===(0,S.So)(e,[13,32])&&((0,k.NS)(e),E!==y.value&&(null!==E&&H(),y.value.focus(),E=y.value,y.value.classList.add("q-btn--active"),document.addEventListener("keyup",U,!0),y.value.addEventListener("blur",U,C))),n("keydown",e))}function N(e){null!==y.value&&(F!==y.value&&(null!==F&&H(),F=y.value,T=e.target,T.addEventListener("touchcancel",U,C),T.addEventListener("touchend",U,C)),A=!0,clearTimeout(q),q=setTimeout((()=>{A=!1}),200),n("touchstart",e))}function V(e){null!==y.value&&(R!==y.value&&(null!==R&&H(),R=y.value,y.value.classList.add("q-btn--active"),document.addEventListener("mouseup",U,C)),e.qSkipRipple=!0===A,n("mousedown",e))}function U(e){if(null!==y.value&&(void 0===e||"blur"!==e.type||document.activeElement!==y.value)){if(void 0!==e&&"keyup"===e.type){if(E===y.value&&!0===(0,S.So)(e,[13,32])){const t=new MouseEvent("click",e);t.qKeyEvent=!0,!0===e.defaultPrevented&&(0,k.X$)(t),!0===e.cancelBubble&&(0,k.sT)(t),y.value.dispatchEvent(t),(0,k.NS)(e),e.qKeyEvent=!0}n("keyup",e)}H()}}function H(e){const t=x.value;!0===e||F!==y.value&&R!==y.value||null===t||t===document.activeElement||(t.setAttribute("tabindex",-1),t.focus()),F===y.value&&(null!==T&&(T.removeEventListener("touchcancel",U,C),T.removeEventListener("touchend",U,C)),F=T=null),R===y.value&&(document.removeEventListener("mouseup",U,C),R=null),E===y.value&&(document.removeEventListener("keyup",U,!0),null!==y.value&&y.value.removeEventListener("blur",U,C),E=null),null!==y.value&&y.value.classList.remove("q-btn--active")}function z(e){(0,k.NS)(e),e.qSkipRipple=!0}return(0,r.Jd)((()=>{H(!0)})),Object.assign(u,{click:I}),()=>{let n=[];void 0!==e.icon&&n.push((0,r.h)(a.Z,{name:e.icon,left:!1===e.stack&&!0===L.value,role:"img","aria-hidden":"true"})),!0===L.value&&n.push((0,r.h)("span",{class:"block"},[e.label])),n=(0,_.vs)(t.default,n),void 0!==e.iconRight&&!1===e.round&&n.push((0,r.h)(a.Z,{name:e.iconRight,right:!1===e.stack&&!0===L.value,role:"img","aria-hidden":"true"}));const o=[(0,r.h)("span",{class:"q-focus-helper",ref:x})];return!0===e.loading&&void 0!==e.percentage&&o.push((0,r.h)("span",{class:"q-btn__progress absolute-full overflow-hidden"},[(0,r.h)("span",{class:"q-btn__progress-indicator fit block"+(!0===e.darkPercentage?" q-btn__progress--dark":""),style:M.value})])),o.push((0,r.h)("span",{class:"q-btn__content text-center col items-center q-anchor--skip "+f.value},n)),null!==e.loading&&o.push((0,r.h)(i.uT,{name:"q-transition--fade"},(()=>!0===e.loading?[(0,r.h)("span",{key:"loading",class:"absolute-full flex flex-center"},void 0!==t.loading?t.loading():[(0,r.h)(l.Z)])]:null))),(0,r.wy)((0,r.h)(g.value,$.value,o),[[s.Z,P.value,void 0,O.value]])}}})},400:(e,t,n)=>{"use strict";n.d(t,{Z:()=>Q});n(6245),n(5363);var r=n(3673),o=n(1959),i=n(5777),a=n(2064),l=n(4554),s=(n(71),n(4688));function u(){const e=(0,o.iH)(!s.uX.value);return!1===e.value&&(0,r.bv)((()=>{e.value=!0})),e}var c=n(908),d=n(4716);const f="undefined"!==typeof ResizeObserver,p=!0===f?{}:{style:"display:block;position:absolute;top:0;left:0;right:0;bottom:0;height:100%;width:100%;overflow:hidden;pointer-events:none;z-index:-1;",url:"about:blank"},v=(0,c.L)({name:"QResizeObserver",props:{debounce:{type:[String,Number],default:100}},emits:["resize"],setup(e,{emit:t}){let n,o=null,i={width:-1,height:-1};function a(t){!0===t||0===e.debounce||"0"===e.debounce?l():null===o&&(o=setTimeout(l,e.debounce))}function l(){if(clearTimeout(o),o=null,n){const{offsetWidth:e,offsetHeight:r}=n;e===i.width&&r===i.height||(i={width:e,height:r},t("resize",i))}}const s=(0,r.FN)();if(Object.assign(s.proxy,{trigger:a}),!0===f){let e;return(0,r.bv)((()=>{(0,r.Y3)((()=>{n=s.proxy.$el.parentNode,n&&(e=new ResizeObserver(a),e.observe(n),l())}))})),(0,r.Jd)((()=>{clearTimeout(o),void 0!==e&&(void 0!==e.disconnect?e.disconnect():n&&e.unobserve(n))})),d.ZT}{const e=u();let t;function i(){clearTimeout(o),void 0!==t&&(void 0!==t.removeEventListener&&t.removeEventListener("resize",a,d.rU.passive),t=void 0)}function c(){i(),n&&n.contentDocument&&(t=n.contentDocument.defaultView,t.addEventListener("resize",a,d.rU.passive),l())}return(0,r.bv)((()=>{(0,r.Y3)((()=>{n=s.proxy.$el,n&&c()}))})),(0,r.Jd)(i),()=>{if(!0===e.value)return(0,r.h)("object",{style:p.style,tabindex:-1,type:"text/html",data:p.url,"aria-hidden":"true",onLoad:c})}}}});var h=n(416),g=n(4955),m=n(7657),b=n(2547);let y=!1;{const e=document.createElement("div"),t=document.createElement("div");e.setAttribute("dir","rtl"),e.style.width="1px",e.style.height="1px",e.style.overflow="auto",t.style.width="1000px",t.style.height="1px",document.body.appendChild(e),e.appendChild(t),e.scrollLeft=-1e3,y=e.scrollLeft>=0,e.remove()}function w(e,t,n){const r=!0===n?["left","right"]:["top","bottom"];return`absolute-${!0===t?r[0]:r[1]}${e?` text-${e}`:""}`}const x=["left","center","right","justify"],_=()=>{},k=(0,c.L)({name:"QTabs",props:{modelValue:[Number,String],align:{type:String,default:"center",validator:e=>x.includes(e)},breakpoint:{type:[String,Number],default:600},vertical:Boolean,shrink:Boolean,stretch:Boolean,activeClass:String,activeColor:String,activeBgColor:String,indicatorColor:String,leftIcon:String,rightIcon:String,outsideArrows:Boolean,mobileArrows:Boolean,switchIndicator:Boolean,narrowIndicator:Boolean,inlineLabel:Boolean,noCaps:Boolean,dense:Boolean,contentClass:String,"onUpdate:modelValue":[Function,Array]},setup(e,{slots:t,emit:n}){const i=(0,r.FN)(),{proxy:{$q:a}}=i,{registerTick:s}=(0,h.Z)(),{registerTimeout:u,removeTimeout:c}=(0,g.Z)(),{registerTimeout:f}=(0,g.Z)(),p=(0,o.iH)(null),x=(0,o.iH)(null),k=(0,o.iH)(e.modelValue),S=(0,o.iH)(!1),C=(0,o.iH)(!0),F=(0,o.iH)(!1),E=(0,o.iH)(!1),R=(0,r.Fl)((()=>!0===a.platform.is.desktop||!0===e.mobileArrows)),A=[],q=(0,o.iH)(!1);let T,L,P,O=!1,M=!0===R.value?Y:d.ZT;const B=(0,r.Fl)((()=>({activeClass:e.activeClass,activeColor:e.activeColor,activeBgColor:e.activeBgColor,indicatorClass:w(e.indicatorColor,e.switchIndicator,e.vertical),narrowIndicator:e.narrowIndicator,inlineLabel:e.inlineLabel,noCaps:e.noCaps}))),$=(0,r.Fl)((()=>{const t=!0===S.value?"left":!0===E.value?"justify":e.align;return`q-tabs__content--align-${t}`})),I=(0,r.Fl)((()=>`q-tabs row no-wrap items-center q-tabs--${!0===S.value?"":"not-"}scrollable q-tabs--`+(!0===e.vertical?"vertical":"horizontal")+" q-tabs__arrows--"+(!0===R.value&&!0===e.outsideArrows?"outside":"inside")+(!0===e.dense?" q-tabs--dense":"")+(!0===e.shrink?" col-shrink":"")+(!0===e.stretch?" self-stretch":""))),j=(0,r.Fl)((()=>"q-tabs__content row no-wrap items-center self-stretch hide-scrollbar relative-position "+$.value+(void 0!==e.contentClass?` ${e.contentClass}`:"")+(!0===a.platform.is.mobile?" scroll":""))),N=(0,r.Fl)((()=>!0===e.vertical?{container:"height",content:"offsetHeight",scroll:"scrollHeight"}:{container:"width",content:"offsetWidth",scroll:"scrollWidth"})),V=(0,r.Fl)((()=>!0!==e.vertical&&!0===a.lang.rtl)),U=(0,r.Fl)((()=>!1===y&&!0===V.value));function H({name:t,setCurrent:r,skipEmit:o,fromRoute:i}){k.value!==t&&(!0!==o&&n("update:modelValue",t),!0!==r&&void 0!==e["onUpdate:modelValue"]||(Z(k.value,t),k.value=t)),void 0!==i&&(O=i)}function z(){s((()=>{!0!==i.isDeactivated&&!0!==i.isUnmounted&&D({width:p.value.offsetWidth,height:p.value.offsetHeight})}))}function D(t){if(void 0===N.value||null===x.value)return;const n=t[N.value.container],o=Math.min(x.value[N.value.scroll],Array.prototype.reduce.call(x.value.children,((e,t)=>e+(t[N.value.content]||0)),0)),i=n>0&&o>n;S.value!==i&&(S.value=i),!0===i&&(0,r.Y3)(M);const a=n<parseInt(e.breakpoint,10);E.value!==a&&(E.value=a)}function Z(t,n){const o=void 0!==t&&null!==t&&""!==t?A.find((e=>e.name.value===t)):null,i=void 0!==n&&null!==n&&""!==n?A.find((e=>e.name.value===n)):null;if(o&&i){const t=o.tabIndicatorRef.value,n=i.tabIndicatorRef.value;clearTimeout(T),t.style.transition="none",t.style.transform="none",n.style.transition="none",n.style.transform="none";const a=t.getBoundingClientRect(),l=n.getBoundingClientRect();n.style.transform=!0===e.vertical?`translate3d(0,${a.top-l.top}px,0) scale3d(1,${l.height?a.height/l.height:1},1)`:`translate3d(${a.left-l.left}px,0,0) scale3d(${l.width?a.width/l.width:1},1,1)`,(0,r.Y3)((()=>{T=setTimeout((()=>{n.style.transition="transform .25s cubic-bezier(.4, 0, .2, 1)",n.style.transform="none"}),70)}))}i&&!0===S.value&&W(i.rootRef.value)}function W(t){const{left:n,width:r,top:o,height:i}=x.value.getBoundingClientRect(),a=t.getBoundingClientRect();let l=!0===e.vertical?a.top-o:a.left-n;if(l<0)return x.value[!0===e.vertical?"scrollTop":"scrollLeft"]+=Math.floor(l),void M();l+=!0===e.vertical?a.height-i:a.width-r,l>0&&(x.value[!0===e.vertical?"scrollTop":"scrollLeft"]+=Math.ceil(l),M())}function Y(){const t=x.value;if(null!==t){const n=t.getBoundingClientRect(),r=!0===e.vertical?t.scrollTop:Math.abs(t.scrollLeft);!0===V.value?(C.value=Math.ceil(r+n.width)<t.scrollWidth-1,F.value=r>0):(C.value=r>0,F.value=!0===e.vertical?Math.ceil(r+n.height)<t.scrollHeight:Math.ceil(r+n.width)<t.scrollWidth)}}function J(e){G(),te(e),L=setInterval((()=>{!0===te(e)&&G()}),5)}function K(){J(!0===U.value?Number.MAX_SAFE_INTEGER:0)}function X(){J(!0===U.value?0:Number.MAX_SAFE_INTEGER)}function G(){clearInterval(L)}function Q(t,n){const r=Array.prototype.filter.call(x.value.children,(e=>e===n||e.matches&&!0===e.matches(".q-tab.q-focusable"))),o=r.length;if(0===o)return;if(36===t)return W(r[0]),!0;if(35===t)return W(r[o-1]),!0;const i=t===(!0===e.vertical?38:37),a=t===(!0===e.vertical?40:39),l=!0===i?-1:!0===a?1:void 0;if(void 0!==l){const e=!0===V.value?-1:1,t=r.indexOf(n)+l*e;return t>=0&&t<o&&(W(r[t]),r[t].focus({preventScroll:!0})),!0}}(0,r.YP)(V,M),(0,r.YP)((()=>e.modelValue),(e=>{H({name:e,setCurrent:!0,skipEmit:!0})})),(0,r.YP)((()=>e.outsideArrows),(()=>{(0,r.Y3)(z())})),(0,r.YP)(R,(e=>{M=!0===e?Y:d.ZT,(0,r.Y3)(z())}));const ee=(0,r.Fl)((()=>!0===U.value?{get:e=>Math.abs(e.scrollLeft),set:(e,t)=>{e.scrollLeft=-t}}:!0===e.vertical?{get:e=>e.scrollTop,set:(e,t)=>{e.scrollTop=t}}:{get:e=>e.scrollLeft,set:(e,t)=>{e.scrollLeft=t}}));function te(e){const t=x.value,{get:n,set:r}=ee.value;let o=!1,i=n(t);const a=e<i?-1:1;return i+=5*a,i<0?(o=!0,i=0):(-1===a&&i<=e||1===a&&i>=e)&&(o=!0,i=e),r(t,i),M(),o}function ne(){return A.filter((e=>void 0!==e.routerProps&&!0===e.routerProps.hasRouterLink.value))}function re(){let e=null,t=O;const n={matchedLen:0,hrefLen:0,exact:!1,found:!1},{hash:r}=i.proxy.$route,o=k.value;let a=!0===t?_:e=>{o===e.name.value&&(t=!0,a=_)};const l=ne();for(const i of l){const t=!0===i.routerProps.exact.value;if(!0!==i.routerProps[!0===t?"linkIsExactActive":"linkIsActive"].value||!0===n.exact&&!0!==t){a(i);continue}const o=i.routerProps.linkRoute.value,l=o.hash;if(!0===t){if(r===l){e=i.name.value;break}if(""!==r&&""!==l){a(i);continue}}const s=o.matched.length,u=o.href.length-l.length;(s===n.matchedLen?u>n.hrefLen:s>n.matchedLen)?(e=i.name.value,Object.assign(n,{matchedLen:s,hrefLen:u,exact:t})):a(i)}!0!==t&&null===e||H({name:e,setCurrent:!0,fromRoute:!0})}function oe(e){if(c(),!0!==q.value&&null!==p.value&&e.target&&"function"===typeof e.target.closest){const t=e.target.closest(".q-tab");t&&!0===p.value.contains(t)&&(q.value=!0)}}function ie(){u((()=>{q.value=!1}),30)}function ae(){!0!==ue.avoidRouteWatcher&&f(re)}function le(e){A.push(e);const t=ne();t.length>0&&(void 0===P&&(P=(0,r.YP)((()=>i.proxy.$route),ae)),ae())}function se(e){if(A.splice(A.indexOf(e),1),void 0!==P){const e=ne();0===e.length&&(P(),P=void 0),ae()}}const ue={currentModel:k,tabProps:B,hasFocus:q,registerTab:le,unregisterTab:se,verifyRouteModel:ae,updateModel:H,recalculateScroll:z,onKbdNavigate:Q,avoidRouteWatcher:!1};return(0,r.JJ)(b.Nd,ue),(0,r.Jd)((()=>{clearTimeout(T),void 0!==P&&P()})),(0,r.dl)(z),()=>{const n=[(0,r.h)(v,{onResize:D}),(0,r.h)("div",{ref:x,class:j.value,onScroll:M},(0,m.KR)(t.default))];return!0===R.value&&n.push((0,r.h)(l.Z,{class:"q-tabs__arrow q-tabs__arrow--left absolute q-tab__icon"+(!0===C.value?"":" q-tabs__arrow--faded"),name:e.leftIcon||a.iconSet.tabs[!0===e.vertical?"up":"left"],onMousedown:K,onTouchstartPassive:K,onMouseup:G,onMouseleave:G,onTouchend:G}),(0,r.h)(l.Z,{class:"q-tabs__arrow q-tabs__arrow--right absolute q-tab__icon"+(!0===F.value?"":" q-tabs__arrow--faded"),name:e.rightIcon||a.iconSet.tabs[!0===e.vertical?"down":"right"],onMousedown:X,onTouchstartPassive:X,onMouseup:G,onMouseleave:G,onTouchend:G})),(0,r.h)("div",{ref:p,class:I.value,role:"tablist",onFocusin:oe,onFocusout:ie},n)}}});var S=n(860),C=n(1436);let F=0;const E=["click","keydown"],R={icon:String,label:[Number,String],alert:[Boolean,String],alertIcon:String,name:{type:[Number,String],default:()=>"t_"+F++},noCaps:Boolean,tabindex:[String,Number],disable:Boolean,contentClass:String,ripple:{type:[Boolean,Object],default:!0}};function A(e,t,n,i){const a=(0,r.f3)(b.Nd,(()=>{console.error("QTab/QRouteTab component needs to be child of QTabs")})),{proxy:s}=(0,r.FN)(),u=(0,o.iH)(null),c=(0,o.iH)(null),f=(0,o.iH)(null),p=(0,r.Fl)((()=>!0!==e.disable&&!1!==e.ripple&&Object.assign({keyCodes:[13,32],early:!0},!0===e.ripple?{}:e.ripple))),v=(0,r.Fl)((()=>a.currentModel.value===e.name)),h=(0,r.Fl)((()=>"q-tab relative-position self-stretch flex flex-center text-center"+(!0===v.value?" q-tab--active"+(a.tabProps.value.activeClass?" "+a.tabProps.value.activeClass:"")+(a.tabProps.value.activeColor?` text-${a.tabProps.value.activeColor}`:"")+(a.tabProps.value.activeBgColor?` bg-${a.tabProps.value.activeBgColor}`:""):" q-tab--inactive")+(e.icon&&e.label&&!1===a.tabProps.value.inlineLabel?" q-tab--full":"")+(!0===e.noCaps||!0===a.tabProps.value.noCaps?" q-tab--no-caps":"")+(!0===e.disable?" disabled":" q-focusable q-hoverable cursor-pointer")+(void 0!==i&&""!==i.linkClass.value?` ${i.linkClass.value}`:""))),g=(0,r.Fl)((()=>"q-tab__content self-stretch flex-center relative-position q-anchor--skip non-selectable "+(!0===a.tabProps.value.inlineLabel?"row no-wrap q-tab__content--inline":"column")+(void 0!==e.contentClass?` ${e.contentClass}`:""))),y=(0,r.Fl)((()=>!0===e.disable||!0===a.hasFocus.value?-1:e.tabindex||0));function w(t,r){if(!0!==r&&null!==u.value&&u.value.focus(),!0!==e.disable){let r;if(void 0!==i){if(!0!==i.hasRouterLink.value)return void n("click",t);r=()=>{t.__qNavigate=!0,a.avoidRouteWatcher=!0;const n=i.navigateToRouterLink(t);!1===n?a.avoidRouteWatcher=!1:n.then((()=>{a.avoidRouteWatcher=!1,a.updateModel({name:e.name,fromRoute:!0})}))}}else r=()=>{a.updateModel({name:e.name,fromRoute:!1})};n("click",t,r),!0!==t.defaultPrevented&&r()}}function x(e){(0,C.So)(e,[13,32])?w(e,!0):!0!==(0,C.Wm)(e)&&e.keyCode>=35&&e.keyCode<=40&&!0===a.onKbdNavigate(e.keyCode,s.$el)&&(0,d.NS)(e),n("keydown",e)}function _(){const n=a.tabProps.value.narrowIndicator,o=[],i=(0,r.h)("div",{ref:f,class:["q-tab__indicator",a.tabProps.value.indicatorClass]});void 0!==e.icon&&o.push((0,r.h)(l.Z,{class:"q-tab__icon",name:e.icon})),void 0!==e.label&&o.push((0,r.h)("div",{class:"q-tab__label"},e.label)),!1!==e.alert&&o.push(void 0!==e.alertIcon?(0,r.h)(l.Z,{class:"q-tab__alert-icon",color:!0!==e.alert?e.alert:void 0,name:e.alertIcon}):(0,r.h)("div",{class:"q-tab__alert"+(!0!==e.alert?` text-${e.alert}`:"")})),!0===n&&o.push(i);const s=[(0,r.h)("div",{class:"q-focus-helper",tabindex:-1,ref:u}),(0,r.h)("div",{class:g.value},(0,m.vs)(t.default,o))];return!1===n&&s.push(i),s}const k={name:(0,r.Fl)((()=>e.name)),rootRef:c,tabIndicatorRef:f,routerProps:i};function F(t,n){const o={ref:c,class:h.value,tabindex:y.value,role:"tab","aria-selected":!0===v.value?"true":"false","aria-disabled":!0===e.disable?"true":void 0,onClick:w,onKeydown:x,...n};return(0,r.wy)((0,r.h)(t,o,_()),[[S.Z,p.value]])}return(0,r.Jd)((()=>{a.unregisterTab(k),a.recalculateScroll()})),(0,r.bv)((()=>{a.registerTab(k),a.recalculateScroll()})),{renderTab:F,$tabs:a}}const q=(0,c.L)({name:"QTab",props:R,emits:E,setup(e,{slots:t,emit:n}){const{renderTab:r}=A(e,t,n);return()=>r("div")}});var T=n(2236),L=n(8880),P=n(6104),O=n(9725);function M(e){const t=[.06,6,50];return"string"===typeof e&&e.length&&e.split(":").forEach(((e,n)=>{const r=parseFloat(e);r&&(t[n]=r)})),t}const B=(0,c.f)({name:"touch-swipe",beforeMount(e,{value:t,arg:n,modifiers:r}){if(!0!==r.mouse&&!0!==s.Lp.has.touch)return;const o=!0===r.mouseCapture?"Capture":"",i={handler:t,sensitivity:M(n),direction:(0,P.R)(r),noop:d.ZT,mouseStart(e){(0,P.n)(e,i)&&(0,d.du)(e)&&((0,d.M0)(i,"temp",[[document,"mousemove","move",`notPassive${o}`],[document,"mouseup","end","notPassiveCapture"]]),i.start(e,!0))},touchStart(e){if((0,P.n)(e,i)){const t=e.target;(0,d.M0)(i,"temp",[[t,"touchmove","move","notPassiveCapture"],[t,"touchcancel","end","notPassiveCapture"],[t,"touchend","end","notPassiveCapture"]]),i.start(e)}},start(t,n){!0===s.Lp.is.firefox&&(0,d.Jf)(e,!0);const r=(0,d.FK)(t);i.event={x:r.left,y:r.top,time:Date.now(),mouse:!0===n,dir:!1}},move(e){if(void 0===i.event)return;if(!1!==i.event.dir)return void(0,d.NS)(e);const t=Date.now()-i.event.time;if(0===t)return;const n=(0,d.FK)(e),r=n.left-i.event.x,o=Math.abs(r),a=n.top-i.event.y,l=Math.abs(a);if(!0!==i.event.mouse){if(o<i.sensitivity[1]&&l<i.sensitivity[1])return void i.end(e)}else if(o<i.sensitivity[2]&&l<i.sensitivity[2])return;const s=o/t,u=l/t;!0===i.direction.vertical&&o<l&&o<100&&u>i.sensitivity[0]&&(i.event.dir=a<0?"up":"down"),!0===i.direction.horizontal&&o>l&&l<100&&s>i.sensitivity[0]&&(i.event.dir=r<0?"left":"right"),!0===i.direction.up&&o<l&&a<0&&o<100&&u>i.sensitivity[0]&&(i.event.dir="up"),!0===i.direction.down&&o<l&&a>0&&o<100&&u>i.sensitivity[0]&&(i.event.dir="down"),!0===i.direction.left&&o>l&&r<0&&l<100&&s>i.sensitivity[0]&&(i.event.dir="left"),!0===i.direction.right&&o>l&&r>0&&l<100&&s>i.sensitivity[0]&&(i.event.dir="right"),!1!==i.event.dir?((0,d.NS)(e),!0===i.event.mouse&&(document.body.classList.add("no-pointer-events--children"),document.body.classList.add("non-selectable"),(0,O.M)(),i.styleCleanup=e=>{i.styleCleanup=void 0,document.body.classList.remove("non-selectable");const t=()=>{document.body.classList.remove("no-pointer-events--children")};!0===e?setTimeout(t,50):t()}),i.handler({evt:e,touch:!0!==i.event.mouse,mouse:i.event.mouse,direction:i.event.dir,duration:t,distance:{x:o,y:l}})):i.end(e)},end(t){void 0!==i.event&&((0,d.ul)(i,"temp"),!0===s.Lp.is.firefox&&(0,d.Jf)(e,!1),void 0!==i.styleCleanup&&i.styleCleanup(!0),void 0!==t&&!1!==i.event.dir&&(0,d.NS)(t),i.event=void 0)}};e.__qtouchswipe=i,!0===r.mouse&&(0,d.M0)(i,"main",[[e,"mousedown","mouseStart",`passive${o}`]]),!0===s.Lp.has.touch&&(0,d.M0)(i,"main",[[e,"touchstart","touchStart","passive"+(!0===r.capture?"Capture":"")],[e,"touchmove","noop","notPassiveCapture"]])},updated(e,t){const n=e.__qtouchswipe;void 0!==n&&(t.oldValue!==t.value&&("function"!==typeof t.value&&n.end(),n.handler=t.value),n.direction=(0,P.R)(t.modifiers))},beforeUnmount(e){const t=e.__qtouchswipe;void 0!==t&&((0,d.ul)(t,"main"),(0,d.ul)(t,"temp"),!0===s.Lp.is.firefox&&(0,d.Jf)(e,!1),void 0!==t.styleCleanup&&t.styleCleanup(),delete e.__qtouchswipe)}});function $(){const e=new Map;return{getCache:function(t,n){return void 0===e[t]?e[t]=n:e[t]},getCacheWithFn:function(t,n){return void 0===e[t]?e[t]=n():e[t]}}}var I=n(7445);const j={name:{required:!0},disable:Boolean},N={setup(e,{slots:t}){return()=>(0,r.h)("div",{class:"q-panel scroll",role:"tabpanel"},(0,m.KR)(t.default))}},V={modelValue:{required:!0},animated:Boolean,infinite:Boolean,swipeable:Boolean,vertical:Boolean,transitionPrev:String,transitionNext:String,transitionDuration:{type:[String,Number],default:300},keepAlive:Boolean,keepAliveInclude:[String,Array,RegExp],keepAliveExclude:[String,Array,RegExp],keepAliveMax:Number},U=["update:modelValue","before-transition","transition"];function H(){const{props:e,emit:t,proxy:n}=(0,r.FN)(),{getCacheWithFn:i}=$();let a,l;const s=(0,o.iH)(null),u=(0,o.iH)(null);function c(t){const r=!0===e.vertical?"up":"left";F((!0===n.$q.lang.rtl?-1:1)*(t.direction===r?1:-1))}const d=(0,r.Fl)((()=>[[B,c,void 0,{horizontal:!0!==e.vertical,vertical:e.vertical,mouse:!0}]])),f=(0,r.Fl)((()=>e.transitionPrev||"slide-"+(!0===e.vertical?"down":"right"))),p=(0,r.Fl)((()=>e.transitionNext||"slide-"+(!0===e.vertical?"up":"left"))),v=(0,r.Fl)((()=>`--q-transition-duration: ${e.transitionDuration}ms`)),h=(0,r.Fl)((()=>"string"===typeof e.modelValue||"number"===typeof e.modelValue?e.modelValue:String(e.modelValue))),g=(0,r.Fl)((()=>({include:e.keepAliveInclude,exclude:e.keepAliveExclude,max:e.keepAliveMax}))),b=(0,r.Fl)((()=>void 0!==e.keepAliveInclude||void 0!==e.keepAliveExclude));function y(){F(1)}function w(){F(-1)}function x(e){t("update:modelValue",e)}function _(e){return void 0!==e&&null!==e&&""!==e}function k(e){return a.findIndex((t=>t.props.name===e&&""!==t.props.disable&&!0!==t.props.disable))}function S(){return a.filter((e=>""!==e.props.disable&&!0!==e.props.disable))}function C(t){const n=0!==t&&!0===e.animated&&-1!==s.value?"q-transition--"+(-1===t?f.value:p.value):null;u.value!==n&&(u.value=n)}function F(n,r=s.value){let o=r+n;while(o>-1&&o<a.length){const e=a[o];if(void 0!==e&&""!==e.props.disable&&!0!==e.props.disable)return C(n),l=!0,t("update:modelValue",e.props.name),void setTimeout((()=>{l=!1}));o+=n}!0===e.infinite&&a.length>0&&-1!==r&&r!==a.length&&F(n,-1===n?a.length:-1)}function E(){const t=k(e.modelValue);return s.value!==t&&(s.value=t),!0}function R(){const t=!0===_(e.modelValue)&&E()&&a[s.value];return!0===e.keepAlive?[(0,r.h)(r.Ob,g.value,[(0,r.h)(!0===b.value?i(h.value,(()=>({...N,name:h.value}))):N,{key:h.value,style:v.value},(()=>t))])]:[(0,r.h)("div",{class:"q-panel scroll",style:v.value,key:h.value,role:"tabpanel"},[t])]}function A(){if(0!==a.length)return!0===e.animated?[(0,r.h)(L.uT,{name:u.value},R)]:R()}function q(e){return a=(0,I.Pf)((0,m.KR)(e.default,[])).filter((e=>null!==e.props&&void 0===e.props.slot&&!0===_(e.props.name))),a.length}function T(){return a}return(0,r.YP)((()=>e.modelValue),((e,n)=>{const o=!0===_(e)?k(e):-1;!0!==l&&C(-1===o?0:o<k(n)?-1:1),s.value!==o&&(s.value=o,t("before-transition",e,n),(0,r.Y3)((()=>{t("transition",e,n)})))})),Object.assign(n,{next:y,previous:w,goTo:x}),{panelIndex:s,panelDirectives:d,updatePanelsList:q,updatePanelIndex:E,getPanelContent:A,getEnabledPanels:S,getPanels:T,isValidPanelName:_,keepAliveProps:g,needsUniqueKeepAliveWrapper:b,goToPanelByOffset:F,goToPanel:x,nextPanel:y,previousPanel:w}}const z=(0,c.L)({name:"QTabPanels",props:{...V,...T.S},emits:U,setup(e,{slots:t}){const n=(0,r.FN)(),o=(0,T.Z)(e,n.proxy.$q),{updatePanelsList:i,getPanelContent:a,panelDirectives:l}=H(),s=(0,r.Fl)((()=>"q-tab-panels q-panel-parent"+(!0===o.value?" q-tab-panels--dark q-dark":"")));return()=>(i(t),(0,m.Jl)("div",{class:s.value},a(),"pan",e.swipeable,(()=>l.value)))}}),D=(0,c.L)({name:"QTabPanel",props:j,setup(e,{slots:t}){return()=>(0,r.h)("div",{class:"q-tab-panel"},(0,m.KR)(t.default))}});var Z=n(9550),W=n(9763),Y=n(3317),J=n(2156);const K=["rgb(255,204,204)","rgb(255,230,204)","rgb(255,255,204)","rgb(204,255,204)","rgb(204,255,230)","rgb(204,255,255)","rgb(204,230,255)","rgb(204,204,255)","rgb(230,204,255)","rgb(255,204,255)","rgb(255,153,153)","rgb(255,204,153)","rgb(255,255,153)","rgb(153,255,153)","rgb(153,255,204)","rgb(153,255,255)","rgb(153,204,255)","rgb(153,153,255)","rgb(204,153,255)","rgb(255,153,255)","rgb(255,102,102)","rgb(255,179,102)","rgb(255,255,102)","rgb(102,255,102)","rgb(102,255,179)","rgb(102,255,255)","rgb(102,179,255)","rgb(102,102,255)","rgb(179,102,255)","rgb(255,102,255)","rgb(255,51,51)","rgb(255,153,51)","rgb(255,255,51)","rgb(51,255,51)","rgb(51,255,153)","rgb(51,255,255)","rgb(51,153,255)","rgb(51,51,255)","rgb(153,51,255)","rgb(255,51,255)","rgb(255,0,0)","rgb(255,128,0)","rgb(255,255,0)","rgb(0,255,0)","rgb(0,255,128)","rgb(0,255,255)","rgb(0,128,255)","rgb(0,0,255)","rgb(128,0,255)","rgb(255,0,255)","rgb(245,0,0)","rgb(245,123,0)","rgb(245,245,0)","rgb(0,245,0)","rgb(0,245,123)","rgb(0,245,245)","rgb(0,123,245)","rgb(0,0,245)","rgb(123,0,245)","rgb(245,0,245)","rgb(214,0,0)","rgb(214,108,0)","rgb(214,214,0)","rgb(0,214,0)","rgb(0,214,108)","rgb(0,214,214)","rgb(0,108,214)","rgb(0,0,214)","rgb(108,0,214)","rgb(214,0,214)","rgb(163,0,0)","rgb(163,82,0)","rgb(163,163,0)","rgb(0,163,0)","rgb(0,163,82)","rgb(0,163,163)","rgb(0,82,163)","rgb(0,0,163)","rgb(82,0,163)","rgb(163,0,163)","rgb(92,0,0)","rgb(92,46,0)","rgb(92,92,0)","rgb(0,92,0)","rgb(0,92,46)","rgb(0,92,92)","rgb(0,46,92)","rgb(0,0,92)","rgb(46,0,92)","rgb(92,0,92)","rgb(255,255,255)","rgb(205,205,205)","rgb(178,178,178)","rgb(153,153,153)","rgb(127,127,127)","rgb(102,102,102)","rgb(76,76,76)","rgb(51,51,51)","rgb(25,25,25)","rgb(0,0,0)"],X="M5 5 h10 v10 h-10 v-10 z",G="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAYAAADED76LAAAAH0lEQVQoU2NkYGAwZkAFZ5G5jPRRgOYEVDeB3EBjBQBOZwTVugIGyAAAAABJRU5ErkJggg==",Q=(0,c.L)({name:"QColor",props:{...T.S,...Z.Fz,modelValue:String,defaultValue:String,defaultView:{type:String,default:"spectrum",validator:e=>["spectrum","tune","palette"].includes(e)},formatModel:{type:String,default:"auto",validator:e=>["auto","hex","rgb","hexa","rgba"].includes(e)},palette:Array,noHeader:Boolean,noHeaderTabs:Boolean,noFooter:Boolean,square:Boolean,flat:Boolean,bordered:Boolean,disable:Boolean,readonly:Boolean},emits:["update:modelValue","change"],setup(e,{emit:t}){const{proxy:n}=(0,r.FN)(),{$q:s}=n,u=(0,T.Z)(e,s),{getCache:c}=$(),f=(0,o.iH)(null),p=(0,o.iH)(null),v=(0,r.Fl)((()=>"auto"===e.formatModel?null:e.formatModel.indexOf("hex")>-1)),h=(0,r.Fl)((()=>"auto"===e.formatModel?null:e.formatModel.indexOf("a")>-1)),g=(0,o.iH)("auto"===e.formatModel?void 0===e.modelValue||null===e.modelValue||""===e.modelValue||e.modelValue.startsWith("#")?"hex":"rgb":e.formatModel.startsWith("hex")?"hex":"rgb"),b=(0,o.iH)(e.defaultView),y=(0,o.iH)(j(e.modelValue||e.defaultValue)),w=(0,r.Fl)((()=>!0!==e.disable&&!0!==e.readonly)),x=(0,r.Fl)((()=>void 0===e.modelValue||null===e.modelValue||""===e.modelValue||e.modelValue.startsWith("#"))),_=(0,r.Fl)((()=>null!==v.value?v.value:x.value)),S=(0,r.Fl)((()=>({type:"hidden",name:e.name,value:y.value[!0===_.value?"hex":"rgb"]}))),C=(0,Z.eX)(S),F=(0,r.Fl)((()=>null!==h.value?h.value:void 0!==y.value.a)),E=(0,r.Fl)((()=>({backgroundColor:y.value.rgb||"#000"}))),R=(0,r.Fl)((()=>{const e=void 0!==y.value.a&&y.value.a<65||(0,J.T2)(y.value)>.4;return"q-color-picker__header-content q-color-picker__header-content--"+(e?"light":"dark")})),A=(0,r.Fl)((()=>({background:`hsl(${y.value.h},100%,50%)`}))),L=(0,r.Fl)((()=>({top:100-y.value.v+"%",[!0===s.lang.rtl?"right":"left"]:`${y.value.s}%`}))),P=(0,r.Fl)((()=>void 0!==e.palette&&e.palette.length>0?e.palette:K)),O=(0,r.Fl)((()=>"q-color-picker"+(!0===e.bordered?" q-color-picker--bordered":"")+(!0===e.square?" q-color-picker--square no-border-radius":"")+(!0===e.flat?" q-color-picker--flat no-shadow":"")+(!0===e.disable?" disabled":"")+(!0===u.value?" q-color-picker--dark q-dark":""))),M=(0,r.Fl)((()=>!0===e.disable?{"aria-disabled":"true"}:!0===e.readonly?{"aria-readonly":"true"}:{})),B=(0,r.Fl)((()=>[[i.Z,ee,void 0,{prevent:!0,stop:!0,mouse:!0}]]));function I(e,n){y.value.hex=(0,J.vq)(e),y.value.rgb=(0,J.Nn)(e),y.value.r=e.r,y.value.g=e.g,y.value.b=e.b,y.value.a=e.a;const r=y.value[!0===_.value?"hex":"rgb"];t("update:modelValue",r),!0===n&&t("change",r)}function j(t){const n=void 0!==h.value?h.value:"auto"===e.formatModel?null:e.formatModel.indexOf("a")>-1;if("string"!==typeof t||0===t.length||!0!==W.E.anyColor(t.replace(/ /g,"")))return{h:0,s:0,v:0,r:0,g:0,b:0,a:!0===n?100:void 0,hex:void 0,rgb:void 0};const r=(0,J.id)(t);return!0===n&&void 0===r.a&&(r.a=100),r.hex=(0,J.vq)(r),r.rgb=(0,J.Nn)(r),Object.assign(r,(0,J.py)(r))}function N(e,t,n){const r=f.value;if(null===r)return;const o=r.clientWidth,i=r.clientHeight,a=r.getBoundingClientRect();let l=Math.min(o,Math.max(0,e-a.left));!0===s.lang.rtl&&(l=o-l);const u=Math.min(i,Math.max(0,t-a.top)),c=Math.round(100*l/o),d=Math.round(100*Math.max(0,Math.min(1,-u/i+1))),p=(0,J.WE)({h:y.value.h,s:c,v:d,a:!0===F.value?y.value.a:void 0});y.value.s=c,y.value.v=d,I(p,n)}function V(e,t){const n=Math.round(e),r=(0,J.WE)({h:n,s:y.value.s,v:y.value.v,a:!0===F.value?y.value.a:void 0});y.value.h=n,I(r,t)}function U(e,t,o,i,a){if(void 0!==i&&(0,d.sT)(i),!/^[0-9]+$/.test(e))return void(!0===a&&n.$forceUpdate());const l=Math.floor(Number(e));if(l<0||l>o)return void(!0===a&&n.$forceUpdate());const s={r:"r"===t?l:y.value.r,g:"g"===t?l:y.value.g,b:"b"===t?l:y.value.b,a:!0===F.value?"a"===t?l:y.value.a:void 0};if("a"!==t){const e=(0,J.py)(s);y.value.h=e.h,y.value.s=e.s,y.value.v=e.v}if(I(s,a),void 0!==i&&!0!==a&&void 0!==i.target.selectionEnd){const e=i.target.selectionEnd;(0,r.Y3)((()=>{i.target.setSelectionRange(e,e)}))}}function H(e,t){let n;const o=e.target.value;if((0,d.sT)(e),"hex"===g.value){if(o.length!==(!0===F.value?9:7)||!/^#[0-9A-Fa-f]+$/.test(o))return!0;n=(0,J.oo)(o)}else{let e;if(!o.endsWith(")"))return!0;if(!0!==F.value&&o.startsWith("rgb(")){if(e=o.substring(4,o.length-1).split(",").map((e=>parseInt(e,10))),3!==e.length||!/^rgb\([0-9]{1,3},[0-9]{1,3},[0-9]{1,3}\)$/.test(o))return!0}else{if(!0!==F.value||!o.startsWith("rgba("))return!0;{if(e=o.substring(5,o.length-1).split(","),4!==e.length||!/^rgba\([0-9]{1,3},[0-9]{1,3},[0-9]{1,3},(0|0\.[0-9]+[1-9]|0\.[1-9]+|1)\)$/.test(o))return!0;for(let n=0;n<3;n++){const t=parseInt(e[n],10);if(t<0||t>255)return!0;e[n]=t}const t=parseFloat(e[3]);if(t<0||t>1)return!0;e[3]=t}}if(e[0]<0||e[0]>255||e[1]<0||e[1]>255||e[2]<0||e[2]>255||!0===F.value&&(e[3]<0||e[3]>1))return!0;n={r:e[0],g:e[1],b:e[2],a:!0===F.value?100*e[3]:void 0}}const i=(0,J.py)(n);if(y.value.h=i.h,y.value.s=i.s,y.value.v=i.v,I(n,t),!0!==t){const t=e.target.selectionEnd;(0,r.Y3)((()=>{e.target.setSelectionRange(t,t)}))}}function Q(e){const t=j(e),n={r:t.r,g:t.g,b:t.b,a:t.a};void 0===n.a&&(n.a=y.value.a),y.value.h=t.h,y.value.s=t.s,y.value.v=t.v,I(n,!0)}function ee(e){e.isFinal?N(e.position.left,e.position.top,!0):te(e)}(0,r.YP)((()=>e.modelValue),(t=>{const n=j(t||e.defaultValue);n.hex!==y.value.hex&&(y.value=n)})),(0,r.YP)((()=>e.defaultValue),(t=>{if(!e.modelValue&&t){const e=j(t);e.hex!==y.value.hex&&(y.value=e)}}));const te=(0,Y.Z)((e=>{N(e.position.left,e.position.top)}),20);function ne(e){N(e.pageX-window.pageXOffset,e.pageY-window.pageYOffset,!0)}function re(e){N(e.pageX-window.pageXOffset,e.pageY-window.pageYOffset)}function oe(e){null!==p.value&&(p.value.$el.style.opacity=e?1:0)}function ie(){const t=[];return!0!==e.noHeaderTabs&&t.push((0,r.h)(k,{class:"q-color-picker__header-tabs",modelValue:g.value,dense:!0,align:"justify",...c("topVTab",{"onUpdate:modelValue":e=>{g.value=e}})},(()=>[(0,r.h)(q,{label:"HEX"+(!0===F.value?"A":""),name:"hex",ripple:!1}),(0,r.h)(q,{label:"RGB"+(!0===F.value?"A":""),name:"rgb",ripple:!1})]))),t.push((0,r.h)("div",{class:"q-color-picker__header-banner row flex-center no-wrap"},[(0,r.h)("input",{class:"fit",value:y.value[g.value],...!0!==w.value?{readonly:!0}:{},...c("topIn",{onInput:e=>{oe(!0===H(e))},onChange:d.sT,onBlur:e=>{!0===H(e,!0)&&n.$forceUpdate(),oe(!1)}})}),(0,r.h)(l.Z,{ref:p,class:"q-color-picker__error-icon absolute no-pointer-events",name:s.iconSet.type.negative})])),(0,r.h)("div",{class:"q-color-picker__header relative-position overflow-hidden"},[(0,r.h)("div",{class:"q-color-picker__header-bg absolute-full"}),(0,r.h)("div",{class:R.value,style:E.value},t)])}function ae(){return(0,r.h)(z,{modelValue:b.value,animated:!0},(()=>[(0,r.h)(D,{class:"q-color-picker__spectrum-tab overflow-hidden",name:"spectrum"},se),(0,r.h)(D,{class:"q-pa-md q-color-picker__tune-tab",name:"tune"},ue),(0,r.h)(D,{class:"q-color-picker__palette-tab",name:"palette"},ce)]))}function le(){return(0,r.h)("div",{class:"q-color-picker__footer relative-position overflow-hidden"},[(0,r.h)(k,{class:"absolute-full",modelValue:b.value,dense:!0,align:"justify",...c("ftIn",{"onUpdate:modelValue":e=>{b.value=e}})},(()=>[(0,r.h)(q,{icon:s.iconSet.colorPicker.spectrum,name:"spectrum",ripple:!1}),(0,r.h)(q,{icon:s.iconSet.colorPicker.tune,name:"tune",ripple:!1}),(0,r.h)(q,{icon:s.iconSet.colorPicker.palette,name:"palette",ripple:!1})]))])}function se(){const e={ref:f,class:"q-color-picker__spectrum non-selectable relative-position cursor-pointer"+(!0!==w.value?" readonly":""),style:A.value,...!0===w.value?{onClick:ne,onMousedown:re}:{}},t=[(0,r.h)("div",{style:{paddingBottom:"100%"}}),(0,r.h)("div",{class:"q-color-picker__spectrum-white absolute-full"}),(0,r.h)("div",{class:"q-color-picker__spectrum-black absolute-full"}),(0,r.h)("div",{class:"absolute",style:L.value},[void 0!==y.value.hex?(0,r.h)("div",{class:"q-color-picker__spectrum-circle"}):null])],n=[(0,r.h)(a.Z,{class:"q-color-picker__hue non-selectable",modelValue:y.value.h,min:0,max:360,trackSize:"8px",innerTrackColor:"transparent",selectionColor:"transparent",readonly:!0!==w.value,thumbPath:X,"onUpdate:modelValue":V,...c("lazyhue",{onChange:e=>V(e,!0)})})];return!0===F.value&&n.push((0,r.h)(a.Z,{class:"q-color-picker__alpha non-selectable",modelValue:y.value.a,min:0,max:100,trackSize:"8px",trackColor:"white",innerTrackColor:"transparent",selectionColor:"transparent",trackImg:G,readonly:!0!==w.value,hideSelection:!0,thumbPath:X,...c("alphaSlide",{"onUpdate:modelValue":e=>U(e,"a",100),onChange:e=>U(e,"a",100,void 0,!0)})})),[(0,m.Jl)("div",e,t,"spec",w.value,(()=>B.value)),(0,r.h)("div",{class:"q-color-picker__sliders"},n)]}function ue(){return[(0,r.h)("div",{class:"row items-center no-wrap"},[(0,r.h)("div","R"),(0,r.h)(a.Z,{modelValue:y.value.r,min:0,max:255,color:"red",dark:u.value,readonly:!0!==w.value,...c("rSlide",{"onUpdate:modelValue":e=>U(e,"r",255),onChange:e=>U(e,"r",255,void 0,!0)})}),(0,r.h)("input",{value:y.value.r,maxlength:3,readonly:!0!==w.value,onChange:d.sT,...c("rIn",{onInput:e=>U(e.target.value,"r",255,e),onBlur:e=>U(e.target.value,"r",255,e,!0)})})]),(0,r.h)("div",{class:"row items-center no-wrap"},[(0,r.h)("div","G"),(0,r.h)(a.Z,{modelValue:y.value.g,min:0,max:255,color:"green",dark:u.value,readonly:!0!==w.value,...c("gSlide",{"onUpdate:modelValue":e=>U(e,"g",255),onChange:e=>U(e,"g",255,void 0,!0)})}),(0,r.h)("input",{value:y.value.g,maxlength:3,readonly:!0!==w.value,onChange:d.sT,...c("gIn",{onInput:e=>U(e.target.value,"g",255,e),onBlur:e=>U(e.target.value,"g",255,e,!0)})})]),(0,r.h)("div",{class:"row items-center no-wrap"},[(0,r.h)("div","B"),(0,r.h)(a.Z,{modelValue:y.value.b,min:0,max:255,color:"blue",readonly:!0!==w.value,dark:u.value,...c("bSlide",{"onUpdate:modelValue":e=>U(e,"b",255),onChange:e=>U(e,"b",255,void 0,!0)})}),(0,r.h)("input",{value:y.value.b,maxlength:3,readonly:!0!==w.value,onChange:d.sT,...c("bIn",{onInput:e=>U(e.target.value,"b",255,e),onBlur:e=>U(e.target.value,"b",255,e,!0)})})]),!0===F.value?(0,r.h)("div",{class:"row items-center no-wrap"},[(0,r.h)("div","A"),(0,r.h)(a.Z,{modelValue:y.value.a,color:"grey",readonly:!0!==w.value,dark:u.value,...c("aSlide",{"onUpdate:modelValue":e=>U(e,"a",100),onChange:e=>U(e,"a",100,void 0,!0)})}),(0,r.h)("input",{value:y.value.a,maxlength:3,readonly:!0!==w.value,onChange:d.sT,...c("aIn",{onInput:e=>U(e.target.value,"a",100,e),onBlur:e=>U(e.target.value,"a",100,e,!0)})})]):null]}function ce(){const e=e=>(0,r.h)("div",{class:"q-color-picker__cube col-auto",style:{backgroundColor:e},...!0===w.value?c("palette#"+e,{onClick:()=>{Q(e)}}):{}});return[(0,r.h)("div",{class:"row items-center q-color-picker__palette-rows"+(!0===w.value?" q-color-picker__palette-rows--editable":"")},P.value.map(e))]}return()=>{const t=[ae()];return void 0!==e.name&&!0!==e.disable&&C(t,"push"),!0!==e.noHeader&&t.unshift(ie()),!0!==e.noFooter&&t.push(le()),(0,r.h)("div",{class:O.value,...M.value},t)}}})},4554:(e,t,n)=>{"use strict";n.d(t,{Z:()=>y});n(71);var r=n(3673),o=n(2417),i=n(908),a=n(7657);const l="0 0 24 24",s=e=>e,u=e=>`ionicons ${e}`,c={"icon-":s,"bt-":e=>`bt ${e}`,"eva-":e=>`eva ${e}`,"ion-md":u,"ion-ios":u,"ion-logo":u,"mdi-":e=>`mdi ${e}`,"iconfont ":s,"ti-":e=>`themify-icon ${e}`,"bi-":e=>`bootstrap-icons ${e}`},d={o_:"-outlined",r_:"-round",s_:"-sharp"},f=new RegExp("^("+Object.keys(c).join("|")+")"),p=new RegExp("^("+Object.keys(d).join("|")+")"),v=/^[Mm]\s?[-+]?\.?\d/,h=/^img:/,g=/^svguse:/,m=/^ion-/,b=/^[lf]a[srlbdk]? /,y=(0,i.L)({name:"QIcon",props:{...o.LU,tag:{type:String,default:"i"},name:String,color:String,left:Boolean,right:Boolean},setup(e,{slots:t}){const{proxy:{$q:n}}=(0,r.FN)(),i=(0,o.ZP)(e),s=(0,r.Fl)((()=>"q-icon"+(!0===e.left?" on-left":"")+(!0===e.right?" on-right":"")+(void 0!==e.color?` text-${e.color}`:""))),u=(0,r.Fl)((()=>{let t,o=e.name;if(!o)return{none:!0};if(null!==n.iconMapFn){const e=n.iconMapFn(o);if(void 0!==e){if(void 0===e.icon)return{cls:e.cls,content:void 0!==e.content?e.content:" "};o=e.icon}}if(!0===v.test(o)){const[e,t=l]=o.split("|");return{svg:!0,viewBox:t,nodes:e.split("&&").map((e=>{const[t,n,o]=e.split("@@");return(0,r.h)("path",{style:n,d:t,transform:o})}))}}if(!0===h.test(o))return{img:!0,src:o.substring(4)};if(!0===g.test(o)){const[e,t=l]=o.split("|");return{svguse:!0,src:e.substring(7),viewBox:t}}let i=" ";const a=o.match(f);if(null!==a)t=c[a[1]](o);else if(!0===b.test(o))t=o;else if(!0===m.test(o))t=`ionicons ion-${!0===n.platform.is.ios?"ios":"md"}${o.substr(3)}`;else{t="notranslate material-icons";const e=o.match(p);null!==e&&(o=o.substring(2),t+=d[e[1]]),i=o}return{cls:t,content:i}}));return()=>{const n={class:s.value,style:i.value,"aria-hidden":"true",role:"presentation"};return!0===u.value.none?(0,r.h)(e.tag,n,(0,a.KR)(t.default)):!0===u.value.img?(0,r.h)("span",n,(0,a.vs)(t.default,[(0,r.h)("img",{src:u.value.src})])):!0===u.value.svg?(0,r.h)("span",n,(0,a.vs)(t.default,[(0,r.h)("svg",{viewBox:u.value.viewBox},u.value.nodes)])):!0===u.value.svguse?(0,r.h)("span",n,(0,a.vs)(t.default,[(0,r.h)("svg",{viewBox:u.value.viewBox},[(0,r.h)("use",{"xlink:href":u.value.src})])])):(void 0!==u.value.cls&&(n.class+=" "+u.value.cls),(0,r.h)(e.tag,n,(0,a.vs)(t.default,[u.value.content])))}}})},4105:(e,t,n)=>{"use strict";n.d(t,{Z:()=>ce});n(71);var r=n(1959),o=n(3673),i=n(8880),a=n(4688),l=n(4554),s=n(9754),u=n(2236),c=n(2547);function d({validate:e,resetValidation:t,requiresQForm:n}){const r=(0,o.f3)(c.vh,!1);if(!1!==r){const{props:n,proxy:i}=(0,o.FN)();Object.assign(i,{validate:e,resetValidation:t}),(0,o.YP)((()=>n.disable),(e=>{!0===e?("function"===typeof t&&t(),r.unbindComponent(i)):r.bindComponent(i)})),!0!==n.disable&&r.bindComponent(i),(0,o.Jd)((()=>{!0!==n.disable&&r.unbindComponent(i)}))}else!0===n&&console.error("Parent QForm not found on useFormChild()!")}var f=n(9763);n(2156),n(2100);Object.prototype.toString,Object.prototype.hasOwnProperty;const p={};"Boolean Number String Function Array Date RegExp Object".split(" ").forEach((e=>{p["[object "+e+"]"]=e.toLowerCase()}));n(6245),n(7965),n(6016),n(4607),n(7070);var v=n(2417);const h={...v.LU,min:{type:Number,default:0},max:{type:Number,default:100},color:String,centerColor:String,trackColor:String,fontSize:String,thickness:{type:Number,default:.2,validator:e=>e>=0&&e<=1},angle:{type:Number,default:0},showValue:Boolean,reverse:Boolean,instantFeedback:Boolean};var g=n(908),m=n(7657),b=n(2130);const y=50,w=2*y,x=w*Math.PI,_=Math.round(1e3*x)/1e3;(0,g.L)({name:"QCircularProgress",props:{...h,value:{type:Number,default:0},animationSpeed:{type:[String,Number],default:600},indeterminate:Boolean},setup(e,{slots:t}){const{proxy:{$q:n}}=(0,o.FN)(),r=(0,v.ZP)(e),i=(0,o.Fl)((()=>{const t=(!0===n.lang.rtl?-1:1)*e.angle;return{transform:e.reverse!==(!0===n.lang.rtl)?`scale3d(-1, 1, 1) rotate3d(0, 0, 1, ${-90-t}deg)`:`rotate3d(0, 0, 1, ${t-90}deg)`}})),a=(0,o.Fl)((()=>!0!==e.instantFeedback&&!0!==e.indeterminate?{transition:`stroke-dashoffset ${e.animationSpeed}ms ease 0s, stroke ${e.animationSpeed}ms ease`}:"")),l=(0,o.Fl)((()=>w/(1-e.thickness/2))),s=(0,o.Fl)((()=>`${l.value/2} ${l.value/2} ${l.value} ${l.value}`)),u=(0,o.Fl)((()=>(0,b.vX)(e.value,e.min,e.max))),c=(0,o.Fl)((()=>x*(1-(u.value-e.min)/(e.max-e.min)))),d=(0,o.Fl)((()=>e.thickness/2*l.value));function f({thickness:e,offset:t,color:n,cls:r}){return(0,o.h)("circle",{class:"q-circular-progress__"+r+(void 0!==n?` text-${n}`:""),style:a.value,fill:"transparent",stroke:"currentColor","stroke-width":e,"stroke-dasharray":_,"stroke-dashoffset":t,cx:l.value,cy:l.value,r:y})}return()=>{const n=[];void 0!==e.centerColor&&"transparent"!==e.centerColor&&n.push((0,o.h)("circle",{class:`q-circular-progress__center text-${e.centerColor}`,fill:"currentColor",r:y-d.value/2,cx:l.value,cy:l.value})),void 0!==e.trackColor&&"transparent"!==e.trackColor&&n.push(f({cls:"track",thickness:d.value,offset:0,color:e.trackColor})),n.push(f({cls:"circle",thickness:d.value,offset:c.value,color:e.color}));const a=[(0,o.h)("svg",{class:"q-circular-progress__svg",style:i.value,viewBox:s.value,"aria-hidden":"true"},n)];return!0===e.showValue&&a.push((0,o.h)("div",{class:"q-circular-progress__text absolute-full row flex-center content-center",style:{fontSize:e.fontSize}},void 0!==t.default?t.default():[(0,o.h)("div",u.value)])),(0,o.h)("div",{class:`q-circular-progress q-circular-progress--${!0===e.indeterminate?"in":""}determinate`,style:r.value,role:"progressbar","aria-valuemin":e.min,"aria-valuemax":e.max,"aria-valuenow":!0===e.indeterminate?void 0:u.value},(0,m.pf)(t.internal,a))}}});n(6801);var k=n(4716);const S={multiple:Boolean,accept:String,capture:String,maxFileSize:[Number,String],maxTotalSize:[Number,String],maxFiles:[Number,String],filter:Function},C=["rejected"];u.S,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean;const F=[...C,"start","finish","added","removed"];const E=()=>!0;function R(e){const t={};return e.forEach((e=>{t[e]=E})),t}R(F);n(5363),n(6701),n(782);n(1845);var A=n(9405);n(2012);n(8400),n(4398),n(979),n(6105),n(5123),n(8685),n(2396);let q,T=0;const L=new Array(256);for(let de=0;de<256;de++)L[de]=(de+256).toString(16).substr(1);const P=(()=>{const e="undefined"!==typeof crypto?crypto:"undefined"!==typeof window?window.crypto||window.msCrypto:void 0;if(void 0!==e){if(void 0!==e.randomBytes)return e.randomBytes;if(void 0!==e.getRandomValues)return t=>{const n=new Uint8Array(t);return e.getRandomValues(n),n}}return e=>{const t=[];for(let n=e;n>0;n--)t.push(Math.floor(256*Math.random()));return t}})(),O=4096;function M(){(void 0===q||T+16>O)&&(T=0,q=P(O));const e=Array.prototype.slice.call(q,T,T+=16);return e[6]=15&e[6]|64,e[8]=63&e[8]|128,L[e[0]]+L[e[1]]+L[e[2]]+L[e[3]]+"-"+L[e[4]]+L[e[5]]+"-"+L[e[6]]+L[e[7]]+"-"+L[e[8]]+L[e[9]]+"-"+L[e[10]]+L[e[11]]+L[e[12]]+L[e[13]]+L[e[14]]+L[e[15]]}var B=n(9085);const $=[!0,!1,"ondemand"],I={modelValue:{},error:{type:Boolean,default:null},errorMessage:String,noErrorIcon:Boolean,rules:Array,reactiveRules:Boolean,lazyRules:{type:[Boolean,String],validator:e=>$.includes(e)}};function j(e,t){const{props:n,proxy:i}=(0,o.FN)(),a=(0,r.iH)(!1),l=(0,r.iH)(null),s=(0,r.iH)(null);d({validate:m,resetValidation:g});let u,c=0;const p=(0,o.Fl)((()=>!0!==n.disable&&void 0!==n.rules&&null!==n.rules&&n.rules.length>0)),v=(0,o.Fl)((()=>!0===n.error||!0===a.value)),h=(0,o.Fl)((()=>"string"===typeof n.errorMessage&&n.errorMessage.length>0?n.errorMessage:l.value));function g(){c++,t.value=!1,s.value=null,a.value=!1,l.value=null,y.cancel()}function m(e=n.modelValue){if(!0!==p.value)return!0;c++,!0!==t.value&&!0!==n.lazyRules&&(s.value=!0);const r=(e,n)=>{a.value!==e&&(a.value=e);const r=n||void 0;l.value!==r&&(l.value=r),!1!==t.value&&(t.value=!1)},o=[];for(let t=0;t<n.rules.length;t++){const i=n.rules[t];let a;if("function"===typeof i?a=i(e):"string"===typeof i&&void 0!==f.E[i]&&(a=f.E[i](e)),!1===a||"string"===typeof a)return r(!0,a),!1;!0!==a&&void 0!==a&&o.push(a)}if(0===o.length)return r(!1),!0;!0!==t.value&&(t.value=!0);const i=c;return Promise.all(o).then((e=>{if(i!==c)return!0;if(void 0===e||!1===Array.isArray(e)||0===e.length)return r(!1),!0;const t=e.find((e=>!1===e||"string"===typeof e));return r(void 0!==t,t),void 0===t}),(e=>i!==c||(console.error(e),r(!0),!1)))}function b(e){!0===p.value&&"ondemand"!==n.lazyRules&&(!0===s.value||!0!==n.lazyRules&&!0!==e)&&y()}(0,o.YP)((()=>n.modelValue),(()=>{b()})),(0,o.YP)((()=>n.reactiveRules),(e=>{!0===e?void 0===u&&(u=(0,o.YP)((()=>n.rules),(()=>{b(!0)}))):void 0!==u&&(u(),u=void 0)}),{immediate:!0}),(0,o.YP)(e,(e=>{!0===e?null===s.value&&(s.value=!1):!1===s.value&&(s.value=!0,!0===p.value&&"ondemand"!==n.lazyRules&&y())}));const y=(0,A.Z)(m,0);return(0,o.Jd)((()=>{void 0!==u&&u(),y.cancel()})),Object.assign(i,{resetValidation:g,validate:m}),(0,B.g)(i,"hasError",(()=>v.value)),{isDirtyModel:s,hasRules:p,hasError:v,errorMessage:h,validate:m,resetValidation:g}}const N=/^on[A-Z]/;function V(e,t){const n={listeners:(0,r.iH)({}),attributes:(0,r.iH)({})};function i(){const r={},o={};for(const t in e)"class"!==t&&"style"!==t&&!1===N.test(t)&&(r[t]=e[t]);for(const e in t.props)!0===N.test(e)&&(o[e]=t.props[e]);n.attributes.value=r,n.listeners.value=o}return(0,o.Xn)(i),i(),n}var U=n(230);function H(e){return void 0===e?`f_${M()}`:e}function z(e){return void 0!==e&&null!==e&&(""+e).length>0}const D={...u.S,...I,label:String,stackLabel:Boolean,hint:String,hideHint:Boolean,prefix:String,suffix:String,labelColor:String,color:String,bgColor:String,filled:Boolean,outlined:Boolean,borderless:Boolean,standout:[Boolean,String],square:Boolean,loading:Boolean,labelSlot:Boolean,bottomSlots:Boolean,hideBottomSpace:Boolean,rounded:Boolean,dense:Boolean,itemAligned:Boolean,counter:Boolean,clearable:Boolean,clearIcon:String,disable:Boolean,readonly:Boolean,autofocus:Boolean,for:String,maxlength:[Number,String]},Z=["update:modelValue","clear","focus","blur","popup-show","popup-hide"];function W(){const{props:e,attrs:t,proxy:n,vnode:i}=(0,o.FN)(),a=(0,u.Z)(e,n.$q);return{isDark:a,editable:(0,o.Fl)((()=>!0!==e.disable&&!0!==e.readonly)),innerLoading:(0,r.iH)(!1),focused:(0,r.iH)(!1),hasPopupOpen:!1,splitAttrs:V(t,i),targetUid:(0,r.iH)(H(e.for)),rootRef:(0,r.iH)(null),targetRef:(0,r.iH)(null),controlRef:(0,r.iH)(null)}}function Y(e){const{props:t,emit:n,slots:r,attrs:u,proxy:c}=(0,o.FN)(),{$q:d}=c;let f;void 0===e.hasValue&&(e.hasValue=(0,o.Fl)((()=>z(t.modelValue)))),void 0===e.emitValue&&(e.emitValue=e=>{n("update:modelValue",e)}),void 0===e.controlEvents&&(e.controlEvents={onFocusin:L,onFocusout:P}),Object.assign(e,{clearValue:O,onControlFocusin:L,onControlFocusout:P,focus:q}),void 0===e.computedCounter&&(e.computedCounter=(0,o.Fl)((()=>{if(!1!==t.counter){const e="string"===typeof t.modelValue||"number"===typeof t.modelValue?(""+t.modelValue).length:!0===Array.isArray(t.modelValue)?t.modelValue.length:0,n=void 0!==t.maxlength?t.maxlength:t.maxValues;return e+(void 0!==n?" / "+n:"")}})));const{isDirtyModel:p,hasRules:v,hasError:h,errorMessage:g,resetValidation:b}=j(e.focused,e.innerLoading),y=void 0!==e.floatingLabel?(0,o.Fl)((()=>!0===t.stackLabel||!0===e.focused.value||!0===e.floatingLabel.value)):(0,o.Fl)((()=>!0===t.stackLabel||!0===e.focused.value||!0===e.hasValue.value)),w=(0,o.Fl)((()=>!0===t.bottomSlots||void 0!==t.hint||!0===v.value||!0===t.counter||null!==t.error)),x=(0,o.Fl)((()=>!0===t.filled?"filled":!0===t.outlined?"outlined":!0===t.borderless?"borderless":t.standout?"standout":"standard")),_=(0,o.Fl)((()=>`q-field row no-wrap items-start q-field--${x.value}`+(void 0!==e.fieldClass?` ${e.fieldClass.value}`:"")+(!0===t.rounded?" q-field--rounded":"")+(!0===t.square?" q-field--square":"")+(!0===y.value?" q-field--float":"")+(!0===C.value?" q-field--labeled":"")+(!0===t.dense?" q-field--dense":"")+(!0===t.itemAligned?" q-field--item-aligned q-item-type":"")+(!0===e.isDark.value?" q-field--dark":"")+(void 0===e.getControl?" q-field--auto-height":"")+(!0===e.focused.value?" q-field--focused":"")+(!0===h.value?" q-field--error":"")+(!0===h.value||!0===e.focused.value?" q-field--highlighted":"")+(!0!==t.hideBottomSpace&&!0===w.value?" q-field--with-bottom":"")+(!0===t.disable?" q-field--disabled":!0===t.readonly?" q-field--readonly":""))),S=(0,o.Fl)((()=>"q-field__control relative-position row no-wrap"+(void 0!==t.bgColor?` bg-${t.bgColor}`:"")+(!0===h.value?" text-negative":"string"===typeof t.standout&&t.standout.length>0&&!0===e.focused.value?` ${t.standout}`:void 0!==t.color?` text-${t.color}`:""))),C=(0,o.Fl)((()=>!0===t.labelSlot||void 0!==t.label)),F=(0,o.Fl)((()=>"q-field__label no-pointer-events absolute ellipsis"+(void 0!==t.labelColor&&!0!==h.value?` text-${t.labelColor}`:""))),E=(0,o.Fl)((()=>({id:e.targetUid.value,editable:e.editable.value,focused:e.focused.value,floatingLabel:y.value,modelValue:t.modelValue,emitValue:e.emitValue}))),R=(0,o.Fl)((()=>{const n={for:e.targetUid.value};return!0===t.disable?n["aria-disabled"]="true":!0===t.readonly&&(n["aria-readonly"]="true"),n}));function A(){const t=document.activeElement;let n=void 0!==e.targetRef&&e.targetRef.value;!n||null!==t&&t.id===e.targetUid.value||(!0===n.hasAttribute("tabindex")||(n=n.querySelector("[tabindex]")),n&&n!==t&&n.focus())}function q(){(0,U.jd)(A)}function T(){(0,U.fP)(A);const t=document.activeElement;null!==t&&e.rootRef.value.contains(t)&&t.blur()}function L(t){!0===e.editable.value&&!1===e.focused.value&&(e.focused.value=!0,n("focus",t))}function P(t,r){clearTimeout(f),f=setTimeout((()=>{(!0!==document.hasFocus()||!0!==e.hasPopupOpen&&(void 0===e.controlRef||null!==e.controlRef.value&&!1===e.controlRef.value.contains(document.activeElement)))&&(!0===e.focused.value&&(e.focused.value=!1,n("blur",t)),void 0!==r&&r())}))}function O(r){if((0,k.NS)(r),!0!==d.platform.is.mobile){const t=void 0!==e.targetRef&&e.targetRef.value||e.rootRef.value;t.focus()}else!0===e.rootRef.value.contains(document.activeElement)&&document.activeElement.blur();"file"===t.type&&(e.inputRef.value.value=null),n("update:modelValue",null),n("clear",t.modelValue),(0,o.Y3)((()=>{b(),!0!==d.platform.is.mobile&&(p.value=!1)}))}function M(){const n=[];return void 0!==r.prepend&&n.push((0,o.h)("div",{class:"q-field__prepend q-field__marginal row no-wrap items-center",key:"prepend",onClick:k.X$},r.prepend())),n.push((0,o.h)("div",{class:"q-field__control-container col relative-position row no-wrap q-anchor--skip"},B())),void 0!==r.append&&n.push((0,o.h)("div",{class:"q-field__append q-field__marginal row no-wrap items-center",key:"append",onClick:k.X$},r.append())),!0===h.value&&!1===t.noErrorIcon&&n.push(I("error",[(0,o.h)(l.Z,{name:d.iconSet.field.error,color:"negative"})])),!0===t.loading||!0===e.innerLoading.value?n.push(I("inner-loading-append",void 0!==r.loading?r.loading():[(0,o.h)(s.Z,{color:t.color})])):!0===t.clearable&&!0===e.hasValue.value&&!0===e.editable.value&&n.push(I("inner-clearable-append",[(0,o.h)(l.Z,{class:"q-field__focusable-action",tag:"button",name:t.clearIcon||d.iconSet.field.clear,tabindex:0,type:"button","aria-hidden":null,role:null,onClick:O})])),void 0!==e.getInnerAppend&&n.push(I("inner-append",e.getInnerAppend())),void 0!==e.getControlChild&&n.push(e.getControlChild()),n}function B(){const n=[];return void 0!==t.prefix&&null!==t.prefix&&n.push((0,o.h)("div",{class:"q-field__prefix no-pointer-events row items-center"},t.prefix)),void 0!==e.getShadowControl&&!0===e.hasShadow.value&&n.push(e.getShadowControl()),void 0!==e.getControl?n.push(e.getControl()):void 0!==r.rawControl?n.push(r.rawControl()):void 0!==r.control&&n.push((0,o.h)("div",{ref:e.targetRef,class:"q-field__native row",...e.splitAttrs.attributes.value,"data-autofocus":!0===t.autofocus||void 0},r.control(E.value))),!0===C.value&&n.push((0,o.h)("div",{class:F.value},(0,m.KR)(r.label,t.label))),void 0!==t.suffix&&null!==t.suffix&&n.push((0,o.h)("div",{class:"q-field__suffix no-pointer-events row items-center"},t.suffix)),n.concat((0,m.KR)(r.default))}function $(){let n,a;!0===h.value?null!==g.value?(n=[(0,o.h)("div",{role:"alert"},g.value)],a=`q--slot-error-${g.value}`):(n=(0,m.KR)(r.error),a="q--slot-error"):!0===t.hideHint&&!0!==e.focused.value||(void 0!==t.hint?(n=[(0,o.h)("div",t.hint)],a=`q--slot-hint-${t.hint}`):(n=(0,m.KR)(r.hint),a="q--slot-hint"));const l=!0===t.counter||void 0!==r.counter;if(!0===t.hideBottomSpace&&!1===l&&void 0===n)return;const s=(0,o.h)("div",{key:a,class:"q-field__messages col"},n);return(0,o.h)("div",{class:"q-field__bottom row items-start q-field__bottom--"+(!0!==t.hideBottomSpace?"animated":"stale")},[!0===t.hideBottomSpace?s:(0,o.h)(i.uT,{name:"q-transition--field-message"},(()=>s)),!0===l?(0,o.h)("div",{class:"q-field__counter"},void 0!==r.counter?r.counter():e.computedCounter.value):null])}function I(e,t){return null===t?null:(0,o.h)("div",{key:e,class:"q-field__append q-field__marginal row no-wrap items-center q-anchor--skip"},t)}return(0,o.YP)((()=>t.for),(t=>{e.targetUid.value=H(t)})),Object.assign(c,{focus:q,blur:T}),(0,o.bv)((()=>{!0===a.uX.value&&void 0===t.for&&(e.targetUid.value=H()),!0===t.autofocus&&c.focus()})),(0,o.Jd)((()=>{clearTimeout(f)})),function(){return(0,o.h)("label",{ref:e.rootRef,class:[_.value,u.class],style:u.style,...R.value},[void 0!==r.before?(0,o.h)("div",{class:"q-field__before q-field__marginal row no-wrap items-center",onClick:k.X$},r.before()):null,(0,o.h)("div",{class:"q-field__inner relative-position col self-stretch"},[(0,o.h)("div",{ref:e.controlRef,class:S.value,tabindex:-1,...e.controlEvents},M()),!0===w.value?$():null]),void 0!==r.after?(0,o.h)("div",{class:"q-field__after q-field__marginal row no-wrap items-center",onClick:k.X$},r.after()):null])}}var J=n(1436);const K={date:"####/##/##",datetime:"####/##/## ##:##",time:"##:##",fulltime:"##:##:##",phone:"(###) ### - ####",card:"#### #### #### ####"},X={"#":{pattern:"[\\d]",negate:"[^\\d]"},S:{pattern:"[a-zA-Z]",negate:"[^a-zA-Z]"},N:{pattern:"[0-9a-zA-Z]",negate:"[^0-9a-zA-Z]"},A:{pattern:"[a-zA-Z]",negate:"[^a-zA-Z]",transform:e=>e.toLocaleUpperCase()},a:{pattern:"[a-zA-Z]",negate:"[^a-zA-Z]",transform:e=>e.toLocaleLowerCase()},X:{pattern:"[0-9a-zA-Z]",negate:"[^0-9a-zA-Z]",transform:e=>e.toLocaleUpperCase()},x:{pattern:"[0-9a-zA-Z]",negate:"[^0-9a-zA-Z]",transform:e=>e.toLocaleLowerCase()}},G=Object.keys(X);G.forEach((e=>{X[e].regex=new RegExp(X[e].pattern)}));const Q=new RegExp("\\\\([^.*+?^${}()|([\\]])|([.*+?^${}()|[\\]])|(["+G.join("")+"])|(.)","g"),ee=/[.*+?^${}()|[\]\\]/g,te=String.fromCharCode(1),ne={mask:String,reverseFillMask:Boolean,fillMask:[Boolean,String],unmaskedValue:Boolean};function re(e,t,n,i){let a,l,s,u;const c=(0,r.iH)(null),d=(0,r.iH)(p());function f(){return!0===e.autogrow||["textarea","text","search","url","tel","password"].includes(e.type)}function p(){if(h(),!0===c.value){const t=w(_(e.modelValue));return!1!==e.fillMask?k(t):t}return e.modelValue}function v(e){if(e<a.length)return a.slice(-e);let t="",n=a;const r=n.indexOf(te);if(r>-1){for(let r=e-n.length;r>0;r--)t+=te;n=n.slice(0,r)+t+n.slice(r)}return n}function h(){if(c.value=void 0!==e.mask&&e.mask.length>0&&f(),!1===c.value)return u=void 0,a="",void(l="");const t=void 0===K[e.mask]?e.mask:K[e.mask],n="string"===typeof e.fillMask&&e.fillMask.length>0?e.fillMask.slice(0,1):"_",r=n.replace(ee,"\\$&"),o=[],i=[],d=[];let p=!0===e.reverseFillMask,v="",h="";t.replace(Q,((e,t,n,r,a)=>{if(void 0!==r){const e=X[r];d.push(e),h=e.negate,!0===p&&(i.push("(?:"+h+"+)?("+e.pattern+"+)?(?:"+h+"+)?("+e.pattern+"+)?"),p=!1),i.push("(?:"+h+"+)?("+e.pattern+")?")}else if(void 0!==n)v="\\"+("\\"===n?"":n),d.push(n),o.push("([^"+v+"]+)?"+v+"?");else{const e=void 0!==t?t:a;v="\\"===e?"\\\\\\\\":e.replace(ee,"\\\\$&"),d.push(e),o.push("([^"+v+"]+)?"+v+"?")}}));const g=new RegExp("^"+o.join("")+"("+(""===v?".":"[^"+v+"]")+"+)?$"),m=i.length-1,b=i.map(((t,n)=>0===n&&!0===e.reverseFillMask?new RegExp("^"+r+"*"+t):n===m?new RegExp("^"+t+"("+(""===h?".":h)+"+)?"+(!0===e.reverseFillMask?"$":r+"*")):new RegExp("^"+t)));s=d,u=e=>{const t=g.exec(e);null!==t&&(e=t.slice(1).join(""));const n=[],r=b.length;for(let o=0,i=e;o<r;o++){const e=b[o].exec(i);if(null===e)break;i=i.slice(e.shift().length),n.push(...e)}return n.length>0?n.join(""):e},a=d.map((e=>"string"===typeof e?e:te)).join(""),l=a.split(te).join(n)}function g(t,r,s){const u=i.value,c=u.selectionEnd,f=u.value.length-c,p=_(t);!0===r&&h();const v=w(p),g=!1!==e.fillMask?k(v):v,m=d.value!==g;u.value!==g&&(u.value=g),!0===m&&(d.value=g),document.activeElement===u&&(0,o.Y3)((()=>{if(g!==l)if("insertFromPaste"!==s||!0===e.reverseFillMask)if(["deleteContentBackward","deleteContentForward"].indexOf(s)>-1){const t=!0===e.reverseFillMask?0===c?g.length>v.length?1:0:Math.max(0,g.length-(g===l?0:Math.min(v.length,f)+1))+1:c;u.setSelectionRange(t,t,"forward")}else if(!0===e.reverseFillMask)if(!0===m){const e=Math.max(0,g.length-(g===l?0:Math.min(v.length,f+1)));1===e&&1===c?u.setSelectionRange(e,e,"forward"):b.rightReverse(u,e,e)}else{const e=g.length-f;u.setSelectionRange(e,e,"backward")}else if(!0===m){const e=Math.max(0,a.indexOf(te),Math.min(v.length,c)-1);b.right(u,e,e)}else{const e=c-1;b.right(u,e,e)}else{const e=c-1;b.right(u,e,e)}else{const t=!0===e.reverseFillMask?l.length:0;u.setSelectionRange(t,t,"forward")}}));const y=!0===e.unmaskedValue?_(g):g;e.modelValue!==y&&n(y,!0)}function m(e,t,n){const r=w(_(e.value));t=Math.max(0,a.indexOf(te),Math.min(r.length,t)),e.setSelectionRange(t,n,"forward")}(0,o.YP)((()=>e.type+e.autogrow),h),(0,o.YP)((()=>e.mask),(n=>{if(void 0!==n)g(d.value,!0);else{const n=_(d.value);h(),e.modelValue!==n&&t("update:modelValue",n)}})),(0,o.YP)((()=>e.fillMask+e.reverseFillMask),(()=>{!0===c.value&&g(d.value,!0)})),(0,o.YP)((()=>e.unmaskedValue),(()=>{!0===c.value&&g(d.value)}));const b={left(e,t,n,r){const o=-1===a.slice(t-1).indexOf(te);let i=Math.max(0,t-1);for(;i>=0;i--)if(a[i]===te){t=i,!0===o&&t++;break}if(i<0&&void 0!==a[t]&&a[t]!==te)return b.right(e,0,0);t>=0&&e.setSelectionRange(t,!0===r?n:t,"backward")},right(e,t,n,r){const o=e.value.length;let i=Math.min(o,n+1);for(;i<=o;i++){if(a[i]===te){n=i;break}a[i-1]===te&&(n=i)}if(i>o&&void 0!==a[n-1]&&a[n-1]!==te)return b.left(e,o,o);e.setSelectionRange(r?t:n,n,"forward")},leftReverse(e,t,n,r){const o=v(e.value.length);let i=Math.max(0,t-1);for(;i>=0;i--){if(o[i-1]===te){t=i;break}if(o[i]===te&&(t=i,0===i))break}if(i<0&&void 0!==o[t]&&o[t]!==te)return b.rightReverse(e,0,0);t>=0&&e.setSelectionRange(t,!0===r?n:t,"backward")},rightReverse(e,t,n,r){const o=e.value.length,i=v(o),a=-1===i.slice(0,n+1).indexOf(te);let l=Math.min(o,n+1);for(;l<=o;l++)if(i[l-1]===te){n=l,n>0&&!0===a&&n--;break}if(l>o&&void 0!==i[n-1]&&i[n-1]!==te)return b.leftReverse(e,o,o);e.setSelectionRange(!0===r?t:n,n,"forward")}};function y(t){if(!0===(0,J.Wm)(t))return;const n=i.value,r=n.selectionStart,o=n.selectionEnd;if(37===t.keyCode||39===t.keyCode){const i=b[(39===t.keyCode?"right":"left")+(!0===e.reverseFillMask?"Reverse":"")];t.preventDefault(),i(n,r,o,t.shiftKey)}else 8===t.keyCode&&!0!==e.reverseFillMask&&r===o?b.left(n,r,o,!0):46===t.keyCode&&!0===e.reverseFillMask&&r===o&&b.rightReverse(n,r,o,!0)}function w(t){if(void 0===t||null===t||""===t)return"";if(!0===e.reverseFillMask)return x(t);const n=s;let r=0,o="";for(let e=0;e<n.length;e++){const i=t[r],a=n[e];if("string"===typeof a)o+=a,i===a&&r++;else{if(void 0===i||!a.regex.test(i))return o;o+=void 0!==a.transform?a.transform(i):i,r++}}return o}function x(e){const t=s,n=a.indexOf(te);let r=e.length-1,o="";for(let i=t.length-1;i>=0&&r>-1;i--){const a=t[i];let l=e[r];if("string"===typeof a)o=a+o,l===a&&r--;else{if(void 0===l||!a.regex.test(l))return o;do{o=(void 0!==a.transform?a.transform(l):l)+o,r--,l=e[r]}while(n===i&&void 0!==l&&a.regex.test(l))}}return o}function _(e){return"string"!==typeof e||void 0===u?"number"===typeof e?u(""+e):e:u(e)}function k(t){return l.length-t.length<=0?t:!0===e.reverseFillMask&&t.length>0?l.slice(0,-t.length)+t:t+l.slice(t.length)}return{innerValue:d,hasMask:c,moveCursorForPaste:m,updateMaskValue:g,onMaskedKeydown:y}}var oe=n(9550);function ie(e,t){function n(){const t=e.modelValue;try{const e="DataTransfer"in window?new DataTransfer:"ClipboardEvent"in window?new ClipboardEvent("").clipboardData:void 0;return Object(t)===t&&("length"in t?Array.from(t):[t]).forEach((t=>{e.items.add(t)})),{files:e.files}}catch(n){return{files:void 0}}}return!0===t?(0,o.Fl)((()=>{if("file"===e.type)return n()})):(0,o.Fl)(n)}const ae=/[\u3000-\u303f\u3040-\u309f\u30a0-\u30ff\uff00-\uff9f\u4e00-\u9faf\u3400-\u4dbf]/,le=/[\u4e00-\u9fff\u3400-\u4dbf\u{20000}-\u{2a6df}\u{2a700}-\u{2b73f}\u{2b740}-\u{2b81f}\u{2b820}-\u{2ceaf}\uf900-\ufaff\u3300-\u33ff\ufe30-\ufe4f\uf900-\ufaff\u{2f800}-\u{2fa1f}]/u,se=/[\u3131-\u314e\u314f-\u3163\uac00-\ud7a3]/;function ue(e){return function(t){if("compositionend"===t.type||"change"===t.type){if(!0!==t.target.composing)return;t.target.composing=!1,e(t)}else"compositionupdate"===t.type?"string"===typeof t.data&&!1===ae.test(t.data)&&!1===le.test(t.data)&&!1===se.test(t.data)&&(t.target.composing=!1):t.target.composing=!0}}const ce=(0,g.L)({name:"QInput",inheritAttrs:!1,props:{...D,...ne,...oe.Fz,modelValue:{required:!1},shadowText:String,type:{type:String,default:"text"},debounce:[String,Number],autogrow:Boolean,inputClass:[Array,String,Object],inputStyle:[Array,String,Object]},emits:[...Z,"paste","change"],setup(e,{emit:t,attrs:n}){const i={};let a,l,s,u,c=NaN;const d=(0,r.iH)(null),f=(0,oe.Do)(e),{innerValue:p,hasMask:v,moveCursorForPaste:h,updateMaskValue:g,onMaskedKeydown:m}=re(e,t,T,d),b=ie(e,!0),y=(0,o.Fl)((()=>z(p.value))),w=ue(q),x=W(),_=(0,o.Fl)((()=>"textarea"===e.type||!0===e.autogrow)),S=(0,o.Fl)((()=>!0===_.value||["text","search","url","tel","password"].includes(e.type))),C=(0,o.Fl)((()=>{const t={...x.splitAttrs.listeners.value,onInput:q,onPaste:A,onChange:P,onBlur:O,onFocus:k.sT};return t.onCompositionstart=t.onCompositionupdate=t.onCompositionend=w,!0===v.value&&(t.onKeydown=m),!0===e.autogrow&&(t.onAnimationend=L),t})),F=(0,o.Fl)((()=>{const t={tabindex:0,"data-autofocus":!0===e.autofocus||void 0,rows:"textarea"===e.type?6:void 0,"aria-label":e.label,name:f.value,...x.splitAttrs.attributes.value,id:x.targetUid.value,maxlength:e.maxlength,disabled:!0===e.disable,readonly:!0===e.readonly};return!1===_.value&&(t.type=e.type),!0===e.autogrow&&(t.rows=1),t}));function E(){(0,U.jd)((()=>{const e=document.activeElement;null===d.value||d.value===e||null!==e&&e.id===x.targetUid.value||d.value.focus()}))}function R(){null!==d.value&&d.value.select()}function A(n){if(!0===v.value&&!0!==e.reverseFillMask){const e=n.target;h(e,e.selectionStart,e.selectionEnd)}t("paste",n)}function q(n){if(!n||!n.target||!0===n.target.composing)return;if("file"===e.type)return void t("update:modelValue",n.target.files);const r=n.target.value;if(!0===v.value)g(r,!1,n.inputType);else if(T(r),!0===S.value&&n.target===document.activeElement){const{selectionStart:e,selectionEnd:t}=n.target;void 0!==e&&void 0!==t&&(0,o.Y3)((()=>{n.target===document.activeElement&&0===r.indexOf(n.target.value)&&n.target.setSelectionRange(e,t)}))}!0===e.autogrow&&L()}function T(n,r){u=()=>{"number"!==e.type&&!0===i.hasOwnProperty("value")&&delete i.value,e.modelValue!==n&&c!==n&&(!0===r&&(l=!0),t("update:modelValue",n),(0,o.Y3)((()=>{c===n&&(c=NaN)}))),u=void 0},"number"===e.type&&(a=!0,i.value=n),void 0!==e.debounce?(clearTimeout(s),i.value=n,s=setTimeout(u,e.debounce)):u()}function L(){const e=d.value;if(null!==e){const t=e.parentNode.style;t.marginBottom=e.scrollHeight-1+"px",e.style.height="1px",e.style.height=e.scrollHeight+"px",t.marginBottom=""}}function P(e){w(e),clearTimeout(s),void 0!==u&&u(),t("change",e.target.value)}function O(t){void 0!==t&&(0,k.sT)(t),clearTimeout(s),void 0!==u&&u(),a=!1,l=!1,delete i.value,"file"!==e.type&&setTimeout((()=>{null!==d.value&&(d.value.value=void 0!==p.value?p.value:"")}))}function M(){return!0===i.hasOwnProperty("value")?i.value:void 0!==p.value?p.value:""}(0,o.YP)((()=>e.modelValue),(t=>{if(!0===v.value){if(!0===l)return void(l=!1);g(t)}else p.value!==t&&(p.value=t,"number"===e.type&&!0===i.hasOwnProperty("value")&&(!0===a?a=!1:delete i.value));!0===e.autogrow&&(0,o.Y3)(L)})),(0,o.YP)((()=>e.autogrow),(e=>{!0===e?(0,o.Y3)(L):null!==d.value&&n.rows>0&&(d.value.style.height="auto")})),(0,o.YP)((()=>e.dense),(()=>{!0===e.autogrow&&(0,o.Y3)(L)})),(0,o.Jd)((()=>{O()})),(0,o.bv)((()=>{!0===e.autogrow&&L()})),Object.assign(x,{innerValue:p,fieldClass:(0,o.Fl)((()=>"q-"+(!0===_.value?"textarea":"input")+(!0===e.autogrow?" q-textarea--autogrow":""))),hasShadow:(0,o.Fl)((()=>"file"!==e.type&&"string"===typeof e.shadowText&&e.shadowText.length>0)),inputRef:d,emitValue:T,hasValue:y,floatingLabel:(0,o.Fl)((()=>!0===y.value||z(e.displayValue))),getControl:()=>(0,o.h)(!0===_.value?"textarea":"input",{ref:d,class:["q-field__native q-placeholder",e.inputClass],style:e.inputStyle,...F.value,...C.value,..."file"!==e.type?{value:M()}:b.value}),getShadowControl:()=>(0,o.h)("div",{class:"q-field__native q-field__shadow absolute-bottom no-pointer-events"+(!0===_.value?"":" text-no-wrap")},[(0,o.h)("span",{class:"invisible"},M()),(0,o.h)("span",e.shadowText)])});const B=Y(x),$=(0,o.FN)();return Object.assign($.proxy,{focus:E,select:R,getNativeElement:()=>d.value}),B}})},3414:(e,t,n)=>{"use strict";n.d(t,{Z:()=>d});var r=n(3673),o=n(1959),i=n(2236),a=n(7277),l=n(908),s=n(7657),u=n(4716),c=n(1436);const d=(0,l.L)({name:"QItem",props:{...i.S,...a.$,tag:{type:String,default:"div"},active:Boolean,clickable:Boolean,dense:Boolean,insetLevel:Number,tabindex:[String,Number],focused:Boolean,manualFocus:Boolean},emits:["click","keyup"],setup(e,{slots:t,emit:n}){const{proxy:{$q:l}}=(0,r.FN)(),d=(0,i.Z)(e,l),{hasRouterLink:f,hasLink:p,linkProps:v,linkClass:h,linkTag:g,navigateToRouterLink:m}=(0,a.Z)(),b=(0,o.iH)(null),y=(0,o.iH)(null),w=(0,r.Fl)((()=>!0===e.clickable||!0===p.value||"label"===e.tag)),x=(0,r.Fl)((()=>!0!==e.disable&&!0===w.value)),_=(0,r.Fl)((()=>"q-item q-item-type row no-wrap"+(!0===e.dense?" q-item--dense":"")+(!0===d.value?" q-item--dark":"")+(!0===p.value?h.value:!0===e.active?(void 0!==e.activeClass?` ${e.activeClass}`:"")+" q-item--active":"")+(!0===e.disable?" disabled":"")+(!0===x.value?" q-item--clickable q-link cursor-pointer "+(!0===e.manualFocus?"q-manual-focusable":"q-focusable q-hoverable")+(!0===e.focused?" q-manual-focusable--focused":""):""))),k=(0,r.Fl)((()=>{if(void 0===e.insetLevel)return null;const t=!0===l.lang.rtl?"Right":"Left";return{["padding"+t]:16+56*e.insetLevel+"px"}}));function S(e){!0===x.value&&(null!==y.value&&(!0!==e.qKeyEvent&&document.activeElement===b.value?y.value.focus():document.activeElement===y.value&&b.value.focus()),!0===f.value&&m(e),n("click",e))}function C(e){if(!0===x.value&&!0===(0,c.So)(e,13)){(0,u.NS)(e),e.qKeyEvent=!0;const t=new MouseEvent("click",e);t.qKeyEvent=!0,b.value.dispatchEvent(t)}n("keyup",e)}function F(){const e=(0,s.Bl)(t.default,[]);return!0===x.value&&e.unshift((0,r.h)("div",{class:"q-focus-helper",tabindex:-1,ref:y})),e}return()=>{const t={ref:b,class:_.value,style:k.value,onClick:S,onKeyup:C};return!0===x.value?(t.tabindex=e.tabindex||"0",Object.assign(t,v.value)):!0===w.value&&(t["aria-disabled"]="true"),(0,r.h)(g.value,t,F())}}})},2350:(e,t,n)=>{"use strict";n.d(t,{Z:()=>a});var r=n(3673),o=n(908),i=n(7657);const a=(0,o.L)({name:"QItemLabel",props:{overline:Boolean,caption:Boolean,header:Boolean,lines:[Number,String]},setup(e,{slots:t}){const n=(0,r.Fl)((()=>parseInt(e.lines,10))),o=(0,r.Fl)((()=>"q-item__label"+(!0===e.overline?" q-item__label--overline text-overline":"")+(!0===e.caption?" q-item__label--caption text-caption":"")+(!0===e.header?" q-item__label--header":"")+(1===n.value?" ellipsis":""))),a=(0,r.Fl)((()=>void 0!==e.lines&&n.value>1?{overflow:"hidden",display:"-webkit-box","-webkit-box-orient":"vertical","-webkit-line-clamp":n.value}:null));return()=>(0,r.h)("div",{style:a.value,class:o.value},(0,i.KR)(t.default))}})},2035:(e,t,n)=>{"use strict";n.d(t,{Z:()=>a});var r=n(3673),o=n(908),i=n(7657);const a=(0,o.L)({name:"QItemSection",props:{avatar:Boolean,thumbnail:Boolean,side:Boolean,top:Boolean,noWrap:Boolean},setup(e,{slots:t}){const n=(0,r.Fl)((()=>"q-item__section column q-item__section--"+(!0===e.avatar||!0===e.side||!0===e.thumbnail?"side":"main")+(!0===e.top?" q-item__section--top justify-start":" justify-center")+(!0===e.avatar?" q-item__section--avatar":"")+(!0===e.thumbnail?" q-item__section--thumbnail":"")+(!0===e.noWrap?" q-item__section--nowrap":"")));return()=>(0,r.h)("div",{class:n.value},(0,i.KR)(t.default))}})},1156:(e,t,n)=>{"use strict";n.d(t,{Z:()=>Le});var r=n(3673),o=n(1959),i=(n(71),n(8880)),a=n(6583);function l(e,t,n){let o;function i(){void 0!==o&&(a.Z.remove(o),o=void 0)}return(0,r.Jd)((()=>{!0===e.value&&i()})),{removeFromHistory:i,addToHistory(){o={condition:()=>!0===n.value,handler:t},a.Z.add(o)}}}var s=n(4955),u=n(416),c=n(7445);const d={modelValue:{type:Boolean,default:null},"onUpdate:modelValue":[Function,Array]},f=["before-show","show","before-hide","hide"];function p({showing:e,canShow:t,hideOnRouteChange:n,handleShow:o,handleHide:i,processOnMount:a}){const l=(0,r.FN)(),{props:s,emit:u,proxy:d}=l;let f;function p(t){!0===e.value?g(t):v(t)}function v(e){if(!0===s.disable||void 0!==e&&!0===e.qAnchorHandled||void 0!==t&&!0!==t(e))return;const n=void 0!==s["onUpdate:modelValue"];!0===n&&(u("update:modelValue",!0),f=e,(0,r.Y3)((()=>{f===e&&(f=void 0)}))),null!==s.modelValue&&!1!==n||h(e)}function h(t){!0!==e.value&&(e.value=!0,u("before-show",t),void 0!==o?o(t):u("show",t))}function g(e){if(!0===s.disable)return;const t=void 0!==s["onUpdate:modelValue"];!0===t&&(u("update:modelValue",!1),f=e,(0,r.Y3)((()=>{f===e&&(f=void 0)}))),null!==s.modelValue&&!1!==t||m(e)}function m(t){!1!==e.value&&(e.value=!1,u("before-hide",t),void 0!==i?i(t):u("hide",t))}function b(t){if(!0===s.disable&&!0===t)void 0!==s["onUpdate:modelValue"]&&u("update:modelValue",!1);else if(!0===t!==e.value){const e=!0===t?h:m;e(f)}}(0,r.YP)((()=>s.modelValue),b),void 0!==n&&!0===(0,c.Rb)(l)&&(0,r.YP)((()=>d.$route),(()=>{!0===n.value&&!0===e.value&&g()})),!0===a&&(0,r.bv)((()=>{b(s.modelValue)}));const y={show:v,hide:g,toggle:p};return Object.assign(d,y),y}const v={transitionShow:{type:String,default:"fade"},transitionHide:{type:String,default:"fade"},transitionDuration:{type:[String,Number],default:300}};function h(e,t){const n=(0,o.iH)(t.value);return(0,r.YP)(t,(e=>{(0,r.Y3)((()=>{n.value=e}))})),{transition:(0,r.Fl)((()=>"q-transition--"+(!0===n.value?e.transitionHide:e.transitionShow))),transitionStyle:(0,r.Fl)((()=>`--q-transition-duration: ${e.transitionDuration}ms`))}}var g=n(4716),m=n(230),b=n(5578);const y=[];let w=document.body;function x(e){const t=document.createElement("div");if(void 0!==e&&(t.id=e),void 0!==b.w6.globalNodes){const e=b.w6.globalNodes["class"];void 0!==e&&(t.className=e)}return w.appendChild(t),y.push(t),t}function _(e){y.splice(y.indexOf(e),1),e.remove()}const k=[];function S(e,t){do{if("QMenu"===e.$options.name){if(e.hide(t),!0===e.$props.separateClosePopup)return(0,c.Kq)(e)}else if(void 0!==e.__qPortalInnerRef){const n=(0,c.Kq)(e);return void 0!==n&&"QPopupProxy"===n.$options.name?(e.hide(t),n):e}e=(0,c.Kq)(e)}while(void 0!==e&&null!==e)}function C(e){e=e.parent;while(void 0!==e&&null!==e){if("QGlobalDialog"===e.type.name)return!0;if("QDialog"===e.type.name||"QMenu"===e.type.name)return!1;e=e.parent}return!1}function F(e,t,n,i){const a=(0,o.iH)(!1);let l=null;const s={},u=!0===i&&C(e);function c(t){!0!==t?!1===a.value&&(!1===u&&null===l&&(l=x()),a.value=!0,k.push(e.proxy),(0,m.YX)(s)):(0,m.xF)(s)}function d(){(0,m.xF)(s),a.value=!1;const t=k.indexOf(e.proxy);t>-1&&k.splice(t,1),null!==l&&(_(l),l=null)}return(0,r.Ah)(d),Object.assign(e.proxy,{__qPortalInnerRef:t}),{showPortal:c,hidePortal:d,portalIsActive:a,renderPortal:()=>!0===u?n():!0===a.value?[(0,r.h)(r.lR,{to:l},n())]:void 0}}var E=n(8400),R=n(4688);let A,q,T,L,P,O,M=0,B=!1;function $(e){I(e)&&(0,g.NS)(e)}function I(e){if(e.target===document.body||e.target.classList.contains("q-layout__backdrop"))return!0;const t=(0,g.AZ)(e),n=e.shiftKey&&!e.deltaX,r=!n&&Math.abs(e.deltaX)<=Math.abs(e.deltaY),o=n||r?e.deltaY:e.deltaX;for(let i=0;i<t.length;i++){const e=t[i];if((0,E.QA)(e,r))return r?o<0&&0===e.scrollTop||o>0&&e.scrollTop+e.clientHeight===e.scrollHeight:o<0&&0===e.scrollLeft||o>0&&e.scrollLeft+e.clientWidth===e.scrollWidth}return!0}function j(e){e.target===document&&(document.scrollingElement.scrollTop=document.scrollingElement.scrollTop)}function N(e){!0!==B&&(B=!0,requestAnimationFrame((()=>{B=!1;const{height:t}=e.target,{clientHeight:n,scrollTop:r}=document.scrollingElement;void 0!==T&&t===window.innerHeight||(T=n-t,document.scrollingElement.scrollTop=r),r>T&&(document.scrollingElement.scrollTop-=Math.ceil((r-T)/8))})))}function V(e){const t=document.body,n=void 0!==window.visualViewport;if("add"===e){const{overflowY:e,overflowX:r}=window.getComputedStyle(t);A=(0,E.OI)(window),q=(0,E.u3)(window),L=t.style.left,P=t.style.top,t.style.left=`-${A}px`,t.style.top=`-${q}px`,"hidden"!==r&&("scroll"===r||t.scrollWidth>window.innerWidth)&&t.classList.add("q-body--force-scrollbar-x"),"hidden"!==e&&("scroll"===e||t.scrollHeight>window.innerHeight)&&t.classList.add("q-body--force-scrollbar-y"),t.classList.add("q-body--prevent-scroll"),document.qScrollPrevented=!0,!0===R.Lp.is.ios&&(!0===n?(window.scrollTo(0,0),window.visualViewport.addEventListener("resize",N,g.rU.passiveCapture),window.visualViewport.addEventListener("scroll",N,g.rU.passiveCapture),window.scrollTo(0,0)):window.addEventListener("scroll",j,g.rU.passiveCapture))}!0===R.Lp.is.desktop&&!0===R.Lp.is.mac&&window[`${e}EventListener`]("wheel",$,g.rU.notPassive),"remove"===e&&(!0===R.Lp.is.ios&&(!0===n?(window.visualViewport.removeEventListener("resize",N,g.rU.passiveCapture),window.visualViewport.removeEventListener("scroll",N,g.rU.passiveCapture)):window.removeEventListener("scroll",j,g.rU.passiveCapture)),t.classList.remove("q-body--prevent-scroll"),t.classList.remove("q-body--force-scrollbar-x"),t.classList.remove("q-body--force-scrollbar-y"),document.qScrollPrevented=!1,t.style.left=L,t.style.top=P,window.scrollTo(A,q),T=void 0)}function U(e){let t="add";if(!0===e){if(M++,void 0!==O)return clearTimeout(O),void(O=void 0);if(M>1)return}else{if(0===M)return;if(M--,M>0)return;if(t="remove",!0===R.Lp.is.ios&&!0===R.Lp.is.nativeMobile)return clearTimeout(O),void(O=setTimeout((()=>{V(t),O=void 0}),100))}V(t)}function H(){let e;return{preventBodyScroll(t){t===e||void 0===e&&!0!==t||(e=t,U(t))}}}var z=n(908),D=n(2012),Z=n(7657),W=n(1436);const Y=[];let J;function K(e){J=27===e.keyCode}function X(){!0===J&&(J=!1)}function G(e){!0===J&&(J=!1,!0===(0,W.So)(e,27)&&Y[Y.length-1](e))}function Q(e){window[e]("keydown",K),window[e]("blur",X),window[e]("keyup",G),J=!1}function ee(e){!0===R.Lp.is.desktop&&(Y.push(e),1===Y.length&&Q("addEventListener"))}function te(e){const t=Y.indexOf(e);t>-1&&(Y.splice(t,1),0===Y.length&&Q("removeEventListener"))}const ne=[];function re(e){ne[ne.length-1](e)}function oe(e){!0===R.Lp.is.desktop&&(ne.push(e),1===ne.length&&document.body.addEventListener("focusin",re))}function ie(e){const t=ne.indexOf(e);t>-1&&(ne.splice(t,1),0===ne.length&&document.body.removeEventListener("focusin",re))}let ae=0;const le={standard:"fixed-full flex-center",top:"fixed-top justify-center",bottom:"fixed-bottom justify-center",right:"fixed-right items-center",left:"fixed-left items-center"},se={standard:["scale","scale"],top:["slide-down","slide-up"],bottom:["slide-up","slide-down"],right:["slide-left","slide-right"],left:["slide-right","slide-left"]},ue=(0,z.L)({name:"QDialog",inheritAttrs:!1,props:{...d,...v,transitionShow:String,transitionHide:String,persistent:Boolean,autoClose:Boolean,noEscDismiss:Boolean,noBackdropDismiss:Boolean,noRouteDismiss:Boolean,noRefocus:Boolean,noFocus:Boolean,noShake:Boolean,seamless:Boolean,maximized:Boolean,fullWidth:Boolean,fullHeight:Boolean,square:Boolean,position:{type:String,default:"standard",validator:e=>"standard"===e||["top","bottom","left","right"].includes(e)}},emits:[...f,"shake","click","escape-key"],setup(e,{slots:t,emit:n,attrs:a}){const c=(0,r.FN)(),d=(0,o.iH)(null),f=(0,o.iH)(!1),v=(0,o.iH)(!1),h=(0,o.iH)(!1);let g,b,y,w=null;const x=(0,r.Fl)((()=>!0!==e.persistent&&!0!==e.noRouteDismiss&&!0!==e.seamless)),{preventBodyScroll:_}=H(),{registerTimeout:k,removeTimeout:S}=(0,s.Z)(),{registerTick:C,removeTick:E}=(0,u.Z)(),{showPortal:R,hidePortal:A,portalIsActive:q,renderPortal:T}=F(c,d,ue,!0),{hide:L}=p({showing:f,hideOnRouteChange:x,handleShow:z,handleHide:W,processOnMount:!0}),{addToHistory:P,removeFromHistory:O}=l(f,L,x),M=(0,r.Fl)((()=>"q-dialog__inner flex no-pointer-events q-dialog__inner--"+(!0===e.maximized?"maximized":"minimized")+` q-dialog__inner--${e.position} ${le[e.position]}`+(!0===h.value?" q-dialog__inner--animating":"")+(!0===e.fullWidth?" q-dialog__inner--fullwidth":"")+(!0===e.fullHeight?" q-dialog__inner--fullheight":"")+(!0===e.square?" q-dialog__inner--square":""))),B=(0,r.Fl)((()=>"q-transition--"+(void 0===e.transitionShow?se[e.position][0]:e.transitionShow))),$=(0,r.Fl)((()=>"q-transition--"+(void 0===e.transitionHide?se[e.position][1]:e.transitionHide))),I=(0,r.Fl)((()=>!0===v.value?$.value:B.value)),j=(0,r.Fl)((()=>`--q-transition-duration: ${e.transitionDuration}ms`)),N=(0,r.Fl)((()=>!0===f.value&&!0!==e.seamless)),V=(0,r.Fl)((()=>!0===e.autoClose?{onClick:Q}:{})),U=(0,r.Fl)((()=>["q-dialog fullscreen no-pointer-events q-dialog--"+(!0===N.value?"modal":"seamless"),a.class]));function z(t){S(),E(),P(),w=!1===e.noRefocus&&null!==document.activeElement?document.activeElement:null,G(e.maximized),R(),h.value=!0,!0!==e.noFocus&&(null!==document.activeElement&&document.activeElement.blur(),C(Y)),k((()=>{if(!0===c.proxy.$q.platform.is.ios){if(!0!==e.seamless&&document.activeElement){const{top:e,bottom:t}=document.activeElement.getBoundingClientRect(),{innerHeight:n}=window,r=void 0!==window.visualViewport?window.visualViewport.height:n;e>0&&t>r/2&&(document.scrollingElement.scrollTop=Math.min(document.scrollingElement.scrollHeight-r,t>=n?1/0:Math.ceil(document.scrollingElement.scrollTop+t-r/2))),document.activeElement.scrollIntoView()}y=!0,d.value.click(),y=!1}R(!0),h.value=!1,n("show",t)}),e.transitionDuration)}function W(t){S(),E(),O(),X(!0),h.value=!0,null!==w&&(w.focus(),w=null),k((()=>{A(),h.value=!1,n("hide",t)}),e.transitionDuration)}function Y(){(0,m.jd)((()=>{let e=d.value;null!==e&&!0!==e.contains(document.activeElement)&&(e=e.querySelector("[autofocus], [data-autofocus]")||e,e.focus())}))}function J(){Y(),n("shake");const e=d.value;null!==e&&(e.classList.remove("q-animate--scale"),e.classList.add("q-animate--scale"),clearTimeout(g),g=setTimeout((()=>{null!==d.value&&(e.classList.remove("q-animate--scale"),Y())}),170))}function K(){!0!==e.seamless&&(!0===e.persistent||!0===e.noEscDismiss?!0!==e.maximized&&!0!==e.noShake&&J():(n("escape-key"),L()))}function X(t){clearTimeout(g),!0!==t&&!0!==f.value||(G(!1),!0!==e.seamless&&(_(!1),ie(re),te(K))),!0!==t&&(w=null)}function G(e){!0===e?!0!==b&&(ae<1&&document.body.classList.add("q-body--dialog"),ae++,b=!0):!0===b&&(ae<2&&document.body.classList.remove("q-body--dialog"),ae--,b=!1)}function Q(e){!0!==y&&(L(e),n("click",e))}function ne(t){!0!==e.persistent&&!0!==e.noBackdropDismiss?L(t):!0!==e.noShake&&J()}function re(e){!0===f.value&&!0===q.value&&!0!==(0,D.mY)(d.value,e.target)&&Y()}function ue(){return(0,r.h)("div",{...a,class:U.value},[(0,r.h)(i.uT,{name:"q-transition--fade",appear:!0},(()=>!0===N.value?(0,r.h)("div",{class:"q-dialog__backdrop fixed-full",style:j.value,"aria-hidden":"true",onMousedown:ne}):null)),(0,r.h)(i.uT,{name:I.value,appear:!0},(()=>!0===f.value?(0,r.h)("div",{ref:d,class:M.value,style:j.value,tabindex:-1,...V.value},(0,Z.KR)(t.default)):null))])}return(0,r.YP)(f,(e=>{(0,r.Y3)((()=>{v.value=e}))})),(0,r.YP)((()=>e.maximized),(e=>{!0===f.value&&G(e)})),(0,r.YP)(N,(e=>{_(e),!0===e?(oe(re),ee(K)):(ie(re),te(K))})),Object.assign(c.proxy,{focus:Y,shake:J,__updateRefocusTarget(e){w=e||null}}),(0,r.Jd)(X),T}});var ce=n(9725);const de={target:{default:!0},noParentEvent:Boolean,contextMenu:Boolean};function fe({showing:e,avoidEmit:t,configureAnchorEl:n}){const{props:i,proxy:a,emit:l}=(0,r.FN)(),s=(0,o.iH)(null);let u;function c(e){return null!==s.value&&(void 0===e||void 0===e.touches||e.touches.length<=1)}const d={};function f(){(0,g.ul)(d,"anchor")}function p(e){s.value=e;while(s.value.classList.contains("q-anchor--skip"))s.value=s.value.parentNode;n()}function v(){if(!1===i.target||""===i.target)s.value=null;else if(!0===i.target)p(a.$el.parentNode);else{let t=i.target;if("string"===typeof i.target)try{t=document.querySelector(i.target)}catch(e){t=void 0}void 0!==t&&null!==t?(s.value=t.$el||t,n()):(s.value=null,console.error(`Anchor: target "${i.target}" not found`))}}return void 0===n&&(Object.assign(d,{hide(e){a.hide(e)},toggle(e){a.toggle(e),e.qAnchorHandled=!0},toggleKey(e){!0===(0,W.So)(e,13)&&d.toggle(e)},contextClick(e){a.hide(e),(0,g.X$)(e),(0,r.Y3)((()=>{a.show(e),e.qAnchorHandled=!0}))},prevent:g.X$,mobileTouch(e){if(d.mobileCleanup(e),!0!==c(e))return;a.hide(e),s.value.classList.add("non-selectable");const t=e.target;(0,g.M0)(d,"anchor",[[t,"touchmove","mobileCleanup","passive"],[t,"touchend","mobileCleanup","passive"],[t,"touchcancel","mobileCleanup","passive"],[s.value,"contextmenu","prevent","notPassive"]]),u=setTimeout((()=>{a.show(e),e.qAnchorHandled=!0}),300)},mobileCleanup(t){s.value.classList.remove("non-selectable"),clearTimeout(u),!0===e.value&&void 0!==t&&(0,ce.M)()}}),n=function(e=i.contextMenu){if(!0===i.noParentEvent||null===s.value)return;let t;t=!0===e?!0===a.$q.platform.is.mobile?[[s.value,"touchstart","mobileTouch","passive"]]:[[s.value,"mousedown","hide","passive"],[s.value,"contextmenu","contextClick","notPassive"]]:[[s.value,"click","toggle","passive"],[s.value,"keyup","toggleKey","passive"]],(0,g.M0)(d,"anchor",t)}),(0,r.YP)((()=>i.contextMenu),(e=>{null!==s.value&&(f(),n(e))})),(0,r.YP)((()=>i.target),(()=>{null!==s.value&&f(),v()})),(0,r.YP)((()=>i.noParentEvent),(e=>{null!==s.value&&(!0===e?f():n())})),(0,r.bv)((()=>{v(),!0!==t&&!0===i.modelValue&&null===s.value&&l("update:modelValue",!1)})),(0,r.Jd)((()=>{clearTimeout(u),f()})),{anchorEl:s,canShow:c,anchorEvents:d}}function pe(e,t){const n=(0,o.iH)(null);let i;function a(e,t){const n=(void 0!==t?"add":"remove")+"EventListener",r=void 0!==t?t:i;e!==window&&e[n]("scroll",r,g.rU.passive),window[n]("scroll",r,g.rU.passive),i=t}function l(){null!==n.value&&(a(n.value),n.value=null)}const s=(0,r.YP)((()=>e.noParentEvent),(()=>{null!==n.value&&(l(),t())}));return(0,r.Jd)(s),{localScrollTarget:n,unconfigureScrollTarget:l,changeScrollEvent:a}}var ve=n(2236);let he;const{notPassiveCapture:ge}=g.rU,me=[];function be(e){clearTimeout(he);const t=e.target;if(void 0===t||8===t.nodeType||!0===t.classList.contains("no-pointer-events"))return;let n=k.length-1;while(n>=0){const e=k[n].$;if("QDialog"!==e.type.name)break;if(!0!==e.props.seamless)return;n--}for(let r=me.length-1;r>=0;r--){const n=me[r];if(null!==n.anchorEl.value&&!1!==n.anchorEl.value.contains(t)||t!==document.body&&(null===n.innerRef.value||!1!==n.innerRef.value.contains(t)))return;e.qClickOutside=!0,n.onClickOutside(e)}}function ye(e){me.push(e),1===me.length&&(document.addEventListener("mousedown",be,ge),document.addEventListener("touchstart",be,ge))}function we(e){const t=me.findIndex((t=>t===e));t>-1&&(me.splice(t,1),0===me.length&&(clearTimeout(he),document.removeEventListener("mousedown",be,ge),document.removeEventListener("touchstart",be,ge)))}let xe,_e;function ke(e){const t=e.split(" ");return 2===t.length&&(!0!==["top","center","bottom"].includes(t[0])?(console.error("Anchor/Self position must start with one of top/center/bottom"),!1):!0===["left","middle","right","start","end"].includes(t[1])||(console.error("Anchor/Self position must end with one of left/middle/right/start/end"),!1))}function Se(e){return!e||2===e.length&&("number"===typeof e[0]&&"number"===typeof e[1])}const Ce={"start#ltr":"left","start#rtl":"right","end#ltr":"right","end#rtl":"left"};function Fe(e,t){const n=e.split(" ");return{vertical:n[0],horizontal:Ce[`${n[1]}#${!0===t?"rtl":"ltr"}`]}}function Ee(e,t){let{top:n,left:r,right:o,bottom:i,width:a,height:l}=e.getBoundingClientRect();return void 0!==t&&(n-=t[1],r-=t[0],i+=t[1],o+=t[0],a+=t[0],l+=t[1]),{top:n,left:r,right:o,bottom:i,width:a,height:l,middle:r+(o-r)/2,center:n+(i-n)/2}}function Re(e){return{top:0,center:e.offsetHeight/2,bottom:e.offsetHeight,left:0,middle:e.offsetWidth/2,right:e.offsetWidth}}function Ae(e){if(!0===R.Lp.is.ios&&void 0!==window.visualViewport){const e=document.body.style,{offsetLeft:t,offsetTop:n}=window.visualViewport;t!==xe&&(e.setProperty("--q-pe-left",t+"px"),xe=t),n!==_e&&(e.setProperty("--q-pe-top",n+"px"),_e=n)}let t;const{scrollLeft:n,scrollTop:r}=e.el;if(void 0===e.absoluteOffset)t=Ee(e.anchorEl,!0===e.cover?[0,0]:e.offset);else{const{top:n,left:r}=e.anchorEl.getBoundingClientRect(),o=n+e.absoluteOffset.top,i=r+e.absoluteOffset.left;t={top:o,left:i,width:1,height:1,right:i+1,center:o,middle:i,bottom:o+1}}let o={maxHeight:e.maxHeight,maxWidth:e.maxWidth,visibility:"visible"};!0!==e.fit&&!0!==e.cover||(o.minWidth=t.width+"px",!0===e.cover&&(o.minHeight=t.height+"px")),Object.assign(e.el.style,o);const i=Re(e.el),a={top:t[e.anchorOrigin.vertical]-i[e.selfOrigin.vertical],left:t[e.anchorOrigin.horizontal]-i[e.selfOrigin.horizontal]};qe(a,t,i,e.anchorOrigin,e.selfOrigin),o={top:a.top+"px",left:a.left+"px"},void 0!==a.maxHeight&&(o.maxHeight=a.maxHeight+"px",t.height>a.maxHeight&&(o.minHeight=o.maxHeight)),void 0!==a.maxWidth&&(o.maxWidth=a.maxWidth+"px",t.width>a.maxWidth&&(o.minWidth=o.maxWidth)),Object.assign(e.el.style,o),e.el.scrollTop!==r&&(e.el.scrollTop=r),e.el.scrollLeft!==n&&(e.el.scrollLeft=n)}function qe(e,t,n,r,o){const i=n.bottom,a=n.right,l=(0,E.np)(),s=window.innerHeight-l,u=document.body.clientWidth;if(e.top<0||e.top+i>s)if("center"===o.vertical)e.top=t[r.vertical]>s/2?Math.max(0,s-i):0,e.maxHeight=Math.min(i,s);else if(t[r.vertical]>s/2){const n=Math.min(s,"center"===r.vertical?t.center:r.vertical===o.vertical?t.bottom:t.top);e.maxHeight=Math.min(i,n),e.top=Math.max(0,n-i)}else e.top=Math.max(0,"center"===r.vertical?t.center:r.vertical===o.vertical?t.top:t.bottom),e.maxHeight=Math.min(i,s-e.top);if(e.left<0||e.left+a>u)if(e.maxWidth=Math.min(a,u),"middle"===o.horizontal)e.left=t[r.horizontal]>u/2?Math.max(0,u-a):0;else if(t[r.horizontal]>u/2){const n=Math.min(u,"middle"===r.horizontal?t.middle:r.horizontal===o.horizontal?t.right:t.left);e.maxWidth=Math.min(a,n),e.left=Math.max(0,n-e.maxWidth)}else e.left=Math.max(0,"middle"===r.horizontal?t.middle:r.horizontal===o.horizontal?t.left:t.right),e.maxWidth=Math.min(a,u-e.left)}["left","middle","right"].forEach((e=>{Ce[`${e}#ltr`]=e,Ce[`${e}#rtl`]=e}));const Te=(0,z.L)({name:"QMenu",inheritAttrs:!1,props:{...de,...d,...ve.S,...v,persistent:Boolean,autoClose:Boolean,separateClosePopup:Boolean,noRouteDismiss:Boolean,noRefocus:Boolean,noFocus:Boolean,fit:Boolean,cover:Boolean,square:Boolean,anchor:{type:String,validator:ke},self:{type:String,validator:ke},offset:{type:Array,validator:Se},scrollTarget:{default:void 0},touchPosition:Boolean,maxHeight:{type:String,default:null},maxWidth:{type:String,default:null}},emits:[...f,"click","escape-key"],setup(e,{slots:t,emit:n,attrs:a}){let l,c,d,f=null;const v=(0,r.FN)(),{proxy:b}=v,{$q:y}=b,w=(0,o.iH)(null),x=(0,o.iH)(!1),_=(0,r.Fl)((()=>!0!==e.persistent&&!0!==e.noRouteDismiss)),k=(0,ve.Z)(e,y),{registerTick:C,removeTick:R}=(0,u.Z)(),{registerTimeout:A,removeTimeout:q}=(0,s.Z)(),{transition:T,transitionStyle:L}=h(e,x),{localScrollTarget:P,changeScrollEvent:O,unconfigureScrollTarget:M}=pe(e,ne),{anchorEl:B,canShow:$}=fe({showing:x}),{hide:I}=p({showing:x,canShow:$,handleShow:X,handleHide:G,hideOnRouteChange:_,processOnMount:!0}),{showPortal:j,hidePortal:N,renderPortal:V}=F(v,w,ue),U={anchorEl:B,innerRef:w,onClickOutside(t){if(!0!==e.persistent&&!0===x.value)return I(t),("touchstart"===t.type||t.target.classList.contains("q-dialog__backdrop"))&&(0,g.NS)(t),!0}},H=(0,r.Fl)((()=>Fe(e.anchor||(!0===e.cover?"center middle":"bottom start"),y.lang.rtl))),z=(0,r.Fl)((()=>!0===e.cover?H.value:Fe(e.self||"top start",y.lang.rtl))),W=(0,r.Fl)((()=>(!0===e.square?" q-menu--square":"")+(!0===k.value?" q-menu--dark q-dark":""))),Y=(0,r.Fl)((()=>!0===e.autoClose?{onClick:re}:{})),J=(0,r.Fl)((()=>!0===x.value&&!0!==e.persistent));function K(){(0,m.jd)((()=>{let e=w.value;e&&!0!==e.contains(document.activeElement)&&(e=e.querySelector("[autofocus], [data-autofocus]")||e,e.focus())}))}function X(t){if(R(),q(),f=!1===e.noRefocus?document.activeElement:null,oe(ae),j(),ne(),l=void 0,void 0!==t&&(e.touchPosition||e.contextMenu)){const e=(0,g.FK)(t);if(void 0!==e.left){const{top:t,left:n}=B.value.getBoundingClientRect();l={left:e.left-n,top:e.top-t}}}void 0===c&&(c=(0,r.YP)((()=>y.screen.width+"|"+y.screen.height+"|"+e.self+"|"+e.anchor+"|"+y.lang.rtl),se)),!0!==e.noFocus&&document.activeElement.blur(),C((()=>{se(),!0!==e.noFocus&&K()})),A((()=>{!0===y.platform.is.ios&&(d=e.autoClose,w.value.click()),se(),j(!0),n("show",t)}),e.transitionDuration)}function G(t){R(),q(),Q(!0),null===f||void 0!==t&&!0===t.qClickOutside||(f.focus(),f=null),A((()=>{N(),n("hide",t)}),e.transitionDuration)}function Q(e){l=void 0,void 0!==c&&(c(),c=void 0),!0!==e&&!0!==x.value||(ie(ae),M(),we(U),te(le)),!0!==e&&(f=null)}function ne(){null===B.value&&void 0===e.scrollTarget||(P.value=(0,E.b0)(B.value,e.scrollTarget),O(P.value,se))}function re(e){!0!==d?(S(b,e),n("click",e)):d=!1}function ae(e){!0===J.value&&!0!==(0,D.mY)(w.value,e.target)&&K()}function le(e){n("escape-key"),I(e)}function se(){const t=w.value;null!==t&&null!==B.value&&Ae({el:t,offset:e.offset,anchorEl:B.value,anchorOrigin:H.value,selfOrigin:z.value,absoluteOffset:l,fit:e.fit,cover:e.cover,maxHeight:e.maxHeight,maxWidth:e.maxWidth})}function ue(){return(0,r.h)(i.uT,{name:T.value,appear:!0},(()=>!0===x.value?(0,r.h)("div",{...a,ref:w,tabindex:-1,class:["q-menu q-position-engine scroll"+W.value,a.class],style:[a.style,L.value],...Y.value},(0,Z.KR)(t.default)):null))}return(0,r.YP)(J,(e=>{!0===e?(ee(le),ye(U)):(te(le),we(U))})),(0,r.Jd)(Q),Object.assign(b,{focus:K,updatePosition:se}),V}}),Le=(0,z.L)({name:"QPopupProxy",props:{...de,breakpoint:{type:[String,Number],default:450}},emits:["show","hide"],setup(e,{slots:t,emit:n,attrs:i}){const{proxy:a}=(0,r.FN)(),{$q:l}=a,s=(0,o.iH)(!1),u=(0,o.iH)(null),c=(0,r.Fl)((()=>parseInt(e.breakpoint,10))),{canShow:d}=fe({showing:s});function f(){return l.screen.width<c.value||l.screen.height<c.value?"dialog":"menu"}const p=(0,o.iH)(f()),v=(0,r.Fl)((()=>"menu"===p.value?{maxHeight:"99vh"}:{}));function h(e){s.value=!0,n("show",e)}function g(e){s.value=!1,p.value=f(),n("hide",e)}return(0,r.YP)((()=>f()),(e=>{!0!==s.value&&(p.value=e)})),Object.assign(a,{show(e){!0===d(e)&&u.value.show(e)},hide(e){u.value.hide(e)},toggle(e){u.value.toggle(e)}}),()=>{const n={ref:u,...v.value,...i,onShow:h,onHide:g};let o;return"dialog"===p.value?o=ue:(o=Te,Object.assign(n,{target:e.target,contextMenu:e.contextMenu,noParentEvent:!0,separateClosePopup:!0})),(0,r.h)(o,n,t.default)}}})},9337:(e,t,n)=>{"use strict";n.d(t,{Z:()=>v});var r=n(3673),o=n(1959),i=n(2236),a=n(2417);function l(e,t){const n=(0,o.iH)(null),i=(0,r.Fl)((()=>!0!==e.disable?null:(0,r.h)("span",{ref:n,class:"no-outline",tabindex:-1})));function a(e){const r=t.value;void 0!==e&&0===e.type.indexOf("key")?null!==r&&document.activeElement!==r&&!0===r.contains(document.activeElement)&&r.focus():null!==n.value&&(void 0===e||null!==r&&!0===r.contains(e.target))&&n.value.focus()}return{refocusTargetEl:i,refocusTarget:a}}var s=n(9550),u=n(908);const c={xs:30,sm:35,md:40,lg:50,xl:60};var d=n(4716),f=n(7657);const p=(0,r.h)("svg",{class:"q-radio__bg absolute non-selectable",viewBox:"0 0 24 24","aria-hidden":"true"},[(0,r.h)("path",{d:"M12,22a10,10 0 0 1 -10,-10a10,10 0 0 1 10,-10a10,10 0 0 1 10,10a10,10 0 0 1 -10,10m0,-22a12,12 0 0 0 -12,12a12,12 0 0 0 12,12a12,12 0 0 0 12,-12a12,12 0 0 0 -12,-12"}),(0,r.h)("path",{class:"q-radio__check",d:"M12,6a6,6 0 0 0 -6,6a6,6 0 0 0 6,6a6,6 0 0 0 6,-6a6,6 0 0 0 -6,-6"})]),v=(0,u.L)({name:"QRadio",props:{...i.S,...a.LU,...s.Fz,modelValue:{required:!0},val:{required:!0},label:String,leftLabel:Boolean,color:String,keepColor:Boolean,dense:Boolean,disable:Boolean,tabindex:[String,Number]},emits:["update:modelValue"],setup(e,{slots:t,emit:n}){const{proxy:u}=(0,r.FN)(),v=(0,i.Z)(e,u.$q),h=(0,a.ZP)(e,c),g=(0,o.iH)(null),{refocusTargetEl:m,refocusTarget:b}=l(e,g),y=(0,r.Fl)((()=>e.modelValue===e.val)),w=(0,r.Fl)((()=>"q-radio cursor-pointer no-outline row inline no-wrap items-center"+(!0===e.disable?" disabled":"")+(!0===v.value?" q-radio--dark":"")+(!0===e.dense?" q-radio--dense":"")+(!0===e.leftLabel?" reverse":""))),x=(0,r.Fl)((()=>{const t=void 0===e.color||!0!==e.keepColor&&!0!==y.value?"":` text-${e.color}`;return`q-radio__inner relative-position q-radio__inner--${!0===y.value?"truthy":"falsy"}${t}`})),_=(0,r.Fl)((()=>!0===e.disable?-1:e.tabindex||0)),k=(0,r.Fl)((()=>{const t={type:"radio"};return void 0!==e.name&&Object.assign(t,{"^checked":!0===y.value?"checked":void 0,name:e.name,value:e.val}),t})),S=(0,s.eX)(k);function C(t){void 0!==t&&((0,d.NS)(t),b(t)),!0!==e.disable&&!0!==y.value&&n("update:modelValue",e.val,t)}function F(e){13!==e.keyCode&&32!==e.keyCode||(0,d.NS)(e)}function E(e){13!==e.keyCode&&32!==e.keyCode||C(e)}return Object.assign(u,{set:C}),()=>{const n=[p];!0!==e.disable&&S(n,"unshift"," q-radio__native q-ma-none q-pa-none");const o=[(0,r.h)("div",{class:x.value,style:h.value},n)];null!==m.value&&o.push(m.value);const i=void 0!==e.label?(0,f.vs)(t.default,[e.label]):(0,f.KR)(t.default);return void 0!==i&&o.push((0,r.h)("div",{class:"q-radio__label q-anchor--skip"},i)),(0,r.h)("div",{ref:g,class:w.value,tabindex:_.value,role:"radio","aria-label":e.label,"aria-checked":!0===y.value?"true":"false","aria-disabled":!0===e.disable?"true":void 0,onClick:C,onKeydown:F,onKeyup:E},o)}}})},2064:(e,t,n)=>{"use strict";n.d(t,{Z:()=>x});var r=n(3673),o=n(1959),i=n(9550),a=(n(7070),n(6801),n(5777)),l=n(2236),s=n(2130),u=n(4716),c=n(782),d=n(7657);const f="q-slider__marker-labels",p=e=>({value:e}),v=({marker:e})=>(0,r.h)("div",{key:e.value,style:e.style,class:e.classes},e.label),h=[34,37,40,33,39,38],g={...l.S,...i.Fz,min:{type:Number,default:0},max:{type:Number,default:100},innerMin:Number,innerMax:Number,step:{type:Number,default:1,validator:e=>e>=0},snap:Boolean,vertical:Boolean,reverse:Boolean,hideSelection:Boolean,color:String,markerLabelsClass:String,label:Boolean,labelColor:String,labelTextColor:String,labelAlways:Boolean,switchLabelSide:Boolean,markers:[Boolean,Number],markerLabels:[Boolean,Array,Object,Function],switchMarkerLabelsSide:Boolean,trackImg:String,trackColor:String,innerTrackImg:String,innerTrackColor:String,selectionColor:String,selectionImg:String,thumbSize:{type:String,default:"20px"},trackSize:{type:String,default:"4px"},disable:Boolean,readonly:Boolean,dense:Boolean,tabindex:[String,Number],thumbColor:String,thumbPath:{type:String,default:"M 4, 10 a 6,6 0 1,0 12,0 a 6,6 0 1,0 -12,0"}},m=["pan","update:modelValue","change"];function b({updateValue:e,updatePosition:t,getDragging:n,formAttrs:g}){const{props:m,emit:b,slots:y,proxy:{$q:w}}=(0,r.FN)(),x=(0,l.Z)(m,w),_=(0,i.eX)(g),k=(0,o.iH)(!1),S=(0,o.iH)(!1),C=(0,o.iH)(!1),F=(0,o.iH)(!1),E=(0,r.Fl)((()=>!0===m.vertical?"--v":"--h")),R=(0,r.Fl)((()=>"-"+(!0===m.switchLabelSide?"switched":"standard"))),A=(0,r.Fl)((()=>!0===m.vertical?!0===m.reverse:m.reverse!==(!0===w.lang.rtl))),q=(0,r.Fl)((()=>!0===isNaN(m.innerMin)||m.innerMin<m.min?m.min:m.innerMin)),T=(0,r.Fl)((()=>!0===isNaN(m.innerMax)||m.innerMax>m.max?m.max:m.innerMax)),L=(0,r.Fl)((()=>!0!==m.disable&&!0!==m.readonly&&q.value<T.value)),P=(0,r.Fl)((()=>(String(m.step).trim("0").split(".")[1]||"").length)),O=(0,r.Fl)((()=>0===m.step?1:m.step)),M=(0,r.Fl)((()=>!0===L.value?m.tabindex||0:-1)),B=(0,r.Fl)((()=>m.max-m.min)),$=(0,r.Fl)((()=>T.value-q.value)),I=(0,r.Fl)((()=>ae(q.value))),j=(0,r.Fl)((()=>ae(T.value))),N=(0,r.Fl)((()=>!0===m.vertical?!0===A.value?"bottom":"top":!0===A.value?"right":"left")),V=(0,r.Fl)((()=>!0===m.vertical?"height":"width")),U=(0,r.Fl)((()=>!0===m.vertical?"width":"height")),H=(0,r.Fl)((()=>!0===m.vertical?"vertical":"horizontal")),z=(0,r.Fl)((()=>{const e={role:"slider","aria-valuemin":q.value,"aria-valuemax":T.value,"aria-orientation":H.value,"data-step":m.step};return!0===m.disable?e["aria-disabled"]="true":!0===m.readonly&&(e["aria-readonly"]="true"),e})),D=(0,r.Fl)((()=>`q-slider q-slider${E.value} q-slider--${!0===k.value?"":"in"}active inline no-wrap `+(!0===m.vertical?"row":"column")+(!0===m.disable?" disabled":" q-slider--enabled"+(!0===L.value?" q-slider--editable":""))+("both"===C.value?" q-slider--focus":"")+(m.label||!0===m.labelAlways?" q-slider--label":"")+(!0===m.labelAlways?" q-slider--label-always":"")+(!0===x.value?" q-slider--dark":"")+(!0===m.dense?" q-slider--dense q-slider--dense"+E.value:"")));function Z(e){const t="q-slider__"+e;return`${t} ${t}${E.value} ${t}${E.value}${R.value}`}function W(e){const t="q-slider__"+e;return`${t} ${t}${E.value}`}const Y=(0,r.Fl)((()=>{const e=m.selectionColor||m.color;return"q-slider__selection absolute"+(void 0!==e?` text-${e}`:"")})),J=(0,r.Fl)((()=>W("markers")+" absolute overflow-hidden")),K=(0,r.Fl)((()=>W("track-container"))),X=(0,r.Fl)((()=>Z("pin"))),G=(0,r.Fl)((()=>Z("label"))),Q=(0,r.Fl)((()=>Z("text-container"))),ee=(0,r.Fl)((()=>Z("marker-labels-container")+(void 0!==m.markerLabelsClass?` ${m.markerLabelsClass}`:""))),te=(0,r.Fl)((()=>"q-slider__track relative-position no-outline"+(void 0!==m.trackColor?` bg-${m.trackColor}`:""))),ne=(0,r.Fl)((()=>{const e={[U.value]:m.trackSize};return void 0!==m.trackImg&&(e.backgroundImage=`url(${m.trackImg}) !important`),e})),re=(0,r.Fl)((()=>"q-slider__inner absolute"+(void 0!==m.innerTrackColor?` bg-${m.innerTrackColor}`:""))),oe=(0,r.Fl)((()=>{const e={[N.value]:100*I.value+"%",[V.value]:100*(j.value-I.value)+"%"};return void 0!==m.innerTrackImg&&(e.backgroundImage=`url(${m.innerTrackImg}) !important`),e}));function ie(e){const{min:t,max:n,step:r}=m;let o=t+e*(n-t);if(r>0){const e=(o-t)%r;o+=(Math.abs(e)>=r/2?(e<0?-1:1)*r:0)-e}return P.value>0&&(o=parseFloat(o.toFixed(P.value))),(0,s.vX)(o,q.value,T.value)}function ae(e){return 0===B.value?0:(e-m.min)/B.value}function le(e,t){const n=(0,u.FK)(e),r=!0===m.vertical?(0,s.vX)((n.top-t.top)/t.height,0,1):(0,s.vX)((n.left-t.left)/t.width,0,1);return(0,s.vX)(!0===A.value?1-r:r,I.value,j.value)}const se=(0,r.Fl)((()=>!0===(0,c.hj)(m.markers)?m.markers:O.value)),ue=(0,r.Fl)((()=>{const e=[],t=se.value,n=m.max;let r=m.min;do{e.push(r),r+=t}while(r<n);return e.push(n),e})),ce=(0,r.Fl)((()=>{const e=` ${f}${E.value}-`;return f+`${e}${!0===m.switchMarkerLabelsSide?"switched":"standard"}`+`${e}${!0===A.value?"rtl":"ltr"}`})),de=(0,r.Fl)((()=>!1===m.markerLabels?null:ve(m.markerLabels).map(((e,t)=>({index:t,value:e.value,label:e.label||e.value,classes:ce.value+(void 0!==e.classes?" "+e.classes:""),style:{...he(e.value),...e.style||{}}}))))),fe=(0,r.Fl)((()=>({markerList:de.value,markerMap:ge.value,classes:ce.value,getStyle:he}))),pe=(0,r.Fl)((()=>{if(0!==$.value){const e=100*se.value/$.value;return{...oe.value,backgroundSize:!0===m.vertical?`2px ${e}%`:`${e}% 2px`}}return null}));function ve(e){if(!1===e)return null;if(!0===e)return ue.value.map(p);if("function"===typeof e)return ue.value.map((t=>{const n=e(t);return Object(n)===n?{...n,value:t}:{value:t,label:n}}));const t=({value:e})=>e>=m.min&&e<=m.max;return!0===Array.isArray(e)?e.map((e=>Object(e)===e?e:{value:e})).filter(t):Object.keys(e).map((t=>{const n=e[t],r=Number(t);return Object(n)===n?{...n,value:r}:{value:r,label:n}})).filter(t)}function he(e){return{[N.value]:100*(e-m.min)/B.value+"%"}}const ge=(0,r.Fl)((()=>{if(!1===m.markerLabels)return null;const e={};return de.value.forEach((t=>{e[t.value]=t})),e}));function me(){if(void 0!==y["marker-label-group"])return y["marker-label-group"](fe.value);const e=y["marker-label"]||v;return de.value.map((t=>e({marker:t,...fe.value})))}const be=(0,r.Fl)((()=>[[a.Z,ye,void 0,{[H.value]:!0,prevent:!0,stop:!0,mouse:!0,mouseAllDir:!0}]]));function ye(r){!0===r.isFinal?(void 0!==F.value&&(t(r.evt),!0===r.touch&&e(!0),F.value=void 0,b("pan","end")),k.value=!1,C.value=!1):!0===r.isFirst?(F.value=n(r.evt),t(r.evt),e(),k.value=!0,b("pan","start")):(t(r.evt),e())}function we(){C.value=!1}function xe(r){t(r,n(r)),e(),S.value=!0,k.value=!0,document.addEventListener("mouseup",_e,!0)}function _e(){S.value=!1,k.value=!1,e(!0),we(),document.removeEventListener("mouseup",_e,!0)}function ke(r){t(r,n(r)),e(!0)}function Se(t){h.includes(t.keyCode)&&e(!0)}function Ce(e){if(!0===m.vertical)return null;const t=w.lang.rtl!==m.reverse?1-e:e;return{transform:`translateX(calc(${2*t-1} * ${m.thumbSize} / 2 + ${50-100*t}%))`}}function Fe(e){const t=(0,r.Fl)((()=>!1!==S.value||C.value!==e.focusValue&&"both"!==C.value?"":" q-slider--focus")),n=(0,r.Fl)((()=>`q-slider__thumb q-slider__thumb${E.value} q-slider__thumb${E.value}-${!0===A.value?"rtl":"ltr"} absolute non-selectable`+t.value+(void 0!==e.thumbColor.value?` text-${e.thumbColor.value}`:""))),o=(0,r.Fl)((()=>({width:m.thumbSize,height:m.thumbSize,[N.value]:100*e.ratio.value+"%",zIndex:C.value===e.focusValue?2:void 0}))),i=(0,r.Fl)((()=>void 0!==e.labelColor.value?` text-${e.labelColor.value}`:"")),a=(0,r.Fl)((()=>Ce(e.ratio.value))),l=(0,r.Fl)((()=>"q-slider__text"+(void 0!==e.labelTextColor.value?` text-${e.labelTextColor.value}`:"")));return()=>{const t=[(0,r.h)("svg",{class:"q-slider__thumb-shape absolute-full",viewBox:"0 0 20 20","aria-hidden":"true"},[(0,r.h)("path",{d:m.thumbPath})]),(0,r.h)("div",{class:"q-slider__focus-ring fit"})];return!0!==m.label&&!0!==m.labelAlways||(t.push((0,r.h)("div",{class:X.value+" absolute fit no-pointer-events"+i.value},[(0,r.h)("div",{class:G.value,style:{minWidth:m.thumbSize}},[(0,r.h)("div",{class:Q.value,style:a.value},[(0,r.h)("span",{class:l.value},e.label.value)])])])),void 0!==m.name&&!0!==m.disable&&_(t,"push")),(0,r.h)("div",{class:n.value,style:o.value,...e.getNodeData()},t)}}function Ee(e,t,n,o){const i=[];"transparent"!==m.innerTrackColor&&i.push((0,r.h)("div",{key:"inner",class:re.value,style:oe.value})),"transparent"!==m.selectionColor&&i.push((0,r.h)("div",{key:"selection",class:Y.value,style:e.value})),!1!==m.markers&&i.push((0,r.h)("div",{key:"marker",class:J.value,style:pe.value})),o(i);const a=[(0,d.Jl)("div",{key:"trackC",class:K.value,tabindex:t.value,...n.value},[(0,r.h)("div",{class:te.value,style:ne.value},i)],"slide",L.value,(()=>be.value))];if(!1!==m.markerLabels){const e=!0===m.switchMarkerLabelsSide?"unshift":"push";a[e]((0,r.h)("div",{key:"markerL",class:ee.value},me()))}return a}return(0,r.Jd)((()=>{document.removeEventListener("mouseup",_e,!0)})),{state:{active:k,focus:C,preventFocus:S,dragging:F,editable:L,classes:D,tabindex:M,attributes:z,step:O,decimals:P,trackLen:B,innerMin:q,innerMinRatio:I,innerMax:T,innerMaxRatio:j,positionProp:N,sizeProp:V,isReversed:A},methods:{onActivate:xe,onMobileClick:ke,onBlur:we,onKeyup:Se,getContent:Ee,getThumbRenderFn:Fe,convertRatioToModel:ie,convertModelToRatio:ae,getDraggingRatio:le}}}var y=n(908);const w=()=>({}),x=(0,y.L)({name:"QSlider",props:{...g,modelValue:{required:!0,default:null,validator:e=>"number"===typeof e||null===e},labelValue:[String,Number]},emits:m,setup(e,{emit:t}){const{proxy:{$q:n}}=(0,r.FN)(),{state:a,methods:l}=b({updateValue:_,updatePosition:S,getDragging:k,formAttrs:(0,i.Vt)(e)}),c=(0,o.iH)(null),d=(0,o.iH)(0),f=(0,o.iH)(0);function p(){f.value=null===e.modelValue?a.innerMin.value:(0,s.vX)(e.modelValue,a.innerMin.value,a.innerMax.value)}(0,r.YP)((()=>`${e.modelValue}|${a.innerMin.value}|${a.innerMax.value}`),p),p();const v=(0,r.Fl)((()=>l.convertModelToRatio(f.value))),g=(0,r.Fl)((()=>!0===a.active.value?d.value:v.value)),m=(0,r.Fl)((()=>{const t={[a.positionProp.value]:100*a.innerMinRatio.value+"%",[a.sizeProp.value]:100*(g.value-a.innerMinRatio.value)+"%"};return void 0!==e.selectionImg&&(t.backgroundImage=`url(${e.selectionImg}) !important`),t})),y=l.getThumbRenderFn({focusValue:!0,getNodeData:w,ratio:g,label:(0,r.Fl)((()=>void 0!==e.labelValue?e.labelValue:f.value)),thumbColor:(0,r.Fl)((()=>e.thumbColor||e.color)),labelColor:(0,r.Fl)((()=>e.labelColor)),labelTextColor:(0,r.Fl)((()=>e.labelTextColor))}),x=(0,r.Fl)((()=>!0!==a.editable.value?{}:!0===n.platform.is.mobile?{onClick:l.onMobileClick}:{onMousedown:l.onActivate,onFocus:C,onBlur:l.onBlur,onKeydown:F,onKeyup:l.onKeyup}));function _(n){f.value!==e.modelValue&&t("update:modelValue",f.value),!0===n&&t("change",f.value)}function k(){return c.value.getBoundingClientRect()}function S(t,n=a.dragging.value){const r=l.getDraggingRatio(t,n);f.value=l.convertRatioToModel(r),d.value=!0!==e.snap||0===e.step?r:l.convertModelToRatio(f.value)}function C(){a.focus.value=!0}function F(e){if(!h.includes(e.keyCode))return;(0,u.NS)(e);const t=([34,33].includes(e.keyCode)?10:1)*a.step.value,n=([34,37,40].includes(e.keyCode)?-1:1)*(!0===a.isReversed.value?-1:1)*t;f.value=(0,s.vX)(parseFloat((f.value+n).toFixed(a.decimals.value)),a.innerMin.value,a.innerMax.value),_()}return()=>{const t=l.getContent(m,a.tabindex,x,(e=>{e.push(y())}));return(0,r.h)("div",{ref:c,class:a.classes.value+(null===e.modelValue?" q-slider--no-value":""),...a.attributes.value,"aria-valuenow":e.modelValue},t)}}})},9754:(e,t,n)=>{"use strict";n.d(t,{Z:()=>s});var r=n(3673),o=n(2417);const i={size:{type:[Number,String],default:"1em"},color:String};function a(e){return{cSize:(0,r.Fl)((()=>e.size in o.Ok?`${o.Ok[e.size]}px`:e.size)),classes:(0,r.Fl)((()=>"q-spinner"+(e.color?` text-${e.color}`:"")))}}var l=n(908);const s=(0,l.L)({name:"QSpinner",props:{...i,thickness:{type:Number,default:5}},setup(e){const{cSize:t,classes:n}=a(e);return()=>(0,r.h)("svg",{class:n.value+" q-spinner-mat",width:t.value,height:t.value,viewBox:"25 25 50 50"},[(0,r.h)("circle",{class:"path",cx:"50",cy:"50",r:"20",fill:"none",stroke:"currentColor","stroke-width":e.thickness,"stroke-miterlimit":"10"})])}})},2236:(e,t,n)=>{"use strict";n.d(t,{S:()=>o,Z:()=>i});var r=n(3673);const o={dark:{type:Boolean,default:null}};function i(e,t){return(0,r.Fl)((()=>null===e.dark?t.dark.isActive:e.dark))}},9550:(e,t,n)=>{"use strict";n.d(t,{Fz:()=>o,Vt:()=>i,eX:()=>a,Do:()=>l});var r=n(3673);const o={name:String};function i(e){return(0,r.Fl)((()=>({type:"hidden",name:e.name,value:e.modelValue})))}function a(e={}){return(t,n,o)=>{t[n]((0,r.h)("input",{class:"hidden"+(o||""),...e.value}))}}function l(e){return(0,r.Fl)((()=>e.name||e.for))}},7277:(e,t,n)=>{"use strict";n.d(t,{$:()=>f,Z:()=>p});n(5363);var r=n(3673),o=n(4716),i=n(7445);function a(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}function l(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function s(e,t){for(const n in t){const r=t[n],o=e[n];if("string"===typeof r){if(r!==o)return!1}else if(!1===Array.isArray(o)||o.length!==r.length||r.some(((e,t)=>e!==o[t])))return!1}return!0}function u(e,t){return!0===Array.isArray(t)?e.length===t.length&&e.every(((e,n)=>e===t[n])):1===e.length&&e[0]===t}function c(e,t){return!0===Array.isArray(e)?u(e,t):!0===Array.isArray(t)?u(t,e):e===t}function d(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(!1===c(e[n],t[n]))return!1;return!0}const f={to:[String,Object],replace:Boolean,exact:Boolean,activeClass:{type:String,default:"q-router-link--active"},exactActiveClass:{type:String,default:"q-router-link--exact-active"},href:String,target:String,disable:Boolean};function p(e){const t=(0,r.FN)(),{props:n,proxy:u}=t,c=(0,i.Rb)(t),f=(0,r.Fl)((()=>!0!==n.disable&&void 0!==n.href)),p=(0,r.Fl)((()=>!0===c&&!0!==n.disable&&!0!==f.value&&void 0!==n.to&&null!==n.to&&""!==n.to)),v=(0,r.Fl)((()=>{if(!0===p.value)try{return u.$router.resolve(n.to)}catch(e){}return null})),h=(0,r.Fl)((()=>null!==v.value)),g=(0,r.Fl)((()=>!0===f.value||!0===h.value)),m=(0,r.Fl)((()=>"a"===n.type||!0===g.value?"a":n.tag||e||"div")),b=(0,r.Fl)((()=>!0===f.value?{href:n.href,target:n.target}:!0===h.value?{href:v.value.href,target:n.target}:{})),y=(0,r.Fl)((()=>{if(!1===h.value)return null;const{matched:e}=v.value,{length:t}=e,n=e[t-1];if(void 0===n)return-1;const r=u.$route.matched;if(0===r.length)return-1;const o=r.findIndex(l.bind(null,n));if(o>-1)return o;const i=a(e[t-2]);return t>1&&a(n)===i&&r[r.length-1].path!==i?r.findIndex(l.bind(null,e[t-2])):o})),w=(0,r.Fl)((()=>!0===h.value&&y.value>-1&&s(u.$route.params,v.value.params))),x=(0,r.Fl)((()=>!0===w.value&&y.value===u.$route.matched.length-1&&d(u.$route.params,v.value.params))),_=(0,r.Fl)((()=>!0===h.value?!0===x.value?` ${n.exactActiveClass} ${n.activeClass}`:!0===n.exact?"":!0===w.value?` ${n.activeClass}`:"":""));function k(e){return!(!0===n.disable||e.metaKey||e.altKey||e.ctrlKey||e.shiftKey||!0!==e.__qNavigate&&!0===e.defaultPrevented||void 0!==e.button&&0!==e.button||"_blank"===n.target)&&((0,o.X$)(e),u.$router[!0===n.replace?"replace":"push"](n.to).catch((()=>{})))}return{hasRouterLink:h,hasHrefLink:f,hasLink:g,linkTag:m,linkRoute:v,linkIsActive:w,linkIsExactActive:x,linkClass:_,linkProps:b,navigateToRouterLink:k}}},2417:(e,t,n)=>{"use strict";n.d(t,{Ok:()=>o,LU:()=>i,ZP:()=>a});var r=n(3673);const o={xs:18,sm:24,md:32,lg:38,xl:46},i={size:String};function a(e,t=o){return(0,r.Fl)((()=>void 0!==e.size?{fontSize:e.size in t?`${t[e.size]}px`:e.size}:null))}},416:(e,t,n)=>{"use strict";n.d(t,{Z:()=>o});var r=n(3673);function o(){let e;return(0,r.Jd)((()=>{e=void 0})),{registerTick(t){e=t,(0,r.Y3)((()=>{e===t&&(e(),e=void 0)}))},removeTick(){e=void 0}}}},4955:(e,t,n)=>{"use strict";n.d(t,{Z:()=>o});var r=n(3673);function o(){let e;return(0,r.Jd)((()=>{clearTimeout(e)})),{registerTimeout(t,n){clearTimeout(e),e=setTimeout(t,n)},removeTimeout(){clearTimeout(e)}}}},860:(e,t,n)=>{"use strict";n.d(t,{Z:()=>c});var r=n(908),o=n(2012),i=n(4716),a=n(1436),l=n(3317);function s(e,t,n,r){!0===n.modifiers.stop&&(0,i.sT)(e);const a=n.modifiers.color;let l=n.modifiers.center;l=!0===l||!0===r;const s=document.createElement("span"),u=document.createElement("span"),c=(0,i.FK)(e),{left:d,top:f,width:p,height:v}=t.getBoundingClientRect(),h=Math.sqrt(p*p+v*v),g=h/2,m=(p-h)/2+"px",b=l?m:c.left-d-g+"px",y=(v-h)/2+"px",w=l?y:c.top-f-g+"px";u.className="q-ripple__inner",(0,o.iv)(u,{height:`${h}px`,width:`${h}px`,transform:`translate3d(${b},${w},0) scale3d(.2,.2,1)`,opacity:0}),s.className="q-ripple"+(a?" text-"+a:""),s.setAttribute("dir","ltr"),s.appendChild(u),t.appendChild(s);const x=()=>{s.remove(),clearTimeout(_)};n.abort.push(x);let _=setTimeout((()=>{u.classList.add("q-ripple__inner--enter"),u.style.transform=`translate3d(${m},${y},0) scale3d(1,1,1)`,u.style.opacity=.2,_=setTimeout((()=>{u.classList.remove("q-ripple__inner--enter"),u.classList.add("q-ripple__inner--leave"),u.style.opacity=0,_=setTimeout((()=>{s.remove(),n.abort.splice(n.abort.indexOf(x),1)}),275)}),250)}),50)}function u(e,{modifiers:t,value:n,arg:r,instance:o}){const i=Object.assign({},o.$q.config.ripple,t,n);e.modifiers={early:!0===i.early,stop:!0===i.stop,center:!0===i.center,color:i.color||r,keyCodes:[].concat(i.keyCodes||13)}}const c=(0,r.f)({name:"ripple",beforeMount(e,t){const n={enabled:!1!==t.value,modifiers:{},abort:[],start(t){!0===n.enabled&&!0!==t.qSkipRipple&&(!0===n.modifiers.early?!0===["mousedown","touchstart"].includes(t.type):"click"===t.type)&&s(t,e,n,!0===t.qKeyEvent)},keystart:(0,l.Z)((t=>{!0===n.enabled&&!0!==t.qSkipRipple&&!0===(0,a.So)(t,n.modifiers.keyCodes)&&t.type==="key"+(!0===n.modifiers.early?"down":"up")&&s(t,e,n,!0)}),300)};u(n,t),e.__qripple=n,(0,i.M0)(n,"main",[[e,"mousedown","start","passive"],[e,"touchstart","start","passive"],[e,"click","start","passive"],[e,"keydown","keystart","passive"],[e,"keyup","keystart","passive"]])},updated(e,t){if(t.oldValue!==t.value){const n=e.__qripple;n.enabled=!1!==t.value,!0===n.enabled&&Object(t.value)===t.value&&u(n,t)}},beforeUnmount(e){const t=e.__qripple;t.abort.forEach((e=>{e()})),(0,i.ul)(t,"main"),delete e._qripple}})},5777:(e,t,n)=>{"use strict";n.d(t,{Z:()=>c});var r=n(4688),o=n(908),i=n(6104),a=n(4716),l=n(9725);function s(e,t,n){const r=(0,a.FK)(e);let o,i=r.left-t.event.x,l=r.top-t.event.y,s=Math.abs(i),u=Math.abs(l);const c=t.direction;!0===c.horizontal&&!0!==c.vertical?o=i<0?"left":"right":!0!==c.horizontal&&!0===c.vertical?o=l<0?"up":"down":!0===c.up&&l<0?(o="up",s>u&&(!0===c.left&&i<0?o="left":!0===c.right&&i>0&&(o="right"))):!0===c.down&&l>0?(o="down",s>u&&(!0===c.left&&i<0?o="left":!0===c.right&&i>0&&(o="right"))):!0===c.left&&i<0?(o="left",s<u&&(!0===c.up&&l<0?o="up":!0===c.down&&l>0&&(o="down"))):!0===c.right&&i>0&&(o="right",s<u&&(!0===c.up&&l<0?o="up":!0===c.down&&l>0&&(o="down")));let d=!1;if(void 0===o&&!1===n){if(!0===t.event.isFirst||void 0===t.event.lastDir)return{};o=t.event.lastDir,d=!0,"left"===o||"right"===o?(r.left-=i,s=0,i=0):(r.top-=l,u=0,l=0)}return{synthetic:d,payload:{evt:e,touch:!0!==t.event.mouse,mouse:!0===t.event.mouse,position:r,direction:o,isFirst:t.event.isFirst,isFinal:!0===n,duration:Date.now()-t.event.time,distance:{x:s,y:u},offset:{x:i,y:l},delta:{x:r.left-t.event.lastX,y:r.top-t.event.lastY}}}}let u=0;const c=(0,o.f)({name:"touch-pan",beforeMount(e,{value:t,modifiers:n}){if(!0!==n.mouse&&!0!==r.Lp.has.touch)return;function o(e,t){!0===n.mouse&&!0===t?(0,a.NS)(e):(!0===n.stop&&(0,a.sT)(e),!0===n.prevent&&(0,a.X$)(e))}const c={uid:"qvtp_"+u++,handler:t,modifiers:n,direction:(0,i.R)(n),noop:a.ZT,mouseStart(e){(0,i.n)(e,c)&&(0,a.du)(e)&&((0,a.M0)(c,"temp",[[document,"mousemove","move","notPassiveCapture"],[document,"mouseup","end","passiveCapture"]]),c.start(e,!0))},touchStart(e){if((0,i.n)(e,c)){const t=e.target;(0,a.M0)(c,"temp",[[t,"touchmove","move","notPassiveCapture"],[t,"touchcancel","end","passiveCapture"],[t,"touchend","end","passiveCapture"]]),c.start(e)}},start(t,o){if(!0===r.Lp.is.firefox&&(0,a.Jf)(e,!0),c.lastEvt=t,!0===o||!0===n.stop){if(!0!==c.direction.all&&(!0!==o||!0!==c.modifiers.mouseAllDir)){const e=t.type.indexOf("mouse")>-1?new MouseEvent(t.type,t):new TouchEvent(t.type,t);!0===t.defaultPrevented&&(0,a.X$)(e),!0===t.cancelBubble&&(0,a.sT)(e),Object.assign(e,{qKeyEvent:t.qKeyEvent,qClickOutside:t.qClickOutside,qAnchorHandled:t.qAnchorHandled,qClonedBy:void 0===t.qClonedBy?[c.uid]:t.qClonedBy.concat(c.uid)}),c.initialEvent={target:t.target,event:e}}(0,a.sT)(t)}const{left:i,top:l}=(0,a.FK)(t);c.event={x:i,y:l,time:Date.now(),mouse:!0===o,detected:!1,isFirst:!0,isFinal:!1,lastX:i,lastY:l}},move(e){if(void 0===c.event)return;const t=(0,a.FK)(e),r=t.left-c.event.x,i=t.top-c.event.y;if(0===r&&0===i)return;c.lastEvt=e;const u=!0===c.event.mouse,d=()=>{o(e,u),!0!==n.preserveCursor&&(document.documentElement.style.cursor="grabbing"),!0===u&&document.body.classList.add("no-pointer-events--children"),document.body.classList.add("non-selectable"),(0,l.M)(),c.styleCleanup=e=>{if(c.styleCleanup=void 0,!0!==n.preserveCursor&&(document.documentElement.style.cursor=""),document.body.classList.remove("non-selectable"),!0===u){const t=()=>{document.body.classList.remove("no-pointer-events--children")};void 0!==e?setTimeout((()=>{t(),e()}),50):t()}else void 0!==e&&e()}};if(!0===c.event.detected){!0!==c.event.isFirst&&o(e,c.event.mouse);const{payload:t,synthetic:n}=s(e,c,!1);return void(void 0!==t&&(!1===c.handler(t)?c.end(e):(void 0===c.styleCleanup&&!0===c.event.isFirst&&d(),c.event.lastX=t.position.left,c.event.lastY=t.position.top,c.event.lastDir=!0===n?void 0:t.direction,c.event.isFirst=!1)))}if(!0===c.direction.all||!0===u&&!0===c.modifiers.mouseAllDir)return d(),c.event.detected=!0,void c.move(e);const f=Math.abs(r),p=Math.abs(i);f!==p&&(!0===c.direction.horizontal&&f>p||!0===c.direction.vertical&&f<p||!0===c.direction.up&&f<p&&i<0||!0===c.direction.down&&f<p&&i>0||!0===c.direction.left&&f>p&&r<0||!0===c.direction.right&&f>p&&r>0?(c.event.detected=!0,c.move(e)):c.end(e,!0))},end(t,n){if(void 0!==c.event){if((0,a.ul)(c,"temp"),!0===r.Lp.is.firefox&&(0,a.Jf)(e,!1),!0===n)void 0!==c.styleCleanup&&c.styleCleanup(),!0!==c.event.detected&&void 0!==c.initialEvent&&c.initialEvent.target.dispatchEvent(c.initialEvent.event);else if(!0===c.event.detected){!0===c.event.isFirst&&c.handler(s(void 0===t?c.lastEvt:t,c).payload);const{payload:e}=s(void 0===t?c.lastEvt:t,c,!0),n=()=>{c.handler(e)};void 0!==c.styleCleanup?c.styleCleanup(n):n()}c.event=void 0,c.initialEvent=void 0,c.lastEvt=void 0}}};e.__qtouchpan=c,!0===n.mouse&&(0,a.M0)(c,"main",[[e,"mousedown","mouseStart","passive"+(!0===n.mouseCapture?"Capture":"")]]),!0===r.Lp.has.touch&&(0,a.M0)(c,"main",[[e,"touchstart","touchStart","passive"+(!0===n.capture?"Capture":"")],[e,"touchmove","noop","notPassiveCapture"]])},updated(e,t){const n=e.__qtouchpan;void 0!==n&&(t.oldValue!==t.value&&("function"!==typeof value&&n.end(),n.handler=t.value),n.direction=(0,i.R)(t.modifiers))},beforeUnmount(e){const t=e.__qtouchpan;void 0!==t&&(void 0!==t.event&&t.end(),(0,a.ul)(t,"main"),(0,a.ul)(t,"temp"),!0===r.Lp.is.firefox&&(0,a.Jf)(e,!1),void 0!==t.styleCleanup&&t.styleCleanup(),delete e.__qtouchpan)}})},6583:(e,t,n)=>{"use strict";n.d(t,{Z:()=>u});n(71);var r=n(4688),o=n(4716);const i=()=>!0;function a(e){return"string"===typeof e&&""!==e&&"/"!==e&&"#/"!==e}function l(e){return!0===e.startsWith("#")&&(e=e.substr(1)),!1===e.startsWith("/")&&(e="/"+e),!0===e.endsWith("/")&&(e=e.substr(0,e.length-1)),"#"+e}function s(e){if(!1===e.backButtonExit)return()=>!1;if("*"===e.backButtonExit)return i;const t=["#/"];return!0===Array.isArray(e.backButtonExit)&&t.push(...e.backButtonExit.filter(a).map(l)),()=>t.includes(window.location.hash)}const u={__history:[],add:o.ZT,remove:o.ZT,install({$q:e}){if(!0===this.__installed)return;const{cordova:t,capacitor:n}=r.Lp.is;if(!0!==t&&!0!==n)return;const o=e.config[!0===t?"cordova":"capacitor"];if(void 0!==o&&!1===o.backButton)return;if(!0===n&&(void 0===window.Capacitor||void 0===window.Capacitor.Plugins.App))return;this.add=e=>{void 0===e.condition&&(e.condition=i),this.__history.push(e)},this.remove=e=>{const t=this.__history.indexOf(e);t>=0&&this.__history.splice(t,1)};const a=s(Object.assign({backButtonExit:!0},o)),l=()=>{if(this.__history.length){const e=this.__history[this.__history.length-1];!0===e.condition()&&(this.__history.pop(),e.handler())}else!0===a()?navigator.app.exitApp():window.history.back()};!0===t?document.addEventListener("deviceready",(()=>{document.addEventListener("backbutton",l,!1)})):window.Capacitor.Plugins.App.addListener("backButton",l)}}},1845:(e,t,n)=>{"use strict";n.d(t,{Z:()=>l,F:()=>o});n(5363);var r=n(2002);const o={isoName:"en-US",nativeName:"English (US)",label:{clear:"Clear",ok:"OK",cancel:"Cancel",close:"Close",set:"Set",select:"Select",reset:"Reset",remove:"Remove",update:"Update",create:"Create",search:"Search",filter:"Filter",refresh:"Refresh"},date:{days:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),daysShort:"Sun_Mon_Tue_Wed_Thu_Fri_Sat".split("_"),months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),monthsShort:"Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec".split("_"),firstDayOfWeek:0,format24h:!1,pluralDay:"days"},table:{noData:"No data available",noResults:"No matching records found",loading:"Loading...",selectedRecords:e=>1===e?"1 record selected.":(0===e?"No":e)+" records selected.",recordsPerPage:"Records per page:",allRows:"All",pagination:(e,t,n)=>e+"-"+t+" of "+n,columns:"Columns"},editor:{url:"URL",bold:"Bold",italic:"Italic",strikethrough:"Strikethrough",underline:"Underline",unorderedList:"Unordered List",orderedList:"Ordered List",subscript:"Subscript",superscript:"Superscript",hyperlink:"Hyperlink",toggleFullscreen:"Toggle Fullscreen",quote:"Quote",left:"Left align",center:"Center align",right:"Right align",justify:"Justify align",print:"Print",outdent:"Decrease indentation",indent:"Increase indentation",removeFormat:"Remove formatting",formatting:"Formatting",fontSize:"Font Size",align:"Align",hr:"Insert Horizontal Rule",undo:"Undo",redo:"Redo",heading1:"Heading 1",heading2:"Heading 2",heading3:"Heading 3",heading4:"Heading 4",heading5:"Heading 5",heading6:"Heading 6",paragraph:"Paragraph",code:"Code",size1:"Very small",size2:"A bit small",size3:"Normal",size4:"Medium-large",size5:"Big",size6:"Very big",size7:"Maximum",defaultFont:"Default Font",viewSource:"View Source"},tree:{noNodes:"No nodes available",noResults:"No matching nodes found"}};function i(){const e=!0===Array.isArray(navigator.languages)&&navigator.languages.length>0?navigator.languages[0]:navigator.language;if("string"===typeof e)return e.split(/[-_]/).map(((e,t)=>0===t?e.toLowerCase():t>1||e.length<4?e.toUpperCase():e[0].toUpperCase()+e.slice(1).toLowerCase())).join("-")}const a=(0,r.Z)({__langPack:{}},{getLocale:i,set(e=o,t){const n={...e,rtl:!0===e.rtl,getLocale:i};{const e=document.documentElement;e.setAttribute("dir",!0===n.rtl?"rtl":"ltr"),e.setAttribute("lang",n.isoName),n.set=a.set,Object.assign(a.__langPack,n),a.props=n,a.isoName=n.isoName,a.nativeName=n.nativeName}},install({$q:e,lang:t,ssrContext:n}){e.lang=a.__langPack,!0===this.__installed?void 0!==t&&this.set(t):this.set(t||o)}}),l=a},4688:(e,t,n)=>{"use strict";n.d(t,{uX:()=>i,aG:()=>a,Lp:()=>h,ZP:()=>m});var r=n(1959),o=n(9085);const i=(0,r.iH)(!1);let a,l=!1;function s(e,t){const n=/(edge|edga|edgios)\/([\w.]+)/.exec(e)||/(opr)[\/]([\w.]+)/.exec(e)||/(vivaldi)[\/]([\w.]+)/.exec(e)||/(chrome|crios)[\/]([\w.]+)/.exec(e)||/(iemobile)[\/]([\w.]+)/.exec(e)||/(version)(applewebkit)[\/]([\w.]+).*(safari)[\/]([\w.]+)/.exec(e)||/(webkit)[\/]([\w.]+).*(version)[\/]([\w.]+).*(safari)[\/]([\w.]+)/.exec(e)||/(firefox|fxios)[\/]([\w.]+)/.exec(e)||/(webkit)[\/]([\w.]+)/.exec(e)||/(opera)(?:.*version|)[\/]([\w.]+)/.exec(e)||/(msie) ([\w.]+)/.exec(e)||e.indexOf("trident")>=0&&/(rv)(?::| )([\w.]+)/.exec(e)||e.indexOf("compatible")<0&&/(mozilla)(?:.*? rv:([\w.]+)|)/.exec(e)||[];return{browser:n[5]||n[3]||n[1]||"",version:n[2]||n[4]||"0",versionNumber:n[4]||n[2]||"0",platform:t[0]||""}}function u(e){return/(ipad)/.exec(e)||/(ipod)/.exec(e)||/(windows phone)/.exec(e)||/(iphone)/.exec(e)||/(kindle)/.exec(e)||/(silk)/.exec(e)||/(android)/.exec(e)||/(win)/.exec(e)||/(mac)/.exec(e)||/(linux)/.exec(e)||/(cros)/.exec(e)||/(playbook)/.exec(e)||/(bb)/.exec(e)||/(blackberry)/.exec(e)||[]}const c="ontouchstart"in window||window.navigator.maxTouchPoints>0;function d(e){a={is:{...e}},delete e.mac,delete e.desktop;const t=Math.min(window.innerHeight,window.innerWidth)>414?"ipad":"iphone";Object.assign(e,{mobile:!0,ios:!0,platform:t,[t]:!0})}function f(e){const t=e.toLowerCase(),n=u(t),r=s(t,n),o={};r.browser&&(o[r.browser]=!0,o.version=r.version,o.versionNumber=parseInt(r.versionNumber,10)),r.platform&&(o[r.platform]=!0);const i=o.android||o.ios||o.bb||o.blackberry||o.ipad||o.iphone||o.ipod||o.kindle||o.playbook||o.silk||o["windows phone"];return!0===i||t.indexOf("mobile")>-1?(o.mobile=!0,o.edga||o.edgios?(o.edge=!0,r.browser="edge"):o.crios?(o.chrome=!0,r.browser="chrome"):o.fxios&&(o.firefox=!0,r.browser="firefox")):o.desktop=!0,(o.ipod||o.ipad||o.iphone)&&(o.ios=!0),o["windows phone"]&&(o.winphone=!0,delete o["windows phone"]),(o.chrome||o.opr||o.safari||o.vivaldi||!0===o.mobile&&!0!==o.ios&&!0!==i)&&(o.webkit=!0),(o.safari&&o.blackberry||o.bb)&&(r.browser="blackberry",o.blackberry=!0),o.safari&&o.playbook&&(r.browser="playbook",o.playbook=!0),o.opr&&(r.browser="opera",o.opera=!0),o.safari&&o.android&&(r.browser="android",o.android=!0),o.safari&&o.kindle&&(r.browser="kindle",o.kindle=!0),o.safari&&o.silk&&(r.browser="silk",o.silk=!0),o.vivaldi&&(r.browser="vivaldi",o.vivaldi=!0),o.name=r.browser,o.platform=r.platform,t.indexOf("electron")>-1?o.electron=!0:document.location.href.indexOf("-extension://")>-1?o.bex=!0:(void 0!==window.Capacitor?(o.capacitor=!0,o.nativeMobile=!0,o.nativeMobileWrapper="capacitor"):void 0===window._cordovaNative&&void 0===window.cordova||(o.cordova=!0,o.nativeMobile=!0,o.nativeMobileWrapper="cordova"),!0===c&&!0===o.mac&&(!0===o.desktop&&!0===o.safari||!0===o.nativeMobile&&!0!==o.android&&!0!==o.ios&&!0!==o.ipad)&&d(o)),o}const p=navigator.userAgent||navigator.vendor||window.opera,v={has:{touch:!1,webStorage:!1},within:{iframe:!1}},h={userAgent:p,is:f(p),has:{touch:c},within:{iframe:window.self!==window.top}},g={install(e){const{$q:t}=e;!0===i.value?(e.onSSRHydrated.push((()=>{i.value=!1,Object.assign(t.platform,h),a=void 0})),t.platform=(0,r.qj)(this)):t.platform=this}};{let e;(0,o.g)(h.has,"webStorage",(()=>{if(void 0!==e)return e;try{if(window.localStorage)return e=!0,!0}catch(t){}return e=!1,!1})),l=!0===h.is.ios&&-1===window.navigator.vendor.toLowerCase().indexOf("apple"),!0===i.value?Object.assign(g,h,a,v):Object.assign(g,h)}const m=g},2156:(e,t,n)=>{"use strict";n.d(t,{vq:()=>o,Nn:()=>i,oo:()=>a,WE:()=>l,py:()=>s,id:()=>u,T2:()=>c});n(6701),n(5363);const r=/^rgb(a)?\((\d{1,3}),(\d{1,3}),(\d{1,3}),?([01]?\.?\d*?)?\)$/;function o({r:e,g:t,b:n,a:r}){const o=void 0!==r;if(e=Math.round(e),t=Math.round(t),n=Math.round(n),e>255||t>255||n>255||o&&r>100)throw new TypeError("Expected 3 numbers below 256 (and optionally one below 100)");return r=o?(256|Math.round(255*r/100)).toString(16).slice(1):"","#"+(n|t<<8|e<<16|1<<24).toString(16).slice(1)+r}function i({r:e,g:t,b:n,a:r}){return`rgb${void 0!==r?"a":""}(${e},${t},${n}${void 0!==r?","+r/100:""})`}function a(e){if("string"!==typeof e)throw new TypeError("Expected a string");e=e.replace(/^#/,""),3===e.length?e=e[0]+e[0]+e[1]+e[1]+e[2]+e[2]:4===e.length&&(e=e[0]+e[0]+e[1]+e[1]+e[2]+e[2]+e[3]+e[3]);const t=parseInt(e,16);return e.length>6?{r:t>>24&255,g:t>>16&255,b:t>>8&255,a:Math.round((255&t)/2.55)}:{r:t>>16,g:t>>8&255,b:255&t}}function l({h:e,s:t,v:n,a:r}){let o,i,a;t/=100,n/=100,e/=360;const l=Math.floor(6*e),s=6*e-l,u=n*(1-t),c=n*(1-s*t),d=n*(1-(1-s)*t);switch(l%6){case 0:o=n,i=d,a=u;break;case 1:o=c,i=n,a=u;break;case 2:o=u,i=n,a=d;break;case 3:o=u,i=c,a=n;break;case 4:o=d,i=u,a=n;break;case 5:o=n,i=u,a=c;break}return{r:Math.round(255*o),g:Math.round(255*i),b:Math.round(255*a),a:r}}function s({r:e,g:t,b:n,a:r}){const o=Math.max(e,t,n),i=Math.min(e,t,n),a=o-i,l=0===o?0:a/o,s=o/255;let u;switch(o){case i:u=0;break;case e:u=t-n+a*(t<n?6:0),u/=6*a;break;case t:u=n-e+2*a,u/=6*a;break;case n:u=e-t+4*a,u/=6*a;break}return{h:Math.round(360*u),s:Math.round(100*l),v:Math.round(100*s),a:r}}function u(e){if("string"!==typeof e)throw new TypeError("Expected a string");const t=e.replace(/ /g,""),n=r.exec(t);if(null===n)return a(t);const o={r:Math.min(255,parseInt(n[2],10)),g:Math.min(255,parseInt(n[3],10)),b:Math.min(255,parseInt(n[4],10))};if(n[1]){const e=parseFloat(n[5]);o.a=100*Math.min(1,!0===isNaN(e)?1:e)}return o}function c(e){if("string"!==typeof e&&(!e||void 0===e.r))throw new TypeError("Expected a string or a {r, g, b} object as color");const t="string"===typeof e?u(e):e,n=t.r/255,r=t.g/255,o=t.b/255,i=n<=.03928?n/12.92:Math.pow((n+.055)/1.055,2.4),a=r<=.03928?r/12.92:Math.pow((r+.055)/1.055,2.4),l=o<=.03928?o/12.92:Math.pow((o+.055)/1.055,2.4);return.2126*i+.7152*a+.0722*l}},9405:(e,t,n)=>{"use strict";function r(e,t=250,n){let r;function o(){const o=arguments,i=()=>{r=void 0,!0!==n&&e.apply(this,o)};clearTimeout(r),!0===n&&void 0===r&&e.apply(this,o),r=setTimeout(i,t)}return o.cancel=()=>{clearTimeout(r)},o}n.d(t,{Z:()=>r})},2012:(e,t,n)=>{"use strict";n.d(t,{iv:()=>o,sb:()=>i,mY:()=>a});var r=n(1959);function o(e,t){const n=e.style;for(const r in t)n[r]=t[r]}function i(e){if(void 0===e||null===e)return;if("string"===typeof e)try{return document.querySelector(e)||void 0}catch(n){return}const t=!0===(0,r.dq)(e)?e.value:e;return t?t.$el||t:void 0}function a(e,t){if(void 0===e||null===e||!0===e.contains(t))return!0;for(let n=e.nextElementSibling;null!==n;n=n.nextElementSibling)if(n.contains(t))return!0;return!1}},4716:(e,t,n)=>{"use strict";n.d(t,{rU:()=>r,ZT:()=>o,du:()=>i,FK:()=>a,AZ:()=>l,sT:()=>s,X$:()=>u,NS:()=>c,Jf:()=>d,M0:()=>f,ul:()=>p});n(71);const r={hasPassive:!1,passiveCapture:!0,notPassiveCapture:!0};try{const e=Object.defineProperty({},"passive",{get(){Object.assign(r,{hasPassive:!0,passive:{passive:!0},notPassive:{passive:!1},passiveCapture:{passive:!0,capture:!0},notPassiveCapture:{passive:!1,capture:!0}})}});window.addEventListener("qtest",null,e),window.removeEventListener("qtest",null,e)}catch(v){}function o(){}function i(e){return 0===e.button}function a(e){return e.touches&&e.touches[0]?e=e.touches[0]:e.changedTouches&&e.changedTouches[0]?e=e.changedTouches[0]:e.targetTouches&&e.targetTouches[0]&&(e=e.targetTouches[0]),{top:e.clientY,left:e.clientX}}function l(e){if(e.path)return e.path;if(e.composedPath)return e.composedPath();const t=[];let n=e.target;while(n){if(t.push(n),"HTML"===n.tagName)return t.push(document),t.push(window),t;n=n.parentElement}}function s(e){e.stopPropagation()}function u(e){!1!==e.cancelable&&e.preventDefault()}function c(e){!1!==e.cancelable&&e.preventDefault(),e.stopPropagation()}function d(e,t){if(void 0===e||!0===t&&!0===e.__dragPrevented)return;const n=!0===t?e=>{e.__dragPrevented=!0,e.addEventListener("dragstart",u,r.notPassiveCapture)}:e=>{delete e.__dragPrevented,e.removeEventListener("dragstart",u,r.notPassiveCapture)};e.querySelectorAll("a, img").forEach(n)}function f(e,t,n){const o=`__q_${t}_evt`;e[o]=void 0!==e[o]?e[o].concat(n):n,n.forEach((t=>{t[0].addEventListener(t[1],e[t[2]],r[t[3]])}))}function p(e,t){const n=`__q_${t}_evt`;void 0!==e[n]&&(e[n].forEach((t=>{t[0].removeEventListener(t[1],e[t[2]],r[t[3]])})),e[n]=void 0)}},2130:(e,t,n)=>{"use strict";n.d(t,{kC:()=>r,vX:()=>o,vk:()=>i});function r(e){return e.charAt(0).toUpperCase()+e.slice(1)}function o(e,t,n){return n<=t?t:Math.min(n,Math.max(t,e))}function i(e,t=2,n="0"){if(void 0===e||null===e)return e;const r=""+e;return r.length>=t?r:new Array(t-r.length+1).join(n)+r}},9763:(e,t,n)=>{"use strict";n.d(t,{E:()=>s});const r=/^#[0-9a-fA-F]{3}([0-9a-fA-F]{3})?$/,o=/^#[0-9a-fA-F]{4}([0-9a-fA-F]{4})?$/,i=/^#([0-9a-fA-F]{3}|[0-9a-fA-F]{4}|[0-9a-fA-F]{6}|[0-9a-fA-F]{8})$/,a=/^rgb\(((0|[1-9][\d]?|1[\d]{0,2}|2[\d]?|2[0-4][\d]|25[0-5]),){2}(0|[1-9][\d]?|1[\d]{0,2}|2[\d]?|2[0-4][\d]|25[0-5])\)$/,l=/^rgba\(((0|[1-9][\d]?|1[\d]{0,2}|2[\d]?|2[0-4][\d]|25[0-5]),){2}(0|[1-9][\d]?|1[\d]{0,2}|2[\d]?|2[0-4][\d]|25[0-5]),(0|0\.[0-9]+[1-9]|0\.[1-9]+|1)\)$/,s={date:e=>/^-?[\d]+\/[0-1]\d\/[0-3]\d$/.test(e),time:e=>/^([0-1]?\d|2[0-3]):[0-5]\d$/.test(e),fulltime:e=>/^([0-1]?\d|2[0-3]):[0-5]\d:[0-5]\d$/.test(e),timeOrFulltime:e=>/^([0-1]?\d|2[0-3]):[0-5]\d(:[0-5]\d)?$/.test(e),hexColor:e=>r.test(e),hexaColor:e=>o.test(e),hexOrHexaColor:e=>i.test(e),rgbColor:e=>a.test(e),rgbaColor:e=>l.test(e),rgbOrRgbaColor:e=>a.test(e)||l.test(e),hexOrRgbColor:e=>r.test(e)||a.test(e),hexaOrRgbaColor:e=>o.test(e)||l.test(e),anyColor:e=>i.test(e)||a.test(e)||l.test(e)}},908:(e,t,n)=>{"use strict";n.d(t,{L:()=>i,f:()=>a});var r=n(1959),o=n(3673);const i=e=>(0,r.Xl)((0,o.aZ)(e)),a=e=>(0,r.Xl)(e)},2002:(e,t,n)=>{"use strict";n.d(t,{Z:()=>i});var r=n(1959),o=n(9085);const i=(e,t)=>{const n=(0,r.qj)(e);for(const r in e)(0,o.g)(t,r,(()=>n[r]),(e=>{n[r]=e}));return t}},230:(e,t,n)=>{"use strict";n.d(t,{YX:()=>a,xF:()=>l,jd:()=>s,fP:()=>u});let r=[],o=[];function i(e){o=o.filter((t=>t!==e))}function a(e){i(e),o.push(e)}function l(e){i(e),0===o.length&&r.length>0&&(r[r.length-1](),r=[])}function s(e){0===o.length?e():r.push(e)}function u(e){r=r.filter((t=>t!==e))}},5578:(e,t,n)=>{"use strict";n.d(t,{w6:()=>r,Uf:()=>o,tP:()=>i});const r={};let o=!1;function i(){o=!0}},9085:(e,t,n)=>{"use strict";function r(e,t,n,r){Object.defineProperty(e,t,{get:n,set:r,enumerable:!0})}n.d(t,{g:()=>r})},782:(e,t,n)=>{"use strict";n.d(t,{J_:()=>r,hj:()=>o});n(71),n(979);function r(e){return"[object Date]"===Object.prototype.toString.call(e)}function o(e){return"number"===typeof e&&isFinite(e)}},1436:(e,t,n)=>{"use strict";n.d(t,{ZK:()=>o,Wm:()=>i,So:()=>a});let r=!1;function o(e){r=!0===e.isComposing}function i(e){return!0===r||e!==Object(e)||!0===e.isComposing||!0===e.qKeyEvent}function a(e,t){return!0!==i(e)&&[].concat(t).includes(e.keyCode)}},7657:(e,t,n)=>{"use strict";n.d(t,{KR:()=>o,Bl:()=>i,vs:()=>a,pf:()=>l,Jl:()=>s});var r=n(3673);function o(e,t){return void 0!==e&&e()||t}function i(e,t){if(void 0!==e){const t=e();if(void 0!==t&&null!==t)return t.slice()}return t}function a(e,t){return void 0!==e?t.concat(e()):t}function l(e,t){return void 0===e?t:void 0!==t?t.concat(e()):e()}function s(e,t,n,o,i,a){t.key=o+i;const l=(0,r.h)(e,t,n);return!0===i?(0,r.wy)(l,a()):l}},9725:(e,t,n)=>{"use strict";n.d(t,{M:()=>o});var r=n(4688);function o(){if(void 0!==window.getSelection){const e=window.getSelection();void 0!==e.empty?e.empty():void 0!==e.removeAllRanges&&(e.removeAllRanges(),!0!==r.ZP.is.mobile&&e.addRange(document.createRange()))}else void 0!==document.selection&&document.selection.empty()}},2547:(e,t,n)=>{"use strict";n.d(t,{Ng:()=>r,vh:()=>o,Nd:()=>i});const r="_q_",o="_q_fo_",i="_q_tabs_"},6104:(e,t,n)=>{"use strict";n.d(t,{R:()=>i,n:()=>a});n(71);const r={left:!0,right:!0,up:!0,down:!0,horizontal:!0,vertical:!0},o=Object.keys(r);function i(e){const t={};for(const n of o)!0===e[n]&&(t[n]=!0);return 0===Object.keys(t).length?r:(!0===t.horizontal?t.left=t.right=!0:!0===t.left&&!0===t.right&&(t.horizontal=!0),!0===t.vertical?t.up=t.down=!0:!0===t.up&&!0===t.down&&(t.vertical=!0),!0===t.horizontal&&!0===t.vertical&&(t.all=!0),t)}function a(e,t){return void 0===t.event&&void 0!==e.target&&!0!==e.target.draggable&&"function"===typeof t.handler&&"INPUT"!==e.target.nodeName.toUpperCase()&&(void 0===e.qClonedBy||-1===e.qClonedBy.indexOf(t.uid))}r.all=!0},7445:(e,t,n)=>{"use strict";n.d(t,{Kq:()=>r,Pf:()=>o,Rb:()=>i});n(71);function r(e){if(Object(e.$parent)===e.$parent)return e.$parent;e=e.$.parent;while(Object(e)===e){if(Object(e.proxy)===e.proxy)return e.proxy;e=e.parent}}function o(e){const t=new Set;return e.forEach((e=>{"symbol"===typeof e.type&&!0===Array.isArray(e.children)?e.children.forEach((e=>{t.add(e)})):t.add(e)})),Array.from(t)}function i(e){return void 0!==e.appContext.config.globalProperties.$router}},8400:(e,t,n)=>{"use strict";n.d(t,{b0:()=>i,u3:()=>a,OI:()=>l,np:()=>u,QA:()=>c});var r=n(2012);const o=[null,document,document.body,document.scrollingElement,document.documentElement];function i(e,t){let n=(0,r.sb)(t);if(void 0===n){if(void 0===e||null===e)return window;n=e.closest(".scroll,.scroll-y,.overflow-auto")}return o.includes(n)?window:n}function a(e){return e===window?window.pageYOffset||window.scrollY||document.body.scrollTop||0:e.scrollTop}function l(e){return e===window?window.pageXOffset||window.scrollX||document.body.scrollLeft||0:e.scrollLeft}let s;function u(){if(void 0!==s)return s;const e=document.createElement("p"),t=document.createElement("div");(0,r.iv)(e,{width:"100%",height:"200px"}),(0,r.iv)(t,{position:"absolute",top:"0px",left:"0px",visibility:"hidden",width:"200px",height:"150px",overflow:"hidden"}),t.appendChild(e),document.body.appendChild(t);const n=e.offsetWidth;t.style.overflow="scroll";let o=e.offsetWidth;return n===o&&(o=t.clientWidth),t.remove(),s=n-o,s}function c(e,t=!0){return!(!e||e.nodeType!==Node.ELEMENT_NODE)&&(t?e.scrollHeight>e.clientHeight&&(e.classList.contains("scroll")||e.classList.contains("overflow-auto")||["auto","scroll"].includes(window.getComputedStyle(e)["overflow-y"])):e.scrollWidth>e.clientWidth&&(e.classList.contains("scroll")||e.classList.contains("overflow-auto")||["auto","scroll"].includes(window.getComputedStyle(e)["overflow-x"])))}},4398:(e,t,n)=>{"use strict";n.d(t,{Z:()=>r});n(6701);function r(e,t,n=document.body){if("string"!==typeof e)throw new TypeError("Expected a string as propName");if("string"!==typeof t)throw new TypeError("Expected a string as value");if(!(n instanceof Element))throw new TypeError("Expected a DOM element");n.style.setProperty(`--q-${e}`,t)}},3317:(e,t,n)=>{"use strict";function r(e,t=250){let n,r=!1;return function(){return!1===r&&(r=!0,setTimeout((()=>{r=!1}),t),n=e.apply(this,arguments)),n}}n.d(t,{Z:()=>r})},8834:(e,t,n)=>{"use strict";n.d(t,{Z:()=>T});var r=n(4688),o=n(2002),i=n(4716),a=n(9405);const l=["sm","md","lg","xl"],{passive:s}=i.rU,u=(0,o.Z)({width:0,height:0,name:"xs",sizes:{sm:600,md:1024,lg:1440,xl:1920},lt:{sm:!0,md:!0,lg:!0,xl:!0},gt:{xs:!1,sm:!1,md:!1,lg:!1},xs:!0,sm:!1,md:!1,lg:!1,xl:!1},{setSizes:i.ZT,setDebounce:i.ZT,install({$q:e,onSSRHydrated:t}){if(e.screen=this,!0===this.__installed)return void(void 0!==e.config.screen&&(!1===e.config.screen.bodyClasses?document.body.classList.remove(`screen--${this.name}`):this.__update(!0)));const n=void 0!==e.config.screen&&!0===e.config.screen.bodyClasses;this.__update=e=>{const t=window.innerWidth,r=window.innerHeight;if(r!==this.height&&(this.height=r),t!==this.width)this.width=t;else if(!0!==e)return;let o=this.sizes;this.gt.xs=t>=o.sm,this.gt.sm=t>=o.md,this.gt.md=t>=o.lg,this.gt.lg=t>=o.xl,this.lt.sm=t<o.sm,this.lt.md=t<o.md,this.lt.lg=t<o.lg,this.lt.xl=t<o.xl,this.xs=this.lt.sm,this.sm=!0===this.gt.xs&&!0===this.lt.md,this.md=!0===this.gt.sm&&!0===this.lt.lg,this.lg=!0===this.gt.md&&!0===this.lt.xl,this.xl=this.gt.lg,o=(!0===this.xs?"xs":!0===this.sm&&"sm")||!0===this.md&&"md"||!0===this.lg&&"lg"||"xl",o!==this.name&&(!0===n&&(document.body.classList.remove(`screen--${this.name}`),document.body.classList.add(`screen--${o}`)),this.name=o)};let o,i={},u=16;this.setSizes=e=>{l.forEach((t=>{void 0!==e[t]&&(i[t]=e[t])}))},this.setDebounce=e=>{u=e};const c=()=>{const e=getComputedStyle(document.body),t=void 0!==window.visualViewport?window.visualViewport:window;e.getPropertyValue("--q-size-sm")&&l.forEach((t=>{this.sizes[t]=parseInt(e.getPropertyValue(`--q-size-${t}`),10)})),this.setSizes=e=>{l.forEach((t=>{e[t]&&(this.sizes[t]=e[t])})),this.__update(!0)},this.setDebounce=e=>{void 0!==o&&t.removeEventListener("resize",o,s),o=e>0?(0,a.Z)(this.__update,e):this.__update,t.addEventListener("resize",o,s)},this.setDebounce(u),Object.keys(i).length>0?(this.setSizes(i),i=void 0):this.__update(),!0===n&&"xs"===this.name&&document.body.classList.add("screen--xs")};!0===r.uX.value?t.push(c):c()}});n(5363);const c=(0,o.Z)({isActive:!1,mode:!1},{__media:void 0,set(e){c.mode=e,"auto"===e?(void 0===c.__media&&(c.__media=window.matchMedia("(prefers-color-scheme: dark)"),c.__updateMedia=()=>{c.set("auto")},c.__media.addListener(c.__updateMedia)),e=c.__media.matches):void 0!==c.__media&&(c.__media.removeListener(c.__updateMedia),c.__media=void 0),c.isActive=!0===e,document.body.classList.remove("body--"+(!0===e?"light":"dark")),document.body.classList.add("body--"+(!0===e?"dark":"light"))},toggle(){c.set(!1===c.isActive)},install({$q:e,onSSRHydrated:t,ssrContext:n}){const{dark:o}=e.config;if(e.dark=this,!0===this.__installed&&void 0===o)return;this.isActive=!0===o;const i=void 0!==o&&o;if(!0===r.uX.value){const e=e=>{this.__fromSSR=e},n=this.set;this.set=e,e(i),t.push((()=>{this.set=n,this.set(this.__fromSSR)}))}else this.set(i)}}),d=c;var f=n(6583),p=n(1845),v=n(4398),h=n(1436);function g(e){return!0===e.ios?"ios":!0===e.android?"android":void 0}function m({is:e,has:t,within:n},r){const o=[!0===e.desktop?"desktop":"mobile",(!1===t.touch?"no-":"")+"touch"];if(!0===e.mobile){const t=g(e);void 0!==t&&o.push("platform-"+t)}if(!0===e.nativeMobile){const t=e.nativeMobileWrapper;o.push(t),o.push("native-mobile"),!0!==e.ios||void 0!==r[t]&&!1===r[t].iosStatusBarPadding||o.push("q-ios-padding")}else!0===e.electron?o.push("electron"):!0===e.bex&&o.push("bex");return!0===n.iframe&&o.push("within-iframe"),o}function b(){const e=document.body.className;let t=e;void 0!==r.aG&&(t=t.replace("desktop","platform-ios mobile")),!0===r.Lp.has.touch&&(t=t.replace("no-touch","touch")),!0===r.Lp.within.iframe&&(t+=" within-iframe"),e!==t&&(document.body.className=t)}function y(e){for(const t in e)(0,v.Z)(t,e[t])}const w={install(e){if(!0!==this.__installed){if(!0===r.uX.value)b();else{const{$q:t}=e;void 0!==t.config.brand&&y(t.config.brand);const n=m(r.Lp,t.config);document.body.classList.add.apply(document.body.classList,n)}!0===r.Lp.is.ios&&document.body.addEventListener("touchstart",i.ZT),window.addEventListener("keydown",h.ZK,!0)}}},x={name:"material-icons",type:{positive:"check_circle",negative:"warning",info:"info",warning:"priority_high"},arrow:{up:"arrow_upward",right:"arrow_forward",down:"arrow_downward",left:"arrow_back",dropdown:"arrow_drop_down"},chevron:{left:"chevron_left",right:"chevron_right"},colorPicker:{spectrum:"gradient",tune:"tune",palette:"style"},pullToRefresh:{icon:"refresh"},carousel:{left:"chevron_left",right:"chevron_right",up:"keyboard_arrow_up",down:"keyboard_arrow_down",navigationIcon:"lens"},chip:{remove:"cancel",selected:"check"},datetime:{arrowLeft:"chevron_left",arrowRight:"chevron_right",now:"access_time",today:"today"},editor:{bold:"format_bold",italic:"format_italic",strikethrough:"strikethrough_s",underline:"format_underlined",unorderedList:"format_list_bulleted",orderedList:"format_list_numbered",subscript:"vertical_align_bottom",superscript:"vertical_align_top",hyperlink:"link",toggleFullscreen:"fullscreen",quote:"format_quote",left:"format_align_left",center:"format_align_center",right:"format_align_right",justify:"format_align_justify",print:"print",outdent:"format_indent_decrease",indent:"format_indent_increase",removeFormat:"format_clear",formatting:"text_format",fontSize:"format_size",align:"format_align_left",hr:"remove",undo:"undo",redo:"redo",heading:"format_size",code:"code",size:"format_size",font:"font_download",viewSource:"code"},expansionItem:{icon:"keyboard_arrow_down",denseIcon:"arrow_drop_down"},fab:{icon:"add",activeIcon:"close"},field:{clear:"cancel",error:"error"},pagination:{first:"first_page",prev:"keyboard_arrow_left",next:"keyboard_arrow_right",last:"last_page"},rating:{icon:"grade"},stepper:{done:"check",active:"edit",error:"warning"},tabs:{left:"chevron_left",right:"chevron_right",up:"keyboard_arrow_up",down:"keyboard_arrow_down"},table:{arrowUp:"arrow_upward",warning:"warning",firstPage:"first_page",prevPage:"chevron_left",nextPage:"chevron_right",lastPage:"last_page"},tree:{icon:"play_arrow"},uploader:{done:"done",clear:"clear",add:"add_box",upload:"cloud_upload",removeQueue:"clear_all",removeUploaded:"done_all"}};var _=n(9085);const k=(0,o.Z)({iconMapFn:null,__icons:{}},{set(e,t){const n={...e,rtl:!0===e.rtl};n.set=k.set,Object.assign(k.__icons,n)},install({$q:e,iconSet:t,ssrContext:n}){void 0!==e.config.iconMapFn&&(this.iconMapFn=e.config.iconMapFn),e.iconSet=this.__icons,(0,_.g)(e,"iconMapFn",(()=>this.iconMapFn),(e=>{this.iconMapFn=e})),!0===this.__installed?void 0!==t&&this.set(t):this.set(t||x)}}),S=k;var C=n(2547),F=n(5578);const E=[r.ZP,w,d,u,f.Z,p.Z,S];function R(e,t){t.forEach((t=>{t.install(e),t.__installed=!0}))}function A(e,t,n){e.config.globalProperties.$q=n.$q,e.provide(C.Ng,n.$q),R(n,E),void 0!==t.components&&Object.values(t.components).forEach((t=>{Object(t)===t&&void 0!==t.name&&e.component(t.name,t)})),void 0!==t.directives&&Object.values(t.directives).forEach((t=>{Object(t)===t&&void 0!==t.name&&e.directive(t.name,t)})),void 0!==t.plugins&&R(n,Object.values(t.plugins).filter((e=>"function"===typeof e.install&&!1===E.includes(e)))),!0===r.uX.value&&(n.$q.onSSRHydrated=()=>{n.onSSRHydrated.forEach((e=>{e()})),n.$q.onSSRHydrated=()=>{}})}const q=function(e,t={}){const n={version:"2.4.10"};!1===F.Uf?(void 0!==t.config&&Object.assign(F.w6,t.config),n.config={...F.w6},(0,F.tP)()):n.config=t.config||{},A(e,t,{parentApp:e,$q:n,lang:t.lang,iconSet:t.iconSet,onSSRHydrated:[]})},T={version:"2.4.10",install:q,lang:p.Z,iconSet:S}},7083:e=>{e.exports.BC=function(e){return e}},392:(e,t,n)=>{var r=n(7358),o=n(419),i=n(3353),a=r.TypeError;e.exports=function(e){if(o(e))return e;throw a(i(e)+" is not a function")}},2722:(e,t,n)=>{var r=n(7358),o=n(7593),i=n(3353),a=r.TypeError;e.exports=function(e){if(o(e))return e;throw a(i(e)+" is not a constructor")}},8248:(e,t,n)=>{var r=n(7358),o=n(419),i=r.String,a=r.TypeError;e.exports=function(e){if("object"==typeof e||o(e))return e;throw a("Can't set "+i(e)+" as a prototype")}},2852:(e,t,n)=>{var r=n(854),o=n(1074),i=n(928),a=r("unscopables"),l=Array.prototype;void 0==l[a]&&i.f(l,a,{configurable:!0,value:o(null)}),e.exports=function(e){l[a][e]=!0}},6412:(e,t,n)=>{"use strict";var r=n(1021).charAt;e.exports=function(e,t,n){return t+(n?r(e,t).length:1)}},2827:(e,t,n)=>{var r=n(7358),o=n(7673),i=r.TypeError;e.exports=function(e,t){if(o(t,e))return e;throw i("Incorrect invocation")}},7950:(e,t,n)=>{var r=n(7358),o=n(776),i=r.String,a=r.TypeError;e.exports=function(e){if(o(e))return e;throw a(i(e)+" is not an object")}},6257:e=>{e.exports="undefined"!=typeof ArrayBuffer&&"undefined"!=typeof DataView},683:(e,t,n)=>{"use strict";var r,o,i,a=n(6257),l=n(9631),s=n(7358),u=n(419),c=n(776),d=n(7322),f=n(5976),p=n(3353),v=n(1904),h=n(298),g=n(928).f,m=n(7673),b=n(4945),y=n(6184),w=n(854),x=n(6862),_=s.Int8Array,k=_&&_.prototype,S=s.Uint8ClampedArray,C=S&&S.prototype,F=_&&b(_),E=k&&b(k),R=Object.prototype,A=s.TypeError,q=w("toStringTag"),T=x("TYPED_ARRAY_TAG"),L=x("TYPED_ARRAY_CONSTRUCTOR"),P=a&&!!y&&"Opera"!==f(s.opera),O=!1,M={Int8Array:1,Uint8Array:1,Uint8ClampedArray:1,Int16Array:2,Uint16Array:2,Int32Array:4,Uint32Array:4,Float32Array:4,Float64Array:8},B={BigInt64Array:8,BigUint64Array:8},$=function(e){if(!c(e))return!1;var t=f(e);return"DataView"===t||d(M,t)||d(B,t)},I=function(e){if(!c(e))return!1;var t=f(e);return d(M,t)||d(B,t)},j=function(e){if(I(e))return e;throw A("Target is not a typed array")},N=function(e){if(u(e)&&(!y||m(F,e)))return e;throw A(p(e)+" is not a typed array constructor")},V=function(e,t,n,r){if(l){if(n)for(var o in M){var i=s[o];if(i&&d(i.prototype,e))try{delete i.prototype[e]}catch(a){try{i.prototype[e]=t}catch(u){}}}E[e]&&!n||h(E,e,n?t:P&&k[e]||t,r)}},U=function(e,t,n){var r,o;if(l){if(y){if(n)for(r in M)if(o=s[r],o&&d(o,e))try{delete o[e]}catch(i){}if(F[e]&&!n)return;try{return h(F,e,n?t:P&&F[e]||t)}catch(i){}}for(r in M)o=s[r],!o||o[e]&&!n||h(o,e,t)}};for(r in M)o=s[r],i=o&&o.prototype,i?v(i,L,o):P=!1;for(r in B)o=s[r],i=o&&o.prototype,i&&v(i,L,o);if((!P||!u(F)||F===Function.prototype)&&(F=function(){throw A("Incorrect invocation")},P))for(r in M)s[r]&&y(s[r],F);if((!P||!E||E===R)&&(E=F.prototype,P))for(r in M)s[r]&&y(s[r].prototype,E);if(P&&b(C)!==E&&y(C,E),l&&!d(E,q))for(r in O=!0,g(E,q,{get:function(){return c(this)?this[T]:void 0}}),M)s[r]&&v(s[r],T,r);e.exports={NATIVE_ARRAY_BUFFER_VIEWS:P,TYPED_ARRAY_CONSTRUCTOR:L,TYPED_ARRAY_TAG:O&&T,aTypedArray:j,aTypedArrayConstructor:N,exportTypedArrayMethod:V,exportTypedArrayStaticMethod:U,isView:$,isTypedArray:I,TypedArray:F,TypedArrayPrototype:E}},62:(e,t,n)=>{"use strict";var r=n(7358),o=n(1890),i=n(9631),a=n(6257),l=n(7961),s=n(1904),u=n(9833),c=n(6400),d=n(2827),f=n(1860),p=n(4068),v=n(833),h=n(8830),g=n(4945),m=n(6184),b=n(1454).f,y=n(928).f,w=n(5786),x=n(5771),_=n(1061),k=n(7624),S=l.PROPER,C=l.CONFIGURABLE,F=k.get,E=k.set,R="ArrayBuffer",A="DataView",q="prototype",T="Wrong length",L="Wrong index",P=r[R],O=P,M=O&&O[q],B=r[A],$=B&&B[q],I=Object.prototype,j=r.Array,N=r.RangeError,V=o(w),U=o([].reverse),H=h.pack,z=h.unpack,D=function(e){return[255&e]},Z=function(e){return[255&e,e>>8&255]},W=function(e){return[255&e,e>>8&255,e>>16&255,e>>24&255]},Y=function(e){return e[3]<<24|e[2]<<16|e[1]<<8|e[0]},J=function(e){return H(e,23,4)},K=function(e){return H(e,52,8)},X=function(e,t){y(e[q],t,{get:function(){return F(this)[t]}})},G=function(e,t,n,r){var o=v(n),i=F(e);if(o+t>i.byteLength)throw N(L);var a=F(i.buffer).bytes,l=o+i.byteOffset,s=x(a,l,l+t);return r?s:U(s)},Q=function(e,t,n,r,o,i){var a=v(n),l=F(e);if(a+t>l.byteLength)throw N(L);for(var s=F(l.buffer).bytes,u=a+l.byteOffset,c=r(+o),d=0;d<t;d++)s[u+d]=c[i?d:t-d-1]};if(a){var ee=S&&P.name!==R;if(c((function(){P(1)}))&&c((function(){new P(-1)}))&&!c((function(){return new P,new P(1.5),new P(NaN),ee&&!C})))ee&&C&&s(P,"name",R);else{O=function(e){return d(this,M),new P(v(e))},O[q]=M;for(var te,ne=b(P),re=0;ne.length>re;)(te=ne[re++])in O||s(O,te,P[te]);M.constructor=O}m&&g($)!==I&&m($,I);var oe=new B(new O(2)),ie=o($.setInt8);oe.setInt8(0,2147483648),oe.setInt8(1,2147483649),!oe.getInt8(0)&&oe.getInt8(1)||u($,{setInt8:function(e,t){ie(this,e,t<<24>>24)},setUint8:function(e,t){ie(this,e,t<<24>>24)}},{unsafe:!0})}else O=function(e){d(this,M);var t=v(e);E(this,{bytes:V(j(t),0),byteLength:t}),i||(this.byteLength=t)},M=O[q],B=function(e,t,n){d(this,$),d(e,M);var r=F(e).byteLength,o=f(t);if(o<0||o>r)throw N("Wrong offset");if(n=void 0===n?r-o:p(n),o+n>r)throw N(T);E(this,{buffer:e,byteLength:n,byteOffset:o}),i||(this.buffer=e,this.byteLength=n,this.byteOffset=o)},$=B[q],i&&(X(O,"byteLength"),X(B,"buffer"),X(B,"byteLength"),X(B,"byteOffset")),u($,{getInt8:function(e){return G(this,1,e)[0]<<24>>24},getUint8:function(e){return G(this,1,e)[0]},getInt16:function(e){var t=G(this,2,e,arguments.length>1?arguments[1]:void 0);return(t[1]<<8|t[0])<<16>>16},getUint16:function(e){var t=G(this,2,e,arguments.length>1?arguments[1]:void 0);return t[1]<<8|t[0]},getInt32:function(e){return Y(G(this,4,e,arguments.length>1?arguments[1]:void 0))},getUint32:function(e){return Y(G(this,4,e,arguments.length>1?arguments[1]:void 0))>>>0},getFloat32:function(e){return z(G(this,4,e,arguments.length>1?arguments[1]:void 0),23)},getFloat64:function(e){return z(G(this,8,e,arguments.length>1?arguments[1]:void 0),52)},setInt8:function(e,t){Q(this,1,e,D,t)},setUint8:function(e,t){Q(this,1,e,D,t)},setInt16:function(e,t){Q(this,2,e,Z,t,arguments.length>2?arguments[2]:void 0)},setUint16:function(e,t){Q(this,2,e,Z,t,arguments.length>2?arguments[2]:void 0)},setInt32:function(e,t){Q(this,4,e,W,t,arguments.length>2?arguments[2]:void 0)},setUint32:function(e,t){Q(this,4,e,W,t,arguments.length>2?arguments[2]:void 0)},setFloat32:function(e,t){Q(this,4,e,J,t,arguments.length>2?arguments[2]:void 0)},setFloat64:function(e,t){Q(this,8,e,K,t,arguments.length>2?arguments[2]:void 0)}});_(O,R),_(B,A),e.exports={ArrayBuffer:O,DataView:B}},5786:(e,t,n)=>{"use strict";var r=n(7475),o=n(1801),i=n(6042);e.exports=function(e){var t=r(this),n=i(t),a=arguments.length,l=o(a>1?arguments[1]:void 0,n),s=a>2?arguments[2]:void 0,u=void 0===s?n:o(s,n);while(u>l)t[l++]=e;return t}},2029:(e,t,n)=>{"use strict";var r=n(7358),o=n(422),i=n(3577),a=n(7475),l=n(9234),s=n(1558),u=n(7593),c=n(6042),d=n(6496),f=n(2151),p=n(7143),v=r.Array;e.exports=function(e){var t=a(e),n=u(this),r=arguments.length,h=r>1?arguments[1]:void 0,g=void 0!==h;g&&(h=o(h,r>2?arguments[2]:void 0));var m,b,y,w,x,_,k=p(t),S=0;if(!k||this==v&&s(k))for(m=c(t),b=n?new this(m):v(m);m>S;S++)_=g?h(t[S],S):t[S],d(b,S,_);else for(w=f(t,k),x=w.next,b=n?new this:[];!(y=i(x,w)).done;S++)_=g?l(w,h,[y.value,S],!0):y.value,d(b,S,_);return b.length=S,b}},6963:(e,t,n)=>{var r=n(7120),o=n(1801),i=n(6042),a=function(e){return function(t,n,a){var l,s=r(t),u=i(s),c=o(a,u);if(e&&n!=n){while(u>c)if(l=s[c++],l!=l)return!0}else for(;u>c;c++)if((e||c in s)&&s[c]===n)return e||c||0;return!e&&-1}};e.exports={includes:a(!0),indexOf:a(!1)}},2099:(e,t,n)=>{var r=n(422),o=n(1890),i=n(2985),a=n(7475),l=n(6042),s=n(6340),u=o([].push),c=function(e){var t=1==e,n=2==e,o=3==e,c=4==e,d=6==e,f=7==e,p=5==e||d;return function(v,h,g,m){for(var b,y,w=a(v),x=i(w),_=r(h,g),k=l(x),S=0,C=m||s,F=t?C(v,k):n||f?C(v,0):void 0;k>S;S++)if((p||S in x)&&(b=x[S],y=_(b,S,w),e))if(t)F[S]=y;else if(y)switch(e){case 3:return!0;case 5:return b;case 6:return S;case 2:u(F,b)}else switch(e){case 4:return!1;case 7:u(F,b)}return d?-1:o||c?c:F}};e.exports={forEach:c(0),map:c(1),filter:c(2),some:c(3),every:c(4),find:c(5),findIndex:c(6),filterReject:c(7)}},5771:(e,t,n)=>{var r=n(7358),o=n(1801),i=n(6042),a=n(6496),l=r.Array,s=Math.max;e.exports=function(e,t,n){for(var r=i(e),u=o(t,r),c=o(void 0===n?r:n,r),d=l(s(c-u,0)),f=0;u<c;u++,f++)a(d,f,e[u]);return d.length=f,d}},6534:(e,t,n)=>{var r=n(5771),o=Math.floor,i=function(e,t){var n=e.length,s=o(n/2);return n<8?a(e,t):l(e,i(r(e,0,s),t),i(r(e,s),t),t)},a=function(e,t){var n,r,o=e.length,i=1;while(i<o){r=i,n=e[i];while(r&&t(e[r-1],n)>0)e[r]=e[--r];r!==i++&&(e[r]=n)}return e},l=function(e,t,n,r){var o=t.length,i=n.length,a=0,l=0;while(a<o||l<i)e[a+l]=a<o&&l<i?r(t[a],n[l])<=0?t[a++]:n[l++]:a<o?t[a++]:n[l++];return e};e.exports=i},330:(e,t,n)=>{var r=n(7358),o=n(6894),i=n(7593),a=n(776),l=n(854),s=l("species"),u=r.Array;e.exports=function(e){var t;return o(e)&&(t=e.constructor,i(t)&&(t===u||o(t.prototype))?t=void 0:a(t)&&(t=t[s],null===t&&(t=void 0))),void 0===t?u:t}},6340:(e,t,n)=>{var r=n(330);e.exports=function(e,t){return new(r(e))(0===t?0:t)}},9234:(e,t,n)=>{var r=n(7950),o=n(8105);e.exports=function(e,t,n,i){try{return i?t(r(n)[0],n[1]):t(n)}catch(a){o(e,"throw",a)}}},8047:(e,t,n)=>{var r=n(854),o=r("iterator"),i=!1;try{var a=0,l={next:function(){return{done:!!a++}},return:function(){i=!0}};l[o]=function(){return this},Array.from(l,(function(){throw 2}))}catch(s){}e.exports=function(e,t){if(!t&&!i)return!1;var n=!1;try{var r={};r[o]=function(){return{next:function(){return{done:n=!0}}}},e(r)}catch(s){}return n}},5173:(e,t,n)=>{var r=n(1890),o=r({}.toString),i=r("".slice);e.exports=function(e){return i(o(e),8,-1)}},5976:(e,t,n)=>{var r=n(7358),o=n(5705),i=n(419),a=n(5173),l=n(854),s=l("toStringTag"),u=r.Object,c="Arguments"==a(function(){return arguments}()),d=function(e,t){try{return e[t]}catch(n){}};e.exports=o?a:function(e){var t,n,r;return void 0===e?"Undefined":null===e?"Null":"string"==typeof(n=d(t=u(e),s))?n:c?a(t):"Object"==(r=a(t))&&i(t.callee)?"Arguments":r}},767:(e,t,n)=>{var r=n(1890),o=r("".replace),i=function(e){return String(Error(e).stack)}("zxcasd"),a=/\n\s*at [^:]*:[^\n]*/,l=a.test(i);e.exports=function(e,t){if(l&&"string"==typeof e)while(t--)e=o(e,a,"");return e}},8438:(e,t,n)=>{var r=n(7322),o=n(7764),i=n(2404),a=n(928);e.exports=function(e,t,n){for(var l=o(t),s=a.f,u=i.f,c=0;c<l.length;c++){var d=l[c];r(e,d)||n&&r(n,d)||s(e,d,u(t,d))}}},123:(e,t,n)=>{var r=n(6400);e.exports=!r((function(){function e(){}return e.prototype.constructor=null,Object.getPrototypeOf(new e)!==e.prototype}))},5912:(e,t,n)=>{"use strict";var r=n(4848).IteratorPrototype,o=n(1074),i=n(5442),a=n(1061),l=n(2184),s=function(){return this};e.exports=function(e,t,n,u){var c=t+" Iterator";return e.prototype=o(r,{next:i(+!u,n)}),a(e,c,!1,!0),l[c]=s,e}},1904:(e,t,n)=>{var r=n(9631),o=n(928),i=n(5442);e.exports=r?function(e,t,n){return o.f(e,t,i(1,n))}:function(e,t,n){return e[t]=n,e}},5442:e=>{e.exports=function(e,t){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:t}}},6496:(e,t,n)=>{"use strict";var r=n(8618),o=n(928),i=n(5442);e.exports=function(e,t,n){var a=r(t);a in e?o.f(e,a,i(0,n)):e[a]=n}},8810:(e,t,n)=>{"use strict";var r=n(8934),o=n(3577),i=n(6692),a=n(7961),l=n(419),s=n(5912),u=n(4945),c=n(6184),d=n(1061),f=n(1904),p=n(298),v=n(854),h=n(2184),g=n(4848),m=a.PROPER,b=a.CONFIGURABLE,y=g.IteratorPrototype,w=g.BUGGY_SAFARI_ITERATORS,x=v("iterator"),_="keys",k="values",S="entries",C=function(){return this};e.exports=function(e,t,n,a,v,g,F){s(n,t,a);var E,R,A,q=function(e){if(e===v&&M)return M;if(!w&&e in P)return P[e];switch(e){case _:return function(){return new n(this,e)};case k:return function(){return new n(this,e)};case S:return function(){return new n(this,e)}}return function(){return new n(this)}},T=t+" Iterator",L=!1,P=e.prototype,O=P[x]||P["@@iterator"]||v&&P[v],M=!w&&O||q(v),B="Array"==t&&P.entries||O;if(B&&(E=u(B.call(new e)),E!==Object.prototype&&E.next&&(i||u(E)===y||(c?c(E,y):l(E[x])||p(E,x,C)),d(E,T,!0,!0),i&&(h[T]=C))),m&&v==k&&O&&O.name!==k&&(!i&&b?f(P,"name",k):(L=!0,M=function(){return o(O,this)})),v)if(R={values:q(k),keys:g?M:q(_),entries:q(S)},F)for(A in R)(w||L||!(A in P))&&p(P,A,R[A]);else r({target:t,proto:!0,forced:w||L},R);return i&&!F||P[x]===M||p(P,x,M,{name:v}),h[t]=M,R}},9631:(e,t,n)=>{var r=n(6400);e.exports=!r((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]}))},5354:(e,t,n)=>{var r=n(7358),o=n(776),i=r.document,a=o(i)&&o(i.createElement);e.exports=function(e){return a?i.createElement(e):{}}},4296:e=>{e.exports={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0}},8753:(e,t,n)=>{var r=n(5354),o=r("span").classList,i=o&&o.constructor&&o.constructor.prototype;e.exports=i===Object.prototype?void 0:i},1544:(e,t,n)=>{var r=n(9173),o=r.match(/firefox\/(\d+)/i);e.exports=!!o&&+o[1]},8979:(e,t,n)=>{var r=n(9173);e.exports=/MSIE|Trident/.test(r)},9173:(e,t,n)=>{var r=n(9694);e.exports=r("navigator","userAgent")||""},5068:(e,t,n)=>{var r,o,i=n(7358),a=n(9173),l=i.process,s=i.Deno,u=l&&l.versions||s&&s.version,c=u&&u.v8;c&&(r=c.split("."),o=r[0]>0&&r[0]<4?1:+(r[0]+r[1])),!o&&a&&(r=a.match(/Edge\/(\d+)/),(!r||r[1]>=74)&&(r=a.match(/Chrome\/(\d+)/),r&&(o=+r[1]))),e.exports=o},1513:(e,t,n)=>{var r=n(9173),o=r.match(/AppleWebKit\/(\d+)\./);e.exports=!!o&&+o[1]},2875:e=>{e.exports=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"]},6429:(e,t,n)=>{var r=n(6400),o=n(5442);e.exports=!r((function(){var e=Error("a");return!("stack"in e)||(Object.defineProperty(e,"stack",o(1,7)),7!==e.stack)}))},8934:(e,t,n)=>{var r=n(7358),o=n(2404).f,i=n(1904),a=n(298),l=n(3534),s=n(8438),u=n(4389);e.exports=function(e,t){var n,c,d,f,p,v,h=e.target,g=e.global,m=e.stat;if(c=g?r:m?r[h]||l(h,{}):(r[h]||{}).prototype,c)for(d in t){if(p=t[d],e.noTargetGet?(v=o(c,d),f=v&&v.value):f=c[d],n=u(g?d:h+(m?".":"#")+d,e.forced),!n&&void 0!==f){if(typeof p==typeof f)continue;s(p,f)}(e.sham||f&&f.sham)&&i(p,"sham",!0),a(c,d,p,e)}}},6400:e=>{e.exports=function(e){try{return!!e()}catch(t){return!0}}},9529:(e,t,n)=>{"use strict";n(7280);var r=n(1890),o=n(298),i=n(4348),a=n(6400),l=n(854),s=n(1904),u=l("species"),c=RegExp.prototype;e.exports=function(e,t,n,d){var f=l(e),p=!a((function(){var t={};return t[f]=function(){return 7},7!=""[e](t)})),v=p&&!a((function(){var t=!1,n=/a/;return"split"===e&&(n={},n.constructor={},n.constructor[u]=function(){return n},n.flags="",n[f]=/./[f]),n.exec=function(){return t=!0,null},n[f](""),!t}));if(!p||!v||n){var h=r(/./[f]),g=t(f,""[e],(function(e,t,n,o,a){var l=r(e),s=t.exec;return s===i||s===c.exec?p&&!a?{done:!0,value:h(t,n,o)}:{done:!0,value:l(n,t,o)}:{done:!1}}));o(String.prototype,e,g[0]),o(c,f,g[1])}d&&s(c[f],"sham",!0)}},4157:(e,t,n)=>{var r=n(8427),o=Function.prototype,i=o.apply,a=o.call;e.exports="object"==typeof Reflect&&Reflect.apply||(r?a.bind(i):function(){return a.apply(i,arguments)})},422:(e,t,n)=>{var r=n(1890),o=n(392),i=n(8427),a=r(r.bind);e.exports=function(e,t){return o(e),void 0===t?e:i?a(e,t):function(){return e.apply(t,arguments)}}},8427:(e,t,n)=>{var r=n(6400);e.exports=!r((function(){var e=function(){}.bind();return"function"!=typeof e||e.hasOwnProperty("prototype")}))},3577:(e,t,n)=>{var r=n(8427),o=Function.prototype.call;e.exports=r?o.bind(o):function(){return o.apply(o,arguments)}},7961:(e,t,n)=>{var r=n(9631),o=n(7322),i=Function.prototype,a=r&&Object.getOwnPropertyDescriptor,l=o(i,"name"),s=l&&"something"===function(){}.name,u=l&&(!r||r&&a(i,"name").configurable);e.exports={EXISTS:l,PROPER:s,CONFIGURABLE:u}},1890:(e,t,n)=>{var r=n(8427),o=Function.prototype,i=o.bind,a=o.call,l=r&&i.bind(a,a);e.exports=r?function(e){return e&&l(e)}:function(e){return e&&function(){return a.apply(e,arguments)}}},9694:(e,t,n)=>{var r=n(7358),o=n(419),i=function(e){return o(e)?e:void 0};e.exports=function(e,t){return arguments.length<2?i(r[e]):r[e]&&r[e][t]}},7143:(e,t,n)=>{var r=n(5976),o=n(2344),i=n(2184),a=n(854),l=a("iterator");e.exports=function(e){if(void 0!=e)return o(e,l)||o(e,"@@iterator")||i[r(e)]}},2151:(e,t,n)=>{var r=n(7358),o=n(3577),i=n(392),a=n(7950),l=n(3353),s=n(7143),u=r.TypeError;e.exports=function(e,t){var n=arguments.length<2?s(e):t;if(i(n))return a(o(n,e));throw u(l(e)+" is not iterable")}},2344:(e,t,n)=>{var r=n(392);e.exports=function(e,t){var n=e[t];return null==n?void 0:r(n)}},8716:(e,t,n)=>{var r=n(1890),o=n(7475),i=Math.floor,a=r("".charAt),l=r("".replace),s=r("".slice),u=/\$([$&'`]|\d{1,2}|<[^>]*>)/g,c=/\$([$&'`]|\d{1,2})/g;e.exports=function(e,t,n,r,d,f){var p=n+e.length,v=r.length,h=c;return void 0!==d&&(d=o(d),h=u),l(f,h,(function(o,l){var u;switch(a(l,0)){case"$":return"$";case"&":return e;case"`":return s(t,0,n);case"'":return s(t,p);case"<":u=d[s(l,1,-1)];break;default:var c=+l;if(0===c)return o;if(c>v){var f=i(c/10);return 0===f?o:f<=v?void 0===r[f-1]?a(l,1):r[f-1]+a(l,1):o}u=r[c-1]}return void 0===u?"":u}))}},7358:(e,t,n)=>{var r=function(e){return e&&e.Math==Math&&e};e.exports=r("object"==typeof globalThis&&globalThis)||r("object"==typeof window&&window)||r("object"==typeof self&&self)||r("object"==typeof n.g&&n.g)||function(){return this}()||Function("return this")()},7322:(e,t,n)=>{var r=n(1890),o=n(7475),i=r({}.hasOwnProperty);e.exports=Object.hasOwn||function(e,t){return i(o(e),t)}},600:e=>{e.exports={}},9970:(e,t,n)=>{var r=n(9694);e.exports=r("document","documentElement")},7021:(e,t,n)=>{var r=n(9631),o=n(6400),i=n(5354);e.exports=!r&&!o((function(){return 7!=Object.defineProperty(i("div"),"a",{get:function(){return 7}}).a}))},8830:(e,t,n)=>{var r=n(7358),o=r.Array,i=Math.abs,a=Math.pow,l=Math.floor,s=Math.log,u=Math.LN2,c=function(e,t,n){var r,c,d,f=o(n),p=8*n-t-1,v=(1<<p)-1,h=v>>1,g=23===t?a(2,-24)-a(2,-77):0,m=e<0||0===e&&1/e<0?1:0,b=0;e=i(e),e!=e||e===1/0?(c=e!=e?1:0,r=v):(r=l(s(e)/u),d=a(2,-r),e*d<1&&(r--,d*=2),e+=r+h>=1?g/d:g*a(2,1-h),e*d>=2&&(r++,d/=2),r+h>=v?(c=0,r=v):r+h>=1?(c=(e*d-1)*a(2,t),r+=h):(c=e*a(2,h-1)*a(2,t),r=0));while(t>=8)f[b++]=255&c,c/=256,t-=8;r=r<<t|c,p+=t;while(p>0)f[b++]=255&r,r/=256,p-=8;return f[--b]|=128*m,f},d=function(e,t){var n,r=e.length,o=8*r-t-1,i=(1<<o)-1,l=i>>1,s=o-7,u=r-1,c=e[u--],d=127&c;c>>=7;while(s>0)d=256*d+e[u--],s-=8;n=d&(1<<-s)-1,d>>=-s,s+=t;while(s>0)n=256*n+e[u--],s-=8;if(0===d)d=1-l;else{if(d===i)return n?NaN:c?-1/0:1/0;n+=a(2,t),d-=l}return(c?-1:1)*n*a(2,d-t)};e.exports={pack:c,unpack:d}},2985:(e,t,n)=>{var r=n(7358),o=n(1890),i=n(6400),a=n(5173),l=r.Object,s=o("".split);e.exports=i((function(){return!l("z").propertyIsEnumerable(0)}))?function(e){return"String"==a(e)?s(e,""):l(e)}:l},9941:(e,t,n)=>{var r=n(419),o=n(776),i=n(6184);e.exports=function(e,t,n){var a,l;return i&&r(a=t.constructor)&&a!==n&&o(l=a.prototype)&&l!==n.prototype&&i(e,l),e}},3725:(e,t,n)=>{var r=n(1890),o=n(419),i=n(1089),a=r(Function.toString);o(i.inspectSource)||(i.inspectSource=function(e){return a(e)}),e.exports=i.inspectSource},5684:(e,t,n)=>{var r=n(776),o=n(1904);e.exports=function(e,t){r(t)&&"cause"in t&&o(e,"cause",t.cause)}},7624:(e,t,n)=>{var r,o,i,a=n(9262),l=n(7358),s=n(1890),u=n(776),c=n(1904),d=n(7322),f=n(1089),p=n(203),v=n(600),h="Object already initialized",g=l.TypeError,m=l.WeakMap,b=function(e){return i(e)?o(e):r(e,{})},y=function(e){return function(t){var n;if(!u(t)||(n=o(t)).type!==e)throw g("Incompatible receiver, "+e+" required");return n}};if(a||f.state){var w=f.state||(f.state=new m),x=s(w.get),_=s(w.has),k=s(w.set);r=function(e,t){if(_(w,e))throw new g(h);return t.facade=e,k(w,e,t),t},o=function(e){return x(w,e)||{}},i=function(e){return _(w,e)}}else{var S=p("state");v[S]=!0,r=function(e,t){if(d(e,S))throw new g(h);return t.facade=e,c(e,S,t),t},o=function(e){return d(e,S)?e[S]:{}},i=function(e){return d(e,S)}}e.exports={set:r,get:o,has:i,enforce:b,getterFor:y}},1558:(e,t,n)=>{var r=n(854),o=n(2184),i=r("iterator"),a=Array.prototype;e.exports=function(e){return void 0!==e&&(o.Array===e||a[i]===e)}},6894:(e,t,n)=>{var r=n(5173);e.exports=Array.isArray||function(e){return"Array"==r(e)}},419:e=>{e.exports=function(e){return"function"==typeof e}},7593:(e,t,n)=>{var r=n(1890),o=n(6400),i=n(419),a=n(5976),l=n(9694),s=n(3725),u=function(){},c=[],d=l("Reflect","construct"),f=/^\s*(?:class|function)\b/,p=r(f.exec),v=!f.exec(u),h=function(e){if(!i(e))return!1;try{return d(u,c,e),!0}catch(t){return!1}},g=function(e){if(!i(e))return!1;switch(a(e)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return v||!!p(f,s(e))}catch(t){return!0}};g.sham=!0,e.exports=!d||o((function(){var e;return h(h.call)||!h(Object)||!h((function(){e=!0}))||e}))?g:h},4389:(e,t,n)=>{var r=n(6400),o=n(419),i=/#|\.prototype\./,a=function(e,t){var n=s[l(e)];return n==c||n!=u&&(o(t)?r(t):!!t)},l=a.normalize=function(e){return String(e).replace(i,".").toLowerCase()},s=a.data={},u=a.NATIVE="N",c=a.POLYFILL="P";e.exports=a},2818:(e,t,n)=>{var r=n(776),o=Math.floor;e.exports=Number.isInteger||function(e){return!r(e)&&isFinite(e)&&o(e)===e}},776:(e,t,n)=>{var r=n(419);e.exports=function(e){return"object"==typeof e?null!==e:r(e)}},6692:e=>{e.exports=!1},410:(e,t,n)=>{var r=n(7358),o=n(9694),i=n(419),a=n(7673),l=n(8476),s=r.Object;e.exports=l?function(e){return"symbol"==typeof e}:function(e){var t=o("Symbol");return i(t)&&a(t.prototype,s(e))}},8105:(e,t,n)=>{var r=n(3577),o=n(7950),i=n(2344);e.exports=function(e,t,n){var a,l;o(e);try{if(a=i(e,"return"),!a){if("throw"===t)throw n;return n}a=r(a,e)}catch(s){l=!0,a=s}if("throw"===t)throw n;if(l)throw a;return o(a),n}},4848:(e,t,n)=>{"use strict";var r,o,i,a=n(6400),l=n(419),s=n(1074),u=n(4945),c=n(298),d=n(854),f=n(6692),p=d("iterator"),v=!1;[].keys&&(i=[].keys(),"next"in i?(o=u(u(i)),o!==Object.prototype&&(r=o)):v=!0);var h=void 0==r||a((function(){var e={};return r[p].call(e)!==e}));h?r={}:f&&(r=s(r)),l(r[p])||c(r,p,(function(){return this})),e.exports={IteratorPrototype:r,BUGGY_SAFARI_ITERATORS:v}},2184:e=>{e.exports={}},6042:(e,t,n)=>{var r=n(4068);e.exports=function(e){return r(e.length)}},7529:(e,t,n)=>{var r=n(5068),o=n(6400);e.exports=!!Object.getOwnPropertySymbols&&!o((function(){var e=Symbol();return!String(e)||!(Object(e)instanceof Symbol)||!Symbol.sham&&r&&r<41}))},6595:(e,t,n)=>{var r=n(6400),o=n(854),i=n(6692),a=o("iterator");e.exports=!r((function(){var e=new URL("b?a=1&b=2&c=3","http://a"),t=e.searchParams,n="";return e.pathname="c%20d",t.forEach((function(e,r){t["delete"]("b"),n+=r+e})),i&&!e.toJSON||!t.sort||"http://a/c%20d?a=1&c=3"!==e.href||"3"!==t.get("c")||"a=1"!==String(new URLSearchParams("?a=1"))||!t[a]||"a"!==new URL("https://a@b").username||"b"!==new URLSearchParams(new URLSearchParams("a=b")).get("a")||"xn--e1aybc"!==new URL("http://тест").host||"#%D0%B1"!==new URL("http://a#б").hash||"a1c3"!==n||"x"!==new URL("http://x",void 0).host}))},9262:(e,t,n)=>{var r=n(7358),o=n(419),i=n(3725),a=r.WeakMap;e.exports=o(a)&&/native code/.test(i(a))},7598:(e,t,n)=>{var r=n(4481);e.exports=function(e,t){return void 0===e?arguments.length<2?"":t:r(e)}},8439:(e,t,n)=>{"use strict";var r=n(9631),o=n(1890),i=n(3577),a=n(6400),l=n(9158),s=n(4199),u=n(5604),c=n(7475),d=n(2985),f=Object.assign,p=Object.defineProperty,v=o([].concat);e.exports=!f||a((function(){if(r&&1!==f({b:1},f(p({},"a",{enumerable:!0,get:function(){p(this,"b",{value:3,enumerable:!1})}}),{b:2})).b)return!0;var e={},t={},n=Symbol(),o="abcdefghijklmnopqrst";return e[n]=7,o.split("").forEach((function(e){t[e]=e})),7!=f({},e)[n]||l(f({},t)).join("")!=o}))?function(e,t){var n=c(e),o=arguments.length,a=1,f=s.f,p=u.f;while(o>a){var h,g=d(arguments[a++]),m=f?v(l(g),f(g)):l(g),b=m.length,y=0;while(b>y)h=m[y++],r&&!i(p,g,h)||(n[h]=g[h])}return n}:f},1074:(e,t,n)=>{var r,o=n(7950),i=n(3605),a=n(2875),l=n(600),s=n(9970),u=n(5354),c=n(203),d=">",f="<",p="prototype",v="script",h=c("IE_PROTO"),g=function(){},m=function(e){return f+v+d+e+f+"/"+v+d},b=function(e){e.write(m("")),e.close();var t=e.parentWindow.Object;return e=null,t},y=function(){var e,t=u("iframe"),n="java"+v+":";return t.style.display="none",s.appendChild(t),t.src=String(n),e=t.contentWindow.document,e.open(),e.write(m("document.F=Object")),e.close(),e.F},w=function(){try{r=new ActiveXObject("htmlfile")}catch(t){}w="undefined"!=typeof document?document.domain&&r?b(r):y():b(r);var e=a.length;while(e--)delete w[p][a[e]];return w()};l[h]=!0,e.exports=Object.create||function(e,t){var n;return null!==e?(g[p]=o(e),n=new g,g[p]=null,n[h]=e):n=w(),void 0===t?n:i.f(n,t)}},3605:(e,t,n)=>{var r=n(9631),o=n(5953),i=n(928),a=n(7950),l=n(7120),s=n(9158);t.f=r&&!o?Object.defineProperties:function(e,t){a(e);var n,r=l(t),o=s(t),u=o.length,c=0;while(u>c)i.f(e,n=o[c++],r[n]);return e}},928:(e,t,n)=>{var r=n(7358),o=n(9631),i=n(7021),a=n(5953),l=n(7950),s=n(8618),u=r.TypeError,c=Object.defineProperty,d=Object.getOwnPropertyDescriptor,f="enumerable",p="configurable",v="writable";t.f=o?a?function(e,t,n){if(l(e),t=s(t),l(n),"function"===typeof e&&"prototype"===t&&"value"in n&&v in n&&!n[v]){var r=d(e,t);r&&r[v]&&(e[t]=n.value,n={configurable:p in n?n[p]:r[p],enumerable:f in n?n[f]:r[f],writable:!1})}return c(e,t,n)}:c:function(e,t,n){if(l(e),t=s(t),l(n),i)try{return c(e,t,n)}catch(r){}if("get"in n||"set"in n)throw u("Accessors not supported");return"value"in n&&(e[t]=n.value),e}},2404:(e,t,n)=>{var r=n(9631),o=n(3577),i=n(5604),a=n(5442),l=n(7120),s=n(8618),u=n(7322),c=n(7021),d=Object.getOwnPropertyDescriptor;t.f=r?d:function(e,t){if(e=l(e),t=s(t),c)try{return d(e,t)}catch(n){}if(u(e,t))return a(!o(i.f,e,t),e[t])}},1454:(e,t,n)=>{var r=n(1587),o=n(2875),i=o.concat("length","prototype");t.f=Object.getOwnPropertyNames||function(e){return r(e,i)}},4199:(e,t)=>{t.f=Object.getOwnPropertySymbols},4945:(e,t,n)=>{var r=n(7358),o=n(7322),i=n(419),a=n(7475),l=n(203),s=n(123),u=l("IE_PROTO"),c=r.Object,d=c.prototype;e.exports=s?c.getPrototypeOf:function(e){var t=a(e);if(o(t,u))return t[u];var n=t.constructor;return i(n)&&t instanceof n?n.prototype:t instanceof c?d:null}},7673:(e,t,n)=>{var r=n(1890);e.exports=r({}.isPrototypeOf)},1587:(e,t,n)=>{var r=n(1890),o=n(7322),i=n(7120),a=n(6963).indexOf,l=n(600),s=r([].push);e.exports=function(e,t){var n,r=i(e),u=0,c=[];for(n in r)!o(l,n)&&o(r,n)&&s(c,n);while(t.length>u)o(r,n=t[u++])&&(~a(c,n)||s(c,n));return c}},9158:(e,t,n)=>{var r=n(1587),o=n(2875);e.exports=Object.keys||function(e){return r(e,o)}},5604:(e,t)=>{"use strict";var n={}.propertyIsEnumerable,r=Object.getOwnPropertyDescriptor,o=r&&!n.call({1:2},1);t.f=o?function(e){var t=r(this,e);return!!t&&t.enumerable}:n},6184:(e,t,n)=>{var r=n(1890),o=n(7950),i=n(8248);e.exports=Object.setPrototypeOf||("__proto__"in{}?function(){var e,t=!1,n={};try{e=r(Object.getOwnPropertyDescriptor(Object.prototype,"__proto__").set),e(n,[]),t=n instanceof Array}catch(a){}return function(n,r){return o(n),i(r),t?e(n,r):n.__proto__=r,n}}():void 0)},9308:(e,t,n)=>{var r=n(7358),o=n(3577),i=n(419),a=n(776),l=r.TypeError;e.exports=function(e,t){var n,r;if("string"===t&&i(n=e.toString)&&!a(r=o(n,e)))return r;if(i(n=e.valueOf)&&!a(r=o(n,e)))return r;if("string"!==t&&i(n=e.toString)&&!a(r=o(n,e)))return r;throw l("Can't convert object to primitive value")}},7764:(e,t,n)=>{var r=n(9694),o=n(1890),i=n(1454),a=n(4199),l=n(7950),s=o([].concat);e.exports=r("Reflect","ownKeys")||function(e){var t=i.f(l(e)),n=a.f;return n?s(t,n(e)):t}},9833:(e,t,n)=>{var r=n(298);e.exports=function(e,t,n){for(var o in t)r(e,o,t[o],n);return e}},298:(e,t,n)=>{var r=n(7358),o=n(419),i=n(7322),a=n(1904),l=n(3534),s=n(3725),u=n(7624),c=n(7961).CONFIGURABLE,d=u.get,f=u.enforce,p=String(String).split("String");(e.exports=function(e,t,n,s){var u,d=!!s&&!!s.unsafe,v=!!s&&!!s.enumerable,h=!!s&&!!s.noTargetGet,g=s&&void 0!==s.name?s.name:t;o(n)&&("Symbol("===String(g).slice(0,7)&&(g="["+String(g).replace(/^Symbol\(([^)]*)\)/,"$1")+"]"),(!i(n,"name")||c&&n.name!==g)&&a(n,"name",g),u=f(n),u.source||(u.source=p.join("string"==typeof g?g:""))),e!==r?(d?!h&&e[t]&&(v=!0):delete e[t],v?e[t]=n:a(e,t,n)):v?e[t]=n:l(t,n)})(Function.prototype,"toString",(function(){return o(this)&&d(this).source||s(this)}))},9395:(e,t,n)=>{var r=n(7358),o=n(3577),i=n(7950),a=n(419),l=n(5173),s=n(4348),u=r.TypeError;e.exports=function(e,t){var n=e.exec;if(a(n)){var r=o(n,e,t);return null!==r&&i(r),r}if("RegExp"===l(e))return o(s,e,t);throw u("RegExp#exec called on incompatible receiver")}},4348:(e,t,n)=>{"use strict";var r=n(3577),o=n(1890),i=n(4481),a=n(136),l=n(2351),s=n(1586),u=n(1074),c=n(7624).get,d=n(5337),f=n(1442),p=s("native-string-replace",String.prototype.replace),v=RegExp.prototype.exec,h=v,g=o("".charAt),m=o("".indexOf),b=o("".replace),y=o("".slice),w=function(){var e=/a/,t=/b*/g;return r(v,e,"a"),r(v,t,"a"),0!==e.lastIndex||0!==t.lastIndex}(),x=l.BROKEN_CARET,_=void 0!==/()??/.exec("")[1],k=w||_||x||d||f;k&&(h=function(e){var t,n,o,l,s,d,f,k=this,S=c(k),C=i(e),F=S.raw;if(F)return F.lastIndex=k.lastIndex,t=r(h,F,C),k.lastIndex=F.lastIndex,t;var E=S.groups,R=x&&k.sticky,A=r(a,k),q=k.source,T=0,L=C;if(R&&(A=b(A,"y",""),-1===m(A,"g")&&(A+="g"),L=y(C,k.lastIndex),k.lastIndex>0&&(!k.multiline||k.multiline&&"\n"!==g(C,k.lastIndex-1))&&(q="(?: "+q+")",L=" "+L,T++),n=new RegExp("^(?:"+q+")",A)),_&&(n=new RegExp("^"+q+"$(?!\\s)",A)),w&&(o=k.lastIndex),l=r(v,R?n:k,L),R?l?(l.input=y(l.input,T),l[0]=y(l[0],T),l.index=k.lastIndex,k.lastIndex+=l[0].length):k.lastIndex=0:w&&l&&(k.lastIndex=k.global?l.index+l[0].length:o),_&&l&&l.length>1&&r(p,l[0],n,(function(){for(s=1;s<arguments.length-2;s++)void 0===arguments[s]&&(l[s]=void 0)})),l&&E)for(l.groups=d=u(null),s=0;s<E.length;s++)f=E[s],d[f[0]]=l[f[1]];return l}),e.exports=h},136:(e,t,n)=>{"use strict";var r=n(7950);e.exports=function(){var e=r(this),t="";return e.global&&(t+="g"),e.ignoreCase&&(t+="i"),e.multiline&&(t+="m"),e.dotAll&&(t+="s"),e.unicode&&(t+="u"),e.sticky&&(t+="y"),t}},2351:(e,t,n)=>{var r=n(6400),o=n(7358),i=o.RegExp,a=r((function(){var e=i("a","y");return e.lastIndex=2,null!=e.exec("abcd")})),l=a||r((function(){return!i("a","y").sticky})),s=a||r((function(){var e=i("^r","gy");return e.lastIndex=2,null!=e.exec("str")}));e.exports={BROKEN_CARET:s,MISSED_STICKY:l,UNSUPPORTED_Y:a}},5337:(e,t,n)=>{var r=n(6400),o=n(7358),i=o.RegExp;e.exports=r((function(){var e=i(".","s");return!(e.dotAll&&e.exec("\n")&&"s"===e.flags)}))},1442:(e,t,n)=>{var r=n(6400),o=n(7358),i=o.RegExp;e.exports=r((function(){var e=i("(?<a>b)","g");return"b"!==e.exec("b").groups.a||"bc"!=="b".replace(e,"$<a>c")}))},7933:(e,t,n)=>{var r=n(7358),o=r.TypeError;e.exports=function(e){if(void 0==e)throw o("Can't call method on "+e);return e}},3534:(e,t,n)=>{var r=n(7358),o=Object.defineProperty;e.exports=function(e,t){try{o(r,e,{value:t,configurable:!0,writable:!0})}catch(n){r[e]=t}return t}},4114:(e,t,n)=>{"use strict";var r=n(9694),o=n(928),i=n(854),a=n(9631),l=i("species");e.exports=function(e){var t=r(e),n=o.f;a&&t&&!t[l]&&n(t,l,{configurable:!0,get:function(){return this}})}},1061:(e,t,n)=>{var r=n(928).f,o=n(7322),i=n(854),a=i("toStringTag");e.exports=function(e,t,n){e&&!n&&(e=e.prototype),e&&!o(e,a)&&r(e,a,{configurable:!0,value:t})}},203:(e,t,n)=>{var r=n(1586),o=n(6862),i=r("keys");e.exports=function(e){return i[e]||(i[e]=o(e))}},1089:(e,t,n)=>{var r=n(7358),o=n(3534),i="__core-js_shared__",a=r[i]||o(i,{});e.exports=a},1586:(e,t,n)=>{var r=n(6692),o=n(1089);(e.exports=function(e,t){return o[e]||(o[e]=void 0!==t?t:{})})("versions",[]).push({version:"3.20.3",mode:r?"pure":"global",copyright:"© 2014-2022 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.20.3/LICENSE",source:"https://github.com/zloirock/core-js"})},7440:(e,t,n)=>{var r=n(7950),o=n(2722),i=n(854),a=i("species");e.exports=function(e,t){var n,i=r(e).constructor;return void 0===i||void 0==(n=r(i)[a])?t:o(n)}},1021:(e,t,n)=>{var r=n(1890),o=n(1860),i=n(4481),a=n(7933),l=r("".charAt),s=r("".charCodeAt),u=r("".slice),c=function(e){return function(t,n){var r,c,d=i(a(t)),f=o(n),p=d.length;return f<0||f>=p?e?"":void 0:(r=s(d,f),r<55296||r>56319||f+1===p||(c=s(d,f+1))<56320||c>57343?e?l(d,f):r:e?u(d,f,f+2):c-56320+(r-55296<<10)+65536)}};e.exports={codeAt:c(!1),charAt:c(!0)}},5253:(e,t,n)=>{"use strict";var r=n(7358),o=n(1890),i=**********,a=36,l=1,s=26,u=38,c=700,d=72,f=128,p="-",v=/[^\0-\u007E]/,h=/[.\u3002\uFF0E\uFF61]/g,g="Overflow: input needs wider integers to process",m=a-l,b=r.RangeError,y=o(h.exec),w=Math.floor,x=String.fromCharCode,_=o("".charCodeAt),k=o([].join),S=o([].push),C=o("".replace),F=o("".split),E=o("".toLowerCase),R=function(e){var t=[],n=0,r=e.length;while(n<r){var o=_(e,n++);if(o>=55296&&o<=56319&&n<r){var i=_(e,n++);56320==(64512&i)?S(t,((1023&o)<<10)+(1023&i)+65536):(S(t,o),n--)}else S(t,o)}return t},A=function(e){return e+22+75*(e<26)},q=function(e,t,n){var r=0;e=n?w(e/c):e>>1,e+=w(e/t);while(e>m*s>>1)e=w(e/m),r+=a;return w(r+(m+1)*e/(e+u))},T=function(e){var t=[];e=R(e);var n,r,o=e.length,u=f,c=0,v=d;for(n=0;n<e.length;n++)r=e[n],r<128&&S(t,x(r));var h=t.length,m=h;h&&S(t,p);while(m<o){var y=i;for(n=0;n<e.length;n++)r=e[n],r>=u&&r<y&&(y=r);var _=m+1;if(y-u>w((i-c)/_))throw b(g);for(c+=(y-u)*_,u=y,n=0;n<e.length;n++){if(r=e[n],r<u&&++c>i)throw b(g);if(r==u){var C=c,F=a;while(1){var E=F<=v?l:F>=v+s?s:F-v;if(C<E)break;var T=C-E,L=a-E;S(t,x(A(E+T%L))),C=w(T/L),F+=a}S(t,x(A(C))),v=q(c,_,m==h),c=0,m++}}c++,u++}return k(t,"")};e.exports=function(e){var t,n,r=[],o=F(C(E(e),h,"."),".");for(t=0;t<o.length;t++)n=o[t],S(r,y(v,n)?"xn--"+T(n):n);return k(r,".")}},7894:(e,t,n)=>{var r=n(7961).PROPER,o=n(6400),i=n(4454),a="​᠎";e.exports=function(e){return o((function(){return!!i[e]()||a[e]()!==a||r&&i[e].name!==e}))}},6304:(e,t,n)=>{var r=n(1890),o=n(7933),i=n(4481),a=n(4454),l=r("".replace),s="["+a+"]",u=RegExp("^"+s+s+"*"),c=RegExp(s+s+"*$"),d=function(e){return function(t){var n=i(o(t));return 1&e&&(n=l(n,u,"")),2&e&&(n=l(n,c,"")),n}};e.exports={start:d(1),end:d(2),trim:d(3)}},1801:(e,t,n)=>{var r=n(1860),o=Math.max,i=Math.min;e.exports=function(e,t){var n=r(e);return n<0?o(n+t,0):i(n,t)}},833:(e,t,n)=>{var r=n(7358),o=n(1860),i=n(4068),a=r.RangeError;e.exports=function(e){if(void 0===e)return 0;var t=o(e),n=i(t);if(t!==n)throw a("Wrong length or index");return n}},7120:(e,t,n)=>{var r=n(2985),o=n(7933);e.exports=function(e){return r(o(e))}},1860:e=>{var t=Math.ceil,n=Math.floor;e.exports=function(e){var r=+e;return r!==r||0===r?0:(r>0?n:t)(r)}},4068:(e,t,n)=>{var r=n(1860),o=Math.min;e.exports=function(e){return e>0?o(r(e),9007199254740991):0}},7475:(e,t,n)=>{var r=n(7358),o=n(7933),i=r.Object;e.exports=function(e){return i(o(e))}},701:(e,t,n)=>{var r=n(7358),o=n(1443),i=r.RangeError;e.exports=function(e,t){var n=o(e);if(n%t)throw i("Wrong offset");return n}},1443:(e,t,n)=>{var r=n(7358),o=n(1860),i=r.RangeError;e.exports=function(e){var t=o(e);if(t<0)throw i("The argument can't be less than 0");return t}},2181:(e,t,n)=>{var r=n(7358),o=n(3577),i=n(776),a=n(410),l=n(2344),s=n(9308),u=n(854),c=r.TypeError,d=u("toPrimitive");e.exports=function(e,t){if(!i(e)||a(e))return e;var n,r=l(e,d);if(r){if(void 0===t&&(t="default"),n=o(r,e,t),!i(n)||a(n))return n;throw c("Can't convert object to primitive value")}return void 0===t&&(t="number"),s(e,t)}},8618:(e,t,n)=>{var r=n(2181),o=n(410);e.exports=function(e){var t=r(e,"string");return o(t)?t:t+""}},5705:(e,t,n)=>{var r=n(854),o=r("toStringTag"),i={};i[o]="z",e.exports="[object z]"===String(i)},4481:(e,t,n)=>{var r=n(7358),o=n(5976),i=r.String;e.exports=function(e){if("Symbol"===o(e))throw TypeError("Cannot convert a Symbol value to a string");return i(e)}},3353:(e,t,n)=>{var r=n(7358),o=r.String;e.exports=function(e){try{return o(e)}catch(t){return"Object"}}},6968:(e,t,n)=>{"use strict";var r=n(8934),o=n(7358),i=n(3577),a=n(9631),l=n(8689),s=n(683),u=n(62),c=n(2827),d=n(5442),f=n(1904),p=n(2818),v=n(4068),h=n(833),g=n(701),m=n(8618),b=n(7322),y=n(5976),w=n(776),x=n(410),_=n(1074),k=n(7673),S=n(6184),C=n(1454).f,F=n(9401),E=n(2099).forEach,R=n(4114),A=n(928),q=n(2404),T=n(7624),L=n(9941),P=T.get,O=T.set,M=A.f,B=q.f,$=Math.round,I=o.RangeError,j=u.ArrayBuffer,N=j.prototype,V=u.DataView,U=s.NATIVE_ARRAY_BUFFER_VIEWS,H=s.TYPED_ARRAY_CONSTRUCTOR,z=s.TYPED_ARRAY_TAG,D=s.TypedArray,Z=s.TypedArrayPrototype,W=s.aTypedArrayConstructor,Y=s.isTypedArray,J="BYTES_PER_ELEMENT",K="Wrong length",X=function(e,t){W(e);var n=0,r=t.length,o=new e(r);while(r>n)o[n]=t[n++];return o},G=function(e,t){M(e,t,{get:function(){return P(this)[t]}})},Q=function(e){var t;return k(N,e)||"ArrayBuffer"==(t=y(e))||"SharedArrayBuffer"==t},ee=function(e,t){return Y(e)&&!x(t)&&t in e&&p(+t)&&t>=0},te=function(e,t){return t=m(t),ee(e,t)?d(2,e[t]):B(e,t)},ne=function(e,t,n){return t=m(t),!(ee(e,t)&&w(n)&&b(n,"value"))||b(n,"get")||b(n,"set")||n.configurable||b(n,"writable")&&!n.writable||b(n,"enumerable")&&!n.enumerable?M(e,t,n):(e[t]=n.value,e)};a?(U||(q.f=te,A.f=ne,G(Z,"buffer"),G(Z,"byteOffset"),G(Z,"byteLength"),G(Z,"length")),r({target:"Object",stat:!0,forced:!U},{getOwnPropertyDescriptor:te,defineProperty:ne}),e.exports=function(e,t,n){var a=e.match(/\d+$/)[0]/8,s=e+(n?"Clamped":"")+"Array",u="get"+e,d="set"+e,p=o[s],m=p,b=m&&m.prototype,y={},x=function(e,t){var n=P(e);return n.view[u](t*a+n.byteOffset,!0)},k=function(e,t,r){var o=P(e);n&&(r=(r=$(r))<0?0:r>255?255:255&r),o.view[d](t*a+o.byteOffset,r,!0)},A=function(e,t){M(e,t,{get:function(){return x(this,t)},set:function(e){return k(this,t,e)},enumerable:!0})};U?l&&(m=t((function(e,t,n,r){return c(e,b),L(function(){return w(t)?Q(t)?void 0!==r?new p(t,g(n,a),r):void 0!==n?new p(t,g(n,a)):new p(t):Y(t)?X(m,t):i(F,m,t):new p(h(t))}(),e,m)})),S&&S(m,D),E(C(p),(function(e){e in m||f(m,e,p[e])})),m.prototype=b):(m=t((function(e,t,n,r){c(e,b);var o,l,s,u=0,d=0;if(w(t)){if(!Q(t))return Y(t)?X(m,t):i(F,m,t);o=t,d=g(n,a);var f=t.byteLength;if(void 0===r){if(f%a)throw I(K);if(l=f-d,l<0)throw I(K)}else if(l=v(r)*a,l+d>f)throw I(K);s=l/a}else s=h(t),l=s*a,o=new j(l);O(e,{buffer:o,byteOffset:d,byteLength:l,length:s,view:new V(o)});while(u<s)A(e,u++)})),S&&S(m,D),b=m.prototype=_(Z)),b.constructor!==m&&f(b,"constructor",m),f(b,H,m),z&&f(b,z,s),y[s]=m,r({global:!0,forced:m!=p,sham:!U},y),J in m||f(m,J,a),J in b||f(b,J,a),R(s)}):e.exports=function(){}},8689:(e,t,n)=>{var r=n(7358),o=n(6400),i=n(8047),a=n(683).NATIVE_ARRAY_BUFFER_VIEWS,l=r.ArrayBuffer,s=r.Int8Array;e.exports=!a||!o((function(){s(1)}))||!o((function(){new s(-1)}))||!i((function(e){new s,new s(null),new s(1.5),new s(e)}),!0)||o((function(){return 1!==new s(new l(2),1,void 0).length}))},9401:(e,t,n)=>{var r=n(422),o=n(3577),i=n(2722),a=n(7475),l=n(6042),s=n(2151),u=n(7143),c=n(1558),d=n(683).aTypedArrayConstructor;e.exports=function(e){var t,n,f,p,v,h,g=i(this),m=a(e),b=arguments.length,y=b>1?arguments[1]:void 0,w=void 0!==y,x=u(m);if(x&&!c(x)){v=s(m,x),h=v.next,m=[];while(!(p=o(h,v)).done)m.push(p.value)}for(w&&b>2&&(y=r(y,arguments[2])),n=l(m),f=new(d(g))(n),t=0;n>t;t++)f[t]=w?y(m[t],t):m[t];return f}},6862:(e,t,n)=>{var r=n(1890),o=0,i=Math.random(),a=r(1..toString);e.exports=function(e){return"Symbol("+(void 0===e?"":e)+")_"+a(++o+i,36)}},8476:(e,t,n)=>{var r=n(7529);e.exports=r&&!Symbol.sham&&"symbol"==typeof Symbol.iterator},5953:(e,t,n)=>{var r=n(9631),o=n(6400);e.exports=r&&o((function(){return 42!=Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype}))},6596:(e,t,n)=>{var r=n(7358),o=r.TypeError;e.exports=function(e,t){if(e<t)throw o("Not enough arguments");return e}},854:(e,t,n)=>{var r=n(7358),o=n(1586),i=n(7322),a=n(6862),l=n(7529),s=n(8476),u=o("wks"),c=r.Symbol,d=c&&c["for"],f=s?c:c&&c.withoutSetter||a;e.exports=function(e){if(!i(u,e)||!l&&"string"!=typeof u[e]){var t="Symbol."+e;l&&i(c,e)?u[e]=c[e]:u[e]=s&&d?d(t):f(t)}return u[e]}},4454:e=>{e.exports="\t\n\v\f\r                　\u2028\u2029\ufeff"},7971:(e,t,n)=>{"use strict";var r=n(9694),o=n(7322),i=n(1904),a=n(7673),l=n(6184),s=n(8438),u=n(9941),c=n(7598),d=n(5684),f=n(767),p=n(6429),v=n(6692);e.exports=function(e,t,n,h){var g=h?2:1,m=e.split("."),b=m[m.length-1],y=r.apply(null,m);if(y){var w=y.prototype;if(!v&&o(w,"cause")&&delete w.cause,!n)return y;var x=r("Error"),_=t((function(e,t){var n=c(h?t:e,void 0),r=h?new y(e):new y;return void 0!==n&&i(r,"message",n),p&&i(r,"stack",f(r.stack,2)),this&&a(w,this)&&u(r,this,_),arguments.length>g&&d(r,arguments[g]),r}));if(_.prototype=w,"Error"!==b&&(l?l(_,x):s(_,x,{name:!0})),s(_,y),!v)try{w.name!==b&&i(w,"name",b),w.constructor=_}catch(k){}return _}}},979:(e,t,n)=>{"use strict";var r=n(8934),o=n(1890),i=n(6400),a=n(62),l=n(7950),s=n(1801),u=n(4068),c=n(7440),d=a.ArrayBuffer,f=a.DataView,p=f.prototype,v=o(d.prototype.slice),h=o(p.getUint8),g=o(p.setUint8),m=i((function(){return!new d(2).slice(1,void 0).byteLength}));r({target:"ArrayBuffer",proto:!0,unsafe:!0,forced:m},{slice:function(e,t){if(v&&void 0===t)return v(l(this),e);var n=l(this).byteLength,r=s(e,n),o=s(void 0===t?n:t,n),i=new(c(this,d))(u(o-r)),a=new f(this),p=new f(i),m=0;while(r<o)g(p,m++,h(a,r++));return i}})},6843:(e,t,n)=>{"use strict";var r=n(7120),o=n(2852),i=n(2184),a=n(7624),l=n(928).f,s=n(8810),u=n(6692),c=n(9631),d="Array Iterator",f=a.set,p=a.getterFor(d);e.exports=s(Array,"Array",(function(e,t){f(this,{type:d,target:r(e),index:0,kind:t})}),(function(){var e=p(this),t=e.target,n=e.kind,r=e.index++;return!t||r>=t.length?(e.target=void 0,{value:void 0,done:!0}):"keys"==n?{value:r,done:!1}:"values"==n?{value:t[r],done:!1}:{value:[r,t[r]],done:!1}}),"values");var v=i.Arguments=i.Array;if(o("keys"),o("values"),o("entries"),!u&&c&&"values"!==v.name)try{l(v,"name",{value:"values"})}catch(h){}},7070:(e,t,n)=>{"use strict";var r=n(8934),o=n(1890),i=n(6894),a=o([].reverse),l=[1,2];r({target:"Array",proto:!0,forced:String(l)===String(l.reverse())},{reverse:function(){return i(this)&&(this.length=this.length),a(this)}})},6245:(e,t,n)=>{var r=n(2852);r("flat")},6701:(e,t,n)=>{var r=n(8934),o=n(7358),i=n(4157),a=n(7971),l="WebAssembly",s=o[l],u=7!==Error("e",{cause:7}).cause,c=function(e,t){var n={};n[e]=a(e,t,u),r({global:!0,forced:u},n)},d=function(e,t){if(s&&s[e]){var n={};n[e]=a(l+"."+e,t,u),r({target:l,stat:!0,forced:u},n)}};c("Error",(function(e){return function(t){return i(e,this,arguments)}})),c("EvalError",(function(e){return function(t){return i(e,this,arguments)}})),c("RangeError",(function(e){return function(t){return i(e,this,arguments)}})),c("ReferenceError",(function(e){return function(t){return i(e,this,arguments)}})),c("SyntaxError",(function(e){return function(t){return i(e,this,arguments)}})),c("TypeError",(function(e){return function(t){return i(e,this,arguments)}})),c("URIError",(function(e){return function(t){return i(e,this,arguments)}})),d("CompileError",(function(e){return function(t){return i(e,this,arguments)}})),d("LinkError",(function(e){return function(t){return i(e,this,arguments)}})),d("RuntimeError",(function(e){return function(t){return i(e,this,arguments)}}))},2100:(e,t,n)=>{var r=n(8934),o=n(7358),i=n(9694),a=n(4157),l=n(1890),s=n(6400),u=o.Array,c=i("JSON","stringify"),d=l(/./.exec),f=l("".charAt),p=l("".charCodeAt),v=l("".replace),h=l(1..toString),g=/[\uD800-\uDFFF]/g,m=/^[\uD800-\uDBFF]$/,b=/^[\uDC00-\uDFFF]$/,y=function(e,t,n){var r=f(n,t-1),o=f(n,t+1);return d(m,e)&&!d(b,o)||d(b,e)&&!d(m,r)?"\\u"+h(p(e,0),16):e},w=s((function(){return'"\\udf06\\ud834"'!==c("\udf06\ud834")||'"\\udead"'!==c("\udead")}));c&&r({target:"JSON",stat:!0,forced:w},{stringify:function(e,t,n){for(var r=0,o=arguments.length,i=u(o);r<o;r++)i[r]=arguments[r];var l=a(c,null,i);return"string"==typeof l?v(l,g,y):l}})},7280:(e,t,n)=>{"use strict";var r=n(8934),o=n(4348);r({target:"RegExp",proto:!0,forced:/./.exec!==o},{exec:o})},839:(e,t,n)=>{"use strict";var r=n(1021).charAt,o=n(4481),i=n(7624),a=n(8810),l="String Iterator",s=i.set,u=i.getterFor(l);a(String,"String",(function(e){s(this,{type:l,string:o(e),index:0})}),(function(){var e,t=u(this),n=t.string,o=t.index;return o>=n.length?{value:void 0,done:!0}:(e=r(n,o),t.index+=e.length,{value:e,done:!1})}))},5363:(e,t,n)=>{"use strict";var r=n(4157),o=n(3577),i=n(1890),a=n(9529),l=n(6400),s=n(7950),u=n(419),c=n(1860),d=n(4068),f=n(4481),p=n(7933),v=n(6412),h=n(2344),g=n(8716),m=n(9395),b=n(854),y=b("replace"),w=Math.max,x=Math.min,_=i([].concat),k=i([].push),S=i("".indexOf),C=i("".slice),F=function(e){return void 0===e?e:String(e)},E=function(){return"$0"==="a".replace(/./,"$0")}(),R=function(){return!!/./[y]&&""===/./[y]("a","$0")}(),A=!l((function(){var e=/./;return e.exec=function(){var e=[];return e.groups={a:"7"},e},"7"!=="".replace(e,"$<a>")}));a("replace",(function(e,t,n){var i=R?"$":"$0";return[function(e,n){var r=p(this),i=void 0==e?void 0:h(e,y);return i?o(i,e,r,n):o(t,f(r),e,n)},function(e,o){var a=s(this),l=f(e);if("string"==typeof o&&-1===S(o,i)&&-1===S(o,"$<")){var p=n(t,a,l,o);if(p.done)return p.value}var h=u(o);h||(o=f(o));var b=a.global;if(b){var y=a.unicode;a.lastIndex=0}var E=[];while(1){var R=m(a,l);if(null===R)break;if(k(E,R),!b)break;var A=f(R[0]);""===A&&(a.lastIndex=v(l,d(a.lastIndex),y))}for(var q="",T=0,L=0;L<E.length;L++){R=E[L];for(var P=f(R[0]),O=w(x(c(R.index),l.length),0),M=[],B=1;B<R.length;B++)k(M,F(R[B]));var $=R.groups;if(h){var I=_([P],M,O,l);void 0!==$&&k(I,$);var j=f(r(o,void 0,I))}else j=g(P,l,O,M,$,o);O>=T&&(q+=C(l,T,O)+j,T=O+P.length)}return q+C(l,T)}]}),!A||!E||R)},6801:(e,t,n)=>{"use strict";var r=n(8934),o=n(6304).trim,i=n(7894);r({target:"String",proto:!0,forced:i("trim")},{trim:function(){return o(this)}})},5123:(e,t,n)=>{"use strict";var r=n(683),o=n(6042),i=n(1860),a=r.aTypedArray,l=r.exportTypedArrayMethod;l("at",(function(e){var t=a(this),n=o(t),r=i(e),l=r>=0?r:n+r;return l<0||l>=n?void 0:t[l]}))},8685:(e,t,n)=>{"use strict";var r=n(7358),o=n(3577),i=n(683),a=n(6042),l=n(701),s=n(7475),u=n(6400),c=r.RangeError,d=r.Int8Array,f=d&&d.prototype,p=f&&f.set,v=i.aTypedArray,h=i.exportTypedArrayMethod,g=!u((function(){var e=new Uint8ClampedArray(2);return o(p,e,{length:1,0:3},1),3!==e[1]})),m=g&&i.NATIVE_ARRAY_BUFFER_VIEWS&&u((function(){var e=new d(2);return e.set(1),e.set("2",1),0!==e[0]||2!==e[1]}));h("set",(function(e){v(this);var t=l(arguments.length>1?arguments[1]:void 0,1),n=s(e);if(g)return o(p,this,n,t);var r=this.length,i=a(n),u=0;if(i+t>r)throw c("Wrong length");while(u<i)this[t+u]=n[u++]}),!g||m)},2396:(e,t,n)=>{"use strict";var r=n(7358),o=n(1890),i=n(6400),a=n(392),l=n(6534),s=n(683),u=n(1544),c=n(8979),d=n(5068),f=n(1513),p=r.Array,v=s.aTypedArray,h=s.exportTypedArrayMethod,g=r.Uint16Array,m=g&&o(g.prototype.sort),b=!!m&&!(i((function(){m(new g(2),null)}))&&i((function(){m(new g(2),{})}))),y=!!m&&!i((function(){if(d)return d<74;if(u)return u<67;if(c)return!0;if(f)return f<602;var e,t,n=new g(516),r=p(516);for(e=0;e<516;e++)t=e%4,n[e]=515-e,r[e]=e-2*t+3;for(m(n,(function(e,t){return(e/4|0)-(t/4|0)})),e=0;e<516;e++)if(n[e]!==r[e])return!0})),w=function(e){return function(t,n){return void 0!==e?+e(t,n)||0:n!==n?-1:t!==t?1:0===t&&0===n?1/t>0&&1/n<0?1:-1:t>n}};h("sort",(function(e){return void 0!==e&&a(e),y?m(this,e):l(v(this),w(e))}),!y||b)},6105:(e,t,n)=>{var r=n(6968);r("Uint8",(function(e){return function(t,n,r){return e(this,t,n,r)}}))},71:(e,t,n)=>{var r=n(7358),o=n(4296),i=n(8753),a=n(6843),l=n(1904),s=n(854),u=s("iterator"),c=s("toStringTag"),d=a.values,f=function(e,t){if(e){if(e[u]!==d)try{l(e,u,d)}catch(r){e[u]=d}if(e[c]||l(e,c,t),o[t])for(var n in a)if(e[n]!==a[n])try{l(e,n,a[n])}catch(r){e[n]=a[n]}}};for(var p in o)f(r[p]&&r[p].prototype,p);f(i,"DOMTokenList")},6016:(e,t,n)=>{"use strict";n(6843);var r=n(8934),o=n(7358),i=n(9694),a=n(3577),l=n(1890),s=n(6595),u=n(298),c=n(9833),d=n(1061),f=n(5912),p=n(7624),v=n(2827),h=n(419),g=n(7322),m=n(422),b=n(5976),y=n(7950),w=n(776),x=n(4481),_=n(1074),k=n(5442),S=n(2151),C=n(7143),F=n(6596),E=n(854),R=n(6534),A=E("iterator"),q="URLSearchParams",T=q+"Iterator",L=p.set,P=p.getterFor(q),O=p.getterFor(T),M=i("fetch"),B=i("Request"),$=i("Headers"),I=B&&B.prototype,j=$&&$.prototype,N=o.RegExp,V=o.TypeError,U=o.decodeURIComponent,H=o.encodeURIComponent,z=l("".charAt),D=l([].join),Z=l([].push),W=l("".replace),Y=l([].shift),J=l([].splice),K=l("".split),X=l("".slice),G=/\+/g,Q=Array(4),ee=function(e){return Q[e-1]||(Q[e-1]=N("((?:%[\\da-f]{2}){"+e+"})","gi"))},te=function(e){try{return U(e)}catch(t){return e}},ne=function(e){var t=W(e,G," "),n=4;try{return U(t)}catch(r){while(n)t=W(t,ee(n--),te);return t}},re=/[!'()~]|%20/g,oe={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+"},ie=function(e){return oe[e]},ae=function(e){return W(H(e),re,ie)},le=f((function(e,t){L(this,{type:T,iterator:S(P(e).entries),kind:t})}),"Iterator",(function(){var e=O(this),t=e.kind,n=e.iterator.next(),r=n.value;return n.done||(n.value="keys"===t?r.key:"values"===t?r.value:[r.key,r.value]),n}),!0),se=function(e){this.entries=[],this.url=null,void 0!==e&&(w(e)?this.parseObject(e):this.parseQuery("string"==typeof e?"?"===z(e,0)?X(e,1):e:x(e)))};se.prototype={type:q,bindURL:function(e){this.url=e,this.update()},parseObject:function(e){var t,n,r,o,i,l,s,u=C(e);if(u){t=S(e,u),n=t.next;while(!(r=a(n,t)).done){if(o=S(y(r.value)),i=o.next,(l=a(i,o)).done||(s=a(i,o)).done||!a(i,o).done)throw V("Expected sequence with length 2");Z(this.entries,{key:x(l.value),value:x(s.value)})}}else for(var c in e)g(e,c)&&Z(this.entries,{key:c,value:x(e[c])})},parseQuery:function(e){if(e){var t,n,r=K(e,"&"),o=0;while(o<r.length)t=r[o++],t.length&&(n=K(t,"="),Z(this.entries,{key:ne(Y(n)),value:ne(D(n,"="))}))}},serialize:function(){var e,t=this.entries,n=[],r=0;while(r<t.length)e=t[r++],Z(n,ae(e.key)+"="+ae(e.value));return D(n,"&")},update:function(){this.entries.length=0,this.parseQuery(this.url.query)},updateURL:function(){this.url&&this.url.update()}};var ue=function(){v(this,ce);var e=arguments.length>0?arguments[0]:void 0;L(this,new se(e))},ce=ue.prototype;if(c(ce,{append:function(e,t){F(arguments.length,2);var n=P(this);Z(n.entries,{key:x(e),value:x(t)}),n.updateURL()},delete:function(e){F(arguments.length,1);var t=P(this),n=t.entries,r=x(e),o=0;while(o<n.length)n[o].key===r?J(n,o,1):o++;t.updateURL()},get:function(e){F(arguments.length,1);for(var t=P(this).entries,n=x(e),r=0;r<t.length;r++)if(t[r].key===n)return t[r].value;return null},getAll:function(e){F(arguments.length,1);for(var t=P(this).entries,n=x(e),r=[],o=0;o<t.length;o++)t[o].key===n&&Z(r,t[o].value);return r},has:function(e){F(arguments.length,1);var t=P(this).entries,n=x(e),r=0;while(r<t.length)if(t[r++].key===n)return!0;return!1},set:function(e,t){F(arguments.length,1);for(var n,r=P(this),o=r.entries,i=!1,a=x(e),l=x(t),s=0;s<o.length;s++)n=o[s],n.key===a&&(i?J(o,s--,1):(i=!0,n.value=l));i||Z(o,{key:a,value:l}),r.updateURL()},sort:function(){var e=P(this);R(e.entries,(function(e,t){return e.key>t.key?1:-1})),e.updateURL()},forEach:function(e){var t,n=P(this).entries,r=m(e,arguments.length>1?arguments[1]:void 0),o=0;while(o<n.length)t=n[o++],r(t.value,t.key,this)},keys:function(){return new le(this,"keys")},values:function(){return new le(this,"values")},entries:function(){return new le(this,"entries")}},{enumerable:!0}),u(ce,A,ce.entries,{name:"entries"}),u(ce,"toString",(function(){return P(this).serialize()}),{enumerable:!0}),d(ue,q),r({global:!0,forced:!s},{URLSearchParams:ue}),!s&&h($)){var de=l(j.has),fe=l(j.set),pe=function(e){if(w(e)){var t,n=e.body;if(b(n)===q)return t=e.headers?new $(e.headers):new $,de(t,"content-type")||fe(t,"content-type","application/x-www-form-urlencoded;charset=UTF-8"),_(e,{body:k(0,x(n)),headers:k(0,t)})}return e};if(h(M)&&r({global:!0,enumerable:!0,forced:!0},{fetch:function(e){return M(e,arguments.length>1?pe(arguments[1]):{})}}),h(B)){var ve=function(e){return v(this,I),new B(e,arguments.length>1?pe(arguments[1]):{})};I.constructor=ve,ve.prototype=I,r({global:!0,forced:!0},{Request:ve})}}e.exports={URLSearchParams:ue,getState:P}},7965:(e,t,n)=>{"use strict";n(839);var r,o=n(8934),i=n(9631),a=n(6595),l=n(7358),s=n(422),u=n(1890),c=n(3605).f,d=n(298),f=n(2827),p=n(7322),v=n(8439),h=n(2029),g=n(5771),m=n(1021).codeAt,b=n(5253),y=n(4481),w=n(1061),x=n(6016),_=n(7624),k=_.set,S=_.getterFor("URL"),C=x.URLSearchParams,F=x.getState,E=l.URL,R=l.TypeError,A=l.parseInt,q=Math.floor,T=Math.pow,L=u("".charAt),P=u(/./.exec),O=u([].join),M=u(1..toString),B=u([].pop),$=u([].push),I=u("".replace),j=u([].shift),N=u("".split),V=u("".slice),U=u("".toLowerCase),H=u([].unshift),z="Invalid authority",D="Invalid scheme",Z="Invalid host",W="Invalid port",Y=/[a-z]/i,J=/[\d+-.a-z]/i,K=/\d/,X=/^0x/i,G=/^[0-7]+$/,Q=/^\d+$/,ee=/^[\da-f]+$/i,te=/[\0\t\n\r #%/:<>?@[\\\]^|]/,ne=/[\0\t\n\r #/:<>?@[\\\]^|]/,re=/^[\u0000-\u0020]+|[\u0000-\u0020]+$/g,oe=/[\t\n\r]/g,ie=function(e){var t,n,r,o,i,a,l,s=N(e,".");if(s.length&&""==s[s.length-1]&&s.length--,t=s.length,t>4)return e;for(n=[],r=0;r<t;r++){if(o=s[r],""==o)return e;if(i=10,o.length>1&&"0"==L(o,0)&&(i=P(X,o)?16:8,o=V(o,8==i?1:2)),""===o)a=0;else{if(!P(10==i?Q:8==i?G:ee,o))return e;a=A(o,i)}$(n,a)}for(r=0;r<t;r++)if(a=n[r],r==t-1){if(a>=T(256,5-t))return null}else if(a>255)return null;for(l=B(n),r=0;r<n.length;r++)l+=n[r]*T(256,3-r);return l},ae=function(e){var t,n,r,o,i,a,l,s=[0,0,0,0,0,0,0,0],u=0,c=null,d=0,f=function(){return L(e,d)};if(":"==f()){if(":"!=L(e,1))return;d+=2,u++,c=u}while(f()){if(8==u)return;if(":"!=f()){t=n=0;while(n<4&&P(ee,f()))t=16*t+A(f(),16),d++,n++;if("."==f()){if(0==n)return;if(d-=n,u>6)return;r=0;while(f()){if(o=null,r>0){if(!("."==f()&&r<4))return;d++}if(!P(K,f()))return;while(P(K,f())){if(i=A(f(),10),null===o)o=i;else{if(0==o)return;o=10*o+i}if(o>255)return;d++}s[u]=256*s[u]+o,r++,2!=r&&4!=r||u++}if(4!=r)return;break}if(":"==f()){if(d++,!f())return}else if(f())return;s[u++]=t}else{if(null!==c)return;d++,u++,c=u}}if(null!==c){a=u-c,u=7;while(0!=u&&a>0)l=s[u],s[u--]=s[c+a-1],s[c+--a]=l}else if(8!=u)return;return s},le=function(e){for(var t=null,n=1,r=null,o=0,i=0;i<8;i++)0!==e[i]?(o>n&&(t=r,n=o),r=null,o=0):(null===r&&(r=i),++o);return o>n&&(t=r,n=o),t},se=function(e){var t,n,r,o;if("number"==typeof e){for(t=[],n=0;n<4;n++)H(t,e%256),e=q(e/256);return O(t,".")}if("object"==typeof e){for(t="",r=le(e),n=0;n<8;n++)o&&0===e[n]||(o&&(o=!1),r===n?(t+=n?":":"::",o=!0):(t+=M(e[n],16),n<7&&(t+=":")));return"["+t+"]"}return e},ue={},ce=v({},ue,{" ":1,'"':1,"<":1,">":1,"`":1}),de=v({},ce,{"#":1,"?":1,"{":1,"}":1}),fe=v({},de,{"/":1,":":1,";":1,"=":1,"@":1,"[":1,"\\":1,"]":1,"^":1,"|":1}),pe=function(e,t){var n=m(e,0);return n>32&&n<127&&!p(t,e)?e:encodeURIComponent(e)},ve={ftp:21,file:null,http:80,https:443,ws:80,wss:443},he=function(e,t){var n;return 2==e.length&&P(Y,L(e,0))&&(":"==(n=L(e,1))||!t&&"|"==n)},ge=function(e){var t;return e.length>1&&he(V(e,0,2))&&(2==e.length||"/"===(t=L(e,2))||"\\"===t||"?"===t||"#"===t)},me=function(e){return"."===e||"%2e"===U(e)},be=function(e){return e=U(e),".."===e||"%2e."===e||".%2e"===e||"%2e%2e"===e},ye={},we={},xe={},_e={},ke={},Se={},Ce={},Fe={},Ee={},Re={},Ae={},qe={},Te={},Le={},Pe={},Oe={},Me={},Be={},$e={},Ie={},je={},Ne=function(e,t,n){var r,o,i,a=y(e);if(t){if(o=this.parse(a),o)throw R(o);this.searchParams=null}else{if(void 0!==n&&(r=new Ne(n,!0)),o=this.parse(a,null,r),o)throw R(o);i=F(new C),i.bindURL(this),this.searchParams=i}};Ne.prototype={type:"URL",parse:function(e,t,n){var o,i,a,l,s=this,u=t||ye,c=0,d="",f=!1,v=!1,m=!1;e=y(e),t||(s.scheme="",s.username="",s.password="",s.host=null,s.port=null,s.path=[],s.query=null,s.fragment=null,s.cannotBeABaseURL=!1,e=I(e,re,"")),e=I(e,oe,""),o=h(e);while(c<=o.length){switch(i=o[c],u){case ye:if(!i||!P(Y,i)){if(t)return D;u=xe;continue}d+=U(i),u=we;break;case we:if(i&&(P(J,i)||"+"==i||"-"==i||"."==i))d+=U(i);else{if(":"!=i){if(t)return D;d="",u=xe,c=0;continue}if(t&&(s.isSpecial()!=p(ve,d)||"file"==d&&(s.includesCredentials()||null!==s.port)||"file"==s.scheme&&!s.host))return;if(s.scheme=d,t)return void(s.isSpecial()&&ve[s.scheme]==s.port&&(s.port=null));d="","file"==s.scheme?u=Le:s.isSpecial()&&n&&n.scheme==s.scheme?u=_e:s.isSpecial()?u=Fe:"/"==o[c+1]?(u=ke,c++):(s.cannotBeABaseURL=!0,$(s.path,""),u=$e)}break;case xe:if(!n||n.cannotBeABaseURL&&"#"!=i)return D;if(n.cannotBeABaseURL&&"#"==i){s.scheme=n.scheme,s.path=g(n.path),s.query=n.query,s.fragment="",s.cannotBeABaseURL=!0,u=je;break}u="file"==n.scheme?Le:Se;continue;case _e:if("/"!=i||"/"!=o[c+1]){u=Se;continue}u=Ee,c++;break;case ke:if("/"==i){u=Re;break}u=Be;continue;case Se:if(s.scheme=n.scheme,i==r)s.username=n.username,s.password=n.password,s.host=n.host,s.port=n.port,s.path=g(n.path),s.query=n.query;else if("/"==i||"\\"==i&&s.isSpecial())u=Ce;else if("?"==i)s.username=n.username,s.password=n.password,s.host=n.host,s.port=n.port,s.path=g(n.path),s.query="",u=Ie;else{if("#"!=i){s.username=n.username,s.password=n.password,s.host=n.host,s.port=n.port,s.path=g(n.path),s.path.length--,u=Be;continue}s.username=n.username,s.password=n.password,s.host=n.host,s.port=n.port,s.path=g(n.path),s.query=n.query,s.fragment="",u=je}break;case Ce:if(!s.isSpecial()||"/"!=i&&"\\"!=i){if("/"!=i){s.username=n.username,s.password=n.password,s.host=n.host,s.port=n.port,u=Be;continue}u=Re}else u=Ee;break;case Fe:if(u=Ee,"/"!=i||"/"!=L(d,c+1))continue;c++;break;case Ee:if("/"!=i&&"\\"!=i){u=Re;continue}break;case Re:if("@"==i){f&&(d="%40"+d),f=!0,a=h(d);for(var b=0;b<a.length;b++){var w=a[b];if(":"!=w||m){var x=pe(w,fe);m?s.password+=x:s.username+=x}else m=!0}d=""}else if(i==r||"/"==i||"?"==i||"#"==i||"\\"==i&&s.isSpecial()){if(f&&""==d)return z;c-=h(d).length+1,d="",u=Ae}else d+=i;break;case Ae:case qe:if(t&&"file"==s.scheme){u=Oe;continue}if(":"!=i||v){if(i==r||"/"==i||"?"==i||"#"==i||"\\"==i&&s.isSpecial()){if(s.isSpecial()&&""==d)return Z;if(t&&""==d&&(s.includesCredentials()||null!==s.port))return;if(l=s.parseHost(d),l)return l;if(d="",u=Me,t)return;continue}"["==i?v=!0:"]"==i&&(v=!1),d+=i}else{if(""==d)return Z;if(l=s.parseHost(d),l)return l;if(d="",u=Te,t==qe)return}break;case Te:if(!P(K,i)){if(i==r||"/"==i||"?"==i||"#"==i||"\\"==i&&s.isSpecial()||t){if(""!=d){var _=A(d,10);if(_>65535)return W;s.port=s.isSpecial()&&_===ve[s.scheme]?null:_,d=""}if(t)return;u=Me;continue}return W}d+=i;break;case Le:if(s.scheme="file","/"==i||"\\"==i)u=Pe;else{if(!n||"file"!=n.scheme){u=Be;continue}if(i==r)s.host=n.host,s.path=g(n.path),s.query=n.query;else if("?"==i)s.host=n.host,s.path=g(n.path),s.query="",u=Ie;else{if("#"!=i){ge(O(g(o,c),""))||(s.host=n.host,s.path=g(n.path),s.shortenPath()),u=Be;continue}s.host=n.host,s.path=g(n.path),s.query=n.query,s.fragment="",u=je}}break;case Pe:if("/"==i||"\\"==i){u=Oe;break}n&&"file"==n.scheme&&!ge(O(g(o,c),""))&&(he(n.path[0],!0)?$(s.path,n.path[0]):s.host=n.host),u=Be;continue;case Oe:if(i==r||"/"==i||"\\"==i||"?"==i||"#"==i){if(!t&&he(d))u=Be;else if(""==d){if(s.host="",t)return;u=Me}else{if(l=s.parseHost(d),l)return l;if("localhost"==s.host&&(s.host=""),t)return;d="",u=Me}continue}d+=i;break;case Me:if(s.isSpecial()){if(u=Be,"/"!=i&&"\\"!=i)continue}else if(t||"?"!=i)if(t||"#"!=i){if(i!=r&&(u=Be,"/"!=i))continue}else s.fragment="",u=je;else s.query="",u=Ie;break;case Be:if(i==r||"/"==i||"\\"==i&&s.isSpecial()||!t&&("?"==i||"#"==i)){if(be(d)?(s.shortenPath(),"/"==i||"\\"==i&&s.isSpecial()||$(s.path,"")):me(d)?"/"==i||"\\"==i&&s.isSpecial()||$(s.path,""):("file"==s.scheme&&!s.path.length&&he(d)&&(s.host&&(s.host=""),d=L(d,0)+":"),$(s.path,d)),d="","file"==s.scheme&&(i==r||"?"==i||"#"==i))while(s.path.length>1&&""===s.path[0])j(s.path);"?"==i?(s.query="",u=Ie):"#"==i&&(s.fragment="",u=je)}else d+=pe(i,de);break;case $e:"?"==i?(s.query="",u=Ie):"#"==i?(s.fragment="",u=je):i!=r&&(s.path[0]+=pe(i,ue));break;case Ie:t||"#"!=i?i!=r&&("'"==i&&s.isSpecial()?s.query+="%27":s.query+="#"==i?"%23":pe(i,ue)):(s.fragment="",u=je);break;case je:i!=r&&(s.fragment+=pe(i,ce));break}c++}},parseHost:function(e){var t,n,r;if("["==L(e,0)){if("]"!=L(e,e.length-1))return Z;if(t=ae(V(e,1,-1)),!t)return Z;this.host=t}else if(this.isSpecial()){if(e=b(e),P(te,e))return Z;if(t=ie(e),null===t)return Z;this.host=t}else{if(P(ne,e))return Z;for(t="",n=h(e),r=0;r<n.length;r++)t+=pe(n[r],ue);this.host=t}},cannotHaveUsernamePasswordPort:function(){return!this.host||this.cannotBeABaseURL||"file"==this.scheme},includesCredentials:function(){return""!=this.username||""!=this.password},isSpecial:function(){return p(ve,this.scheme)},shortenPath:function(){var e=this.path,t=e.length;!t||"file"==this.scheme&&1==t&&he(e[0],!0)||e.length--},serialize:function(){var e=this,t=e.scheme,n=e.username,r=e.password,o=e.host,i=e.port,a=e.path,l=e.query,s=e.fragment,u=t+":";return null!==o?(u+="//",e.includesCredentials()&&(u+=n+(r?":"+r:"")+"@"),u+=se(o),null!==i&&(u+=":"+i)):"file"==t&&(u+="//"),u+=e.cannotBeABaseURL?a[0]:a.length?"/"+O(a,"/"):"",null!==l&&(u+="?"+l),null!==s&&(u+="#"+s),u},setHref:function(e){var t=this.parse(e);if(t)throw R(t);this.searchParams.update()},getOrigin:function(){var e=this.scheme,t=this.port;if("blob"==e)try{return new Ve(e.path[0]).origin}catch(n){return"null"}return"file"!=e&&this.isSpecial()?e+"://"+se(this.host)+(null!==t?":"+t:""):"null"},getProtocol:function(){return this.scheme+":"},setProtocol:function(e){this.parse(y(e)+":",ye)},getUsername:function(){return this.username},setUsername:function(e){var t=h(y(e));if(!this.cannotHaveUsernamePasswordPort()){this.username="";for(var n=0;n<t.length;n++)this.username+=pe(t[n],fe)}},getPassword:function(){return this.password},setPassword:function(e){var t=h(y(e));if(!this.cannotHaveUsernamePasswordPort()){this.password="";for(var n=0;n<t.length;n++)this.password+=pe(t[n],fe)}},getHost:function(){var e=this.host,t=this.port;return null===e?"":null===t?se(e):se(e)+":"+t},setHost:function(e){this.cannotBeABaseURL||this.parse(e,Ae)},getHostname:function(){var e=this.host;return null===e?"":se(e)},setHostname:function(e){this.cannotBeABaseURL||this.parse(e,qe)},getPort:function(){var e=this.port;return null===e?"":y(e)},setPort:function(e){this.cannotHaveUsernamePasswordPort()||(e=y(e),""==e?this.port=null:this.parse(e,Te))},getPathname:function(){var e=this.path;return this.cannotBeABaseURL?e[0]:e.length?"/"+O(e,"/"):""},setPathname:function(e){this.cannotBeABaseURL||(this.path=[],this.parse(e,Me))},getSearch:function(){var e=this.query;return e?"?"+e:""},setSearch:function(e){e=y(e),""==e?this.query=null:("?"==L(e,0)&&(e=V(e,1)),this.query="",this.parse(e,Ie)),this.searchParams.update()},getSearchParams:function(){return this.searchParams.facade},getHash:function(){var e=this.fragment;return e?"#"+e:""},setHash:function(e){e=y(e),""!=e?("#"==L(e,0)&&(e=V(e,1)),this.fragment="",this.parse(e,je)):this.fragment=null},update:function(){this.query=this.searchParams.serialize()||null}};var Ve=function(e){var t=f(this,Ue),n=arguments.length>1?arguments[1]:void 0,r=k(t,new Ne(e,!1,n));i||(t.href=r.serialize(),t.origin=r.getOrigin(),t.protocol=r.getProtocol(),t.username=r.getUsername(),t.password=r.getPassword(),t.host=r.getHost(),t.hostname=r.getHostname(),t.port=r.getPort(),t.pathname=r.getPathname(),t.search=r.getSearch(),t.searchParams=r.getSearchParams(),t.hash=r.getHash())},Ue=Ve.prototype,He=function(e,t){return{get:function(){return S(this)[e]()},set:t&&function(e){return S(this)[t](e)},configurable:!0,enumerable:!0}};if(i&&c(Ue,{href:He("serialize","setHref"),origin:He("getOrigin"),protocol:He("getProtocol","setProtocol"),username:He("getUsername","setUsername"),password:He("getPassword","setPassword"),host:He("getHost","setHost"),hostname:He("getHostname","setHostname"),port:He("getPort","setPort"),pathname:He("getPathname","setPathname"),search:He("getSearch","setSearch"),searchParams:He("getSearchParams"),hash:He("getHash","setHash")}),d(Ue,"toJSON",(function(){return S(this).serialize()}),{enumerable:!0}),d(Ue,"toString",(function(){return S(this).serialize()}),{enumerable:!0}),E){var ze=E.createObjectURL,De=E.revokeObjectURL;ze&&d(Ve,"createObjectURL",s(ze,E)),De&&d(Ve,"revokeObjectURL",s(De,E))}w(Ve,"URL"),o({global:!0,forced:!a,sham:!i},{URL:Ve})},4260:(e,t)=>{"use strict";t.Z=(e,t)=>{const n=e.__vccOpts||e;for(const[r,o]of t)n[r]=o;return n}},9582:(e,t,n)=>{"use strict";n.d(t,{p7:()=>tt,r5:()=>Z});var r=n(3673),o=n(1959);
/*!
  * vue-router v4.0.12
  * (c) 2021 Eduardo San Martin Morote
  * @license MIT
  */
const i="function"===typeof Symbol&&"symbol"===typeof Symbol.toStringTag,a=e=>i?Symbol(e):"_vr_"+e,l=a("rvlm"),s=a("rvd"),u=a("r"),c=a("rl"),d=a("rvl"),f="undefined"!==typeof window;function p(e){return e.__esModule||i&&"Module"===e[Symbol.toStringTag]}const v=Object.assign;function h(e,t){const n={};for(const r in t){const o=t[r];n[r]=Array.isArray(o)?o.map(e):e(o)}return n}const g=()=>{};const m=/\/$/,b=e=>e.replace(m,"");function y(e,t,n="/"){let r,o={},i="",a="";const l=t.indexOf("?"),s=t.indexOf("#",l>-1?l:0);return l>-1&&(r=t.slice(0,l),i=t.slice(l+1,s>-1?s:t.length),o=e(i)),s>-1&&(r=r||t.slice(0,s),a=t.slice(s,t.length)),r=E(null!=r?r:t,n),{fullPath:r+(i&&"?")+i+a,path:r,query:o,hash:a}}function w(e,t){const n=t.query?e(t.query):"";return t.path+(n&&"?")+n+(t.hash||"")}function x(e,t){return t&&e.toLowerCase().startsWith(t.toLowerCase())?e.slice(t.length)||"/":e}function _(e,t,n){const r=t.matched.length-1,o=n.matched.length-1;return r>-1&&r===o&&k(t.matched[r],n.matched[o])&&S(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}function k(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function S(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(!C(e[n],t[n]))return!1;return!0}function C(e,t){return Array.isArray(e)?F(e,t):Array.isArray(t)?F(t,e):e===t}function F(e,t){return Array.isArray(t)?e.length===t.length&&e.every(((e,n)=>e===t[n])):1===e.length&&e[0]===t}function E(e,t){if(e.startsWith("/"))return e;if(!e)return t;const n=t.split("/"),r=e.split("/");let o,i,a=n.length-1;for(o=0;o<r.length;o++)if(i=r[o],1!==a&&"."!==i){if(".."!==i)break;a--}return n.slice(0,a).join("/")+"/"+r.slice(o-(o===r.length?1:0)).join("/")}var R,A;(function(e){e["pop"]="pop",e["push"]="push"})(R||(R={})),function(e){e["back"]="back",e["forward"]="forward",e["unknown"]=""}(A||(A={}));function q(e){if(!e)if(f){const t=document.querySelector("base");e=t&&t.getAttribute("href")||"/",e=e.replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return"/"!==e[0]&&"#"!==e[0]&&(e="/"+e),b(e)}const T=/^[^#]+#/;function L(e,t){return e.replace(T,"#")+t}function P(e,t){const n=document.documentElement.getBoundingClientRect(),r=e.getBoundingClientRect();return{behavior:t.behavior,left:r.left-n.left-(t.left||0),top:r.top-n.top-(t.top||0)}}const O=()=>({left:window.pageXOffset,top:window.pageYOffset});function M(e){let t;if("el"in e){const n=e.el,r="string"===typeof n&&n.startsWith("#");0;const o="string"===typeof n?r?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!o)return;t=P(o,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(null!=t.left?t.left:window.pageXOffset,null!=t.top?t.top:window.pageYOffset)}function B(e,t){const n=history.state?history.state.position-t:-1;return n+e}const $=new Map;function I(e,t){$.set(e,t)}function j(e){const t=$.get(e);return $.delete(e),t}let N=()=>location.protocol+"//"+location.host;function V(e,t){const{pathname:n,search:r,hash:o}=t,i=e.indexOf("#");if(i>-1){let t=o.includes(e.slice(i))?e.slice(i).length:1,n=o.slice(t);return"/"!==n[0]&&(n="/"+n),x(n,"")}const a=x(n,e);return a+r+o}function U(e,t,n,r){let o=[],i=[],a=null;const l=({state:i})=>{const l=V(e,location),s=n.value,u=t.value;let c=0;if(i){if(n.value=l,t.value=i,a&&a===s)return void(a=null);c=u?i.position-u.position:0}else r(l);o.forEach((e=>{e(n.value,s,{delta:c,type:R.pop,direction:c?c>0?A.forward:A.back:A.unknown})}))};function s(){a=n.value}function u(e){o.push(e);const t=()=>{const t=o.indexOf(e);t>-1&&o.splice(t,1)};return i.push(t),t}function c(){const{history:e}=window;e.state&&e.replaceState(v({},e.state,{scroll:O()}),"")}function d(){for(const e of i)e();i=[],window.removeEventListener("popstate",l),window.removeEventListener("beforeunload",c)}return window.addEventListener("popstate",l),window.addEventListener("beforeunload",c),{pauseListeners:s,listen:u,destroy:d}}function H(e,t,n,r=!1,o=!1){return{back:e,current:t,forward:n,replaced:r,position:window.history.length,scroll:o?O():null}}function z(e){const{history:t,location:n}=window,r={value:V(e,n)},o={value:t.state};function i(r,i,a){const l=e.indexOf("#"),s=l>-1?(n.host&&document.querySelector("base")?e:e.slice(l))+r:N()+e+r;try{t[a?"replaceState":"pushState"](i,"",s),o.value=i}catch(u){console.error(u),n[a?"replace":"assign"](s)}}function a(e,n){const a=v({},t.state,H(o.value.back,e,o.value.forward,!0),n,{position:o.value.position});i(e,a,!0),r.value=e}function l(e,n){const a=v({},o.value,t.state,{forward:e,scroll:O()});i(a.current,a,!0);const l=v({},H(r.value,e,null),{position:a.position+1},n);i(e,l,!1),r.value=e}return o.value||i(r.value,{back:null,current:r.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0),{location:r,state:o,push:l,replace:a}}function D(e){e=q(e);const t=z(e),n=U(e,t.state,t.location,t.replace);function r(e,t=!0){t||n.pauseListeners(),history.go(e)}const o=v({location:"",base:e,go:r,createHref:L.bind(null,e)},t,n);return Object.defineProperty(o,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(o,"state",{enumerable:!0,get:()=>t.state.value}),o}function Z(e){return e=location.host?e||location.pathname+location.search:"",e.includes("#")||(e+="#"),D(e)}function W(e){return"string"===typeof e||e&&"object"===typeof e}function Y(e){return"string"===typeof e||"symbol"===typeof e}const J={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0},K=a("nf");var X;(function(e){e[e["aborted"]=4]="aborted",e[e["cancelled"]=8]="cancelled",e[e["duplicated"]=16]="duplicated"})(X||(X={}));function G(e,t){return v(new Error,{type:e,[K]:!0},t)}function Q(e,t){return e instanceof Error&&K in e&&(null==t||!!(e.type&t))}const ee="[^/]+?",te={sensitive:!1,strict:!1,start:!0,end:!0},ne=/[.+*?^${}()[\]/\\]/g;function re(e,t){const n=v({},te,t),r=[];let o=n.start?"^":"";const i=[];for(const c of e){const e=c.length?[]:[90];n.strict&&!c.length&&(o+="/");for(let t=0;t<c.length;t++){const r=c[t];let a=40+(n.sensitive?.25:0);if(0===r.type)t||(o+="/"),o+=r.value.replace(ne,"\\$&"),a+=40;else if(1===r.type){const{value:e,repeatable:n,optional:l,regexp:s}=r;i.push({name:e,repeatable:n,optional:l});const d=s||ee;if(d!==ee){a+=10;try{new RegExp(`(${d})`)}catch(u){throw new Error(`Invalid custom RegExp for param "${e}" (${d}): `+u.message)}}let f=n?`((?:${d})(?:/(?:${d}))*)`:`(${d})`;t||(f=l&&c.length<2?`(?:/${f})`:"/"+f),l&&(f+="?"),o+=f,a+=20,l&&(a+=-8),n&&(a+=-20),".*"===d&&(a+=-50)}e.push(a)}r.push(e)}if(n.strict&&n.end){const e=r.length-1;r[e][r[e].length-1]+=.7000000000000001}n.strict||(o+="/?"),n.end?o+="$":n.strict&&(o+="(?:/|$)");const a=new RegExp(o,n.sensitive?"":"i");function l(e){const t=e.match(a),n={};if(!t)return null;for(let r=1;r<t.length;r++){const e=t[r]||"",o=i[r-1];n[o.name]=e&&o.repeatable?e.split("/"):e}return n}function s(t){let n="",r=!1;for(const o of e){r&&n.endsWith("/")||(n+="/"),r=!1;for(const e of o)if(0===e.type)n+=e.value;else if(1===e.type){const{value:i,repeatable:a,optional:l}=e,s=i in t?t[i]:"";if(Array.isArray(s)&&!a)throw new Error(`Provided param "${i}" is an array but it is not repeatable (* or + modifiers)`);const u=Array.isArray(s)?s.join("/"):s;if(!u){if(!l)throw new Error(`Missing required param "${i}"`);o.length<2&&(n.endsWith("/")?n=n.slice(0,-1):r=!0)}n+=u}}return n}return{re:a,score:r,keys:i,parse:l,stringify:s}}function oe(e,t){let n=0;while(n<e.length&&n<t.length){const r=t[n]-e[n];if(r)return r;n++}return e.length<t.length?1===e.length&&80===e[0]?-1:1:e.length>t.length?1===t.length&&80===t[0]?1:-1:0}function ie(e,t){let n=0;const r=e.score,o=t.score;while(n<r.length&&n<o.length){const e=oe(r[n],o[n]);if(e)return e;n++}return o.length-r.length}const ae={type:0,value:""},le=/[a-zA-Z0-9_]/;function se(e){if(!e)return[[]];if("/"===e)return[[ae]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(e){throw new Error(`ERR (${n})/"${u}": ${e}`)}let n=0,r=n;const o=[];let i;function a(){i&&o.push(i),i=[]}let l,s=0,u="",c="";function d(){u&&(0===n?i.push({type:0,value:u}):1===n||2===n||3===n?(i.length>1&&("*"===l||"+"===l)&&t(`A repeatable param (${u}) must be alone in its segment. eg: '/:ids+.`),i.push({type:1,value:u,regexp:c,repeatable:"*"===l||"+"===l,optional:"*"===l||"?"===l})):t("Invalid state to consume buffer"),u="")}function f(){u+=l}while(s<e.length)if(l=e[s++],"\\"!==l||2===n)switch(n){case 0:"/"===l?(u&&d(),a()):":"===l?(d(),n=1):f();break;case 4:f(),n=r;break;case 1:"("===l?n=2:le.test(l)?f():(d(),n=0,"*"!==l&&"?"!==l&&"+"!==l&&s--);break;case 2:")"===l?"\\"==c[c.length-1]?c=c.slice(0,-1)+l:n=3:c+=l;break;case 3:d(),n=0,"*"!==l&&"?"!==l&&"+"!==l&&s--,c="";break;default:t("Unknown state");break}else r=n,n=4;return 2===n&&t(`Unfinished custom RegExp for param "${u}"`),d(),a(),o}function ue(e,t,n){const r=re(se(e.path),n);const o=v(r,{record:e,parent:t,children:[],alias:[]});return t&&!o.record.aliasOf===!t.record.aliasOf&&t.children.push(o),o}function ce(e,t){const n=[],r=new Map;function o(e){return r.get(e)}function i(e,n,r){const o=!r,l=fe(e);l.aliasOf=r&&r.record;const u=ge(t,e),c=[l];if("alias"in e){const t="string"===typeof e.alias?[e.alias]:e.alias;for(const e of t)c.push(v({},l,{components:r?r.record.components:l.components,path:e,aliasOf:r?r.record:l}))}let d,f;for(const t of c){const{path:c}=t;if(n&&"/"!==c[0]){const e=n.record.path,r="/"===e[e.length-1]?"":"/";t.path=n.record.path+(c&&r+c)}if(d=ue(t,n,u),r?r.alias.push(d):(f=f||d,f!==d&&f.alias.push(d),o&&e.name&&!ve(d)&&a(e.name)),"children"in l){const e=l.children;for(let t=0;t<e.length;t++)i(e[t],d,r&&r.children[t])}r=r||d,s(d)}return f?()=>{a(f)}:g}function a(e){if(Y(e)){const t=r.get(e);t&&(r.delete(e),n.splice(n.indexOf(t),1),t.children.forEach(a),t.alias.forEach(a))}else{const t=n.indexOf(e);t>-1&&(n.splice(t,1),e.record.name&&r.delete(e.record.name),e.children.forEach(a),e.alias.forEach(a))}}function l(){return n}function s(e){let t=0;while(t<n.length&&ie(e,n[t])>=0)t++;n.splice(t,0,e),e.record.name&&!ve(e)&&r.set(e.record.name,e)}function u(e,t){let o,i,a,l={};if("name"in e&&e.name){if(o=r.get(e.name),!o)throw G(1,{location:e});a=o.record.name,l=v(de(t.params,o.keys.filter((e=>!e.optional)).map((e=>e.name))),e.params),i=o.stringify(l)}else if("path"in e)i=e.path,o=n.find((e=>e.re.test(i))),o&&(l=o.parse(i),a=o.record.name);else{if(o=t.name?r.get(t.name):n.find((e=>e.re.test(t.path))),!o)throw G(1,{location:e,currentLocation:t});a=o.record.name,l=v({},t.params,e.params),i=o.stringify(l)}const s=[];let u=o;while(u)s.unshift(u.record),u=u.parent;return{name:a,path:i,params:l,matched:s,meta:he(s)}}return t=ge({strict:!1,end:!0,sensitive:!1},t),e.forEach((e=>i(e))),{addRoute:i,resolve:u,removeRoute:a,getRoutes:l,getRecordMatcher:o}}function de(e,t){const n={};for(const r of t)r in e&&(n[r]=e[r]);return n}function fe(e){return{path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:void 0,beforeEnter:e.beforeEnter,props:pe(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||{}:{default:e.component}}}function pe(e){const t={},n=e.props||!1;if("component"in e)t.default=n;else for(const r in e.components)t[r]="boolean"===typeof n?n:n[r];return t}function ve(e){while(e){if(e.record.aliasOf)return!0;e=e.parent}return!1}function he(e){return e.reduce(((e,t)=>v(e,t.meta)),{})}function ge(e,t){const n={};for(const r in e)n[r]=r in t?t[r]:e[r];return n}const me=/#/g,be=/&/g,ye=/\//g,we=/=/g,xe=/\?/g,_e=/\+/g,ke=/%5B/g,Se=/%5D/g,Ce=/%5E/g,Fe=/%60/g,Ee=/%7B/g,Re=/%7C/g,Ae=/%7D/g,qe=/%20/g;function Te(e){return encodeURI(""+e).replace(Re,"|").replace(ke,"[").replace(Se,"]")}function Le(e){return Te(e).replace(Ee,"{").replace(Ae,"}").replace(Ce,"^")}function Pe(e){return Te(e).replace(_e,"%2B").replace(qe,"+").replace(me,"%23").replace(be,"%26").replace(Fe,"`").replace(Ee,"{").replace(Ae,"}").replace(Ce,"^")}function Oe(e){return Pe(e).replace(we,"%3D")}function Me(e){return Te(e).replace(me,"%23").replace(xe,"%3F")}function Be(e){return null==e?"":Me(e).replace(ye,"%2F")}function $e(e){try{return decodeURIComponent(""+e)}catch(t){}return""+e}function Ie(e){const t={};if(""===e||"?"===e)return t;const n="?"===e[0],r=(n?e.slice(1):e).split("&");for(let o=0;o<r.length;++o){const e=r[o].replace(_e," "),n=e.indexOf("="),i=$e(n<0?e:e.slice(0,n)),a=n<0?null:$e(e.slice(n+1));if(i in t){let e=t[i];Array.isArray(e)||(e=t[i]=[e]),e.push(a)}else t[i]=a}return t}function je(e){let t="";for(let n in e){const r=e[n];if(n=Oe(n),null==r){void 0!==r&&(t+=(t.length?"&":"")+n);continue}const o=Array.isArray(r)?r.map((e=>e&&Pe(e))):[r&&Pe(r)];o.forEach((e=>{void 0!==e&&(t+=(t.length?"&":"")+n,null!=e&&(t+="="+e))}))}return t}function Ne(e){const t={};for(const n in e){const r=e[n];void 0!==r&&(t[n]=Array.isArray(r)?r.map((e=>null==e?null:""+e)):null==r?r:""+r)}return t}function Ve(){let e=[];function t(t){return e.push(t),()=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)}}function n(){e=[]}return{add:t,list:()=>e,reset:n}}function Ue(e,t,n,r,o){const i=r&&(r.enterCallbacks[o]=r.enterCallbacks[o]||[]);return()=>new Promise(((a,l)=>{const s=e=>{!1===e?l(G(4,{from:n,to:t})):e instanceof Error?l(e):W(e)?l(G(2,{from:t,to:e})):(i&&r.enterCallbacks[o]===i&&"function"===typeof e&&i.push(e),a())},u=e.call(r&&r.instances[o],t,n,s);let c=Promise.resolve(u);e.length<3&&(c=c.then(s)),c.catch((e=>l(e)))}))}function He(e,t,n,r){const o=[];for(const i of e)for(const e in i.components){let a=i.components[e];if("beforeRouteEnter"===t||i.instances[e])if(ze(a)){const l=a.__vccOpts||a,s=l[t];s&&o.push(Ue(s,n,r,i,e))}else{let l=a();0,o.push((()=>l.then((o=>{if(!o)return Promise.reject(new Error(`Couldn't resolve component "${e}" at "${i.path}"`));const a=p(o)?o.default:o;i.components[e]=a;const l=a.__vccOpts||a,s=l[t];return s&&Ue(s,n,r,i,e)()}))))}}return o}function ze(e){return"object"===typeof e||"displayName"in e||"props"in e||"__vccOpts"in e}function De(e){const t=(0,r.f3)(u),n=(0,r.f3)(c),i=(0,r.Fl)((()=>t.resolve((0,o.SU)(e.to)))),a=(0,r.Fl)((()=>{const{matched:e}=i.value,{length:t}=e,r=e[t-1],o=n.matched;if(!r||!o.length)return-1;const a=o.findIndex(k.bind(null,r));if(a>-1)return a;const l=Ke(e[t-2]);return t>1&&Ke(r)===l&&o[o.length-1].path!==l?o.findIndex(k.bind(null,e[t-2])):a})),l=(0,r.Fl)((()=>a.value>-1&&Je(n.params,i.value.params))),s=(0,r.Fl)((()=>a.value>-1&&a.value===n.matched.length-1&&S(n.params,i.value.params)));function d(n={}){return Ye(n)?t[(0,o.SU)(e.replace)?"replace":"push"]((0,o.SU)(e.to)).catch(g):Promise.resolve()}return{route:i,href:(0,r.Fl)((()=>i.value.href)),isActive:l,isExactActive:s,navigate:d}}const Ze=(0,r.aZ)({name:"RouterLink",props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"}},useLink:De,setup(e,{slots:t}){const n=(0,o.qj)(De(e)),{options:i}=(0,r.f3)(u),a=(0,r.Fl)((()=>({[Xe(e.activeClass,i.linkActiveClass,"router-link-active")]:n.isActive,[Xe(e.exactActiveClass,i.linkExactActiveClass,"router-link-exact-active")]:n.isExactActive})));return()=>{const o=t.default&&t.default(n);return e.custom?o:(0,r.h)("a",{"aria-current":n.isExactActive?e.ariaCurrentValue:null,href:n.href,onClick:n.navigate,class:a.value},o)}}}),We=Ze;function Ye(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&(void 0===e.button||0===e.button)){if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function Je(e,t){for(const n in t){const r=t[n],o=e[n];if("string"===typeof r){if(r!==o)return!1}else if(!Array.isArray(o)||o.length!==r.length||r.some(((e,t)=>e!==o[t])))return!1}return!0}function Ke(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const Xe=(e,t,n)=>null!=e?e:null!=t?t:n,Ge=(0,r.aZ)({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},setup(e,{attrs:t,slots:n}){const i=(0,r.f3)(d),a=(0,r.Fl)((()=>e.route||i.value)),u=(0,r.f3)(s,0),c=(0,r.Fl)((()=>a.value.matched[u]));(0,r.JJ)(s,u+1),(0,r.JJ)(l,c),(0,r.JJ)(d,a);const f=(0,o.iH)();return(0,r.YP)((()=>[f.value,c.value,e.name]),(([e,t,n],[r,o,i])=>{t&&(t.instances[n]=e,o&&o!==t&&e&&e===r&&(t.leaveGuards.size||(t.leaveGuards=o.leaveGuards),t.updateGuards.size||(t.updateGuards=o.updateGuards))),!e||!t||o&&k(t,o)&&r||(t.enterCallbacks[n]||[]).forEach((t=>t(e)))}),{flush:"post"}),()=>{const o=a.value,i=c.value,l=i&&i.components[e.name],s=e.name;if(!l)return Qe(n.default,{Component:l,route:o});const u=i.props[e.name],d=u?!0===u?o.params:"function"===typeof u?u(o):u:null,p=e=>{e.component.isUnmounted&&(i.instances[s]=null)},h=(0,r.h)(l,v({},d,t,{onVnodeUnmounted:p,ref:f}));return Qe(n.default,{Component:h,route:o})||h}}});function Qe(e,t){if(!e)return null;const n=e(t);return 1===n.length?n[0]:n}const et=Ge;function tt(e){const t=ce(e.routes,e),n=e.parseQuery||Ie,i=e.stringifyQuery||je,a=e.history;const l=Ve(),s=Ve(),p=Ve(),m=(0,o.XI)(J);let b=J;f&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const x=h.bind(null,(e=>""+e)),k=h.bind(null,Be),S=h.bind(null,$e);function C(e,n){let r,o;return Y(e)?(r=t.getRecordMatcher(e),o=n):o=e,t.addRoute(o,r)}function F(e){const n=t.getRecordMatcher(e);n&&t.removeRoute(n)}function E(){return t.getRoutes().map((e=>e.record))}function A(e){return!!t.getRecordMatcher(e)}function q(e,r){if(r=v({},r||m.value),"string"===typeof e){const o=y(n,e,r.path),i=t.resolve({path:o.path},r),l=a.createHref(o.fullPath);return v(o,i,{params:S(i.params),hash:$e(o.hash),redirectedFrom:void 0,href:l})}let o;if("path"in e)o=v({},e,{path:y(n,e.path,r.path).path});else{const t=v({},e.params);for(const e in t)null==t[e]&&delete t[e];o=v({},e,{params:k(e.params)}),r.params=k(r.params)}const l=t.resolve(o,r),s=e.hash||"";l.params=x(S(l.params));const u=w(i,v({},e,{hash:Le(s),path:l.path})),c=a.createHref(u);return v({fullPath:u,hash:s,query:i===je?Ne(e.query):e.query||{}},l,{redirectedFrom:void 0,href:c})}function T(e){return"string"===typeof e?y(n,e,m.value.path):v({},e)}function L(e,t){if(b!==e)return G(8,{from:t,to:e})}function P(e){return V(e)}function $(e){return P(v(T(e),{replace:!0}))}function N(e){const t=e.matched[e.matched.length-1];if(t&&t.redirect){const{redirect:n}=t;let r="function"===typeof n?n(e):n;return"string"===typeof r&&(r=r.includes("?")||r.includes("#")?r=T(r):{path:r},r.params={}),v({query:e.query,hash:e.hash,params:e.params},r)}}function V(e,t){const n=b=q(e),r=m.value,o=e.state,a=e.force,l=!0===e.replace,s=N(n);if(s)return V(v(T(s),{state:o,force:a,replace:l}),t||n);const u=n;let c;return u.redirectedFrom=t,!a&&_(i,r,n)&&(c=G(16,{to:u,from:r}),oe(r,r,!0,!1)),(c?Promise.resolve(c):H(u,r)).catch((e=>Q(e)?e:te(e,u,r))).then((e=>{if(e){if(Q(e,2))return V(v(T(e.to),{state:o,force:a,replace:l}),t||u)}else e=D(u,r,!0,l,o);return z(u,r,e),e}))}function U(e,t){const n=L(e,t);return n?Promise.reject(n):Promise.resolve()}function H(e,t){let n;const[r,o,i]=rt(e,t);n=He(r.reverse(),"beforeRouteLeave",e,t);for(const l of r)l.leaveGuards.forEach((r=>{n.push(Ue(r,e,t))}));const a=U.bind(null,e,t);return n.push(a),nt(n).then((()=>{n=[];for(const r of l.list())n.push(Ue(r,e,t));return n.push(a),nt(n)})).then((()=>{n=He(o,"beforeRouteUpdate",e,t);for(const r of o)r.updateGuards.forEach((r=>{n.push(Ue(r,e,t))}));return n.push(a),nt(n)})).then((()=>{n=[];for(const r of e.matched)if(r.beforeEnter&&!t.matched.includes(r))if(Array.isArray(r.beforeEnter))for(const o of r.beforeEnter)n.push(Ue(o,e,t));else n.push(Ue(r.beforeEnter,e,t));return n.push(a),nt(n)})).then((()=>(e.matched.forEach((e=>e.enterCallbacks={})),n=He(i,"beforeRouteEnter",e,t),n.push(a),nt(n)))).then((()=>{n=[];for(const r of s.list())n.push(Ue(r,e,t));return n.push(a),nt(n)})).catch((e=>Q(e,8)?e:Promise.reject(e)))}function z(e,t,n){for(const r of p.list())r(e,t,n)}function D(e,t,n,r,o){const i=L(e,t);if(i)return i;const l=t===J,s=f?history.state:{};n&&(r||l?a.replace(e.fullPath,v({scroll:l&&s&&s.scroll},o)):a.push(e.fullPath,o)),m.value=e,oe(e,t,n,l),re()}let Z;function W(){Z=a.listen(((e,t,n)=>{const r=q(e),o=N(r);if(o)return void V(v(o,{replace:!0}),r).catch(g);b=r;const i=m.value;f&&I(B(i.fullPath,n.delta),O()),H(r,i).catch((e=>Q(e,12)?e:Q(e,2)?(V(e.to,r).then((e=>{Q(e,20)&&!n.delta&&n.type===R.pop&&a.go(-1,!1)})).catch(g),Promise.reject()):(n.delta&&a.go(-n.delta,!1),te(e,r,i)))).then((e=>{e=e||D(r,i,!1),e&&(n.delta?a.go(-n.delta,!1):n.type===R.pop&&Q(e,20)&&a.go(-1,!1)),z(r,i,e)})).catch(g)}))}let K,X=Ve(),ee=Ve();function te(e,t,n){re(e);const r=ee.list();return r.length?r.forEach((r=>r(e,t,n))):console.error(e),Promise.reject(e)}function ne(){return K&&m.value!==J?Promise.resolve():new Promise(((e,t)=>{X.add([e,t])}))}function re(e){K||(K=!0,W(),X.list().forEach((([t,n])=>e?n(e):t())),X.reset())}function oe(t,n,o,i){const{scrollBehavior:a}=e;if(!f||!a)return Promise.resolve();const l=!o&&j(B(t.fullPath,0))||(i||!o)&&history.state&&history.state.scroll||null;return(0,r.Y3)().then((()=>a(t,n,l))).then((e=>e&&M(e))).catch((e=>te(e,t,n)))}const ie=e=>a.go(e);let ae;const le=new Set,se={currentRoute:m,addRoute:C,removeRoute:F,hasRoute:A,getRoutes:E,resolve:q,options:e,push:P,replace:$,go:ie,back:()=>ie(-1),forward:()=>ie(1),beforeEach:l.add,beforeResolve:s.add,afterEach:p.add,onError:ee.add,isReady:ne,install(e){const t=this;e.component("RouterLink",We),e.component("RouterView",et),e.config.globalProperties.$router=t,Object.defineProperty(e.config.globalProperties,"$route",{enumerable:!0,get:()=>(0,o.SU)(m)}),f&&!ae&&m.value===J&&(ae=!0,P(a.location).catch((e=>{0})));const n={};for(const o in J)n[o]=(0,r.Fl)((()=>m.value[o]));e.provide(u,t),e.provide(c,(0,o.qj)(n)),e.provide(d,m);const i=e.unmount;le.add(e),e.unmount=function(){le.delete(e),le.size<1&&(b=J,Z&&Z(),m.value=J,ae=!1,K=!1),i()}}};return se}function nt(e){return e.reduce(((e,t)=>e.then((()=>t()))),Promise.resolve())}function rt(e,t){const n=[],r=[],o=[],i=Math.max(t.matched.length,e.matched.length);for(let a=0;a<i;a++){const i=t.matched[a];i&&(e.matched.find((e=>k(e,i)))?r.push(i):n.push(i));const l=e.matched[a];l&&(t.matched.find((e=>k(e,l)))||o.push(l))}return[n,r,o]}}}]);