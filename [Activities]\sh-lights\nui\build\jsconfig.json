{
  "compilerOptions": {
    "baseUrl": ".",
    "paths": {
      "src/*": [
        "src/*"
      ],
      "app/*": [
        "*"
      ],
      "utils/*": [
        "src/utils/*"
      ],
      // "components/*": [
      //   "src/components/*"
      // ],
      // "layouts/*": [
      //   "src/layouts/*"
      // ],
      // "pages/*": [
      //   "src/pages/*"
      // ],
      // "assets/*": [
      //   "src/assets/*"
      // ],
      // "boot/*": [
      //   "src/boot/*"
      // ],
      "vue$": [
        "node_modules/vue/dist/vue.runtime.esm-bundler.js"
      ]
    }
  },
  "exclude": [
    "dist",
    ".quasar",
    "node_modules"
  ]
}
