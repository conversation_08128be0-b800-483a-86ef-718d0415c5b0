Thank you for buying this amazing script!

First -- https://github.com/mkafrin/PolyZone/releases you need this its dependency 
Please import uniqers-marriage-database.sql your database.
after open your server server.cfg or what u use cfg.
add this code 
-------------------------------------------
FOR QBCORE/QBUS
ensure qb-target
ensure uniqers-marriage
or
start qb-target
start uniqers-marriage
-------------------------------------------
FOR ESX
ensure ox_lib
ensure ox_target
ensure uniqers-marriage
or
start ox_lib
start ox_target
start uniqers-marriage
-------------------------------------------
save and close.

and open  uniqers-marriage config at script select your framework and notify and mysql service and save close


-------------------------------------------
after if use qbcore add items
this to qbcore/shared/items.lua

-------------------------------------------
FOR OLD QBCORE / QBUS

	['weddingring'] 			 	 	 = {['name'] = 'weddingring', 			  		['label'] = 'Wedding Ring', 				['weight'] = 0, 		['type'] = 'item', 		['image'] = 'weddingring.png', 			['unique'] = true, 		['useable'] = true, 	['shouldClose'] = false,   ['combinable'] = nil,   ['description'] = 'Wedding Ring :)'},
	['weddingcertificate'] 			 	 	 = {['name'] = 'weddingcertificate', 			  		['label'] = 'Wedding Certificate', 				['weight'] = 0, 		['type'] = 'item', 		['image'] = 'weddingcertificate.png', 			['unique'] = true, 		['useable'] = true, 	['shouldClose'] = false,   ['combinable'] = nil,   ['description'] = 'Wedding Certificate :)'},

-------------------------------------------
FOR NEW QBCORE

	weddingring			 	 	 = { name = 'weddingring', label = 'Wedding Ring', weight =  0, type =  'item', image = 'weddingring.png', unique = true, useable = true, shouldClose = false, description = 'Wedding Ring :)'},
    weddingcertificate 			 = { name = 'weddingcertificate', label = 'Wedding Certificate', weight =  0, type =  'item', image = 'weddingcertificate.png', unique = true, useable = true, 	shouldClose = false, description = 'Wedding Certificate :)'},

-------------------------------------------

FOR ESX

this 

INSERT INTO `items` (`name`, `label`, `limit`) VALUES
  ('weddingcertificate', 'Wedding Certificate', 1),
  ('weddingring', 'Wedding Ring', 1)
;

or
this

INSERT INTO `items` (`name`, `label`, `weight`, `rare`, `can_remove`) VALUES 
('weddingcertificate', 'Wedding Certificate', 1, 0, 1),
('weddingring', 'Wedding Ring', 10, 0, 1)
;
-------------------------------------------

then

if u use OLD qbcore you need this, if u use new qbcore you dont need this. also its for "qb-inventory old" too. if u use other inventory like ox-inventory you dont need this.
open js
add this code

} else if  (itemData.name == "weddingcertificate") {
  $(".item-info-title").html('<p>'+itemData.label+'</p>')
  $(".item-info-description").html('<p><strong>Weight: </strong><span>' + ((itemData.weight * itemData.amount) / 1000).toFixed(1) + 'kg</span></p><p><strong>Family: </strong><span>' + itemData.info.family + '</span></p><p><strong>Married Date: </strong><span>' + itemData.info.date + '</span></p>');			
} else if  (itemData.name == "weddingring") {
  $(".item-info-title").html('<p>'+itemData.label+'</p>')
  $(".item-info-description").html('<p><strong>Weight: </strong><span>' + ((itemData.weight * itemData.amount) / 1000).toFixed(1) + 'kg</span></p><p><strong>Family: </strong><span>' + itemData.info.family + '</span></p><p><strong>Engaged Date: </strong><span>' + itemData.info.date + '</span></p>');			
 
if u use ox_inventory add this code
\ox_inventory\data items.lua
	["weddingring"] = {
		label = "weddingring",
		weight = 10,
		stack = true,
		close = true,
	},

	["weddingcertificate"] = {
		label = "weddingcertificate",
		weight = 10,
		stack = true,
		close = true,
	},
	
you done need add anything


and
 
open your inventory and add image items


and if u are use qbcore and qbox 

Add this cods before qb-core/server/player.lua before function self.Functions.SetPlayerData(key, val)function
	
    function self.Functions.SetNameFull(firstname, lastname)
        self.PlayerData.charinfo.firstname = firstname
        self.PlayerData.charinfo.lastname = lastname
        self.Functions.UpdatePlayerData()
    end	
	
    function self.Functions.SetNameFirst(firstname)
        self.PlayerData.charinfo.firstname = firstname
        self.Functions.UpdatePlayerData()
    end
	
    function self.Functions.SetNameLast(lastname)
        self.PlayerData.charinfo.lastname = lastname
        self.Functions.UpdatePlayerData()
    end

and save!	

if u are use esx you dont need add anything.

and

and start your server and login your character then use script!!

done anjoy!

Support and updates info at > 
https://discord.gg/MyrGhbf9tM



local citizenid = "buradaki_silinmek_isteyen_karakterin_citizenid" -- örnek citizenid değişkeni

TriggerEvent('uniqers-marriage:CharacterDeleted', citizenid)
