shared_scripts { '@FiniAC/fini_events.js', '@FiniAC/fini_events.lua' }

fx_version 'cerulean'
game 'gta5'

name 'BDX-Skate'
description 'Pro Skate Career'
author 'Bodhix'
version '2.0.7'

lua54 'yes'

shared_scripts {
  'config.lua',
}

client_scripts {
  'client/*.lua',
}

server_scripts {
  'server/*.lua',
}

files {
  'nui/fonts/*.ttf',
  'nui/index.html',
  'nui/Main.css',
  'nui/sounds/*.MP3',
  'nui/music/*.mp3',
  'nui/customs/*.png',
  'stream/Add-ons/*.ydr',
  'stream/Skateboards/Modern/Designs/*.yft',
  'stream/Skateboards/Modern/Trucks/*.yft',
  'stream/Skateboards/Modern/Wheels/*.yft',
  'stream/Anims/<EMAIL>',
  'stream/Anims/bodhix@<EMAIL>',
  'server/version.json',
}

ui_page 'nui/index.html'
nui_page 'nui/index.html'

data_file "DLC_ITYP_REQUEST" "stream/Add-ons/*.ytyp"
data_file "DLC_ITYP_REQUEST" "stream/Skateboards/decks.ytyp"
data_file "DLC_ITYP_REQUEST" "stream/Skateboards/trucks.ytyp"
data_file "DLC_ITYP_REQUEST" "stream/Skateboards/wheels.ytyp"

escrow_ignore {
  'config.lua',
  'server/skate-shops-sv.lua',
  'client/skate-shops-cl.lua',
}
dependency '/assetpacks'