ESX = ESX or nil
if ESX == nil then
	TriggerEvent(Config.SharedObjectName, function(obj) ESX = obj end)
end

function GetItemLabel(item)
	local label = ESX.GetItemLabel(item)
    if label then
	    return label
    else
        print('^1Item: ^3['..item..']^1 missing in database^0')
        return item
    end
end

function addMoney(xPlayer, money)
	if Config.BlackMoney then
		xPlayer.addAccountMoney('money', money)
	else
		xPlayer.addMoney(money)
	end
end

function removeItem(xPlayer, item, qty)
	xPlayer.removeInventoryItem(item, qty)
end

RegisterNetEvent('ak47_drugdealerv2:increase_gang_loyalty')
AddEventHandler('ak47_drugdealerv2:increase_gang_loyalty', function()
    local src = source
    TriggerEvent('rcore_gangs:server:increase_loyalty', src, 'DRUGS', 1)
end)
