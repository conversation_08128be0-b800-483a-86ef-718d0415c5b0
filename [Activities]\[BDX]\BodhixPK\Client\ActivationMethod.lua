-- HOW TO PARKOUR

--To Active or Deactive Your Mod Press:
--A + DPad Down / Caps + Alt or F5

local asleep = 2000

Citizen.CreateThread(function()
    while true do
        Citizen.Wait(0)
        character = PlayerPedId()
        -- Check conditions for Active Parkour Mode.
        if IsControlPressed(0, config.<PERSON><PERSON>ey) and IsControlJustPressed(0, config.<PERSON>Key) or IsControlJustPressed(0, config.Alternative) then
            if not IsEntityPlayingAnim(character, "mp_arrest_paired", "cop_p2_back_right", 3) then
                -- Start Parkour Mode.
                TriggerEvent("ActiveParkour") -- Activated Parkour.
            end
        end
    end
end)

Citizen.CreateThread(function()
    while true do
        Citizen.Wait(asleep)
        character = PlayerPedId()
        -- Check the conditions that make Parkour Mode off.
        if IsEntityPlayingAnim(character, "mp_arrest_paired", "cop_p2_back_right", 3) then
            -- Turn Off Parkour Mode.
            TriggerEvent("EndParkour")
        end
    end
end)

function ShowHelpNotification(text)
    BeginTextCommandDisplayHelp("STRING")
    AddTextComponentSubstringPlayerName(text)
    EndTextCommandDisplayHelp(0, false, true, -1)
end