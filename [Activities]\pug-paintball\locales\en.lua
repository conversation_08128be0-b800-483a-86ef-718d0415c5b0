Translations = {
    error = {
        choose_team_first = "You need to join a team first",
        no_lives_entered = "Player lives not entered.",
        max_lives_set = "Maximum lives that can be set is ",
        no_wager = "Wager Amount not entered.",
        wager_cap = "Wager must be between ",
        active_game = "A game is already active",
        ammo_given = "You will recieve a new bullet in 7 seconds",
        cant_do_this = "You cannot do this until you respawn fully",
        no_id_entered = "No id was entered",
        need_to_be_lobby_host = "You need to be lobby host to change this setting",
    },
    success = {
        savedfrinfall = "Don't worry you've been saved king! :)",
        weapon_chosen = "Youve chosen ",
        start_in = "The arena will start in 10 seconds",
        e_open_menu = "[E] OPEN WEAPON MENU",
    },
    menu = {
        arenalobby = "Paintball Arena Lobby",
        start = "Start",
        players = "Players",
        redteam = "Red Team",
        ffateam = "FFA Lobby",
        blueteam = "Blue Team",
        lives = 'Amount of lives',
        arena_lives = 'Arena Lives',
        wager = 'Wager Amount',
        arena_wager = 'Arena Wager',
        weapon = 'Weapon',
        map = 'Map',
        mode = 'Game Mode',
        spectate = 'Spectate Players',
        random = 'Random',
        pistol_option = 'Small and deadly',
        smg_option = "Lightweight machine guns",
        shotgun_option = 'Heavy close range shotguns',
        assault_option = 'Big boy big gun',
        sniper_option = 'Faze Up',
        pistol_header = "Pistol's",
        smg_header = "Smg's",
        shotgun_header = "Shotgun's",
        assault_header = "Assault Rifle's",
        sniper_header = "Sniper Rifle's",
        close_back = "Close (ESC)",
        go_back = "< Go Back",
        player_lives = "Player lives",
        arena_lives = "Arena Lives",
        view_lobby = "View Lobby",
        become_host = "Host Lobby",
        become_hostt_description = "Become host of the lobby",
        give_host = "Transfer Lobby Host",
        give_host_description = "Give lobby host to another player",
        player_id = "(PLAYER ID)",
        host_of_lobby = "Host of the lobby is player ID: [",

        -- ADDED IN 3.1.2
        join = "Join ",
        choose = "Choose ",
        respawn_in = "Respawn in ",
        you_respawn_in = "You will respawn in 10 seconds",
        use_uav = "USE UAV",
        use = "USE ",
        removed_lobby_host = "You went to far from the paintball game. Removed as lobby host",
    },
}
