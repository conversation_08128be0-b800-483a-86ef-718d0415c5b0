import{u as E,r as o,K as F,G as j,q as P,s as V,a0 as K,a,aa as q,b0 as z,j as n,F as B,L as s,ai as M,x as $,m as C,ab as H,a5 as X,h as J,E as b,A as I,k as Q,b8 as Z,O as ee,d as T,C as p,b as R,ax as ae,f as te,i as se,P as w}from"./index-a04bc7c5.js";import{formatDate as ie}from"./Marketplace-ae19e30c.js";import{T as le}from"./Textarea-63971279.js";const y={posts:[{id:"1",title:"Looking for a new job",description:"I am looking for a new job in the field of software development. I have 5 years of experience in the field and I am looking for a new challenge. I am open to any offers and I am willing to relocate.",number:"0601232354"},{id:"2",title:"Banshee",description:"Selling my 2020 model Bravado Banshee, low mileage and in perfect condition. Price is negotiable.",number:"0606643134",attachment:"https://www.gtabase.com/images/gta-5/vehicles/sports/main/banshee.jpg",price:74999},{id:"3",title:"2018+ Sanchez",description:"Looking for a 2018 or newer model Sanchez. I am willing to pay up to 20k for a good condition bike.",number:"0651566734"}]};function oe(){const S=E(R),g=E(I),L=E(w.PhoneNumber),[Y,k]=o.useState(!1),x=E(w.Styles.TextColor),[m,h]=o.useState([]),[t,d]=o.useState(null),[l,G]=o.useState(""),[v,W]=o.useState(""),[A,u]=o.useState([]),{handleScroll:D,setPage:N,setFetchedEverything:f}=F({fetchData:e=>P("YellowPages",{action:"getPosts",query:(l==null?void 0:l.length)>0?l:null,page:e}),onDataFetched:e=>l.length>0?u([...A,...e]):h([...m,...e]),perPage:10});o.useEffect(()=>{j("YellowPages")&&(N(0),f(!1),u([]),P("YellowPages",{action:"getPosts",page:0},y.posts).then(e=>{!e||e.length===0||h(e)}),P("isAdmin",null,!1).then(e=>k(e)))},[g==null?void 0:g.active]),o.useEffect(()=>{N(0),f(!1),u([]),l.length>0&&P("YellowPages",{action:"getPosts",query:l},y.posts.filter(e=>{var i,r;return((i=e.title)==null?void 0:i.toLowerCase().includes(l==null?void 0:l.toLowerCase()))||((r=e.description)==null?void 0:r.toLowerCase().includes(l==null?void 0:l.toLowerCase()))})).then(e=>{if(!e)return V("error","No data returned from search");u(e)})},[l]),o.useEffect(()=>{const e=setTimeout(()=>G(v),500);return()=>clearTimeout(e)},[v]),K("yellowPages:newPost",e=>{h([e,...m])},{waitUntilService:!0});const O=()=>{if(!(t!=null&&t.title)||!(t!=null&&t.description)){let i;switch(!0){case!(t!=null&&t.title):i=s("APPS.YELLOWPAGES.ERROR_POPUP.NO_TITLE");break;case!(t!=null&&t.description):i=s("APPS.YELLOWPAGES.ERROR_POPUP.NO_DESCRIPTION");break;default:i="UNKNOWN ERROR";break}p.PopUp.set({title:s("APPS.YELLOWPAGES.ERROR_POPUP.TITLE"),description:i,buttons:[{title:s("APPS.YELLOWPAGES.ERROR_POPUP.OK")}]});return}let e={...t,number:L};P("YellowPages",{action:"sendPost",data:e},!0).then(i=>{i&&(h([{...e,id:i},...m]),d(null))})},U=e=>{e&&p.PopUp.set({title:s("APPS.YELLOWPAGES.DELETE_POPUP.TITLE"),description:s("APPS.YELLOWPAGES.DELETE_POPUP.TEXT"),buttons:[{title:s("APPS.YELLOWPAGES.DELETE_POPUP.CANCEL")},{title:s("APPS.YELLOWPAGES.DELETE_POPUP.PROCEED"),color:"red",cb:()=>{P("YellowPages",{action:"deletePost",id:e},!0).then(i=>{i&&h(m.filter(r=>r.id!==e))})}}]})},_=e=>{e&&p.PopUp.set({title:s("APPS.SERVICES.CALL_POPUP.TITLE"),description:s("APPS.SERVICES.CALL_POPUP.DESCRIPTION").format({name:te(e)}),buttons:[{title:s("APPS.SERVICES.CALL_POPUP.CANCEL")},{title:s("APPS.SERVICES.CALL_POPUP.PROCEED"),cb:()=>{let i=b(e);se({...i,number:e})}}]})};return a("div",{className:"pages-container",children:q()?n(B,{children:[n("div",{className:"pages-header",children:[n("div",{className:"pages-header-top",children:[a("div",{className:"title",children:s("APPS.YELLOWPAGES.PAGES")}),a(M,{onClick:()=>d({})})]}),a($,{placeholder:s("APPS.YELLOWPAGES.SEARCH"),onChange:e=>W(e.target.value)})]}),a("div",{className:"pages-wrapper",children:a("div",{className:"posts",onScroll:D,children:(l.length>0?A:m).map((e,i)=>{var r;return a(C.div,{className:"post-item",initial:{opacity:0,scale:.9},whileInView:{opacity:1,scale:1},exit:{opacity:0,scale:.9},viewport:{once:!0},transition:{duration:.2,ease:"easeInOut"},children:n("div",{className:"post-info",children:[n("div",{className:"post-header",children:[a("div",{className:"title",children:e.title}),a("div",{className:"date",children:ie(e.timestamp)})]}),n("div",{className:"post-content",children:[n("div",{className:"post-text",style:{maxWidth:e.attachment?"65%":"100%"},children:[a("div",{className:"description",children:H(e.description)}),e.price&&a("div",{className:"price",children:S==null?void 0:S.CurrencyFormat.replace("%s",(r=e.price)==null?void 0:r.toLocaleString())})]}),e.attachment&&a("div",{className:"attachment",children:a(X,{src:e.attachment,blur:e.number!==L})})]}),n("div",{className:"post-footer",children:[a(J,{className:"green",onClick:()=>{let c=b(e.number);I.patch({active:{name:"Messages",data:{number:e.number,name:c==null?void 0:c.name,avatar:c==null?void 0:c.avatar,view:"messages"}}})}}),a(Q,{className:"blue",onClick:()=>_(e.number)}),(L===e.number||Y)&&a(Z,{className:"red",onClick:c=>{c.stopPropagation(),U(e.id)}})]})]})},i)})})}),a(ee,{children:t&&n(C.div,{initial:{opacity:0,y:100},animate:{opacity:1,y:0},exit:{opacity:0,y:100},transition:{duration:.2,ease:"easeInOut"},className:"new-post-container",children:[n("div",{className:"new-post-header",children:[a("div",{}),a("div",{className:"close",children:a("div",{onClick:()=>d(null)})}),a("div",{className:"post",onClick:O,children:s("APPS.MARKETPLACE.POST")})]}),n("div",{className:"new-post-body",children:[n("div",{className:"item",children:[a("div",{className:"title",children:s("APPS.YELLOWPAGES.TITLE")}),a(T,{type:"text",placeholder:s("APPS.YELLOWPAGES.TITLE"),maxLength:50,onChange:e=>d({...t,title:e.target.value})})]}),n("div",{className:"item",children:[a("div",{className:"title",children:s("APPS.YELLOWPAGES.DESCRIPTION")}),a(le,{type:"text",placeholder:s("APPS.YELLOWPAGES.DESCRIPTION"),maxLength:250,rows:5,onChange:e=>d({...t,description:e.target.value})})]}),a("div",{className:"item",children:a("div",{className:"images",children:a("div",{className:"image",style:{backgroundImage:`url(${t==null?void 0:t.attachment})`},onClick:()=>{var e,i,r;if(t!=null&&t.attachment)return d({...t,attachment:null});p.Gallery.set({allowExternal:(r=(i=(e=R)==null?void 0:e.value)==null?void 0:i.AllowExternal)==null?void 0:r.Pages,onSelect:c=>d({...t,attachment:c.src})})},children:!(t!=null&&t.attachment)&&a(ae,{})})})}),n("div",{className:"item",children:[a("div",{className:"title",children:s("APPS.YELLOWPAGES.PRICE")}),a(T,{type:"number",placeholder:"0",onChange:e=>{if(!e.target.value.match(/^[0-9]*$/))return e.preventDefault();d({...t,price:!isNaN(parseFloat(e.target.value))&&parseFloat(e.target.value)})}})]}),a("div",{className:"button",onClick:()=>O(),children:s("APPS.YELLOWPAGES.POST")})]})]})})]}):a("div",{className:"loading",children:a(z,{size:40,lineWeight:5,speed:2,color:x})})})}export{oe as default};
