Config = {}

-------------------------------------------------------


Config.pedmodel = 'a_m_m_prolhost_01' -- ped model hash

Config.scenario = 'WORLD_HUMAN_CLIPBOARD' -- scenario for ped to play, false to disable

Config.locations = {
    ['prison'] = {
        ped = true, -- if false uses boxzone (below)

        coords = vector4(1852.1954, 2582.2512, 45.6726, 273.8113),
        
        -------- boxzone (only used if ped is false) --------

        length = 1.0,  
        width = 1.0,   
        minZ = 30.81,  
        maxZ = 30.81,  
        debug = false, 

        -----------------------------------------------------
        vehicles = {
            ['faggio2']        = {     -- vehicle model name
                price = 20,        -- ['vehicle'] = price
                image = 'https://r2.fivemanage.com/pub/g2fjfemllvp7.png',      -- image for menu, false for no image
            },
            ['asea']        = {     -- vehicle model name
                price = 60,        -- ['vehicle'] = price
                image = 'https://r2.fivemanage.com/pub/kz4ux1p26i00.png',      -- image for menu, false for no image
            },
            ['bison']       = {
                price = 60, 
                image = 'https://r2.fivemanage.com/pub/dipuei15tglu.png',
            },
            ['patriot']     = {
                price = 60, 
                image = 'https://r2.fivemanage.com/pub/rl6l5426npnb.png',
            },

        },

        vehiclespawncoords = vector4(1854.62, 2578.74, 45.67, 267.22), -- where vehicle spawns when rented

    },


    ['paleto'] = {
        ped = true, -- if false uses boxzone (below)

        coords = vector4(-214.48, 6355.65, 31.49, 136.78),
        
        -------- boxzone (only used if ped is false) --------

        length = 1.0,  
        width = 1.0,   
        minZ = 30.81,  
        maxZ = 30.81,  
        debug = false, 

        -----------------------------------------------------
        vehicles = {
            ['faggio2']        = {     -- vehicle model name
                price = 20,        -- ['vehicle'] = price
                image = 'https://r2.fivemanage.com/pub/g2fjfemllvp7.png',      -- image for menu, false for no image
            },
            ['asea']        = {     -- vehicle model name
                price = 60,        -- ['vehicle'] = price
                image = 'https://r2.fivemanage.com/pub/kz4ux1p26i00.png',      -- image for menu, false for no image
            },
            ['bison']       = {
                price = 60, 
                image = 'https://r2.fivemanage.com/pub/dipuei15tglu.png',
            },
            ['patriot']     = {
                price = 60, 
                image = 'https://r2.fivemanage.com/pub/rl6l5426npnb.png',
            },

        },

        vehiclespawncoords = vector4(-217.8561, 6348.7910, 31.6978, 222.4448), -- where vehicle spawns when rented

    },


    ['airport'] = {
        ped = true, -- if false uses boxzone (below)

        coords = vector4(-1027.4825, -2659.7830, 13.8308, 126.5407),
        
        -------- boxzone (only used if ped is false) --------

        length = 1.0,  
        width = 1.0,   
        minZ = 30.81,  
        maxZ = 30.81,  
        debug = false, 

        -----------------------------------------------------
        vehicles = {
            ['faggio2']        = {     -- vehicle model name
                price = 20,        -- ['vehicle'] = price
                image = 'https://r2.fivemanage.com/pub/g2fjfemllvp7.png',      -- image for menu, false for no image
            },
            ['asea']        = {     -- vehicle model name
                price = 60,        -- ['vehicle'] = price
                image = 'https://r2.fivemanage.com/pub/kz4ux1p26i00.png',      -- image for menu, false for no image
            },
            ['bison']       = {
                price = 60, 
                image = 'https://r2.fivemanage.com/pub/dipuei15tglu.png',
            },
            ['patriot']     = {
                price = 60, 
                image = 'https://r2.fivemanage.com/pub/rl6l5426npnb.png',
            },

        },

        vehiclespawncoords = vector4(-1038.3280, -2667.5757, 13.8308, 241.6562), -- where vehicle spawns when rented

    },


    ['legion'] = {
        ped = true, -- if false uses boxzone (below)

        coords = vector4(711.421, -296.714, 59.198, 31.399),
        
        -------- boxzone (only used if ped is false) --------

        length = 1.0,  
        width = 1.0,   
        minZ = 50.73,  
        maxZ = 54.73,  
        debug = false, 

        -----------------------------------------------------
        vehicles = {
            ['faggio2']        = {     -- vehicle model name
                price = 20,        -- ['vehicle'] = price
                image = 'https://r2.fivemanage.com/pub/g2fjfemllvp7.png',      -- image for menu, false for no image
            },
            ['asea']        = {     -- vehicle model name
                price = 60,        -- ['vehicle'] = price
                image = 'https://r2.fivemanage.com/pub/kz4ux1p26i00.png',      -- image for menu, false for no image
            },
            ['bison']       = {
                price = 60, 
                image = 'https://r2.fivemanage.com/pub/dipuei15tglu.png',
            },
            ['patriot']     = {
                price = 60, 
                image = 'https://r2.fivemanage.com/pub/rl6l5426npnb.png',
            },

        },

        vehiclespawncoords = vector4(174.25, -1011.492, 29.477, 198.839), -- where vehicle spawns when rented

    },

}

