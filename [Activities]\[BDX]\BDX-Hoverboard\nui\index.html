<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Menu</title>
    <link rel="stylesheet" href="Main.css">
</head>

<body>
    <script src="https://www.youtube.com/iframe_api"></script>
    <!-- Modal para mostrar el video de YouTube -->
    <div id="videoModal" class="modal">
        <div class="modal-content">
            <span class="close" id="closeModal">&times;</span>
            <iframe id="youtubeVideo" width="100%" height="100%"
                src="https://www.youtube.com/embed/XmV02A6qT2E?enablejsapi=1&cc_load_policy=0" frameborder="0"
                allowfullscreen>
            </iframe>
        </div>
    </div>

    <div id="boost-wrapper" style="display: none;">
        <div id="boost-label">BOOST</div>
        <div id="boost-bar-container">
            <div id="boost-bar"></div>
        </div>
    </div>

    <!-- Main -------------------------------------------------------------------------------------------------------------------------------------------->
    <div id="container" style="display: none;">
        <button class="closeMain" id="closeContainerNew">x</button> <!-- Botón de cierre -->
        <div class="menu-line left"></div>
        <div class="menu-title">Bodhix's Studio</div>
        <div class="menu-line right"></div>
        <div class="menu">

            <!-- <a href="best-spots.html" class="menu-item best-spots">
                <img src="images/Spots.jpg" alt="Best Spots">
            </a> -->

            <button class="menu-item best-spots"> <!-- id="openMenuBox" -->
                <img src="https://github.com/Bodh1x/fivem-skate-ui/blob/main/Images/comingsoon.jpg?raw=true"
                    alt="Select Sport">
                <span class="menu-text" id="equipmentBtn">Loading...</span>
            </button>

            <button class="menu-item select-sport" id="openMenuBox">
                <img src="https://github.com/Bodh1x/fivem-skate-ui/blob/main/Images/gear.jpg?raw=true"
                    alt="Select Sport">
                <span class="menu-text" id="gearBtn">Loading...</span>
            </button>
            <button class="menu-item whats-new" id="openModal">
                <img src="https://github.com/Bodh1x/fivem-skate-ui/blob/main/Images/features.jpg?raw=true"
                    alt="What's New">
                <span class="menu-text" id="whatsNewBtn">Loading...</span>
            </button>
        </div>

        <!-- Sección de enlaces -->
        <div class="links">
            <img src="https://github.com/Bodh1x/fivem-skate-ui/blob/main/Images/Logos/Discord.png?raw=true"
                alt="Discord" class="social-icon discord-icon"
                onclick="window.invokeNative('openUrl', 'https://discord.com/invite/PjN7AWqkpF');">
            <img src="https://github.com/Bodh1x/fivem-skate-ui/blob/main/Images/Logos/YouTube.png?raw=true"
                alt="YouTube" class="social-icon youtube-icon"
                onclick="window.invokeNative('openUrl', 'https://youtube.com/@bodhix?si=qqenaPcA3cVcXf0b');">
        </div>
    </div>

    <div id="menuBox" style="display: none;">
        <!-- Logo -->
        <img src="https://github.com/Bodh1x/fivem-skate-ui/blob/main/Images/logo.png?raw=true" alt="Logo" id="logo">

        <!-- First Page -->
        <div id="firstPage">
            <div class="buttons">
                <button class="words-style" id="HoverboardBtn" onclick="proceedToMainMenu()">Loading...</button>
                <button class="back-arrow" onclick="goBack()">&#11013;</button>
                <!-------------------------------------------------------goBack--------------------------------------------------->
            </div>
        </div>

        <!-- Main (Hover) Menu -->
        <div id="mainMenu" style="display: none;">
            <div class="buttons">
                <button class="words-style" id="boardBtn" onclick="showSubMenu('board')">Loading...</button>
                <button class="back-arrow" onclick="goBack()">&#11013;</button>
            </div>
        </div>

        <!-- Submenu -->
        <div id="subMenu" style="display: none;">
            <h1 id="submenu-title">Submenu</h1>
            <div id="options"></div>
            <button class="back-arrow" onclick="goBack()">&#11013;</button>
            <button class="purchase-button" id="purchaseBtn" onclick="purchaseItem()">Loading...</button>
            <!-----------------------------------------Usar Esto-------------------------------------------->
        </div>
    </div>


    <!-- Audio Players -->
    <audio id="audioPlayer1" src="" type="audio/mp3"></audio>
    <audio id="audioPlayer2" src="" type="audio/mp3"></audio>
    <audio id="audioPlayer3" src="" type="audio/mp3"></audio>

    <script> //------------------------------------------------------____________________JavaScript___________________________-----------------------------------------------------------------------
        let currentPage = "container"; // Track the current page------------------------------------------------------------------------------------------------------------------------
        //let currentPage = "firstPage"; // Track the current page

        // ✅ Agregar evento para abrir el menú cuando el usuario haga clic en "Select Sport"
        document.getElementById("openMenuBox").addEventListener("click", function () {
            document.getElementById("menuBox").style.display = "block"; // Mostrar menú
            document.getElementById("firstPage").style.display = "block"; // Mostrar botones
            document.getElementById("container").style.display = "none"; // Ocultar pantalla principal
        });

        //-----------------------------------------------------------------------------------------------------------------------------------------------------------------------
        document.addEventListener("DOMContentLoaded", function () {
            let closeBtn = document.getElementById("closeContainerNew");

            if (closeBtn) {
                closeBtn.addEventListener("click", function () {
                    document.getElementById("container").style.display = "none"; // Hide the container
                    fetch(`https://${GetParentResourceName()}/closeMenu`, { method: "POST" });
                });
            }
        });
        document.addEventListener("DOMContentLoaded", function () {
            fetch(`https://${GetParentResourceName()}/getLanguage`, { method: "POST" })
                .then(response => response.json())
                .then(language => {
                    document.getElementById("equipmentBtn").innerText = language.equipment;
                    document.getElementById("gearBtn").innerText = language.gear;
                    document.getElementById("whatsNewBtn").innerText = language.whats_new;
                    document.getElementById("HoverboardBtn").innerText = language.hoverboard;
                    document.getElementById("purchaseBtn").innerText = language.purchase;
                    document.getElementById("modernBtn").innerText = language.ultra;
                    document.getElementById("classicBtn").innerText = language.retro;
                })
                .catch(error => console.error("Failed to load translations:", error));
        });


        document.addEventListener("DOMContentLoaded", function () {
            const openModalButton = document.getElementById("openModal");
            const closeModalButton = document.getElementById("closeModal");
            const modal = document.getElementById("videoModal");
            const video = document.getElementById("youtubeVideo");

            if (openModalButton) {
                // ✅ Abrir modal al hacer clic en el botón
                openModalButton.addEventListener("click", function () {
                    modal.style.display = "flex";
                });
            }

            if (closeModalButton) {
                // ✅ Cerrar modal al hacer clic en la "X"
                closeModalButton.addEventListener("click", function () {
                    modal.style.display = "none";
                    video.src = video.src; // Detener video al cerrar
                });
            }

            // ✅ Cerrar modal si el usuario hace clic fuera de él
            window.addEventListener("click", function (event) {
                if (event.target === modal) {
                    modal.style.display = "none";
                    video.src = video.src;
                }
            });
        });
        document.addEventListener("keydown", function (event) {
            let modal = document.getElementById("videoModal");

            if (event.code === "Space" && modal.style.display !== "flex") {
                event.preventDefault();  // Stop the video from playing outside the modal
            }
        });
        window.onclick = function (event) {
            let modal = document.getElementById("videoModal");
            if (event.target === modal) {
                closeVideoModal();
            }
        };

        //--------------------------------------------------------------------------------------------------------------------------------------------------
        function proceedToMainMenu(type = null) {
            // Clean any old HoverType UI in case it's still on screen
            const old = document.getElementById("HoverTypeSelect");
            if (old) old.remove();

            const payload = type ? JSON.stringify({ boardType: type }) : "{}";

            fetch(`https://${GetParentResourceName()}/proceedToMenu`, {
                method: "POST",
                body: payload
            });
        }

        let designCount = 16; // Default fallback

        // Request Config.DesignCount from Lua
        fetch(`https://${GetParentResourceName()}/getDesignCount`, {
            method: "POST",
        })
            .then(response => response.json())
            .then(data => {
                if (data.designCount) {
                    designCount = data.designCount; // Update the count from Lua
                }
            })
            .catch(error => console.error("Failed to get DesignCount:", error));

        function showSubMenu(type) {
            const options = document.getElementById("options");
            const title = document.getElementById("submenu-title");

            document.getElementById("mainMenu").style.display = "none";
            document.getElementById("subMenu").style.display = "block";

            title.innerText = type.charAt(0).toUpperCase() + type.slice(1);
            options.innerHTML = "";

            // Ask Lua for correct design count based on style + type
            fetch(`https://${GetParentResourceName()}/getDesignCount`, {
                method: "POST",
                body: JSON.stringify({ type: type, style: HoverStyle })
            })
                .then(response => response.json())
                .then(data => {
                    const count = data.designCount || 16;

                    let prefix = "";
                    let resource = GetParentResourceName(); // default

                    if (HoverStyle === "classic") {
                        if (type === "board") prefix = "retro_";
                    } else {
                        // Modern
                        if (type === "board") prefix = "ultra_";
                    }

                    for (let i = 1; i <= count; i++) {
                        const img = document.createElement("img");
                        img.src = `nui://${resource}/nui/customs/${prefix}${i}.png`;
                        img.alt = `${type} ${i}`;
                        img.setAttribute("data-id", i);
                        img.setAttribute("data-type", type);
                        img.onclick = () => selectItem(type, i, img);
                        options.appendChild(img);
                    }
                })
                .catch(error => console.error("Failed to get design count:", error));
        }


        function selectItem(type, id, element) {
            // Remove previous selections
            document.querySelectorAll(".selected-item").forEach(item => {
                item.classList.remove("selected-item");
            });

            // Set the selected item
            element.classList.add("selected-item");

            // Store selected item's attributes for purchase
            element.setAttribute("data-id", id);
            element.setAttribute("data-type", type);

            const callback = type === "board" ? "Hoverelected" : type === "trucks" ? "trucksSelected" : "wheelsSelected";

            fetch(`https://${GetParentResourceName()}/${callback}`, {
                method: "POST",
                body: JSON.stringify({ id }),
            });
        }

        function purchaseItem() {
            let selectedItem = document.querySelector(".selected-item"); // Get the selected item
            if (!selectedItem) {
                return;
            }

            let itemId = selectedItem.getAttribute("data-id"); // Get selected item ID
            let itemType = selectedItem.getAttribute("data-type"); // Get selected item type

            fetch(`https://${GetParentResourceName()}/purchaseItem`, {
                method: "POST",
                body: JSON.stringify({ id: itemId, type: itemType }),
            });
        }



        function closeMenu() { //----------------------------------------------------------------------------??????????????-------------------------------------------
            document.getElementById("menuBox").style.display = "none"; // Ocultar menú
            document.getElementById("container").style.display = "block"; // Volver a la pantalla principal------------------------?????????????????????--------------------------------
            //fetch(`https://${GetParentResourceName()}/closeMenu`, { method: "POST" });
        }

        function closeMain() { //----------------------------------------------------------------------------??????????????-------------------------------------------
            document.getElementById("menuBox").style.display = "none"; // Ocultar menú
            document.getElementById("container").style.display = "none"; // Volver a la pantalla principal------------------------?????????????????????--------------------------------
            fetch(`https://${GetParentResourceName()}/closeMenu`, { method: "POST" });
        }



        let youtubePlayer; // Store the YouTube player instance

        // Initialize the YouTube Player
        function onYouTubeIframeAPIReady() {
            youtubePlayer = new YT.Player('youtubeVideo', {
                events: {
                    'onReady': onPlayerReady
                }
            });
        }

        // Ensure video loads when the modal is opened
        function openVideoModal() {
            document.getElementById("videoModal").style.display = "flex"; // Show modal
            document.getElementById("mainMenu").style.display = "none";  // Hide main menu
            document.getElementById("subMenu").style.display = "none";  // Hide subMenu

            if (youtubePlayer) {
                youtubePlayer.playVideo(); // Play video when modal opens
            }
        }

        // Pause the video when closing the modal
        function closeVideoModal() {
            document.getElementById("videoModal").style.display = "none"; // Hide modal
            document.getElementById("mainMenu").style.display = "block";  // Show main menu

            if (youtubePlayer) {
                youtubePlayer.pauseVideo(); // Pause video without restarting
            }
        }
        function onYouTubeIframeAPIReady() {
            youtubePlayer = new YT.Player('youtubeVideo', {
                events: {
                    'onReady': onPlayerReady,
                    'onStateChange': onPlayerStateChange
                }
            });
        }

        // Set best quality when video starts playing
        function onPlayerStateChange(event) {
            if (event.data === YT.PlayerState.PLAYING) {
                youtubePlayer.setPlaybackQuality('hd1080'); // Force 1080p (change to 'highres' for max available)
            }
        }

        function goBack() {
            const HoverTypeSelect = document.getElementById("HoverTypeSelect");
            const subMenu = document.getElementById("subMenu");
            const firstPage = document.getElementById("firstPage");
            const container = document.getElementById("container");

            // ⬅ From Trucks/Wheels (HoverTypeSelect), go back to Modern/Classic
            if (HoverTypeSelect) {
                HoverTypeSelect.remove();
                subMenu.style.display = "none";
                firstPage.style.display = "block";
                currentPage = "firstPage";
                return;
            }

            // ⬅ From board submenu (subMenu), go back to Modern/Classic
            if (subMenu && subMenu.style.display === "block") {
                subMenu.style.display = "none";
                firstPage.style.display = "block";
                currentPage = "firstPage";

                fetch(`https://${GetParentResourceName()}/goBack`, {
                    method: "POST",
                    body: JSON.stringify({ currentPage: "mainMenu" }),
                });
                return;
            }

            // ⬅ From Modern/Classic, go back to category menu
            if (firstPage && firstPage.style.display === "block") {
                firstPage.style.display = "none";
                document.getElementById("menuBox").style.display = "none";
                container.style.display = "block";
                currentPage = "container";
                return;
            }
        }

        function sendHoverType(type) {
            fetch(`https://${GetParentResourceName()}/${type}Selected`, {
                method: "POST"
            });
        }

        // Handle NUI Messages
        window.addEventListener("message", (event) => {
            const data = event.data;
            const boostBar = document.getElementById("boost-bar");
            const boostWrapper = document.getElementById("boost-wrapper");
            
            //-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------?????????????
            if (data.action === "showContainer") {
                document.getElementById("menuBox").style.display = "none";
                document.getElementById("container").style.display = "block";
                document.getElementById("firstPage").style.display = "none";
                document.getElementById("mainMenu").style.display = "none";
                document.getElementById("subMenu").style.display = "none";
            } else if (data.action === "showFirstPage") {
                document.getElementById("menuBox").style.display = "block";
                document.getElementById("container").style.display = "none";
                document.getElementById("firstPage").style.display = "block";
                document.getElementById("mainMenu").style.display = "none";
                document.getElementById("subMenu").style.display = "none";
            } else if (data.action === "showMainMenu") {
                HoverStyle = data.HoverStyle || "modern";

                // Clean up HoverType menu if still present
                const HoverTypeElement = document.getElementById("HoverTypeSelect");
                if (HoverTypeElement) {
                    HoverTypeElement.remove();
                }

                document.getElementById("firstPage").style.display = "none";
                // document.getElementById("mainMenu").style.display = "block";
                showSubMenu('board'); // <-- Automatically open board submenu
            }
            else if (data.action === "hideMenu") {
                document.getElementById("menuBox").style.display = "none";
            } else if (data.action === "showHoverTypeSelect") {
                // Remove any previous HoverType menu
                const existing = document.getElementById("HoverTypeSelect");
                if (existing) existing.remove();

                document.getElementById("firstPage").style.display = "none";
                document.getElementById("mainMenu").style.display = "none";
                document.getElementById("subMenu").style.display = "none";

                const HoverTypeSelectDiv = document.createElement("div");
                HoverTypeSelectDiv.id = "HoverTypeSelect";

                const buttonsDiv = document.createElement("div");
                buttonsDiv.className = "buttons";

                const modernBtn = document.createElement("button");
                modernBtn.className = "words-style";
                modernBtn.innerText = "Modern";
                modernBtn.onclick = () => sendHoverType("modern");

                const classicBtn = document.createElement("button");
                classicBtn.className = "words-style";
                classicBtn.innerText = "Classic";
                classicBtn.onclick = () => sendHoverType("classic");

                const backBtn = document.createElement("button");
                backBtn.className = "back-arrow";
                backBtn.innerHTML = "&#11013;";
                backBtn.onclick = () => {
                    HoverTypeSelectDiv.remove();
                    goBack();
                };

                buttonsDiv.appendChild(modernBtn);
                buttonsDiv.appendChild(classicBtn);
                buttonsDiv.appendChild(backBtn);

                HoverTypeSelectDiv.appendChild(buttonsDiv);
                document.getElementById("menuBox").appendChild(HoverTypeSelectDiv);
            } else if (data.transactionType === "stopSound") {
                if (data.player === 1) {
                    audioPlayer1.pause();
                    audioPlayer1.currentTime = 0;
                } else if (data.player === 2) {
                    audioPlayer2.pause();
                    audioPlayer2.currentTime = 0;
                } else if (data.player === 3) {
                    audioPlayer3.pause();
                    audioPlayer3.currentTime = 0;
                }
            } else if (event.data.type === "boost_bar") {
                if (boostBar) boostBar.style.width = event.data.value + "%";
            } else if (event.data.type === "toggle_boost_bar") {
                if (boostWrapper) boostWrapper.style.display = event.data.state ? "block" : "none";
            }
        });

        // Audio Player Integration
        const audioPlayer1 = document.getElementById("audioPlayer1");
        const audioPlayer2 = document.getElementById("audioPlayer2");
        const audioPlayer3 = document.getElementById("audioPlayer3");

        audioPlayer1.volume = 1;
        audioPlayer2.volume = 1;
        audioPlayer3.volume = 0.3;

        window.addEventListener("message", (event) => {
            const data = event.data;

            // Play sound on specific player based on the message
            if (data.transactionType === "playSound") {
                if (data.player === 1) {
                    audioPlayer1.src = data.transactionFile;
                    audioPlayer1.play();
                } else if (data.player === 2) {
                    audioPlayer2.src = data.transactionFile;
                    audioPlayer2.play();
                } else if (data.player === 3) {
                    audioPlayer3.src = data.transactionFile;
                    audioPlayer3.play();
                }
            }

            // Stop sound on the specific player
            else if (data.transactionType === "stopSound") {
                if (data.player === 1) {
                    audioPlayer1.pause();
                    audioPlayer1.currentTime = 0;
                } else if (data.player === 2) {
                    audioPlayer2.pause();
                    audioPlayer2.currentTime = 0;
                } else if (data.player === 3) {
                    audioPlayer3.pause();
                    audioPlayer3.currentTime = 0;
                }
            }

            // Set volume for the specific player
            else if (data.transactionType === "setVolume") {
                if (data.player === 1) {
                    audioPlayer1.volume = data.volume;
                } else if (data.player === 2) {
                    audioPlayer2.volume = data.volume;
                } else if (data.player === 3) {
                    audioPlayer3.volume = data.volume;
                }
            }
        });

        document.addEventListener("DOMContentLoaded", function () {
            document.getElementById("closeContainer").addEventListener("click", function () {
                document.getElementById("container").style.display = "none"; // Oculta el contenedor
            });
        });

    </script>
    <!--<script src="Main.js"></script>-->
</body>

</html>