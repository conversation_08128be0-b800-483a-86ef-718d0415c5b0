Config = {}

Config.Cmd = "deletevehicles" -- Chat command to delete vehicles

Config.Framework = "esx" -- Options: "esx", "qbcore", "other"

Config.QBUsePermissionsUpdate = true

Config.AdminGroups = { -- Admin groups that can access the admin menu
	"superadmin",
	"god",
	"admin",
	"mod"
}

Config.AdminList = { -- IF YOU USE ESX OR QBCORE IGNORE THIS
	'license:2ash123ad1337a15029a21a6s4e3622f91cde1d0', -- Example, change this
	'discord:1159468436905275432' -- Example, change this
}

Config.DeleteVehicleTimer = 1 -- Time (in minutes) that it will take to delete vehicles since you execute the command

Config.DeleteVehiclesIfInSafeZone = false -- If true it'll delete vehicles inside safezones

Config.DeleteVehiclesAt = { -- Delete vehicles automatically at this time every day (h = hour m = minutes)
	{['h'] = 1, ['m'] = 0},
	{['h'] = 2, ['m'] = 0},
	{['h'] = 3, ['m'] = 0},
	{['h'] = 4, ['m'] = 0},
	{['h'] = 5, ['m'] = 0},
	{['h'] = 6, ['m'] = 0},
	{['h'] = 7, ['m'] = 0},
	{['h'] = 8, ['m'] = 0},
	{['h'] = 9, ['m'] = 0},
	{['h'] = 10, ['m'] = 0},
	{['h'] = 11, ['m'] = 0},
	{['h'] = 12, ['m'] = 0},
	{['h'] = 13, ['m'] = 0},
	{['h'] = 14, ['m'] = 0},
	{['h'] = 15, ['m'] = 0},
	{['h'] = 16, ['m'] = 0},
	{['h'] = 17, ['m'] = 0},
	{['h'] = 18, ['m'] = 0},
	{['h'] = 19, ['m'] = 0},
	{['h'] = 20, ['m'] = 0},
	{['h'] = 21, ['m'] = 0},
	{['h'] = 22, ['m'] = 0},
	{['h'] = 23, ['m'] = 0},
	{['h'] = 24, ['m'] = 0},
}

-- Set safezones
-- For the blip color check: https://docs.fivem.net/docs/game-references/blips/#blip-colors
-- If you want to remove the blip simply set 'alpha' to 0
Config.SafeZones = {
	{ ['x'] = -33.5, ['y'] = -1101.72, ['z'] = 26.42, ['radius'] = 42.0, ['color'] = 2, ['alpha'] = 0},
}