
#body {
  display:none;
  overflow: hidden;
  cursor: default;

}

.alert {
margin-top: 50.1%;
margin-left: 77%;
display:block;
}

.gold {
position: fixed;
top: 73%;
left: 0%;
height: 34%;
}

.money {
position: fixed;
top: 59%;
left: 78%;
height: 50%;
transform: rotate(-46deg);
}

.moneydrop {
position: fixed;
height: 79%;
top: -14%;
left: 74%;
filter: blur(3px);
}

.confirmation {
display:none;
color:white;
width: 21%;
height: 39%;
top: 29%;
position: absolute;
left: 40%;
background-color: #0000008f;
border-radius: 12px;
text-align: center;
}

.conalert {
font-family: Maven Pro;
font-size: 48px;
text-align: center;
margin-left: 1%;
}

.confirm1 {
font-family: Oswald;
text-align: inherit;
font-size: 22px;
}


.v2_3 {
width: 118px;
height: 127px;
background: linear-gradient(rgba(196,196,196,0), rgba(65,210,14,0.8799999952316284));
opacity: 1;
position: absolute;
top: 88.25%;
left: 82%;
overflow: hidden;
}
.v2_5 {
width: 147px;
height: 70px;
background: url("./img/v2_5.png");
background-repeat: no-repeat;
background-position: center center;
background-size: cover;
opacity: 1;
position: absolute;
top: 93%;
left: 81.2%;
transform: rotate(-5deg);
overflow: hidden;
}
.v3_6 {
width: 99px;
color: rgba(255,255,255,1);
position: absolute;
top: 89%;
left: 82.5%;
font-family: Russo One;
font-weight: Regular;
font-size: 10px;
opacity: 0.6499999761581421;
text-align: center;
transform: rotate(-1deg);
}
.v3_7 {
width: 131px;
color: rgba(255,255,255,1);
position: absolute;
top: 92%;
left: 81.6%;
font-family: Russo One;
font-weight: Regular;
font-size: 10px;
opacity: 0.6499999761581421;
text-align: center;
transform: rotate(-2deg);
}
.v3_8 {
width: 113px;
color: rgba(255,255,255,1);
position: absolute;
top: 90%;
left: 82.1%;
font-family: Russo One;
font-weight: Regular;
font-size: 18px;
opacity: 1;
text-align: center;
transform: rotate(-2deg);
}
.v3_9 {
width: 150px;
color: rgba(255,255,255,1);
position: absolute;
font-family: Russo One;
font-weight: Regular;
font-size: 16px;
opacity: 1;
text-align: center;
transform: rotate(-3deg);
top: 95.4%;
left: 81.1%;
text-shadow: 0px 0px 10px rgba(0,0,0,0.67);
}

.panel {
display:none;
width: 102%;
height: 103%;
background: url("./img/bck.jpg");
background-repeat: no-repeat;
background-position: center center;
background-size: cover;
opacity: 1;
position: relative;
top: 0px;
left: 0px;
overflow: hidden;
margin-left: -1%;
margin-top: -1%;
}

.investment {
display: block;
margin-top: 13%;
margin-left: 26%;
overflow-y: scroll;
position: absolute;
padding: 0 43;
width: 45%;
height: 69%;

}

.investment::-webkit-scrollbar {
width: 5px;

}

.investment::-webkit-scrollbar-track {
background: rgb(99, 97, 97);
}

.investment::-webkit-scrollbar-thumb {
background: #000000;
}

.v4_4 {
color: rgba(255,255,255,1);
position: absolute;
top: 9%;
left: 43%;
font-family: Maven Pro;
font-weight: Bold;
font-size: 69px;
opacity: 1;
text-align: center;
}
.v4_5 {
width: 95%;
height: 24.7%;
background: rgba(0,0,0,0.46000000834465027);
opacity: 1;
position: absolute;
border-top-left-radius: 10px;
border-top-right-radius: 10px;
border-bottom-left-radius: 10px;
border-bottom-right-radius: 10px;
overflow: hidden;
margin-left: -4%;
}
.v4_52 {
width: 95%;
height: 24.7%;
background: rgba(0,0,0,0.46000000834465027);
opacity: 1;
position: absolute;
border-top-left-radius: 10px;
border-top-right-radius: 10px;
border-bottom-left-radius: 10px;
border-bottom-right-radius: 10px;
overflow: hidden;
margin-top: 22%;
margin-left: -4%;
}

.v4_53 {
width: 95%;
height: 24.7%;
background: rgba(0,0,0,0.46000000834465027);
opacity: 1;
position: absolute;
border-top-left-radius: 10px;
border-top-right-radius: 10px;
border-bottom-left-radius: 10px;
border-bottom-right-radius: 10px;
overflow: hidden;
margin-top: 44%;
margin-left: -4%;
}

.v4_54 {
width: 95%;
height: 24.7%;
background: rgba(0,0,0,0.46000000834465027);
opacity: 1;
position: absolute;
border-top-left-radius: 10px;
border-top-right-radius: 10px;
border-bottom-left-radius: 10px;
border-bottom-right-radius: 10px;
overflow: hidden;
margin-top: 66%;
margin-left: -4%;
}

.v4_55 {
width: 95%;
height: 24.7%;
background: rgba(0,0,0,0.46000000834465027);
opacity: 1;
position: absolute;
border-top-left-radius: 10px;
border-top-right-radius: 10px;
border-bottom-left-radius: 10px;
border-bottom-right-radius: 10px;
overflow: hidden;
margin-top: 88%;
margin-left: -4%;
}

.v4_56 {
width: 95%;
height: 24.7%;
background: rgba(0,0,0,0.46000000834465027);
opacity: 1;
position: absolute;
border-top-left-radius: 10px;
border-top-right-radius: 10px;
border-bottom-left-radius: 10px;
border-bottom-right-radius: 10px;
overflow: hidden;
margin-top: 110%;
margin-left: -4%;
}

.v4_5 img{
width: 22%;
}
.v4_52 img{
width: 22%;
}
.v4_53 img{
width: 22%;
}
.v4_54 img{
width: 22%;
}
.v4_55 img{
width: 22%;
}
.v4_56 img{
width: 22%;
}

.v4_6 {
width: 194px;
height: 194px;
background: url("./img/1.jpg");
background-repeat: no-repeat;
background-position: center center;
background-size: cover;
opacity: 1;
position: absolute;
top: 228px;
left: 290px;
overflow: hidden;
}
.v5_7 {
font-family: Maven Pro;
font-weight: Bold;
font-size: 23px;
opacity: 1;
text-align: left;
margin-top: 5%;
margin-left: 3%;
color: rgba(255,255,255,1);
position: absolute;
}
.v5_8 {
font-size: 16px;
opacity: 1;
text-align: left;
margin-top: 9%;
margin-left: 3%;
font-family: Oswald;
color: rgba(255,255,255,1);
position: absolute;
}
.v5_9 {
font-family: Maven Pro;
font-weight: Bold;
font-size: 15px;
opacity: 1;
text-align: left;
margin-top: 13%;
margin-left: 3%;
color: rgba(255,255,255,0.6800000071525574);
position: absolute;
}
.v5_13 {
font-family: Maven Pro;
font-weight: Bold;
font-size: 15px;
opacity: 1;
text-align: right;
margin-top: 5%;
margin-left: 67%;
color: rgba(255,255,255,0.8399999737739563);
position: absolute;
}
.v5_14 {
font-family: Oswald;
font-weight: Bold;
font-size: 38px;
opacity: 1;
text-align: right;
margin-left: 60%;
margin-top: 6%;
color: rgba(255,255,255,1);
position: absolute;
}
.v5_15 {
border: 2px solid rgba(222,203,55,1);
border-top-left-radius: 100px;
border-top-right-radius: 100px;
border-bottom-left-radius: 100px;
border-bottom-right-radius: 100px;
overflow: hidden;
margin-top: -10%;
margin-left: 85%;
background-repeat: no-repeat;
background-position: center center;
background-size: cover;
opacity: 1;
position: absolute;
width: 91px;
height: 38px;
background: url("../images/v5_15.png");
color: rgba(222,203,55,1);
}

.v5_152 {
border: 2px solid rgba(222,203,55,1);
border-top-left-radius: 100px;
border-top-right-radius: 100px;
border-bottom-left-radius: 100px;
border-bottom-right-radius: 100px;
overflow: hidden;
margin-top: 12%;
margin-left: 32%;
opacity: 1;
position: absolute;
width: 152px;
height: 56px;
background: url("../images/v5_15.png");
color: rgba(222,203,55,1);
}

.v5_15:hover {
background-color: rgba(222,203,55,1);
color: black;
}

.v5_152:hover {
background-color: rgba(222,203,55,1);
color: black;
}

.v5_16 {
font-family: Oswald;
font-weight: Bold;
font-size: 18px;
opacity: 1;
text-align: right;
margin-top: 7%;
margin-left: 21%;
width: 51px;
position: absolute;
}

.v5_162 {
font-family: Oswald;
font-weight: Bold;
font-size: 24px;
opacity: 1;
text-align: right;
margin-top: 6%;
margin-left: -28%;
width: 51px;
position: absolute;
}

.notificationitem {
background-color: #ffdb03;
width: 18%;
height: 9%;
top: 47%;
position: absolute;
left: 83%;
border-top-left-radius: 20px;
border-bottom-left-radius: 20px;
-webkit-animation: bounce-in-right 1.1s both;
        animation: bounce-in-right 1.1s both;
text-align: center;
}

.notification {
display:none;
}

.notificationitem img {
position: absolute;
height: 208%;
top: -108%;
left: 11%;
transform: scaleX(-1);
}

.titlenoti {
font-family: Maven Pro;
font-size: 29px;
margin-left: -42%;
}

.descnoti {
font-family: Russo One;
margin-left: -46%;
}

.moneynoti {
font-family: Russo One;
margin-left: -43%;
font-size: 36px;
}

.congrats {
background-color: black;
color: white;
border-top-left-radius: 20px;
}


@-webkit-keyframes bounce-in-right {
0% {
  -webkit-transform: translateX(600px);
          transform: translateX(600px);
  -webkit-animation-timing-function: ease-in;
          animation-timing-function: ease-in;
  opacity: 0;
}
38% {
  -webkit-transform: translateX(0);
          transform: translateX(0);
  -webkit-animation-timing-function: ease-out;
          animation-timing-function: ease-out;
  opacity: 1;
}
55% {
  -webkit-transform: translateX(68px);
          transform: translateX(68px);
  -webkit-animation-timing-function: ease-in;
          animation-timing-function: ease-in;
}
72% {
  -webkit-transform: translateX(0);
          transform: translateX(0);
  -webkit-animation-timing-function: ease-out;
          animation-timing-function: ease-out;
}
81% {
  -webkit-transform: translateX(32px);
          transform: translateX(32px);
  -webkit-animation-timing-function: ease-in;
          animation-timing-function: ease-in;
}
90% {
  -webkit-transform: translateX(0);
          transform: translateX(0);
  -webkit-animation-timing-function: ease-out;
          animation-timing-function: ease-out;
}
95% {
  -webkit-transform: translateX(8px);
          transform: translateX(8px);
  -webkit-animation-timing-function: ease-in;
          animation-timing-function: ease-in;
}
100% {
  -webkit-transform: translateX(0);
          transform: translateX(0);
  -webkit-animation-timing-function: ease-out;
          animation-timing-function: ease-out;
}
}
@keyframes bounce-in-right {
0% {
  -webkit-transform: translateX(600px);
          transform: translateX(600px);
  -webkit-animation-timing-function: ease-in;
          animation-timing-function: ease-in;
  opacity: 0;
}
38% {
  -webkit-transform: translateX(0);
          transform: translateX(0);
  -webkit-animation-timing-function: ease-out;
          animation-timing-function: ease-out;
  opacity: 1;
}
55% {
  -webkit-transform: translateX(68px);
          transform: translateX(68px);
  -webkit-animation-timing-function: ease-in;
          animation-timing-function: ease-in;
}
72% {
  -webkit-transform: translateX(0);
          transform: translateX(0);
  -webkit-animation-timing-function: ease-out;
          animation-timing-function: ease-out;
}
81% {
  -webkit-transform: translateX(32px);
          transform: translateX(32px);
  -webkit-animation-timing-function: ease-in;
          animation-timing-function: ease-in;
}
90% {
  -webkit-transform: translateX(0);
          transform: translateX(0);
  -webkit-animation-timing-function: ease-out;
          animation-timing-function: ease-out;
}
95% {
  -webkit-transform: translateX(8px);
          transform: translateX(8px);
  -webkit-animation-timing-function: ease-in;
          animation-timing-function: ease-in;
}
100% {
  -webkit-transform: translateX(0);
          transform: translateX(0);
  -webkit-animation-timing-function: ease-out;
          animation-timing-function: ease-out;
}
}

@media screen and (width:3840px){
.v2_3 {
  top: 91.25%;
}

.v2_5 { 
  top: 95%;
  left: 81.6%;
}

.v3_9 {
  top: 97%;
  left: 81.5%;
  font-size: 1.5rem;
}

.v3_7 {
  top: 94.6%;
  left: 81.8%;
}

.v3_8 { 
  top: 93%;
}

.v3_6 {
  top: 92.2%;
  left: 82.3%;
}

.v4_4 {
  left: 46%;
  font-size: 87px;
}

.investment {
  margin-top: 6%;
  margin-left: 28%;
}

.v4_5 {
  width: 57%;
  margin-left: 15%;
}

.v4_52 {
  margin-top: 12%;
  width: 57%;
  margin-left: 15%;
}

.v4_53 {
  margin-top: 24%;
  width: 57%;
  margin-left: 15%;
}

.v4_54 {
  margin-top: 36%;
  width: 57%;
  margin-left: 15%;
}

.v4_55 {
  margin-top: 48%;
  width: 57%;
  margin-left: 15%;
}

.v4_56 {
  margin-top: 60%;
  width: 57%;
  margin-left: 15%;
}

.v5_14 {
  margin-left: 63%;
}

.v5_15 {
  margin-top: -10%;
  margin-left: 87%;
}

.money {
  left: 84%;
}

.conalert {
  font-size: 103px;
}

.confirm1 {
  font-size: 32px;
}

.v5_152 {
  width: 245px;
  height: 89px;
  margin-top: 8%;
}

.v5_162 { 
  font-size: 40px;
}

.notificationitem {
  left: 86%;
}

.titlenoti {
  margin-left: -50%;
}

.descnoti {
  margin-left: -52%;
  font-size: 25px;
  position: absolute;
  left: 26rem;
  top: 2rem;
}

.moneynoti {
  font-size: 47px;
  position: absolute;
  top: 3.2rem;
  left: 25rem;
}
.panel{
  height: 105%;
}
}


@media screen and (max-width:3440px){
.v2_3 {
  top: 91.25%;
}

.v2_5 { 
  top: 95%;
  left: 81.6%;
}

.v3_9 {
  top: 96.8%;
  left: 81.5%;
}

.v3_7 {
  top: 94.6%;
  left: 81.8%;
}

.v3_8 { 
  top: 93%;
}

.v3_6 {
  top: 92.2%;
  left: 82.3%;
}

.v4_4 {
  left: 46%;
  font-size: 87px;
}

.investment {
  margin-top: 10%;
  margin-left: 28%;
}

.v4_52 {
  margin-top: 18%;
}

.v4_53 {
  margin-top: 36%;
}

.v4_54 {
  margin-top: 54%;
}

.v4_55 {
  margin-top: 72%;
}

.v4_56 {
  margin-top: 90%;
}

.v5_14 {
  margin-left: 63%;
}

.v5_15 {
  margin-top: -12%;
  margin-left: 86%;
}

.money {
  left: 84%;
}

.conalert {
  font-size: 103px;
}

.confirm1 {
  font-size: 32px;
}

.v5_152 {
  width: 245px;
  height: 89px;
  margin-top: 8%;
}

.v5_162 { 
  font-size: 40px;
}

.notificationitem {
  left: 86%;
}

.titlenoti {
  margin-left: -55%;
}

.descnoti {
  margin-left: -57%;
  font-size: 25px;
}

.moneynoti {
  margin-left: -57%;
  font-size: 50px;
}
}


@media screen and (max-width:2560px){

.v4_52 {
  margin-top: 22%;
}

.v4_53 {
  margin-top: 44%;
}

.v4_54 {
  margin-top: 66%;
}

.v4_55 {
  margin-top: 88%;
}

.v4_56 {
  margin-top: 110%;
}

.v4_4 {
  left: 43.3%;
  font-size: 87px;
}

.v5_152 {
  margin-left: 27%;
}

.notificationitem {
  left: 82%;
}

.notificationitem img {
  left: 15%;
}

.titlenoti {
  margin-left: -40%;
}

.descnoti {
  margin-left: -42%;
  font-size: 25px;
}

.moneynoti {
  margin-left: -42%;
  font-size: 50px;
}
}

@media screen and (max-width:1920px){

.v2_5 {
  top: 94.1%;
  left: 81.4%;
}

.v3_7 {
  top: 93.1%;
  left: 81.6%;
}

.v3_8 {
  top: 90.8%;
}

.v3_6 {
  top: 89.5%;
  left: 82.3%;
}

.v2_3 {
  top: 89.25%;
}

.v3_9 {
  top: 96.5%;
  left: 81.1%;
}

.money {
  left: 79%;
}

.investment {
  margin-top: 13%;
  margin-left: 28%;
}

.v5_14 {
  margin-left: 60%;
}

.v5_15 {
  margin-top: -9%;
  margin-left: 84%;
}

.conalert {
  font-size: 48px;
}

.confirmation {
  left: 41.7%;
  height: 32%;
  top: 32%;
}

.confirm1 {
  font-size: 18px;
}

.v5_152 {
  margin-left: 31%;
  width: 150px;
  height: 57px;
}

.v5_162 {
  font-size: 29px;
  margin-top: 4%;
  margin-left: -34%;
}

.descnoti {
  margin-left: -42%;
  font-size: 18px;
}

.titlenoti {
  margin-left: -40%;
  font-size: 21px;
}

.moneynoti {
  margin-left: -42%;
  font-size: 43px;
}

.v4_4 {
  left: 44.3%;
  font-size: 69px;
}
}

@media screen and (max-width:1280px){

.v2_5 {
  top: 91%;
  margin-left: -0.7%;
}

.v3_9 {
  top: 94.5%;
  left: 80.7%;
}

.v3_7 {
  top: 90.6%;
  left: 81.6%;
}

.v3_8 {
  top: 87.8%;
}

.v3_6 {
  top: 86.3%;
  left: 82.5%;
}

.v2_3 {
  top: 85.25%;
}

.v4_4 {
  left: 43.3%;
  font-size: 58px;
}

.investment {
  margin-top: 13%;
  margin-left: 26%;
}

.v5_8 {
  font-size: 13px;
}

.v5_13 {
  font-size: 13px;
}

.v5_14 {
  font-size: 30px;
  margin-left: 60%;
}

.v5_15 {
  margin-top: -9%;
  margin-left: 86%;
  width: 67px;
  height: 23px;
}

.v5_16 {
  font-size: 15px;
  margin-top: 1%;
  margin-left: 7%;
}

.conalert {
  font-size: 41px;
}

.confirm1 {
  font-size: 14px;
}

.v5_152 {
  margin-left: 33%;
  width: 91px;
  height: 33px;
  margin-top: 6%;
}

.v5_162 {
  font-size: 17px;
  margin-top: 3%;
  margin-left: -34%;
}

.titlenoti {
  margin-left: -40%;
  font-size: 18px;
}

.descnoti {
  margin-left: -42%;
  font-size: 13px;
}

.moneynoti {
  margin-left: -42%;
  font-size: 24px;
}
}