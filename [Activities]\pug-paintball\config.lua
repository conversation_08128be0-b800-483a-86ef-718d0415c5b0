-- FULL MAP MAKING TUTORIAL HERE: https://youtu.be/wALLIIaNoPE (If you make any more new maps please share them in pugs discord in the snippet section. THank you!)
---------- [STRINGS] ----------
Config.Currency = "bank" -- bank, cash, or crypto. This is the buy in money type to pull from players and give to players when the game starts and finishes 
Config.PaintballBusinessName = "paintball" -- The name of the paintball job that will collect the money of the business. (ignore if your not making this a business)
Config.ScoreBoardCommand = 'pballboard' -- the scoreboard command for opening the scoreboard while in a match (wouldnt change this)
Config.ScoreBoardKeyBind = 'G' -- the scoreboard key for opening the scoreboard while in a match (wouldnt change this)
Config.SurrenderCommand = 'surrenderpaintball' -- this is a command to surrender the paintball match (wouldnt change this)
Config.RedFlagModel = 'apa_prop_flag_switzerland' -- Red Flag model that is use in Capture the flag
Config.BlueFlagModel = 'apa_prop_flag_eu_yt' -- Blue Flag model that is use in Capture the flag
Config.BombSiteModel = 'xm_prop_crates_weapon_mix_01a' -- Model of the bomb site for search & Destroy (un used as search and destroy was scrapped)
Config.OneInTheChamberWeapon = "weapon_pistol" -- Weapon used for the one in the chanmber game mode
Config.SpecailWeaponItem = "weapon_minigun" -- Special weapon granted for the Config.SpecialWeaponKillsStreak kill streak
Config.RedOutfitCommand = "redoutfit" -- Admin command to save the current clothes you are wearing and set them as the red teams clothes. (To set the girls outfit you need to be the female model and vice versa)
Config.BlueOutfitCommand = "blueoutfit" -- Admin command to save the current clothes you are wearing and set them as the blue teams clothes. (To set the girls outfit you need to be the female model and vice versa)
---------- [INTAGERS] ----------
Config.MaxTDMScore = 30 -- what score does the winning team need to reach to win in the team deathmatch game mode
Config.MaxFFAScore = 30 -- How many kills does a player need to get to winn a free for all, & Gun Game, & One In The Chamber match.
Config.MaxLives = 7 -- Maximum amount of lives players can set (only for hold your own game mode)
Config.RedTeamRadio = 945 -- Radio channel the red team gets put on too.
Config.BlueTeamRadio = 900 -- Radio channel the blue team gets put on too.
Config.MinWager = 0 -- minimum bet players can make on a game.
Config.MaxWager = 500000 -- maximum bet players can make on a game.
Config.MaxTeam = 12 -- (DONT CHANGE THIS, I PUT THIS HERE IN CASE I MAKE MORE THEN 24 PLAYER SUPPORT IN THE FUTURE)
Config.UavKillstreak = 3 -- How many kills a player needs to get without dying to earn a uav killstreak reward.
Config.SpecialWeaponKillsStreak = 5 -- How many kills a player needs to get without dying to earn the Config.SpecailWeaponItem kill streak.
Config.PassiveModeCoolDownWaitTime = 3 -- How many seconds the player is in passive mode after respawning.
Config.ArmorAmountGivenToPlayer = 100 -- (0-100) Set the players armor value every time the player spawns throughout the game.
Config.CaptureTheFlagDeathTime = 10 -- Amount of seconds for death cooldown in capture the flag.
---------- [BOOLS] ----------
Config.Debug = false -- enables debug poly for zones and prints
Config.SetUnlimitedSprint = true -- Make this false if you want to remove the unlimited sprint feature.
Config.GiveFullHealthOnkill = true -- Make this false if you do not want the players health to be reset upon getting a kill.
Config.HostOnlyCanControllGame = true -- Make this false if you want everyone to be able to choose there own weapon and change the game settings for everyone before the game starts.
Config.EnableKillStreaks = true -- Make this false if want to disable killstreaks.
Config.UavAlwaysOnDuringFFA = true -- Make this false if you want there to not be a constant uav during FFA game modes.
Config.RequireAdminToStartGane = false -- Make this true if you want only admins to be able to start a battle royale game.
Config.RequirePlayersOnBothTeams = false -- true requires there to be at least 1 player on each team to start
Config.CanChooseGunMidGame = true -- change this to false if you do not want players to be able to change guns mid game.
Config.SpectateEnabled = true -- if you dont want players to have the option to spectate make this false
Config.LockInventory = true -- locks the players inventory when a match starts.
Config.MakeCloneOfPlayer = false -- Makes a clone of the player where they were standing if this is true
Config.UseVrHeadSet = false -- Toggle this true if you are using the vr headset instead of peds (only works/compatible if you also own my battle royale script that can be found here: https://pug-webstore.tebex.io/package/5513593)
Config.PaintballIsABusiness = false -- Make this true if you want 15% of every games wager to go into the Config.PaintballBusinessName management acount. (ONLY QBCORE AND ONLY QB-MANAGEMENT)
Config.RemoveAllItemsForPlayer = false -- Make this true if you want players to have all of their items removed when the game starts. (This is here because some inventories do not lock being able to use hotbar when inventory is locked)
Config.UsingCrossHairByDefault = false -- This was requested for server that have crosshair on always. If you are one of those servers then make this true or else just leave it.
Config.DoScreenFadeOut = true -- Do a screen fade out at the end of the game.
------------------------------
Config.PedLocation = vector4(-282.17, -2031.36, 29.23, 293.96) -- The location of the ped where join the game.
Config.ArenaPed = 'cs_stevehains' -- The model of the ped where you join the game at.
------------------------------
Config.WhitelistedCIDsToStartGame = { -- If you want to whitelist the game to only be able to be started by specific people, put there CID inside of this table if you are qbcore. If you are esx then put there identifer in this table.
    -- "EJD97814",
}
------------------------------
-- "/redoutfit" and "/blueoutfit" are the commands to save the current outfit you are wearing as each teams outfit. To set the girls outfit you need to be the female model and vice versa.
------------------------------
------------------------------
-- FULL MAP MAKING TUTORIAL HERE: https://youtu.be/wALLIIaNoPE (If you make any more new maps please share them in pugs discord in the snippet section. THank you!)
-- Map selection (you can add as many as you like. Just make sure to add the spawn locations for each team to Config.RedTeamSpawns & Config.BlueTeamSpawns & Config.FFASpawns
-- As well as the flag locations found in Config.RedFlagLocation & Config.BlueFlagLocation tables.
Config.Arenas = {
    [1] = {
        name = 'Jurassic Park',
        map = 'Set_Dystopian_02',
        url = 'https://r2.fivemanage.com/DAM6EWhKT2jYdC9k2rRgZ/Jurastic park.png',
        description = 'Welcome To Jurassic Park'
    },
    [2] = {
        name = 'Wrecking Ball',
        map = 'Set_Dystopian_03',
        url = 'https://r2.fivemanage.com/DAM6EWhKT2jYdC9k2rRgZ/wrecking ball.png',
        description = 'Time to wreck some balls'
    },
    [3] = {
        name = 'Scrap Yard',
        map = 'Set_Dystopian_04',
        url = 'https://r2.fivemanage.com/DAM6EWhKT2jYdC9k2rRgZ/scrap yard.png',
        description = 'Good cover'
    },
    [4] = {
        name = 'Ship Wreck',
        map = 'Set_Dystopian_07',
        url = 'https://r2.fivemanage.com/DAM6EWhKT2jYdC9k2rRgZ/ship wreck.png',
        description = 'A ship has been wrecked'
    },
    [5] = {
        name = 'Industrial Whore House',
        map = 'Set_Dystopian_09',
        url = 'https://r2.fivemanage.com/DAM6EWhKT2jYdC9k2rRgZ/industrial whore house.png',
        description = 'Dont fall off'
    },
    [6] = {
        name = 'Scrap Yard 2',
        map = 'Set_Dystopian_10',
        url = 'https://r2.fivemanage.com/DAM6EWhKT2jYdC9k2rRgZ/scrap yard 2.png',
        description = 'Down goes the titan'
    },
    [7] = {
        name = 'Future Palace',
        map = 'Set_Scifi_09',
        url = 'https://r2.fivemanage.com/DAM6EWhKT2jYdC9k2rRgZ/future palace.png',
        description = 'Too futuristic no cover'
    },
    [8] = {
        name = 'Toy Soldier',
        map = 'Set_Wasteland_01',
        url = 'https://r2.fivemanage.com/DAM6EWhKT2jYdC9k2rRgZ/toy soldier.png',
        description = 'Little man in a big world'
    },
    [9] = {
        name = 'Tire City',
        map = 'Set_Wasteland_03',
        url = 'https://r2.fivemanage.com/DAM6EWhKT2jYdC9k2rRgZ/tire city.png',
        description = 'Not that tire city'
    },
    [10] = {
        name = 'Walk In The Park',
        map = 'Set_Wasteland_07',
        url = 'https://r2.fivemanage.com/DAM6EWhKT2jYdC9k2rRgZ/walk in the park.png',
        description = 'Tire Park City'
    },
    [11] = {
        name = 'Tube Town',
        map = 'Set_Wasteland_09',
        url = 'https://r2.fivemanage.com/DAM6EWhKT2jYdC9k2rRgZ/tube town.png',
        description = 'Tube City Town'
    },
    [12] = {
        name = 'Nuketown Arena',
        map = 'nuketown',
        url = 'https://r2.fivemanage.com/DAM6EWhKT2jYdC9k2rRgZ/nuketown arena.png',
        description = 'Classic Nuketown',
    },
    [13] = {
        name = 'Nuketown Mirror Park',
        map = 'nuketownmirrorpark',
        url = 'https://r2.fivemanage.com/DAM6EWhKT2jYdC9k2rRgZ/nuketown mirrorpark.png',
        description = 'Nuketown in the city',
    },
    [14] = {
        name = 'Uptown Construction',
        map = 'uptownconstruction',
        url = 'https://r2.fivemanage.com/DAM6EWhKT2jYdC9k2rRgZ/uptown construction.png',
        description = 'Construction site up down',
        ZoneCenter = vector3(69.37, -387.77, 39.92), -- if you add a zone center vector to the map listing it will create a zone around a map location so players cant leave it.
        Radius = 105.0, -- put a radius value here for how big the zone should be.
        -- This is how you make new maps wherever you want throughout the gta 5 world.
    },
    [15] = {
        name = 'Grove Street',
        map = 'grove',
        url = 'https://r2.fivemanage.com/DAM6EWhKT2jYdC9k2rRgZ/grove street.png',
        description = 'Who runnin the block',
        ZoneCenter = vector3(105.94, -1938.13, 20.8), -- if you add a zone center vector to the map listing it will create a zone around a map location so players cant leave it.
        Radius = 77.0, -- put a radius value here for how big the zone should be.
        -- This is how you make new maps wherever you want throughout the gta 5 world.
    },
    [16] = {
        name = 'Stab City',
        map = 'stab',
        url = 'https://r2.fivemanage.com/DAM6EWhKT2jYdC9k2rRgZ/stab city.png',
        description = 'Trailer Park Boys',
        ZoneCenter = vector3(65.36, 3702.91, 39.75), -- if you add a zone center vector to the map listing it will create a zone around a map location so players cant leave it.
        Radius = 87.0, -- put a radius value here for how big the zone should be.
        -- This is how you make new maps wherever you want throughout the gta 5 world.
    },
    [17] = {
        name = 'Reds Salvage Yard',
        map = 'salvage',
        url = 'https://r2.fivemanage.com/DAM6EWhKT2jYdC9k2rRgZ/reds salvage yard.png',
        description = 'Trailer Park Boys',
        ZoneCenter = vector3(-185.22, 6271.65, 31.49), -- if you add a zone center vector to the map listing it will create a zone around a map location so players cant leave it.
        Radius = 50.0, -- put a radius value here for how big the zone should be.
        -- This is how you make new maps wherever you want throughout the gta 5 world.
    },
    [18] = {
        name = 'Grandmas House',
        map = 'grandmas',
        url = 'https://r2.fivemanage.com/DAM6EWhKT2jYdC9k2rRgZ/grandmas house.png',
        description = 'Makes the best cookies',
        ZoneCenter = vector3(2444.54, 4974.28, 46.81), -- if you add a zone center vector to the map listing it will create a zone around a map location so players cant leave it.
        Radius = 25.0, -- put a radius value here for how big the zone should be.
        -- This is how you make new maps wherever you want throughout the gta 5 world.
    },
    -- [19] = {
    --     name = 'In Door Arena',
    --     map = 'gabz',
    --     url = 'https://r2.fivemanage.com/DAM6EWhKT2jYdC9k2rRgZ/gabz.png',
    --     description = 'Red VS Blue',
    -- },
    -- [20] = {
    --     name = 'In Door Derby',
    --     map = 'derby',
    --     url = 'https://r2.fivemanage.com/DAM6EWhKT2jYdC9k2rRgZ/derby.png',
    --     description = 'Meant for cars but ya know',
    -- },
}

------------------------------
------------------------------
-- If You cahnge the map names they need to match here as well or they will scuff for capture the flag
Config.RedFlagLocation = {
    ['Jurassic Park'] = { 
        ['Coords'] = vector3(2937.17, -3802.91, 144.25),
    },
    ['Wrecking Ball'] = { 
        ['Coords'] = vector3(2945.65, -3796.44, 143.26),
    },
    ['Scrap Yard'] = { 
        ['Coords'] = vector3(2945.2, -3791.37, 140.03),
    },
    ['Ship Wreck'] = { 
        ['Coords'] = vector3(2946.17, -3797.53, 140.1),
    },
    ['Industrial Whore House'] = { 
        ['Coords'] = vector3(2947.03, -3800.03, 139.63),
    },
    ['Scrap Yard 2'] = { 
        ['Coords'] = vector3(2948.42, -3790.24, 139.96),
    },
    ['Future Palace'] = { 
        ['Coords'] = vector3(2927.6, -3799.88, 140.0),
    },
    ['Toy Soldier'] = { 
        ['Coords'] = vector3(2946.13, -3795.33, 138.0),
    },
    ['Tire City'] = { 
        ['Coords'] = vector3(2908.72, -3791.98, 137.2),
    },
    ['Walk In The Park'] = { 
        ['Coords'] = vector3(2912.38, -3816.65, 139.89),
    },
    ['Tube Town'] = { 
        ['Coords'] = vector3(2910.97, -3831.78, 139.67),
    },
    ['In Door Arena'] = { 
        ['Coords'] = vector3(-340.46, -1955.01, 21.64),
    },
    ['In Door Derby'] = { 
        ['Coords'] = vector3(-357.69, -1940.84, 21.63),
    },
    ['Nuketown Arena'] = { 
        ['Coords'] = vector3(-3234.46, 7045.22, 637.62),
    },
    ['Nuketown Mirror Park'] = { 
        ['Coords'] = vector3(1376.41, -784.41, 67.64),
    },
    ['Uptown Construction'] = { 
        ['Coords'] = vector3(141.65, -368.0, 43.26),
    },
    ['Grove Street'] = { 
        ['Coords'] = vector3(70.32, -1968.94, 20.79),
    },
    ['Stab City'] = { 
        ['Coords'] = vector3(110.48, 3679.04, 39.75),
    },
    ['Reds Salvage Yard'] = { 
        ['Coords'] = vector3(-221.28, 6283.61, 31.49),
    },
    ['Grandmas House'] = { 
        ['Coords'] = vector3(2434.99, 4967.02, 42.35),
    },
}
Config.BlueFlagLocation = {
    ['Jurassic Park'] = { 
        ['Coords'] = vector3(2664.6, -3793.08, 143.71),
    },
    ['Wrecking Ball'] = { 
        ['Coords'] = vector3(2658.59, -3797.45, 143.58),
    },
    ['Scrap Yard'] = { 
        ['Coords'] = vector3(2655.69, -3804.56, 140.03),
    },
    ['Ship Wreck'] = { 
        ['Coords'] = vector3(2650.22, -3778.95, 140.14),
    },
    ['Industrial Whore House'] = { 
        ['Coords'] = vector3(2650.93, -3799.97, 139.68),
    },
    ['Scrap Yard 2'] = { 
        ['Coords'] = vector3(2642.46, -3798.63, 139.97),
    },
    ['Future Palace'] = { 
        ['Coords'] = vector3(2672.77, -3799.88, 140.0),
    },
    ['Toy Soldier'] = { 
        ['Coords'] = vector3(2655.36, -3803.95, 137.43),
    },
    ['Tire City'] = { 
        ['Coords'] = vector3(2699.51, -3804.63, 139.01),
    },
    ['Walk In The Park'] = { 
        ['Coords'] = vector3(2685.83, -3783.29, 139.89),
    },
    ['Tube Town'] = { 
        ['Coords'] = vector3(2689.6, -3776.92, 139.49),
    },
    ['In Door Arena'] = { 
        ['Coords'] = vector3(-308.23, -1981.9, 21.64),
    },
    ['In Door Derby'] = { 
        ['Coords'] = vector3(-289.12, -1998.23, 21.63),
    },
    ['Nuketown Arena'] = { 
        ['Coords'] = vector3(-3253.7, 6965.81, 637.62),
    },
    ['Nuketown Mirror Park'] = { 
        ['Coords'] = vector3(1399.15, -709.86, 67.07),
    },
    ['Uptown Construction'] = { 
        ['Coords'] = vector3(9.63, -402.06, 39.52),
    },
    ['Grove Street'] = { 
        ['Coords'] = vector3(139.96, -1888.05, 23.31),
    },
    ['Stab City'] = { 
        ['Coords'] = vector3(-10.65, 3714.67, 39.26),
    },
    ['Reds Salvage Yard'] = { 
        ['Coords'] = vector3(-144.94, 6264.45, 31.56),
    },
    ['Grandmas House'] = { 
        ['Coords'] = vector3(2446.45, 4986.21, 51.57),
    },
}
----------
----------
-- UNFINISHED SEARCH & DESTROY (i stopped progress on snd because the maps are very open)
-- Config.BombLocations = {
--     ['Jurassic Park'] = { 
--         ['A-Site'] = vector3(2822.22, -3844.59, 141.83),
--         ['B-Site'] = vector3(2833.45, -3748.29, 148.15),
--     },
--     ['Wrecking Ball'] = { 
--         ['A-Site'] = vector3(2664.6, -3793.08, 143.71),
--         ['B-Site'] = vector3(2664.6, -3793.08, 143.71),
--     },
--     ['Scrap Yard'] = { 
--         ['A-Site'] = vector3(2664.6, -3793.08, 143.71),
--         ['B-Site'] = vector3(2664.6, -3793.08, 143.71),
--     },
--     ['Ship Wreck'] = { 
--         ['A-Site'] = vector3(2664.6, -3793.08, 143.71),
--         ['B-Site'] = vector3(2664.6, -3793.08, 143.71),
--     },
--     ['Industrial Whore House'] = { 
--         ['A-Site'] = vector3(2664.6, -3793.08, 143.71),
--         ['B-Site'] = vector3(2664.6, -3793.08, 143.71),
--     },
--     ['Scrap Yard 2'] = { 
--         ['A-Site'] = vector3(2664.6, -3793.08, 143.71),
--         ['B-Site'] = vector3(2664.6, -3793.08, 143.71),
--     },
--     ['Future Palace'] = { 
--         ['A-Site'] = vector3(2664.6, -3793.08, 143.71),
--         ['B-Site'] = vector3(2664.6, -3793.08, 143.71),
--     },
--     ['Toy Soldier'] = { 
--         ['A-Site'] = vector3(2664.6, -3793.08, 143.71),
--         ['B-Site'] = vector3(2664.6, -3793.08, 143.71),
--     },
--     ['Tire City'] = { 
--         ['A-Site'] = vector3(2664.6, -3793.08, 143.71),
--         ['B-Site'] = vector3(2664.6, -3793.08, 143.71),
--     },
--     ['Walk In The Park'] = { 
--         ['A-Site'] = vector3(2664.6, -3793.08, 143.71),
--         ['B-Site'] = vector3(2664.6, -3793.08, 143.71),
--     },
--     ['Tube Town'] = { 
--         ['A-Site'] = vector3(2664.6, -3793.08, 143.71),
--         ['B-Site'] = vector3(2664.6, -3793.08, 143.71),
--     },
-- }
----------
----------
-- NEVER CHANGE THE ORDER OF THESE AS IT WILL CAUSE SCUFF
Config.GameModes = {
    [1] = {
        name = 'Team DeathMatch',
        description = '12v12 | The team that reaches '..Config.MaxTDMScore..' kills first wins'
    },
    [2] = {
        name = 'Hold Your Own',
        description = '12v12 | Each player tries to last as many lives that are set'
    },
    [3] = {
        name = 'Capture The Flag',
        description = '12v12 | The first team to capture the enemy flag 3 times wins'
    },
    [4] = {
        name = 'Gun Game',
        description = '(BEATA) With every kill comes a new weapon granted. go through '..Config.MaxFFAScore..' weapons to win!'
    },
    [5] = {
        name = 'Free For All',
        description = '(BETA) Every man for himself! Earn '..Config.MaxFFAScore..' kills to win!'
    },
    [6] = {
        name = 'One In The Chamber',
        description = '(BETA) You have 1 bullet, dont waste it! Every kill earns one more bullet.'
    },
    [7] = {
        name = 'Random',
        description = '12v12 | Randomly chosen game mode'
    },
}
----------
----------
-- EXPLOSIVE WEAPONS DO NOT ALWAYS REGISTER KILLS SO DO NOT ADD THEM HERE!!!!!!!!!!!!!!!!!
-- this is used to display waopon information. (if you add more weapons add them here)
Config.WeaponItems = {
    -- Handguns
	['weapon_pistol'] 				 = {['name'] = 'weapon_pistol', 			 	['label'] = 'Beretta M9', 		        ['description'] = 'A small firearm designed to be held in one hand'},
	['weapon_combatpistol'] 		 = {['name'] = 'weapon_combatpistol', 	 	  	['label'] = 'Combat Pistol', 	        ['description'] = 'A combat version small firearm designed to be held in one hand'},
	['weapon_appistol'] 			 = {['name'] = 'weapon_appistol', 		 	  	['label'] = 'AP Pistol', 		        ['description'] = 'A small firearm designed to be held in one hand that is automatic'},
	['weapon_pistol50'] 			 = {['name'] = 'weapon_pistol50', 		 	  	['label'] = 'Desert Eagle', 	        ['description'] = 'A .50 caliber firearm designed to be held with both hands'},
	
    -- Submachine Guns
	['weapon_microsmg'] 			 = {['name'] = 'weapon_microsmg', 		 	  	['label'] = 'Micro SMG', 				['description'] = 'A handheld lightweight machine gun'},
	['weapon_smg'] 				 	 = {['name'] = 'weapon_smg', 			 	  	['label'] = 'SMG', 						['description'] = 'A handheld lightweight machine gun'},
	['weapon_assaultsmg'] 			 = {['name'] = 'weapon_assaultsmg', 		 	['label'] = 'Assault SMG', 				['description'] = 'An assault version of a handheld lightweight machine gun'},
	['weapon_combatpdw'] 			 = {['name'] = 'weapon_combatpdw', 		 	  	['label'] = 'Combat PDW', 				['description'] = 'A combat version of a handheld lightweight machine gun'},
	
	-- Shotguns
	['weapon_pumpshotgun'] 			 = {['name'] = 'weapon_pumpshotgun', 	 	  	['label'] = 'Pump Shotgun', 			['description'] = 'A pump-action smoothbore gun for firing small shot at short range'},
	['weapon_sawnoffshotgun'] 		 = {['name'] = 'weapon_sawnoffshotgun', 	 	['label'] = 'Sawn-off Shotgun', 		['description'] = 'A sawn-off smoothbore gun for firing small shot at short range'},
	['weapon_assaultshotgun'] 		 = {['name'] = 'weapon_assaultshotgun', 	 	['label'] = 'Assault Shotgun', 			['description'] = 'An assault version of asmoothbore gun for firing small shot at short range'},
	
	-- Assault Rifles
	['weapon_assaultrifle'] 		 = {['name'] = 'weapon_assaultrifle', 	 	  	['label'] = 'Assault Rifle', 			['description'] = 'A rapid-fire, magazine-fed automatic rifle designed for infantry use'},
	['weapon_carbinerifle'] 		 = {['name'] = 'weapon_carbinerifle', 	 	  	['label'] = 'Carbine Rifle', 			['description'] = 'A lightweight automatic rifle'},
	['weapon_advancedrifle'] 		 = {['name'] = 'weapon_advancedrifle', 	 	  	['label'] = 'Advanced Rifle', 			['description'] = 'An assault version of a rapid-fire, magazine-fed automatic rifle designed for infantry use'},
	
	-- Light Machine Guns
	
	-- Sniper Rifles
	['weapon_sniperrifle'] 			 = {['name'] = 'weapon_sniperrifle', 	 	  	['label'] = 'Sniper Rifle', 			['description'] = 'A high-precision, long-range rifle'},
	['weapon_heavysniper'] 			 = {['name'] = 'weapon_heavysniper', 	 	  	['label'] = 'Heavy Sniper', 			['description'] = 'An upgraded high-precision, long-range rifle'},
	['weapon_marksmanrifle'] 		 = {['name'] = 'weapon_marksmanrifle', 	 	  	['label'] = 'Marksman Rifle', 			['description'] = 'A very accurate single-fire rifle'},
	
	-- Heavy Weapons
    -- ['weapon_paintgun'] 				 = {['name'] = 'weapon_paintgun', 		 	  	['label'] = 'Paintball Gun', 					['description'] = 'The classics'},
}
----------
----------
-- (this is where you can add more weapons to the menues)
-- Pistol selection menu.
Config.Pistols = {
    ["EnableThisMenu"] = true, -- Make this false if you want to remove the pistol menu
    [1] = {
        description = Config.WeaponItems['weapon_pistol']["description"],
        weapon = 'weapon_pistol',
    },
    [2] = {
        description = Config.WeaponItems['weapon_combatpistol']["description"],
        weapon = 'weapon_combatpistol', 
    },
    [3] = {
        description = Config.WeaponItems['weapon_appistol']["description"],
        weapon = 'weapon_appistol', 
    },
    [4] = {
        description = Config.WeaponItems['weapon_pistol50']["description"],
        weapon = 'weapon_pistol50', 
    },
    
}
-- Smg selection menu.
Config.Smgs = {
    ["EnableThisMenu"] = true, -- Make this false if you want to remove the Smgs menu
    [1] = {
        description = Config.WeaponItems['weapon_microsmg']["description"],
        weapon = 'weapon_microsmg', 
    },
    [2] = {
        description = Config.WeaponItems['weapon_smg']["description"],
        weapon = 'weapon_smg', 
    },
    [3] = {
        description = Config.WeaponItems['weapon_assaultsmg']["description"],
        weapon = 'weapon_assaultsmg', 
    },
    [4] = {
        description = Config.WeaponItems['weapon_combatpdw']["description"],
        weapon = 'weapon_combatpdw', 
    },
}
-- Shotgun selection menu.
Config.Shotguns = {
    ["EnableThisMenu"] = true, -- Make this false if you want to remove the Shotguns menu
    [1] = {
        description = Config.WeaponItems['weapon_pumpshotgun']["description"],
        weapon = 'weapon_pumpshotgun', 
    },
    [2] = {
        description = Config.WeaponItems['weapon_sawnoffshotgun']["description"],
        weapon = 'weapon_sawnoffshotgun', 
    },
    [3] = {
        description = Config.WeaponItems['weapon_assaultshotgun']["description"],
        weapon = 'weapon_assaultshotgun', 
    },
}
-- Assault Rifle selection menu.
Config.AssaultRifles = {
    ["EnableThisMenu"] = true, -- Make this false if you want to remove the Assault Rifles menu
    [1] = {
        description = Config.WeaponItems['weapon_assaultrifle']["description"],
        weapon = 'weapon_assaultrifle', 
    },
    [2] = {
        description = Config.WeaponItems['weapon_carbinerifle']["description"],
        weapon = 'weapon_carbinerifle', 
    },
    [3] = {
        description = Config.WeaponItems['weapon_advancedrifle']["description"],
        weapon = 'weapon_advancedrifle', 
    },
}
-- Sniper Rifle selection menu.
Config.SniperRifles = {
    ["EnableThisMenu"] = true, -- Make this false if you want to remove the Snipers menu
    [1] = {
        description = Config.WeaponItems['weapon_heavysniper']["description"],
        weapon = 'weapon_heavysniper', 
    },
    [2] = {
        description = Config.WeaponItems['weapon_sniperrifle']["description"],
        weapon = 'weapon_sniperrifle', 
    },
    [3] = {
        description = Config.WeaponItems['weapon_marksmanrifle']["description"],
        weapon = 'weapon_marksmanrifle', 
    },
}
----------
----------
-- This is for if a cheater tries to fly away with weapons from the arenea. (this was requested to be added but doesnt make any sense as if they are cheating they will spawn guns in...)
Config.ArenaZone = {
    ["ArenaMain"] = {
        ['Zone'] = {
            ['Shape'] = { --polyzone that surrounds the area
                vector2(2944.2971191406, -3915.4157714844),
                vector2(2700.78125, -3920.5346679688),
                vector2(2657.9755859375, -3914.0756835938),
                vector2(2609.1960449219, -3860.6259765625),
                vector2(2584.8696289063, -3812.9060058594),
                vector2(2586.7077636719, -3763.1921386719),
                vector2(2632.6293945313, -3687.7729492188),
                vector2(2765.46484375, -3668.984375),
                vector2(2927.2419433594, -3675.6569824219),
                vector2(2998.0849609375, -3734.0246582031),
                vector2(3012.6418457031, -3811.8872070313),
                vector2(2995.7155761719, -3865.7541503906)
            },
        ['minZ'] = 142.90644836426,  -- min height of the tournament area
        ['maxZ'] = 196.62568664551  -- max height of the tournament area
        },
    },
}

-- red team random spawns for each map
Config.RedTeamSpawns = {
    ['Set_Dystopian_02'] = { -- Jurassic Park
        [1] = vector4(2949.9, -3858.89, 139.9, 70.0),
        [2] = vector4(2913.58, -3852.01, 139.53, 70.0),
        [3] = vector4(2916.06, -3843.72, 139.83, 70.0),
        [4] = vector4(2907.76, -3846.54, 139.83, 70.0),
        [5] = vector4(2929.09, -3836.31, 140.43, 70.0),
        [6] = vector4(2942.37, -3830.64, 140.34, 70.0),
        [7] = vector4(2959.52, -3811.62, 140.46, 70.0),
        [8] = vector4(2970.22, -3786.28, 140.73, 70.0),
        [9] = vector4(2957.51, -3765.73, 140.79, 70.0),
        [10] = vector4(2943.06, -3764.74, 140.91, 70.0),
        [11] = vector4(2937.39, -3760.26, 141.16, 70.0),
        [12] = vector4(2950.21, -3735.02, 140.24, 70.0),
        [13] = vector4(2944.52, -3739.47, 140.21, 70.0),
        [14] = vector4(2935.83, -3724.61, 140.26, 70.0),
        [15] = vector4(2915.49, -3721.26, 139.79, 70.0),
    },
    ['Set_Dystopian_03'] = { -- Wrecking Ball
        [1] = vector4(2923.43, -3876.73, 140.63, 70.0),
        [2] = vector4(2929.05, -3871.97, 141.12, 70.0),
        [3] = vector4(2922.78, -3859.93, 141.63, 70.0),
        [4] = vector4(2921.61, -3840.53, 141.98, 70.0),
        [5] = vector4(2931.5, -3830.69, 142.98, 70.0),
        [6] = vector4(2955.98, -3839.52, 142.24, 70.0),
        [7] = vector4(2968.24, -3834.53, 140.5, 70.0),
        [8] = vector4(2967.11, -3826.33, 140.87, 70.0),
        [9] = vector4(2975.63, -3798.0, 141.24, 70.0),
        [10] = vector4(2953.52, -3775.01, 142.73, 70.0),
        [11] = vector4(2956.15, -3744.54, 141.2, 70.0),
        [12] = vector4(2929.4, -3741.37, 141.74, 70.0),
        [13] = vector4(2924.11, -3733.33, 140.9, 70.0),
        [14] = vector4(2903.09, -3735.12, 142.56, 70.0),
        [15] = vector4(2876.37, -3731.86, 140.11, 70.0),
    },
    ['Set_Dystopian_04'] = { -- scrap yard
        [1] = vector4(2932.45, -3839.94, 140.03, 70.0),
        [2] = vector4(2894.31, -3795.03, 145.02, 70.0),
        [3] = vector4(2918.61, -3761.1, 140.93, 70.0),
        [4] = vector4(2946.92, -3778.35, 140.03, 70.0),
        [5] = vector4(2929.73, -3841.67, 140.15, 70.0),
        [6] = vector4(2943.84, -3842.19, 140.03, 70.0),
        [7] = vector4(2958.69, -3776.8, 140.03, 70.0),
        [8] = vector4(2895.44, -3737.2, 140.2, 70.0),
        [9] = vector4(2883.5, -3758.99, 143.66, 70.0),
        [10] = vector4(2887.4, -3859.44, 140.03, 70.0),
        [11] = vector4(2923.95, -3840.76, 140.03, 70.0),
        [12] = vector4(2953.01, -3814.16, 140.03, 70.0),
        [13] = vector4(2936.23, -3747.79, 140.03, 70.0),
        [14] = vector4(2916.36, -3737.15, 140.03, 70.0),
        [15] = vector4(2927.52, -3846.26, 140.03, 70.0),
    },
    ['Set_Dystopian_07'] = {  --Ship Wreck
        [1] = vector4(2967.49, -3815.76, 140.03, 70.0),
        [2] = vector4(2953.62, -3771.4, 140.15, 70.0),
        [3] = vector4(2910.63, -3799.06, 141.18, 70.0),
        [4] = vector4(2906.09, -3856.3, 140.07, 70.0),
        [5] = vector4(2941.63, -3861.55, 140.34, 70.0),
        [6] = vector4(2951.73, -3852.77, 140.5, 70.0),
        [7] = vector4(2900.84, -3759.33, 139.66, 70.0),
        [8] = vector4(2919.57, -3732.13, 140.25, 70.0),
        [9] = vector4(2949.53, -3807.99, 140.32, 70.0),
        [10] = vector4(2921.97, -3740.48, 140.17, 70.0),
        [11] = vector4(2940.01, -3731.0, 140.08, 70.0),
        [12] = vector4(2858.84, -3752.35, 136.33, 70.0),
        [13] = vector4(2882.23, -3817.89, 134.76, 70.0),
        [14] = vector4(2969.37, -3771.56, 139.98, 70.0),
        [15] = vector4(2873.54, -3775.81, 134.3, 70.0),
    },
    ['Set_Dystopian_09'] = { --Industrial Whore House
        [1] = vector4(2935.72, -3838.2, 139.49, 70.0),
        [2] = vector4(2935.88, -3828.53, 139.38, 70.0),
        [3] = vector4(2933.84, -3810.23, 139.46, 70.0),
        [4] = vector4(2935.93, -3784.83, 139.29, 70.0),
        [5] = vector4(2920.56, -3770.07, 132.91, 70.0),
        [6] = vector4(2922.45, -3826.97, 132.9, 70.0),
        [7] = vector4(2926.35, -3856.59, 139.93, 70.0),
        [8] = vector4(2955.38, -3830.06, 139.8, 70.0),
        [9] = vector4(2948.03, -3746.44, 139.8, 70.0),
        [10] = vector4(2889.2, -3840.47, 132.44, 70.0),
        [11] = vector4(2885.13, -3775.92, 132.03, 70.0),
        [12] = vector4(2910.59, -3754.15, 132.8, 70.0),
        [13] = vector4(2911.51, -3819.0, 132.72, 70.0),
        [14] = vector4(2938.39, -3842.79, 139.73, 70.0),
        [15] = vector4(2955.31, -3754.23, 139.8, 70.0),
    },
    ['Set_Dystopian_10'] = { --Scrap Yard 2
        [1] = vector4(2946.17, -3830.07, 139.96, 70.0),
        [2] = vector4(2955.71, -3807.72, 139.98, 70.0),
        [3] = vector4(2933.88, -3808.88, 140.07, 70.0),
        [4] = vector4(2928.77, -3749.3, 140.02, 70.0),
        [5] = vector4(2917.59, -3737.87, 140.3, 70.0),
        [6] = vector4(2920.46, -3743.98, 140.85, 70.0),
        [7] = vector4(2937.8, -3845.64, 139.95, 70.0),
        [8] = vector4(2925.83, -3858.04, 139.92, 70.0),
        [9] = vector4(2915.77, -3865.53, 140.07, 70.0),
        [10] = vector4(2912.94, -3857.98, 141.13, 70.0),
        [11] = vector4(2904.27, -3842.57, 139.94, 70.0),
        [12] = vector4(2946.9, -3770.23, 139.97, 70.0),
        [13] = vector4(2875.36, -3811.16, 140.12, 70.0),
        [14] = vector4(2873.69, -3789.46, 140.18, 70.0),
        [15] = vector4(2904.87, -3824.6, 139.93, 70.0),
    },
    ['Set_Scifi_09'] = {  --Future Palace
        [1] = vector4(2960.56, -3806.27, 140.0, 70.0),
        [2] = vector4(2960.84, -3795.93, 140.0, 70.0),
        [3] = vector4(2933.9, -3815.74, 128.0, 70.0),
        [4] = vector4(2943.31, -3779.14, 128.0, 70.0),
        [5] = vector4(2930.44, -3788.41, 128.0, 70.0),
        [6] = vector4(2900.73, -3805.38, 128.0, 70.0),
        [7] = vector4(2900.37, -3790.02, 128.0, 70.0),
        [8] = vector4(2896.3, -3748.51, 128.0, 70.0),
        [9] = vector4(2896.88, -3852.51, 128.0, 70.0),
        [10] = vector4(2889.95, -3862.37, 139.05, 70.0),
        [11] = vector4(2926.14, -3881.05, 140.0, 70.0),
        [12] = vector4(2894.79, -3736.16, 139.68, 70.0),
        [13] = vector4(2936.06, -3748.97, 140.0, 70.0),
        [14] = vector4(2900.96, -3788.9, 139.99, 70.0),
        [15] = vector4(2900.04, -3810.61, 139.99, 70.0),
    },
    ['Set_Wasteland_01'] = { --Toy Soldier
        [1] = vector4(2962.88, -3807.86, 140.0, 70.0),
        [2] = vector4(2954.89, -3762.06, 140.0, 70.0),
        [3] = vector4(2949.17, -3748.84, 140.0, 70.0),
        [4] = vector4(2940.68, -3761.74, 139.35, 70.0),
        [5] = vector4(2918.63, -3732.21, 140.0, 70.0),
        [6] = vector4(2884.53, -3732.06, 137.59, 70.0),
        [7] = vector4(2914.8, -3763.43, 130.88, 70.0),
        [8] = vector4(2935.48, -3795.81, 133.77, 70.0),
        [9] = vector4(2947.95, -3826.44, 139.54, 70.0),
        [10] = vector4(2931.48, -3855.26, 139.84, 70.0),
        [11] = vector4(2933.31, -3873.23, 140.0, 70.0),
        [12] = vector4(2887.53, -3870.45, 138.96, 70.0),
        [13] = vector4(2908.05, -3757.35, 130.63, 70.0),
        [14] = vector4(2911.11, -3732.75, 139.71, 70.0),
        [15] = vector4(2884.24, -3873.87, 140.0, 70.0),
    },
    ['Set_Wasteland_03'] = {  -- Tire City
        [1] = vector4(2932.48, -3835.41, 138.9, 70.0),
        [2] = vector4(2921.29, -3835.11, 138.37, 70.0),
        [3] = vector4(2921.09, -3797.76, 137.9, 70.0),
        [4] = vector4(2917.74, -3781.16, 137.81, 70.0),
        [5] = vector4(2910.74, -3758.41, 138.19, 70.0),
        [6] = vector4(2896.31, -3742.38, 138.59, 70.0),
        [7] = vector4(2915.49, -3738.7, 139.4, 70.0),
        [8] = vector4(2897.32, -3774.75, 137.02, 70.0),
        [9] = vector4(2892.87, -3787.52, 138.85, 70.0),
        [10] = vector4(2896.97, -3851.19, 138.97, 70.0),
        [11] = vector4(2877.31, -3816.16, 136.74, 70.0),
        [12] = vector4(2936.94, -3791.92, 138.64, 70.0),
        [13] = vector4(2934.38, -3844.57, 139.33, 70.0),
        [14] = vector4(2883.72, -3848.37, 140.59, 70.0),
        [15] = vector4(2892.32, -3789.55, 139.23, 70.0),
    },
    ['Set_Wasteland_07'] = { --Walk in the Park
        [1] = vector4(2972.27, -3814.6, 140.07, 70.0),
        [2] = vector4(2974.53, -3791.13, 140.05, 70.0),
        [3] = vector4(2951.05, -3765.52, 140.08, 70.0),
        [4] = vector4(2955.25, -3815.38, 140.08, 70.0),
        [5] = vector4(2948.13, -3840.96, 140.08, 70.0),
        [6] = vector4(2933.85, -3850.52, 140.08, 70.0),
        [7] = vector4(2936.88, -3771.23, 136.24, 70.0),
        [8] = vector4(2926.14, -3792.81, 134.37, 70.0),
        [9] = vector4(2920.02, -3772.16, 130.65, 70.0),
        [10] = vector4(2876.34, -3752.88, 128.09, 70.0),
        [11] = vector4(2878.19, -3868.54, 137.12, 70.0),
        [12] = vector4(2906.56, -3877.52, 140.08, 70.0),
        [13] = vector4(2918.64, -3739.99, 140.36, 70.0),
        [14] = vector4(2915.26, -3751.16, 142.46, 70.0),
        [15] = vector4(2962.38, -3825.52, 140.08, 70.0),
    },
    ['Set_Wasteland_09'] = { --Tube Town
        [1] = vector4(2942.41, -3809.4, 142.19, 70.0),
        [2] = vector4(2940.17, -3792.96, 143.2, 70.0),
        [3] = vector4(2933.01, -3780.45, 140.31, 70.0),
        [4] = vector4(2915.98, -3778.05, 139.55, 70.0),
        [5] = vector4(2901.63, -3793.95, 139.39, 70.0),
        [6] = vector4(2893.69, -3807.53, 138.06, 70.0),
        [7] = vector4(2903.47, -3851.89, 139.75, 70.0),
        [8] = vector4(2888.03, -3857.01, 139.74, 70.0),
        [9] = vector4(2874.09, -3837.62, 139.68, 70.0),
        [10] = vector4(2937.93, -3744.85, 141.02, 70.0),
        [11] = vector4(2901.68, -3788.6, 138.95, 70.0),
        [12] = vector4(2858.82, -3788.83, 142.84, 70.0),
        [13] = vector4(2908.89, -3807.78, 139.78, 70.0),
        [14] = vector4(2953.68, -3821.83, 140.69, 70.0),
        [15] = vector4(2930.53, -3872.77, 143.41, 70.0),
    },
    ['gabz'] = { -- Gabz mlo (part of mba mlo)
        [1] = vector4(-334.85, -1950.4, 21.64, 225.0),
        [2] = vector4(-336.62, -1952.42, 21.64, 225.0),
        [3] = vector4(-338.26, -1954.53, 21.64, 225.0),
        [4] = vector4(-339.42, -1955.86, 21.64, 225.0),
        [5] = vector4(-340.69, -1957.24, 21.64, 225.0),
        [6] = vector4(-342.04, -1958.72, 21.64, 225.0),
        [7] = vector4(-343.64, -1960.64, 21.64, 225.0),
        [8] = vector4(-333.58, -1952.91, 21.64, 225.0),
        [9] = vector4(-335.11, -1954.88, 21.64, 225.0),
        [10] = vector4(-337.0, -1957.07, 21.64, 225.0),
        [11] = vector4(-338.65, -1958.89, 21.64, 225.0),
        [12] = vector4(-340.32, -1960.74, 21.64, 225.0),
        [13] = vector4(-334.45, -1959.91, 21.64, 225.0),
        [14] = vector4(-331.29, -1957.27, 21.64, 225.0),
        [15] = vector4(-337.05, -1962.75, 21.64, 225.0),
    },
    ['derby'] = { -- Gabz Derby mlo (part of mba mlo)
        [1] = vector4(-343.06, -1928.4, 21.63, 225.0),
        [2] = vector4(-345.72, -1931.25, 21.63, 225.0),
        [3] = vector4(-349.83, -1935.56, 21.63, 225.0),
        [4] = vector4(-352.01, -1938.14, 21.63, 225.0),
        [5] = vector4(-354.26, -1940.91, 21.63, 225.0),
        [6] = vector4(-356.22, -1943.32, 21.63, 225.0),
        [7] = vector4(-358.97, -1947.0, 21.63, 225.0),
        [8] = vector4(-361.35, -1950.34, 21.63, 225.0),
        [9] = vector4(-363.26, -1953.51, 21.63, 225.0),
        [10] = vector4(-364.6, -1955.22, 21.63, 225.0),
        [11] = vector4(-351.01, -1942.8, 21.63, 225.0),
        [12] = vector4(-354.81, -1946.7, 21.63, 225.0),
        [13] = vector4(-352.37, -1954.79, 21.7, 225.0),
        [14] = vector4(-346.82, -1949.25, 22.03, 225.0),
        [15] = vector4(-336.08, -1939.85, 22.34, 225.0),
    },
    ['nuketown'] = { -- Nuketown Arena mlo
        [1] = vector4(-3222.61, 7038.63, 637.62, 142.44),
        [2] = vector4(-3223.93, 7039.54, 637.62, 150.14),
        [3] = vector4(-3225.61, 7040.63, 637.62, 144.3),
        [4] = vector4(-3226.98, 7041.58, 637.62, 149.57),
        [5] = vector4(-3228.7, 7042.74, 637.62, 146.34),
        [6] = vector4(-3230.35, 7043.86, 637.62, 143.54),
        [7] = vector4(-3231.57, 7042.0, 637.62, 145.41),
        [8] = vector4(-3229.77, 7040.95, 637.62, 142.52),
        [9] = vector4(-3228.1, 7040.1, 637.62, 143.43),
        [10] = vector4(-3226.26, 7038.41, 637.62, 149.64),
        [11] = vector4(-3224.18, 7036.85, 637.62, 138.91),
        [12] = vector4(-3234.81, 7044.23, 637.62, 149.95),
        [13] = vector4(-3236.94, 7045.56, 637.62, 148.83),
        [14] = vector4(-3238.66, 7046.94, 637.62, 150.94),
        [15] = vector4(-3241.6, 7046.89, 637.62, 175.77),
    },
    ['nuketownmirrorpark'] = { -- Nuketown Mirror Park mlo
        [1] = vector4(1380.56, -783.5, 67.26, 355.9),
        [2] = vector4(1378.97, -783.38, 67.25, 349.19),
        [3] = vector4(1377.51, -783.23, 67.2, 351.19),
        [4] = vector4(1376.0, -782.57, 67.02, 356.01),
        [5] = vector4(1380.98, -782.0, 67.29, 350.33),
        [6] = vector4(1379.2, -781.66, 67.22, 358.04),
        [7] = vector4(1377.52, -781.1, 67.2, 353.23),
        [8] = vector4(1375.78, -780.83, 67.17, 352.76),
        [9] = vector4(1381.49, -780.23, 67.16, 354.28),
        [10] = vector4(1379.97, -779.84, 67.2, 2.18),
        [11] = vector4(1378.32, -779.65, 67.19, 352.45),
        [12] = vector4(1376.9, -779.41, 67.18, 350.39),
        [13] = vector4(1375.47, -779.21, 67.17, 349.94),
        [14] = vector4(1370.67, -782.23, 67.27, 347.36),
        [15] = vector4(1367.98, -782.21, 67.25, 354.37),
    },
    ['uptownconstruction'] = { -- Uptown Construction
        [1] = vector4(145.81, -372.78, 43.26, 72.5),
        [2] = vector4(134.82, -342.22, 43.78, 223.49),
        [3] = vector4(146.63, -379.92, 43.17, 338.69),
        [4] = vector4(120.36, -346.67, 42.95, 75.05),
        [5] = vector4(110.92, -382.25, 41.77, 161.94),
        [6] = vector4(97.27, -425.07, 37.55, 346.97),
        [7] = vector4(95.47, -410.12, 37.55, 70.83),
        [8] = vector4(110.42, -383.63, 41.76, 156.57),
        [9] = vector4(111.4, -360.52, 55.5, 78.88),
        [10] = vector4(101.95, -396.94, 41.27, 65.38),
        [11] = vector4(145.82, -375.85, 43.26, 76.87),
        [12] = vector4(118.57, -427.07, 41.08, 67.46),
        [13] = vector4(117.05, -450.02, 41.13, 69.11),
        [14] = vector4(113.8, -394.64, 41.22, 73.13),
        [15] = vector4(110.99, -374.95, 42.23, 116.12),
    },
    ['grove'] = { -- Grove Street
        [1] = vector4(57.92, -1931.52, 21.5, 222.97),
        [2] = vector4(42.59, -1940.35, 21.5, 320.55),
        [3] = vector4(59.62, -1932.51, 21.47, 137.0),
        [4] = vector4(57.32, -1944.59, 20.98, 224.52),
        [5] = vector4(59.29, -1954.22, 21.16, 315.06),
        [6] = vector4(68.4, -1950.7, 20.83, 314.88),
        [7] = vector4(65.58, -1962.86, 20.92, 46.3),
        [8] = vector4(78.87, -1955.78, 20.78, 318.84),
        [9] = vector4(76.47, -1975.64, 20.89, 319.71),
        [10] = vector4(97.22, -1966.64, 20.92, 161.44),
        [11] = vector4(87.01, -1980.21, 20.59, 319.79),
        [12] = vector4(76.64, -1937.54, 20.98, 316.14),
        [13] = vector4(102.73, -1982.04, 20.81, 22.62),
        [14] = vector4(69.72, -1956.86, 24.42, 318.32),
        [15] = vector4(89.37, -1955.49, 20.84, 320.92),
    },
    ['stab'] = { -- Stab City
        [1] = vector4(92.95, 3636.74, 39.73, 87.05),
        [2] = vector4(102.16, 3648.74, 39.75, 280.69),
        [3] = vector4(94.01, 3660.19, 39.76, 86.64),
        [4] = vector4(106.65, 3673.66, 39.75, 180.67),
        [5] = vector4(96.33, 3669.55, 39.75, 87.34),
        [6] = vector4(112.34, 3695.12, 39.75, 45.55),
        [7] = vector4(119.97, 3701.84, 39.75, 36.8),
        [8] = vector4(121.96, 3713.78, 39.75, 77.69),
        [9] = vector4(112.39, 3730.41, 39.73, 202.23),
        [10] = vector4(124.28, 3720.87, 39.75, 86.28),
        [11] = vector4(109.76, 3739.76, 39.74, 55.24),
        [12] = vector4(94.55, 3744.02, 39.72, 169.41),
        [13] = vector4(92.11, 3717.12, 39.73, 200.08),
        [14] = vector4(69.06, 3681.21, 39.73, 140.07),
        [15] = vector4(118.51, 3729.64, 39.75, 116.8),
    },
    ['salvage'] = { -- Reds Salvage Yard
        [1] = vector4(-189.16, 6316.72, 31.54, 170.32),
        [2] = vector4(-195.88, 6314.67, 31.6, 215.24),
        [3] = vector4(-210.69, 6298.07, 31.49, 228.03),
        [4] = vector4(-203.32, 6294.46, 31.49, 168.6),
        [5] = vector4(-204.98, 6287.89, 31.49, 137.06),
        [6] = vector4(-214.93, 6293.75, 31.49, 224.73),
        [7] = vector4(-205.01, 6284.44, 31.49, 224.75),
        [8] = vector4(-218.64, 6285.62, 31.49, 221.24),
        [9] = vector4(-221.51, 6275.94, 31.49, 318.28),
        [10] = vector4(-217.02, 6273.24, 31.49, 309.26),
        [11] = vector4(-211.05, 6269.58, 31.49, 229.52),
        [12] = vector4(-194.78, 6266.63, 31.49, 35.38),
        [13] = vector4(-184.99, 6306.64, 31.49, 156.19),
        [14] = vector4(-181.25, 6307.31, 31.49, 346.76),
        [15] = vector4(-200.82, 6309.27, 31.49, 224.52),
    },
    ['grandmas'] = { -- Grandmas House
        [1] = vector4(2428.61, 4970.3, 42.35, 224.19),
        [2] = vector4(2432.67, 4971.44, 42.35, 143.39),
        [3] = vector4(2434.99, 4969.16, 42.35, 132.46),
        [4] = vector4(2437.44, 4966.34, 42.35, 63.32),
        [5] = vector4(2431.14, 4962.88, 42.35, 325.98),
        [6] = vector4(2430.69, 4965.52, 42.35, 313.73),
        [7] = vector4(2431.84, 4967.75, 42.35, 139.83),
        [8] = vector4(2432.92, 4961.91, 46.82, 229.42),
        [9] = vector4(2437.04, 4958.77, 46.81, 43.05),
        [10] = vector4(2439.19, 4960.46, 46.81, 42.21),
        [11] = vector4(2435.59, 4972.77, 46.83, 222.96),
        [12] = vector4(2434.4, 4971.11, 46.81, 184.41),
        [13] = vector4(2428.13, 4966.43, 46.94, 272.88),
        [14] = vector4(2432.01, 4963.93, 46.94, 296.49),
        [15] = vector4(2434.93, 4963.51, 47.03, 10.69),
    },
}
------------------------------------------------
------------------------------------------------
------------------------------------------------
------------------------------------------------
------------------------------------------------
------------------------------------------------
------------------------------------------------
-- Blue team random spawns for each map
Config.BlueTeamSpawns = {
    ['Set_Dystopian_02'] = { -- Jurassic Park 
        [1] = vector4(2690.13, -3740.12, 140.14, 265.0),
        [2] = vector4(2675.94, -3721.23, 140.02, 265.0),
        [3] = vector4(2651.0, -3738.95, 140.08, 265.0),
        [4] = vector4(2636.73, -3750.42, 140.39, 265.0),
        [5] = vector4(2634.46, -3764.62, 139.99, 265.0),
        [6] = vector4(2625.45, -3797.1, 140.41, 265.0),
        [7] = vector4(2652.98, -3824.73, 140.44, 265.0),
        [8] = vector4(2653.39, -3831.81, 140.49, 265.0),
        [9] = vector4(2661.11, -3827.69, 140.47, 265.0),
        [10] = vector4(2668.33, -3841.02, 140.16, 265.0),
        [11] = vector4(2673.83, -3843.57, 140.01, 265.0),
        [12] = vector4(2661.02, -3873.47, 140.12, 265.0),
        [13] = vector4(2647.34, -3862.91, 140.17, 265.0),
        [14] = vector4(2651.45, -3865.05, 140.32, 265.0),
        [15] = vector4(2673.98, -3880.7, 139.81, 265.0),
    },
    ['Set_Dystopian_03'] = { -- Wrecking Ball
        [1] = vector4(2686.45, -3743.12, 139.54, 265.0),
        [2] = vector4(2681.05, -3744.84, 139.97, 265.0),
        [3] = vector4(2671.01, -3750.56, 141.6, 265.0),
        [4] = vector4(2651.89, -3745.67, 140.68, 265.0),
        [5] = vector4(2642.16, -3752.72, 140.55, 265.0),
        [6] = vector4(2639.21, -3762.56, 140.59, 265.0),
        [7] = vector4(2646.61, -3774.65, 141.61, 265.0),
        [8] = vector4(2633.03, -3793.3, 141.84, 265.0),
        [9] = vector4(2621.77, -3807.41, 140.72, 265.0),
        [10] = vector4(2639.21, -3830.32, 141.9, 265.0),
        [11] = vector4(2649.6, -3849.85, 141.04, 265.0),
        [12] = vector4(2663.08, -3850.49, 141.34, 265.0),
        [13] = vector4(2673.38, -3866.9, 142.49, 265.0),
        [14] = vector4(2689.45, -3869.69, 142.21, 265.0),
        [15] = vector4(2701.16, -3858.94, 141.91, 265.0),
    },
    ['Set_Dystopian_04'] = { -- scrap yard
        [1] = vector4(2643.75, -3775.7, 140.03, 265.0),
        [2] = vector4(2658.9, -3765.14, 140.03, 265.0),
        [3] = vector4(2660.56, -3774.84, 140.29, 265.0),
        [4] = vector4(2653.43, -3811.65, 140.03, 265.0),
        [5] = vector4(2652.72, -3820.97, 140.03, 265.0),
        [6] = vector4(2667.05, -3821.62, 140.03, 265.0),
        [7] = vector4(2691.71, -3825.45, 140.22, 265.0),
        [8] = vector4(2698.39, -3857.3, 140.03, 265.0),
        [9] = vector4(2649.84, -3835.46, 140.03, 265.0),
        [10] = vector4(2642.48, -3816.9, 140.03, 265.0),
        [11] = vector4(2696.42, -3765.8, 140.08, 265.0),
        [12] = vector4(2652.35, -3762.08, 140.03, 265.0),
        [13] = vector4(2668.67, -3821.03, 140.03, 265.0),
        [14] = vector4(2669.24, -3761.2, 140.03, 265.0),
        [15] = vector4(2689.04, -3734.18, 140.03, 265.0),
    },
    ['Set_Dystopian_07'] = {  --Ship Wreck
        [1] = vector4(2634.52, -3794.62, 139.91, 265.0),
        [2] = vector4(2633.9, -3811.94, 140.2, 265.0),
        [3] = vector4(2655.13, -3815.85, 139.86, 265.0),
        [4] = vector4(2681.45, -3811.73, 140.3, 265.0),
        [5] = vector4(2684.14, -3792.35, 139.99, 265.0),
        [6] = vector4(2700.69, -3752.11, 140.41, 265.0),
        [7] = vector4(2699.41, -3734.6, 140.41, 265.0),
        [8] = vector4(2672.65, -3742.47, 139.97, 265.0),
        [9] = vector4(2730.57, -3819.27, 136.06, 265.0),
        [10] = vector4(2694.61, -3831.46, 140.0, 265.0),
        [11] = vector4(2669.8, -3866.13, 140.05, 265.0),
        [12] = vector4(2640.92, -3848.87, 140.27, 265.0),
        [13] = vector4(2656.69, -3792.44, 139.93, 265.0),
        [14] = vector4(2666.04, -3810.54, 140.17, 265.0),
        [15] = vector4(2648.29, -3772.97, 140.05, 265.0),
    },
    ['Set_Dystopian_09'] = { --Industrial Whore House
        [1] = vector4(2643.17, -3773.94, 139.8, 265.0),
        [2] = vector4(2661.39, -3786.96, 139.41, 265.0),
        [3] = vector4(2663.06, -3812.46, 139.35, 265.0),
        [4] = vector4(2669.34, -3833.76, 139.15, 265.0),
        [5] = vector4(2661.82, -3759.76, 139.66, 265.0),
        [6] = vector4(2643.01, -3753.83, 139.8, 265.0),
        [7] = vector4(2679.93, -3848.91, 137.91, 265.0),
        [8] = vector4(2684.42, -3842.8, 132.88, 265.0),
        [9] = vector4(2705.98, -3837.62, 132.4, 265.0),
        [10] = vector4(2673.93, -3821.53, 132.92, 265.0),
        [11] = vector4(2679.27, -3779.43, 132.86, 265.0),
        [12] = vector4(2688.11, -3769.18, 132.75, 265.0),
        [13] = vector4(2698.44, -3763.86, 132.57, 265.0),
        [14] = vector4(2676.32, -3771.91, 132.92, 265.0),
        [15] = vector4(2712.75, -3840.45, 132.24, 265.0),
    },
    ['Set_Dystopian_10'] = { --Scrap Yard 2
        [1] = vector4(2635.08, -3792.84, 139.99, 265.0),
        [2] = vector4(2640.8, -3799.76, 139.98, 265.0),
        [3] = vector4(2661.03, -3843.46, 139.97, 265.0),
        [4] = vector4(2666.96, -3855.06, 139.98, 265.0),
        [5] = vector4(2677.66, -3854.96, 139.97, 265.0),
        [6] = vector4(2717.21, -3813.69, 139.93, 265.0),
        [7] = vector4(2710.74, -3820.96, 139.93, 265.0),
        [8] = vector4(2667.57, -3789.03, 140.03, 265.0),
        [9] = vector4(2659.34, -3752.39, 140.13, 265.0),
        [10] = vector4(2672.51, -3740.31, 139.99, 265.0),
        [11] = vector4(2695.41, -3734.21, 139.97, 265.0),
        [12] = vector4(2686.58, -3872.21, 139.99, 265.0),
        [13] = vector4(2652.44, -3797.45, 139.88, 265.0),
        [14] = vector4(2664.62, -3753.69, 140.97, 265.0),
        [15] = vector4(2670.46, -3801.58, 140.3, 265.0),
    },
    ['Set_Scifi_09'] = {  --Future Palace
        [1] = vector4(2639.41, -3794.99, 140.0, 265.0),
        [2] = vector4(2639.81, -3806.93, 140.0, 265.0),
        [3] = vector4(2699.95, -3789.21, 139.99, 265.0),
        [4] = vector4(2699.9, -3810.82, 139.99, 265.0),
        [5] = vector4(2706.52, -3861.39, 139.57, 265.0),
        [6] = vector4(2705.26, -3738.98, 139.71, 265.0),
        [7] = vector4(2704.43, -3751.83, 128.0, 265.0),
        [8] = vector4(2662.69, -3782.92, 128.11, 265.0),
        [9] = vector4(2665.88, -3814.82, 128.08, 265.0),
        [10] = vector4(2695.47, -3793.61, 128.0, 265.0),
        [11] = vector4(2699.91, -3810.42, 128.0, 265.0),
        [12] = vector4(2702.9, -3853.29, 128.0, 265.0),
        [13] = vector4(2663.27, -3738.9, 140.0, 265.0),
        [14] = vector4(2657.98, -3862.58, 140.0, 265.0),
        [15] = vector4(2688.22, -3804.3, 128.0, 265.0),
    },
    ['Set_Wasteland_01'] = { --Toy Soldier
        [1] = vector4(2635.06, -3793.31, 140.0, 265.0),
        [2] = vector4(2634.98, -3816.37, 140.0, 265.0),
        [3] = vector4(2646.69, -3820.14, 139.87, 265.0),
        [4] = vector4(2655.04, -3838.7, 139.84, 265.0),
        [5] = vector4(2647.23, -3840.94, 140.0, 265.0),
        [6] = vector4(2665.58, -3873.08, 140.0, 265.0),
        [7] = vector4(2685.64, -3845.17, 133.15, 265.0),
        [8] = vector4(2661.54, -3809.78, 135.16, 265.0),
        [9] = vector4(2659.35, -3792.57, 135.9, 265.0),
        [10] = vector4(2685.23, -3765.42, 130.43, 265.0),
        [11] = vector4(2657.49, -3747.73, 140.0, 265.0),
        [12] = vector4(2662.55, -3813.38, 134.99, 265.0),
        [13] = vector4(2651.16, -3835.04, 139.99, 265.0),
        [14] = vector4(2658.55, -3812.7, 136.54, 265.0),
        [15] = vector4(2692.76, -3844.98, 131.13, 265.0),
    },
    ['Set_Wasteland_03'] = {  -- Tire City
        [1] = vector4(2668.32, -3761.75, 138.97, 265.0),
        [2] = vector4(2675.84, -3778.77, 138.15, 265.0),
        [3] = vector4(2687.78, -3836.22, 138.04, 265.0),
        [4] = vector4(2695.99, -3853.76, 138.55, 265.0),
        [5] = vector4(2703.83, -3812.63, 138.37, 265.0),
        [6] = vector4(2700.47, -3799.52, 138.3, 265.0),
        [7] = vector4(2720.32, -3785.94, 137.8, 265.0),
        [8] = vector4(2705.29, -3753.33, 138.79, 265.0),
        [9] = vector4(2713.56, -3742.0, 139.81, 265.0),
        [10] = vector4(2714.58, -3824.11, 137.69, 265.0),
        [11] = vector4(2743.41, -3889.52, 140.13, 265.0),
        [12] = vector4(2751.54, -3712.64, 141.07, 265.0),
        [13] = vector4(2695.72, -3768.87, 137.55, 265.0),
        [14] = vector4(2672.47, -3850.81, 139.28, 265.0),
        [15] = vector4(2662.73, -3762.53, 139.2, 265.0),
    },
    ['Set_Wasteland_07'] = { --Walk in the Park
        [1] = vector4(2627.69, -3783.68, 140.06, 265.0),
        [2] = vector4(2626.21, -3799.63, 140.06, 265.0),
        [3] = vector4(2628.8, -3817.67, 140.07, 265.0),
        [4] = vector4(2654.02, -3846.24, 140.48, 265.0),
        [5] = vector4(2664.9, -3859.23, 140.08, 265.0),
        [6] = vector4(2646.01, -3785.59, 140.08, 265.0),
        [7] = vector4(2658.63, -3751.55, 140.08, 265.0),
        [8] = vector4(2679.57, -3726.08, 140.08, 265.0),
        [9] = vector4(2697.65, -3744.75, 133.75, 265.0),
        [10] = vector4(2724.66, -3732.2, 136.39, 265.0),
        [11] = vector4(2734.43, -3813.35, 128.09, 265.0),
        [12] = vector4(2729.48, -3844.39, 128.09, 265.0),
        [13] = vector4(2736.25, -3851.81, 128.09, 265.0),
        [14] = vector4(2670.23, -3850.32, 140.14, 265.0),
        [15] = vector4(2666.82, -3856.43, 140.08, 265.0),
    },
    ['Set_Wasteland_09'] = { --Tube Town
        [1] = vector4(2667.08, -3753.35, 139.88, 265.0),
        [2] = vector4(2684.91, -3731.5, 139.98, 265.0),
        [3] = vector4(2708.1, -3743.07, 139.74, 265.0),
        [4] = vector4(2710.37, -3735.44, 139.73, 265.0),
        [5] = vector4(2699.78, -3789.82, 138.85, 265.0),
        [6] = vector4(2671.54, -3784.21, 140.53, 265.0),
        [7] = vector4(2704.07, -3808.19, 138.97, 265.0),
        [8] = vector4(2698.26, -3810.97, 138.95, 265.0),
        [9] = vector4(2669.2, -3823.02, 139.78, 265.0),
        [10] = vector4(2712.14, -3842.94, 139.63, 265.0),
        [11] = vector4(2743.31, -3790.11, 143.2, 265.0),
        [12] = vector4(2698.99, -3745.28, 139.74, 265.0),
        [13] = vector4(2680.42, -3761.5, 139.75, 265.0),
        [14] = vector4(2677.98, -3747.18, 139.81, 265.0),
        [15] = vector4(2687.07, -3731.54, 139.9, 265.0),
    },
    ['gabz'] = { -- Gabz mlo (part of mba mlo)
        [1] = vector4(-313.13, -1987.18, 21.64, 50.0),
        [2] = vector4(-310.67, -1984.45, 21.64, 50.0),
        [3] = vector4(-309.27, -1982.8, 21.64, 50.0),
        [4] = vector4(-307.9, -1981.28, 21.64, 50.0),
        [5] = vector4(-306.38, -1979.68, 21.64, 50.0),
        [6] = vector4(-305.04, -1978.14, 21.64, 50.0),
        [7] = vector4(-303.65, -1976.46, 21.64, 50.0),
        [8] = vector4(-315.06, -1984.42, 21.64, 50.0),
        [9] = vector4(-313.37, -1982.43, 21.64, 50.0),
        [10] = vector4(-311.26, -1980.14, 21.64, 50.0),
        [11] = vector4(-309.23, -1977.64, 21.64, 50.0),
        [12] = vector4(-306.53, -1974.41, 21.64, 50.0),
        [13] = vector4(-314.18, -1976.82, 21.64, 50.0),
        [14] = vector4(-317.18, -1979.88, 21.64, 50.0),
        [15] = vector4(-312.06, -1973.23, 21.64, 50.0),
    },
    ['derby'] = { -- Gabz mlo (part of mba mlo)
        [1] = vector4(-288.37, -1971.63, 21.68, 50.0),
        [2] = vector4(-286.64, -1976.77, 21.63, 50.0),
        [3] = vector4(-287.09, -1983.03, 21.63, 50.0),
        [4] = vector4(-285.87, -1991.49, 21.63, 50.0),
        [5] = vector4(-289.44, -1996.38, 21.63, 50.0),
        [6] = vector4(-294.67, -2001.13, 21.63, 50.0),
        [7] = vector4(-300.65, -2002.5, 21.63, 50.0),
        [8] = vector4(-305.36, -2003.88, 21.63, 50.0),
        [9] = vector4(-312.98, -2003.5, 21.63, 50.0),
        [10] = vector4(-318.32, -2002.1, 21.64, 50.0),
        [11] = vector4(-310.89, -2010.77, 21.66, 50.0),
        [12] = vector4(-303.51, -2007.31, 21.63, 50.0),
        [13] = vector4(-306.83, -1987.15, 21.82, 50.0),
        [14] = vector4(-302.54, -1987.12, 22.29, 50.0),
        [15] = vector4(-295.8, -1982.2, 22.3, 50.0),
    },
    ['nuketown'] = { -- Nuketown Arena mlo
        [1] = vector4(-3249.14, 6965.64, 637.62, 356.96),
        [2] = vector4(-3250.83, 6965.78, 637.62, 357.44),
        [3] = vector4(-3252.64, 6965.92, 637.62, 350.57),
        [4] = vector4(-3254.37, 6965.98, 637.62, 357.5),
        [5] = vector4(-3256.03, 6966.1, 637.62, 357.02),
        [6] = vector4(-3258.06, 6966.32, 637.62, 358.51),
        [7] = vector4(-3260.16, 6966.51, 637.62, 357.76),
        [8] = vector4(-3249.29, 6969.22, 637.62, 356.35),
        [9] = vector4(-3251.09, 6969.34, 637.62, 352.32),
        [10] = vector4(-3252.93, 6969.39, 637.62, 350.84),
        [11] = vector4(-3254.29, 6969.72, 637.62, 347.96),
        [12] = vector4(-3255.96, 6969.77, 637.62, 352.19),
        [13] = vector4(-3257.14, 6970.0, 637.62, 353.9),
        [14] = vector4(-3258.47, 6970.04, 637.62, 357.4),
        [15] = vector4(-3259.8, 6969.85, 637.62, 351.51),
    },
    ['nuketownmirrorpark'] = { -- Nuketown Mirror Park mlo
        [1] = vector4(1394.59, -706.07, 67.26, 146.39),
        [2] = vector4(1396.27, -707.23, 67.35, 151.82),
        [3] = vector4(1398.15, -709.01, 67.27, 159.8),
        [4] = vector4(1399.82, -711.36, 66.66, 144.5),
        [5] = vector4(1392.77, -708.1, 67.37, 147.81),
        [6] = vector4(1394.76, -709.72, 67.3, 147.42),
        [7] = vector4(1396.48, -710.9, 67.19, 143.28),
        [8] = vector4(1397.92, -712.23, 66.95, 141.5),
        [9] = vector4(1396.56, -714.43, 67.27, 142.07),
        [10] = vector4(1394.84, -713.05, 67.27, 144.28),
        [11] = vector4(1393.06, -712.0, 67.27, 144.76),
        [12] = vector4(1410.16, -716.77, 66.48, 158.69),
        [13] = vector4(1407.45, -715.5, 66.39, 145.65),
        [14] = vector4(1404.95, -714.73, 66.2, 153.28),
        [15] = vector4(1408.48, -721.76, 66.99, 146.84),
    },
    ['uptownconstruction'] = { -- Uptown Construction
        [1] = vector4(-4.14, -454.13, 40.06, 264.39),
        [2] = vector4(-2.32, -446.47, 39.74, 255.14),
        [3] = vector4(5.43, -432.3, 39.74, 249.47),
        [4] = vector4(-2.28, -437.02, 39.7, 159.41),
        [5] = vector4(15.86, -407.26, 39.67, 156.25),
        [6] = vector4(31.57, -360.64, 39.33, 251.87),
        [7] = vector4(23.71, -368.37, 39.31, 232.62),
        [8] = vector4(22.18, -402.98, 45.56, 252.67),
        [9] = vector4(26.31, -350.47, 42.59, 341.26),
        [10] = vector4(38.28, -320.94, 44.34, 243.99),
        [11] = vector4(35.82, -363.43, 39.39, 253.09),
        [12] = vector4(1.91, -421.96, 39.44, 253.45),
        [13] = vector4(8.9, -440.54, 45.56, 251.17),
        [14] = vector4(30.56, -428.26, 39.92, 310.11),
        [15] = vector4(48.58, -346.94, 42.52, 160.99),
    },
    ['grove'] = { -- Grove Street
        [1] = vector4(107.03, -1877.67, 23.92, 145.12),
        [2] = vector4(102.98, -1882.82, 23.97, 322.22),
        [3] = vector4(88.53, -1894.78, 23.75, 225.81),
        [4] = vector4(112.56, -1883.81, 23.6, 333.72),
        [5] = vector4(119.74, -1885.87, 23.58, 243.53),
        [6] = vector4(116.86, -1901.96, 23.47, 335.55),
        [7] = vector4(126.09, -1895.32, 23.3, 332.32),
        [8] = vector4(132.51, -1905.79, 23.31, 157.9),
        [9] = vector4(137.57, -1896.1, 23.42, 61.81),
        [10] = vector4(160.53, -1901.34, 22.93, 64.14),
        [11] = vector4(139.55, -1887.77, 23.31, 156.65),
        [12] = vector4(147.37, -1903.47, 26.29, 154.28),
        [13] = vector4(140.78, -1918.61, 20.99, 62.5),
        [14] = vector4(113.8, -1908.02, 21.06, 155.86),
        [15] = vector4(164.33, -1926.14, 21.05, 131.04),
    },
    ['stab'] = { -- Stab City
        [1] = vector4(25.2, 3738.84, 39.68, 183.6),
        [2] = vector4(23.49, 3725.79, 39.65, 326.69),
        [3] = vector4(10.87, 3719.54, 39.59, 240.76),
        [4] = vector4(-4.45, 3729.98, 39.64, 192.16),
        [5] = vector4(10.63, 3708.75, 39.67, 252.99),
        [6] = vector4(-4.88, 3700.97, 39.41, 212.52),
        [7] = vector4(8.61, 3677.43, 39.78, 293.33),
        [8] = vector4(19.4, 3661.22, 39.81, 234.32),
        [9] = vector4(36.03, 3665.41, 39.73, 343.18),
        [10] = vector4(32.59, 3648.26, 39.76, 334.35),
        [11] = vector4(29.57, 3667.86, 40.44, 266.54),
        [12] = vector4(35.03, 3706.06, 39.59, 70.86),
        [13] = vector4(39.3, 3719.11, 39.58, 68.69),
        [14] = vector4(2.85, 3727.71, 44.12, 229.86),
        [15] = vector4(34.69, 3743.46, 39.68, 223.71),
    },
    ['salvage'] = { -- Reds Salvage Yard
        [1] = vector4(-150.48, 6274.29, 31.6, 136.48),
        [2] = vector4(-145.2, 6265.25, 31.54, 138.4),
        [3] = vector4(-160.24, 6253.23, 31.49, 328.83),
        [4] = vector4(-162.4, 6251.49, 31.49, 50.95),
        [5] = vector4(-166.12, 6247.15, 31.49, 41.98),
        [6] = vector4(-169.92, 6244.07, 31.49, 35.86),
        [7] = vector4(-179.65, 6236.51, 31.49, 317.56),
        [8] = vector4(-185.96, 6242.76, 31.49, 316.07),
        [9] = vector4(-177.31, 6244.11, 31.49, 22.71),
        [10] = vector4(-191.33, 6252.52, 31.49, 235.39),
        [11] = vector4(-185.97, 6257.52, 31.75, 223.65),
        [12] = vector4(-179.17, 6263.27, 31.51, 225.94),
        [13] = vector4(-152.15, 6280.35, 31.49, 44.34),
        [14] = vector4(-181.57, 6250.9, 31.49, 324.84),
        [15] = vector4(-166.17, 6282.92, 31.49, 216.28),
    },
    ['grandmas'] = { -- Grandmas House
        [1] = vector4(2449.14, 4988.28, 51.57, 225.65),
        [2] = vector4(2451.92, 4986.08, 51.57, 144.0),
        [3] = vector4(2454.55, 4982.91, 51.57, 54.07),
        [4] = vector4(2460.88, 4976.76, 51.57, 134.25),
        [5] = vector4(2459.56, 4978.06, 51.57, 135.59),
        [6] = vector4(2454.08, 4970.02, 51.57, 346.31),
        [7] = vector4(2456.83, 4977.79, 51.56, 92.47),
        [8] = vector4(2451.28, 4972.71, 51.56, 321.68),
        [9] = vector4(2451.97, 4978.34, 51.58, 135.37),
        [10] = vector4(2444.89, 4989.68, 51.76, 216.57),
        [11] = vector4(2442.93, 4987.68, 51.56, 227.83),
        [12] = vector4(2450.92, 4982.64, 51.56, 55.34),
        [13] = vector4(2441.83, 4986.48, 51.56, 184.22),
        [14] = vector4(2435.2, 4973.75, 51.57, 318.94),
        [15] = vector4(2436.36, 4971.22, 51.56, 249.44),
    },
}
------------------------------------------------
------------------------------------------------
------------------------------------------------
------------------------------------------------
------------------------------------------------
------------------------------------------------
------------------------------------------------
---------- [FFA SPAWNS FOR EVERY MAP] ----------
Config.FFASpawns = {
    ['Set_Dystopian_02'] = { -- Jurassic Park
        [1] = vector4(2913.96, -3878.37, 139.74, 38.02),
        [2] = vector4(2952.34, -3849.13, 140.14, 44.46),
        [3] = vector4(2972.88, -3817.92, 140.22, 84.12),
        [4] = vector4(2942.61, -3764.58, 140.95, 304.34),
        [5] = vector4(2935.75, -3724.43, 140.27, 152.64),
        [6] = vector4(2910.51, -3775.3, 143.81, 81.53),
        [7] = vector4(2865.3, -3808.31, 139.58, 129.28),
        [8] = vector4(2875.9, -3833.92, 140.1, 218.3),
        [9] = vector4(2846.86, -3874.15, 139.8, 33.57),
        [10] = vector4(2806.73, -3832.31, 140.06, 78.51),
        [11] = vector4(2800.72, -3747.62, 139.86, 96.48),
        [12] = vector4(2740.05, -3714.04, 139.65, 200.78),
        [13] = vector4(2751.17, -3780.94, 139.63, 273.14),
        [14] = vector4(2755.01, -3888.62, 139.77, 26.2),
        [15] = vector4(2729.22, -3826.88, 139.74, 277.35),
        [16] = vector4(2698.64, -3828.0, 141.82, 9.34),
        [17] = vector4(2707.82, -3774.48, 143.18, 291.97),
        [18] = vector4(2689.0, -3759.18, 141.7, 123.18),
        [19] = vector4(2635.64, -3751.25, 140.32, 198.9),
        [20] = vector4(2633.59, -3841.64, 140.52, 289.54),
        [21] = vector4(2650.3, -3865.47, 140.26, 30.11),
        [22] = vector4(2653.61, -3825.84, 140.41, 45.49),
        [23] = vector4(2713.04, -3822.67, 149.73, 309.98),
        [24] = vector4(2822.36, -3748.79, 152.96, 117.83),
        [25] = vector4(2823.73, -3810.74, 140.41, 93.96),

        [26] = vector4(2755.35, -3688.53, 140.0, 173.24),
        [27] = vector4(2834.65, -3690.85, 140.0, 168.75),
        [28] = vector4(2835.12, -3908.43, 140.0, 0.36),
        [29] = vector4(2765.31, -3913.86, 140.0, 7.89),
        [30] = vector4(2805.0, -3689.51, 140.0, 179.66),
    },
    ['Set_Dystopian_03'] = { -- Wrecking Ball
        [1] = vector4(2926.56, -3877.29, 140.49, 350.05),
        [2] = vector4(2964.76, -3823.78, 141.23, 46.45),
        [3] = vector4(2968.82, -3759.17, 141.54, 29.49),
        [4] = vector4(2927.91, -3736.09, 143.9, 255.16),
        [5] = vector4(2919.53, -3764.58, 142.32, 87.04),
        [6] = vector4(2897.64, -3796.62, 143.12, 89.51),
        [7] = vector4(2919.31, -3837.01, 142.29, 278.53),
        [8] = vector4(2915.67, -3865.87, 146.86, 52.2),
        [9] = vector4(2873.02, -3718.25, 140.73, 280.62),
        [10] = vector4(2847.22, -3797.53, 139.75, 271.08),
        [11] = vector4(2832.92, -3843.82, 139.26, 98.15),
        [12] = vector4(2833.81, -3888.52, 140.06, 97.98),
        [13] = vector4(2838.35, -3748.57, 139.49, 111.42),
        [14] = vector4(2768.1, -3719.9, 141.16, 256.82),
        [15] = vector4(2753.51, -3775.11, 144.18, 92.76),
        [16] = vector4(2756.34, -3817.51, 141.36, 88.16),
        [17] = vector4(2801.37, -3864.98, 138.51, 0.51),
        [18] = vector4(2714.95, -3838.07, 141.0, 192.68),
        [19] = vector4(2676.54, -3865.56, 142.63, 218.21),
        [20] = vector4(2661.92, -3836.09, 142.51, 269.71),
        [21] = vector4(2645.63, -3745.79, 140.53, 266.87),
        [22] = vector4(2621.44, -3803.5, 140.77, 189.07),
        [23] = vector4(2732.72, -3803.86, 154.05, 357.45),
        [24] = vector4(2887.62, -3803.95, 152.06, 6.18),
        [25] = vector4(2801.58, -3793.56, 166.27, 258.15),
        [26] = vector4(2919.8, -3845.12, 141.38, 92.39),
        [27] = vector4(2919.7, -3765.97, 142.35, 86.09),
        [28] = vector4(2835.12, -3908.43, 140.0, 0.36),
        [29] = vector4(2765.31, -3913.86, 140.0, 7.89),
        [30] = vector4(2805.0, -3689.51, 140.0, 179.66),
    },
    ['Set_Dystopian_04'] = { -- scrap yard
        [1] = vector4(2661.11, -3775.82, 140.25, 358.24),
        [2] = vector4(2697.51, -3766.25, 140.08, 57.12),
        [3] = vector4(2653.86, -3812.47, 140.03, 50.95),
        [4] = vector4(2698.24, -3829.93, 140.0, 284.39),
        [5] = vector4(2730.6, -3779.34, 140.23, 317.59),
        [6] = vector4(2720.9, -3734.73, 140.03, 102.13),
        [7] = vector4(2771.32, -3762.51, 140.09, 256.69),
        [8] = vector4(2802.96, -3734.72, 140.03, 260.1),
        [9] = vector4(2820.53, -3838.17, 139.76, 354.94),
        [10] = vector4(2829.7, -3763.27, 146.11, 268.42),
        [11] = vector4(2869.34, -3799.63, 146.28, 207.36),
        [12] = vector4(2890.74, -3782.44, 145.18, 18.49),
        [13] = vector4(2877.16, -3759.23, 143.95, 212.17),
        [14] = vector4(2891.14, -3818.17, 162.94, 19.43),
        [15] = vector4(2922.64, -3754.07, 140.12, 178.83),
        [16] = vector4(2934.95, -3833.27, 140.03, 170.74),
        [17] = vector4(2883.97, -3861.01, 140.03, 90.05),
        [18] = vector4(2829.86, -3753.25, 145.36, 297.81),
        [19] = vector4(2736.42, -3835.71, 145.51, 298.47),
        [20] = vector4(2720.94, -3756.93, 142.21, 241.32),
        [21] = vector4(2756.2, -3795.08, 140.03, 290.53),
        [22] = vector4(2908.96, -3726.01, 140.03, 173.44),
        [23] = vector4(2928.69, -3796.64, 140.44, 313.45),
        [24] = vector4(2624.44, -3800.07, 140.03, 265.94),
        [25] = vector4(2714.84, -3746.44, 162.74, 239.68),
        [26] = vector4(2755.35, -3688.53, 140.0, 173.24),
        [27] = vector4(2834.65, -3690.85, 140.0, 168.75),
        [28] = vector4(2835.12, -3908.43, 140.0, 0.36),
        [29] = vector4(2765.31, -3913.86, 140.0, 7.89),
        [30] = vector4(2805.0, -3689.51, 140.0, 179.66),
    },
    ['Set_Dystopian_07'] = {  --Ship Wreck
        [1] = vector4(2890.31, -3860.37, 140.5, 40.91),
        [2] = vector4(2940.33, -3854.38, 140.4, 220.32),
        [3] = vector4(2954.23, -3806.82, 140.14, 88.71),
        [4] = vector4(2939.91, -3759.78, 140.15, 118.18),
        [5] = vector4(2905.43, -3733.47, 140.68, 132.29),
        [6] = vector4(2833.94, -3793.49, 134.19, 339.96),
        [7] = vector4(2867.87, -3774.83, 134.07, 139.7),
        [8] = vector4(2848.15, -3847.94, 136.09, 43.15),
        [9] = vector4(2799.91, -3864.9, 140.17, 96.09),
        [10] = vector4(2776.07, -3851.56, 136.82, 334.36),
        [11] = vector4(2810.71, -3770.24, 135.45, 309.68),
        [12] = vector4(2799.87, -3727.39, 139.99, 0.07),
        [13] = vector4(2819.4, -3780.84, 153.25, 244.52),
        [14] = vector4(2766.94, -3737.2, 139.91, 92.72),
        [15] = vector4(2697.4, -3737.33, 140.44, 49.75),
        [16] = vector4(2640.55, -3811.07, 140.2, 175.62),
        [17] = vector4(2696.19, -3810.45, 139.16, 220.21),
        [18] = vector4(2655.86, -3858.62, 140.13, 290.73),
        [19] = vector4(2730.74, -3833.96, 143.5, 207.37),
        [20] = vector4(2767.26, -3830.85, 140.73, 194.73),
        [21] = vector4(2862.85, -3818.92, 133.65, 159.32),
        [22] = vector4(2767.8, -3773.46, 133.73, 287.12),
        [23] = vector4(2672.38, -3858.95, 140.22, 310.33),
        [24] = vector4(2730.36, -3716.76, 139.97, 218.76),
        [25] = vector4(2837.12, -3798.09, 136.75, 280.06),
        [26] = vector4(2755.35, -3688.53, 140.0, 173.24),
        [27] = vector4(2834.65, -3690.85, 140.0, 168.75),
        [28] = vector4(2835.12, -3908.43, 140.0, 0.36),
        [29] = vector4(2765.31, -3913.86, 140.0, 7.89),
        [30] = vector4(2805.0, -3689.51, 140.0, 179.66),
    },
    ['Set_Dystopian_09'] = { --Industrial Whore House
        [1] = vector4(2935.72, -3838.2, 139.49, 70.0),
        [2] = vector4(2665.94, -3802.63, 132.94, 271.12),
        [3] = vector4(2772.87, -3750.9, 128.97, 357.32),
        [4] = vector4(2842.71, -3866.09, 129.22, 334.06),
        [5] = vector4(2889.01, -3775.53, 132.27, 94.45),
        [6] = vector4(2902.44, -3837.55, 132.58, 93.36),
        [7] = vector4(2814.03, -3738.24, 137.04, 268.5),
        [8] = vector4(2813.19, -3835.41, 148.67, 20.77),
        [9] = vector4(2786.95, -3738.1, 137.04, 90.09),
        [10] = vector4(2889.2, -3840.47, 132.44, 70.0),
        [11] = vector4(2885.13, -3775.92, 132.03, 70.0),
        [12] = vector4(2910.59, -3754.15, 132.8, 70.0),
        [13] = vector4(2911.51, -3819.0, 132.72, 70.0),
        [14] = vector4(2938.39, -3842.79, 139.73, 70.0),
        [15] = vector4(2955.31, -3754.23, 139.8, 70.0),
        [16] = vector4(2704.72, -3762.95, 132.5, 359.24),
        [17] = vector4(2786.23, -3839.03, 128.97, 268.16),
        [18] = vector4(2841.28, -3783.06, 128.97, 217.56),
        [19] = vector4(2911.99, -3820.39, 132.72, 7.65),
        [20] = vector4(2815.1, -3862.53, 137.04, 270.54),
        [21] = vector4(2801.57, -3756.61, 139.85, 179.68),
        [22] = vector4(2728.05, -3799.91, 139.99, 95.25),
        [23] = vector4(2781.02, -3862.43, 137.05, 88.44),
        [24] = vector4(2705.98, -3837.62, 132.4, 265.0),
        [25] = vector4(2673.93, -3821.53, 132.92, 265.0),
        [26] = vector4(2679.27, -3779.43, 132.86, 265.0),
        [27] = vector4(2688.11, -3769.18, 132.75, 265.0),
        [28] = vector4(2698.44, -3763.86, 132.57, 265.0),
        [29] = vector4(2676.32, -3771.91, 132.92, 265.0),
        [30] = vector4(2712.75, -3840.45, 132.24, 265.0),
    },
    ['Set_Dystopian_10'] = { --Scrap Yard 2
        [1] = vector4(2912.95, -3858.01, 141.13, 160.62),
        [2] = vector4(2911.22, -3730.67, 139.98, 110.29),
        [3] = vector4(2954.87, -3809.21, 139.98, 231.02),
        [4] = vector4(2930.62, -3836.25, 139.84, 65.24),
        [5] = vector4(2937.9, -3810.53, 142.39, 59.91),
        [6] = vector4(2818.31, -3734.31, 140.66, 328.15),
        [7] = vector4(2823.48, -3822.7, 146.06, 199.61),
        [8] = vector4(2810.44, -3785.8, 146.72, 188.4),
        [9] = vector4(2774.65, -3731.79, 139.74, 331.22),
        [10] = vector4(2757.0, -3781.54, 141.54, 70.28),
        [11] = vector4(2786.14, -3858.45, 140.63, 279.16),
        [12] = vector4(2753.11, -3847.12, 140.08, 63.12),
        [13] = vector4(2722.01, -3810.69, 139.98, 88.57),
        [14] = vector4(2700.73, -3735.71, 139.97, 78.94),
        [15] = vector4(2651.26, -3797.18, 139.91, 242.96),
        [16] = vector4(2671.09, -3854.46, 139.98, 167.88),
        [17] = vector4(2688.74, -3795.73, 140.3, 273.57),
        [18] = vector4(2742.44, -3863.6, 140.65, 91.95),
        [19] = vector4(2819.36, -3836.95, 161.68, 26.19),
        [20] = vector4(2794.01, -3808.6, 145.47, 353.5),
        [21] = vector4(2857.98, -3794.02, 167.23, 111.32),
        [22] = vector4(2762.81, -3845.57, 140.06, 270.11),
        [23] = vector4(2875.86, -3784.9, 140.0, 91.87),
        [24] = vector4(2968.68, -3832.58, 140.0, 63.6),
        [25] = vector4(2754.6, -3856.41, 145.58, 284.21),
        [26] = vector4(2919.8, -3845.12, 141.38, 92.39),
        [27] = vector4(2919.7, -3765.97, 142.35, 86.09),
        [28] = vector4(2835.12, -3908.43, 140.0, 0.36),
        [29] = vector4(2765.31, -3913.86, 140.0, 7.89),
        [30] = vector4(2805.0, -3689.51, 140.0, 179.66),
    }, 
    ['Set_Scifi_09'] = {  --Future Palace
        [1] = vector4(2931.95, -3828.34, 139.98, 32.98),
        [2] = vector4(2927.47, -3799.9, 140.0, 89.13),
        [3] = vector4(2932.21, -3771.64, 139.98, 38.63),
        [4] = vector4(2890.72, -3749.78, 139.99, 170.62),
        [5] = vector4(2879.83, -3800.1, 139.99, 269.67),
        [6] = vector4(2900.9, -3800.0, 140.0, 90.49),
        [7] = vector4(2890.7, -3850.02, 139.99, 10.88),
        [8] = vector4(2854.06, -3799.88, 140.0, 91.25),
        [9] = vector4(2827.91, -3800.02, 140.0, 269.24),
        [10] = vector4(2822.63, -3854.84, 148.01, 90.07),
        [11] = vector4(2822.75, -3745.23, 148.01, 87.55),
        [12] = vector4(2777.74, -3854.91, 148.01, 267.94),
        [13] = vector4(2777.26, -3745.14, 148.01, 273.45),
        [14] = vector4(2799.97, -3799.88, 140.09, 90.85),
        [15] = vector4(2772.44, -3799.97, 140.0, 90.28),
        [16] = vector4(2745.61, -3799.76, 140.0, 269.34),
        [17] = vector4(2720.54, -3800.17, 139.99, 89.09),
        [18] = vector4(2699.16, -3799.91, 140.0, 90.8),
        [19] = vector4(2709.64, -3749.56, 139.99, 120.04),
        [20] = vector4(2709.58, -3850.42, 139.99, 59.63),
        [21] = vector4(2667.87, -3828.47, 139.98, 355.58),
        [22] = vector4(2667.8, -3771.52, 139.98, 179.13),
        [23] = vector4(2672.5, -3799.96, 140.0, 269.61),
        [24] = vector4(2918.9, -3800.4, 128.0, 89.74),
        [25] = vector4(2680.26, -3799.43, 128.0, 271.14),
        [26] = vector4(2757.77, -3737.49, 128.0, 184.06),
        [27] = vector4(2762.95, -3864.13, 128.0, 5.05),
        [28] = vector4(2843.61, -3863.92, 128.0, 0.84),
        [29] = vector4(2842.24, -3736.67, 128.0, 180.81),
        [30] = vector4(2879.82, -3800.48, 156.28, 90.95),
    },
    ['Set_Wasteland_01'] = { -- Toy Soldier
        [1] = vector4(2932.29, -3868.88, 140.0, 122.31),
        [2] = vector4(2951.09, -3831.9, 140.0, 337.21),
        [3] = vector4(2935.62, -3795.91, 133.82, 279.76),
        [4] = vector4(2969.01, -3792.27, 140.0, 97.24),
        [5] = vector4(2923.62, -3734.87, 140.0, 53.59),
        [6] = vector4(2892.57, -3760.31, 128.08, 104.43),
        [7] = vector4(2890.29, -3851.0, 129.71, 61.29),
        [8] = vector4(2888.73, -3789.59, 116.99, 102.09),
        [9] = vector4(2861.19, -3748.38, 148.37, 246.15),
        [10] = vector4(2859.55, -3852.69, 149.63, 293.16),
        [11] = vector4(2811.12, -3823.71, 120.16, 99.47),
        [12] = vector4(2847.45, -3803.34, 128.0, 283.54),
        [13] = vector4(2796.62, -3776.17, 120.15, 73.38),
        [14] = vector4(2746.93, -3800.77, 128.0, 95.08),
        [15] = vector4(2733.24, -3853.18, 148.64, 26.89),
        [16] = vector4(2736.95, -3747.05, 149.1, 114.76),
        [17] = vector4(2661.72, -3798.04, 134.84, 91.8),
        [18] = vector4(2706.94, -3838.57, 128.08, 289.19),
        [19] = vector4(2703.1, -3751.66, 130.06, 243.81),
        [20] = vector4(2690.64, -3717.57, 140.0, 115.88),
        [21] = vector4(2653.79, -3839.71, 139.98, 207.43),
        [22] = vector4(2798.92, -3852.7, 139.94, 179.05),
        [23] = vector4(2800.18, -3756.57, 139.94, 358.73),
        [24] = vector4(2793.51, -3793.12, 168.01, 92.64),
        [25] = vector4(2806.22, -3807.77, 168.03, 245.98),
        [26] = vector4(2920.0, -3847.5, 135.57, 26.07),
        [27] = vector4(2918.94, -3761.83, 132.5, 175.94),
        [28] = vector4(2835.12, -3908.43, 140.0, 0.36),
        [29] = vector4(2765.31, -3913.86, 140.0, 7.89),
        [30] = vector4(2805.0, -3689.51, 140.0, 179.66),
    }, 
    ['Set_Wasteland_03'] = {  -- Tire City
        [1] = vector4(2923.91, -3832.32, 138.4, 185.57),
        [2] = vector4(2902.94, -3744.27, 138.66, 41.06),
        [3] = vector4(2905.71, -3759.58, 137.96, 235.09),
        [4] = vector4(2924.83, -3815.81, 138.09, 351.69),
        [5] = vector4(2877.66, -3798.35, 142.12, 257.9),
        [6] = vector4(2861.56, -3860.33, 140.31, 63.56),
        [7] = vector4(2779.29, -3725.67, 140.15, 252.3),
        [8] = vector4(2803.78, -3759.95, 135.5, 106.69),
        [9] = vector4(2837.25, -3828.93, 134.67, 317.26),
        [10] = vector4(2798.07, -3800.11, 134.51, 275.23),
        [11] = vector4(2804.42, -3751.09, 155.79, 231.78),
        [12] = vector4(2723.6, -3801.35, 142.16, 97.02),
        [13] = vector4(2649.27, -3820.62, 139.48, 291.47),
        [14] = vector4(2686.68, -3742.59, 139.1, 241.46),
        [15] = vector4(2709.56, -3867.33, 139.16, 279.89),
        [16] = vector4(2794.34, -3843.85, 135.92, 290.81),
        [17] = vector4(2837.58, -3830.56, 134.85, 104.06),
        [18] = vector4(2805.79, -3757.2, 135.84, 108.06),
        [19] = vector4(2762.89, -3771.13, 134.7, 287.21),
        [20] = vector4(2910.29, -3792.91, 137.94, 105.12),
        [21] = vector4(2699.25, -3804.56, 139.11, 280.36),
        [22] = vector4(2871.2, -3833.05, 136.82, 29.0),
        [23] = vector4(2805.48, -3721.51, 140.52, 219.63),
        [24] = vector4(2797.27, -3881.27, 141.03, 6.0),
        [25] = vector4(2803.25, -3720.36, 140.72, 187.14),
        [26] = vector4(2755.35, -3688.53, 140.0, 173.24),
        [27] = vector4(2834.65, -3690.85, 140.0, 168.75),
        [28] = vector4(2835.12, -3908.43, 140.0, 0.36),
        [29] = vector4(2765.31, -3913.86, 140.0, 7.89),
        [30] = vector4(2805.0, -3689.51, 140.0, 179.66),
    },
    ['Set_Wasteland_07'] = { --Walk in the Park
        [1] = vector4(2920.51, -3875.78, 140.08, 21.18),
        [2] = vector4(2974.81, -3799.49, 140.05, 88.31),
        [3] = vector4(2911.01, -3750.47, 142.81, 158.47),
        [4] = vector4(2912.27, -3814.89, 139.89, 218.52),
        [5] = vector4(2888.81, -3807.23, 128.17, 45.57),
        [6] = vector4(2843.55, -3737.16, 131.04, 182.83),
        [7] = vector4(2842.06, -3861.92, 130.61, 1.12),
        [8] = vector4(2852.12, -3772.06, 128.09, 76.79),
        [9] = vector4(2795.13, -3832.76, 128.09, 83.4),
        [10] = vector4(2794.71, -3765.43, 128.09, 96.77),
        [11] = vector4(2742.04, -3825.46, 128.09, 257.7),
        [12] = vector4(2825.48, -3864.86, 139.94, 244.47),
        [13] = vector4(2798.94, -3800.15, 141.55, 2.97),
        [14] = vector4(2802.74, -3723.0, 140.91, 172.92),
        [15] = vector4(2748.33, -3722.59, 140.08, 152.13),
        [16] = vector4(2684.35, -3783.44, 139.89, 273.74),
        [17] = vector4(2711.22, -3791.75, 128.18, 253.99),
        [18] = vector4(2750.14, -3885.48, 143.53, 82.47),
        [19] = vector4(2672.64, -3858.36, 140.08, 250.97),
        [20] = vector4(2624.68, -3799.61, 140.04, 270.71),
        [21] = vector4(2647.78, -3740.17, 140.04, 240.83),
        [22] = vector4(2830.03, -3868.5, 149.23, 61.53),
        [23] = vector4(2694.63, -3724.75, 140.08, 206.36),
        [24] = vector4(2688.34, -3849.98, 142.66, 319.56),
        [25] = vector4(2954.63, -3746.9, 140.68, 141.23),
        [26] = vector4(2755.35, -3688.53, 140.0, 173.24),
        [27] = vector4(2834.65, -3690.85, 140.0, 168.75),
        [28] = vector4(2835.12, -3908.43, 140.0, 0.36),
        [29] = vector4(2765.31, -3913.86, 140.0, 7.89),
        [30] = vector4(2805.0, -3689.51, 140.0, 179.66),
    },
    ['Set_Wasteland_09'] = { --Tube Town
        [1] = vector4(2887.92, -3856.13, 139.74, 145.1),
        [2] = vector4(2854.05, -3876.5, 139.58, 66.18),
        [3] = vector4(2896.2, -3846.65, 139.73, 288.78),
        [4] = vector4(2856.06, -3835.13, 140.29, 15.62),
        [5] = vector4(2896.75, -3796.83, 138.48, 92.51),
        [6] = vector4(2873.79, -3837.59, 139.68, 347.43),
        [7] = vector4(2929.77, -3744.54, 140.06, 125.96),
        [8] = vector4(2832.22, -3767.22, 148.68, 155.45),
        [9] = vector4(2801.5, -3847.55, 150.46, 5.93),
        [10] = vector4(2765.11, -3772.11, 143.01, 32.85),
        [11] = vector4(2801.38, -3766.89, 141.25, 1.7),
        [12] = vector4(2704.98, -3752.28, 139.73, 102.29),
        [13] = vector4(2679.79, -3747.34, 139.79, 115.28),
        [14] = vector4(2694.76, -3789.71, 139.26, 287.97),
        [15] = vector4(2663.23, -3800.78, 142.98, 92.7),
        [16] = vector4(2699.52, -3812.54, 138.84, 256.25),
        [17] = vector4(2719.7, -3843.71, 139.61, 345.85),
        [18] = vector4(2799.24, -3847.59, 139.66, 263.7),
        [19] = vector4(2854.57, -3878.68, 139.43, 41.28),
        [20] = vector4(2848.21, -3733.13, 149.91, 153.71),
        [21] = vector4(2704.76, -3801.79, 138.22, 269.52),
        [22] = vector4(2827.86, -3801.9, 148.86, 87.16),
        [23] = vector4(2757.98, -3802.18, 146.24, 271.22),
        [24] = vector4(2741.1, -3775.84, 142.03, 331.54),
        [25] = vector4(2859.82, -3848.5, 145.0, 194.53),
        [26] = vector4(2755.35, -3688.53, 140.0, 173.24),
        [27] = vector4(2834.65, -3690.85, 140.0, 168.75),
        [28] = vector4(2835.12, -3908.43, 140.0, 0.36),
        [29] = vector4(2765.31, -3913.86, 140.0, 7.89),
        [30] = vector4(2805.0, -3689.51, 140.0, 179.66),
    },
    ['gabz'] = { -- Gabz mlo (part of mba mlo)
        [1] = vector4(-334.85, -1950.4, 21.64, 225.0),
        [2] = vector4(-336.62, -1952.42, 21.64, 225.0),
        [3] = vector4(-338.26, -1954.53, 21.64, 225.0),
        [4] = vector4(-339.42, -1955.86, 21.64, 225.0),
        [5] = vector4(-340.69, -1957.24, 21.64, 225.0),
        [6] = vector4(-342.04, -1958.72, 21.64, 225.0),
        [7] = vector4(-343.64, -1960.64, 21.64, 225.0),
        [8] = vector4(-333.58, -1952.91, 21.64, 225.0),
        [9] = vector4(-335.11, -1954.88, 21.64, 225.0),
        [10] = vector4(-337.0, -1957.07, 21.64, 225.0),
        [11] = vector4(-338.65, -1958.89, 21.64, 225.0),
        [12] = vector4(-340.32, -1960.74, 21.64, 225.0),
        [13] = vector4(-334.45, -1959.91, 21.64, 225.0),
        [14] = vector4(-331.29, -1957.27, 21.64, 225.0),
        [15] = vector4(-337.05, -1962.75, 21.64, 225.0),
        [16] = vector4(-313.13, -1987.18, 21.64, 50.0),
        [17] = vector4(-310.67, -1984.45, 21.64, 50.0),
        [18] = vector4(-309.27, -1982.8, 21.64, 50.0),
        [19] = vector4(-307.9, -1981.28, 21.64, 50.0),
        [20] = vector4(-306.38, -1979.68, 21.64, 50.0),
        [21] = vector4(-305.04, -1978.14, 21.64, 50.0),
        [22] = vector4(-303.65, -1976.46, 21.64, 50.0),
        [23] = vector4(-315.06, -1984.42, 21.64, 50.0),
        [24] = vector4(-313.37, -1982.43, 21.64, 50.0),
        [25] = vector4(-311.26, -1980.14, 21.64, 50.0),
        [26] = vector4(-309.23, -1977.64, 21.64, 50.0),
        [27] = vector4(-306.53, -1974.41, 21.64, 50.0),
        [28] = vector4(-314.18, -1976.82, 21.64, 50.0),
        [29] = vector4(-317.18, -1979.88, 21.64, 50.0),
        [30] = vector4(-312.06, -1973.23, 21.64, 50.0),
    },
    ['derby'] = { -- Gabz mlo (part of mba mlo)
        [1] = vector4(-343.06, -1928.4, 21.63, 225.0),
        [2] = vector4(-345.72, -1931.25, 21.63, 225.0),
        [3] = vector4(-349.83, -1935.56, 21.63, 225.0),
        [4] = vector4(-352.01, -1938.14, 21.63, 225.0),
        [5] = vector4(-354.26, -1940.91, 21.63, 225.0),
        [6] = vector4(-356.22, -1943.32, 21.63, 225.0),
        [7] = vector4(-358.97, -1947.0, 21.63, 225.0),
        [8] = vector4(-361.35, -1950.34, 21.63, 225.0),
        [9] = vector4(-363.26, -1953.51, 21.63, 225.0),
        [10] = vector4(-364.6, -1955.22, 21.63, 225.0),
        [11] = vector4(-351.01, -1942.8, 21.63, 225.0),
        [12] = vector4(-354.81, -1946.7, 21.63, 225.0),
        [13] = vector4(-352.37, -1954.79, 21.7, 225.0),
        [14] = vector4(-346.82, -1949.25, 22.03, 225.0),
        [15] = vector4(-336.08, -1939.85, 22.34, 225.0),
        [16] = vector4(-288.37, -1971.63, 21.68, 50.0),
        [17] = vector4(-286.64, -1976.77, 21.63, 50.0),
        [18] = vector4(-287.09, -1983.03, 21.63, 50.0),
        [19] = vector4(-285.87, -1991.49, 21.63, 50.0),
        [20] = vector4(-289.44, -1996.38, 21.63, 50.0),
        [21] = vector4(-294.67, -2001.13, 21.63, 50.0),
        [22] = vector4(-300.65, -2002.5, 21.63, 50.0),
        [23] = vector4(-305.36, -2003.88, 21.63, 50.0),
        [24] = vector4(-312.98, -2003.5, 21.63, 50.0),
        [25] = vector4(-318.32, -2002.1, 21.64, 50.0),
        [26] = vector4(-310.89, -2010.77, 21.66, 50.0),
        [27] = vector4(-303.51, -2007.31, 21.63, 50.0),
        [28] = vector4(-306.83, -1987.15, 21.82, 50.0),
        [29] = vector4(-302.54, -1987.12, 22.29, 50.0),
        [30] = vector4(-295.8, -1982.2, 22.3, 50.0),
    },
    ['nuketown'] = { -- Nuketown Arena mlo
        [1] = vector4(-3238.04, 6991.96, 637.62, 49.38),
        [2] = vector4(-3231.91, 7014.06, 637.62, 108.22),
        [3] = vector4(-3219.89, 7037.71, 637.62, 69.52),
        [4] = vector4(-3262.26, 6962.65, 637.62, 5.46),
        [5] = vector4(-3255.19, 6989.89, 637.72, 35.32),
        [6] = vector4(-3239.38, 7025.55, 637.65, 130.07),
        [7] = vector4(-3240.59, 7055.84, 637.62, 154.34),
        [8] = vector4(-3244.58, 7030.76, 637.67, 135.4),
        [9] = vector4(-3268.42, 7000.31, 637.62, 325.6),
        [10] = vector4(-3256.49, 6991.35, 641.31, 24.72),
        [11] = vector4(-3241.64, 7023.66, 641.26, 130.69),
        [12] = vector4(-3251.37, 6984.11, 637.69, 337.6),
        [13] = vector4(-3261.86, 6986.01, 637.69, 238.75),
        [14] = vector4(-3235.32, 7026.62, 637.65, 13.3),
        [15] = vector4(-3249.57, 7044.64, 637.62, 159.0),
        [16] = vector4(-3232.02, 7049.09, 637.62, 150.45),
        [17] = vector4(-3239.62, 7025.36, 641.26, 2.59),
        [18] = vector4(-3255.84, 6988.6, 641.31, 143.15),
        [19] = vector4(-3238.29, 7035.17, 641.26, 279.33),
        [20] = vector4(-3268.64, 7019.37, 637.62, 254.5),
        [21] = vector4(-3271.19, 7008.83, 637.62, 256.88),
        [22] = vector4(-3262.76, 6984.79, 641.31, 1.58),
        [23] = vector4(-3234.15, 7028.03, 641.26, 147.67),
        [24] = vector4(-3242.46, 7030.34, 637.65, 288.27),
        [25] = vector4(-3241.01, 6979.19, 637.62, 33.8),
        [26] = vector4(-3241.43, 6967.18, 637.62, 57.91),
        [27] = vector4(-3233.22, 7025.25, 637.62, 151.54),
        [28] = vector4(-3267.99, 7000.89, 637.62, 320.22),
        [29] = vector4(-3241.1, 6975.8, 637.62, 89.4),
        [30] = vector4(-3268.45, 6973.59, 637.62, 265.71),
    }, 
    ['nuketownmirrorpark'] = { -- Nuketown Mirror Park mlo
        [1] = vector4(1391.31, -761.11, 66.78, 42.74),
        [2] = vector4(1399.79, -742.22, 66.82, 89.57),
        [3] = vector4(1413.6, -719.1, 66.97, 66.97),
        [4] = vector4(1368.91, -787.84, 68.84, 355.84),
        [5] = vector4(1394.57, -699.19, 67.19, 151.39),
        [6] = vector4(1388.92, -723.02, 67.28, 104.94),
        [7] = vector4(1378.56, -769.13, 67.17, 326.33),
        [8] = vector4(1391.19, -724.3, 67.28, 275.11),
        [9] = vector4(1368.21, -766.16, 67.19, 231.51),
        [10] = vector4(1388.67, -783.01, 67.43, 86.48),
        [11] = vector4(1406.89, -709.58, 66.76, 151.17),
        [12] = vector4(1374.73, -764.41, 70.78, 140.38),
        [13] = vector4(1393.61, -729.69, 70.87, 355.51),
        [14] = vector4(1389.2, -778.79, 67.21, 86.75),
        [15] = vector4(1402.44, -707.5, 67.38, 143.97),
        [16] = vector4(1363.75, -749.4, 67.24, 298.09),
        [17] = vector4(1400.09, -730.41, 67.29, 148.58),
        [18] = vector4(1357.38, -727.53, 67.22, 263.14),
        [19] = vector4(1354.52, -739.97, 67.05, 256.26),
        [20] = vector4(1391.02, -730.56, 70.87, 123.05),
        [21] = vector4(1370.7, -761.11, 70.77, 332.04),
        [22] = vector4(1393.57, -729.59, 67.27, 117.05),
        [23] = vector4(1375.1, -763.55, 67.2, 34.39),
        [24] = vector4(1401.34, -735.61, 67.43, 97.5),
        [25] = vector4(1384.83, -710.87, 66.58, 157.52),
        [26] = vector4(1374.62, -773.22, 70.71, 114.11),
        [27] = vector4(1395.7, -719.18, 70.81, 270.12),
        [28] = vector4(1399.48, -727.3, 70.87, 142.12),
        [29] = vector4(1367.39, -768.1, 70.78, 353.48),
        [30] = vector4(1399.85, -750.11, 67.55, 85.96),
    },
    ['uptownconstruction'] = { -- Uptown Construction
        [1] = vector4(134.94, -342.4, 43.77, 230.37),
        [2] = vector4(-7.01, -453.07, 40.04, 258.67),
        [3] = vector4(115.8, -459.85, 41.13, 98.83),
        [4] = vector4(140.8, -387.65, 43.25, 69.46),
        [5] = vector4(39.28, -321.59, 44.31, 246.68),
        [6] = vector4(22.68, -358.77, 39.31, 252.03),
        [7] = vector4(71.32, -462.44, 39.92, 64.96),
        [8] = vector4(98.43, -412.91, 37.55, 68.96),
        [9] = vector4(107.8, -380.97, 41.77, 27.71),
        [10] = vector4(120.52, -337.27, 43.36, 161.32),
        [11] = vector4(80.29, -326.55, 44.29, 159.49),
        [12] = vector4(67.13, -351.38, 42.44, 344.25),
        [13] = vector4(6.22, -432.98, 39.74, 341.71),
        [14] = vector4(0.87, -422.1, 39.42, 256.02),
        [15] = vector4(112.99, -364.53, 55.5, 68.46),
        [16] = vector4(20.3, -403.2, 45.56, 249.35),
        [17] = vector4(53.07, -370.07, 39.92, 338.26),
        [18] = vector4(29.86, -408.67, 39.92, 250.06),
        [19] = vector4(40.94, -463.41, 39.92, 349.08),
        [20] = vector4(147.22, -373.07, 43.26, 69.78),
        [21] = vector4(24.13, -355.12, 39.31, 190.18),
        [22] = vector4(53.52, -343.96, 55.51, 252.44),
        [23] = vector4(42.42, -421.98, 45.56, 252.73),
        [24] = vector4(56.67, -400.19, 39.92, 206.77),
        [25] = vector4(60.52, -351.47, 42.63, 215.91),
        [26] = vector4(90.75, -345.81, 42.26, 245.17),
        [27] = vector4(42.46, -382.23, 39.92, 249.4),
        [28] = vector4(37.66, -387.22, 73.94, 249.23),
        [29] = vector4(50.92, -393.58, 73.94, 252.08),
        [30] = vector4(96.4, -372.6, 67.31, 157.98),
    },
    ['grove'] = { -- Grove Street
        [1] = vector4(163.26, -1908.99, 21.55, 182.39),
        [2] = vector4(43.18, -1908.31, 21.74, 234.28),
        [3] = vector4(170.47, -1940.96, 19.85, 52.67),
        [4] = vector4(41.85, -1937.73, 21.55, 316.39),
        [5] = vector4(159.16, -1930.25, 19.79, 138.44),
        [6] = vector4(88.7, -1895.28, 23.76, 228.07),
        [7] = vector4(58.09, -1931.12, 21.53, 140.42),
        [8] = vector4(62.5, -1950.0, 21.03, 41.88),
        [9] = vector4(76.23, -1942.55, 20.86, 314.32),
        [10] = vector4(112.25, -1960.34, 20.95, 29.75),
        [11] = vector4(124.28, -1929.84, 21.38, 35.67),
        [12] = vector4(126.96, -1910.88, 20.93, 62.85),
        [13] = vector4(98.74, -1904.36, 21.08, 147.86),
        [14] = vector4(106.89, -1877.18, 23.93, 143.89),
        [15] = vector4(127.57, -1893.74, 23.36, 245.41),
        [16] = vector4(132.68, -1907.3, 23.2, 155.83),
        [17] = vector4(125.46, -1951.6, 20.69, 54.03),
        [18] = vector4(96.98, -1980.82, 20.56, 324.72),
        [19] = vector4(112.89, -1978.4, 20.96, 21.68),
        [20] = vector4(124.04, -1965.83, 20.51, 30.64),
        [21] = vector4(83.38, -1906.38, 21.27, 141.58),
        [22] = vector4(117.39, -1950.16, 20.75, 50.39),
        [23] = vector4(149.84, -1959.29, 22.33, 46.78),
        [24] = vector4(107.67, -1903.83, 24.65, 147.17),
        [25] = vector4(63.16, -1944.35, 24.68, 302.51),
        [26] = vector4(69.5, -1970.57, 20.81, 231.1),
        [27] = vector4(116.4, -1902.28, 23.48, 333.83),
        [28] = vector4(65.47, -1881.47, 22.23, 213.49),
        [29] = vector4(47.37, -1932.06, 24.57, 315.4),
        [30] = vector4(97.71, -1965.43, 20.92, 165.32),
    },
    ['stab'] = { -- Stab City
        [1] = vector4(93.67, 3636.64, 39.75, 84.88),
        [2] = vector4(50.59, 3630.23, 39.63, 16.18),
        [3] = vector4(102.54, 3653.88, 39.75, 356.43),
        [4] = vector4(10.75, 3734.32, 39.99, 247.45),
        [5] = vector4(15.32, 3723.91, 39.7, 315.67),
        [6] = vector4(49.04, 3639.8, 39.79, 307.58),
        [7] = vector4(82.79, 3711.73, 41.08, 66.08),
        [8] = vector4(120.08, 3701.37, 39.75, 6.38),
        [9] = vector4(92.35, 3751.9, 40.77, 158.75),
        [10] = vector4(69.27, 3749.19, 39.72, 188.91),
        [11] = vector4(2.38, 3715.52, 39.59, 280.53),
        [12] = vector4(66.68, 3685.14, 39.83, 59.73),
        [13] = vector4(28.93, 3666.24, 40.44, 246.0),
        [14] = vector4(25.5, 3639.41, 39.83, 301.26),
        [15] = vector4(121.19, 3714.25, 39.75, 87.54),
        [16] = vector4(84.54, 3709.2, 39.75, 241.82),
        [17] = vector4(86.14, 3722.47, 39.74, 144.7),
        [18] = vector4(3.73, 3728.39, 44.15, 227.01),
        [19] = vector4(8.51, 3705.91, 39.62, 17.0),
        [20] = vector4(28.73, 3673.92, 39.73, 29.03),
        [21] = vector4(53.72, 3686.58, 39.77, 149.12),
        [22] = vector4(57.06, 3690.76, 39.92, 324.35),
        [23] = vector4(54.0, 3677.21, 39.74, 323.46),
        [24] = vector4(74.22, 3642.61, 39.54, 15.32),
        [25] = vector4(116.88, 3742.47, 39.62, 124.26),
        [26] = vector4(69.45, 3731.71, 39.61, 140.96),
        [27] = vector4(44.56, 3739.63, 39.63, 210.34),
        [28] = vector4(40.64, 3656.38, 39.81, 335.9),
        [29] = vector4(59.25, 3670.01, 39.73, 169.21),
        [30] = vector4(60.72, 3724.78, 39.73, 178.14),
    },
    ['salvage'] = { -- Reds Salvage Yard
        [1] = vector4(-188.93, 6316.7, 31.5, 162.98),
        [2] = vector4(-175.03, 6232.81, 31.46, 315.16),
        [3] = vector4(-221.17, 6276.09, 31.49, 318.58),
        [4] = vector4(-144.99, 6265.27, 31.54, 126.35),
        [5] = vector4(-212.44, 6297.76, 31.49, 230.76),
        [6] = vector4(-179.24, 6304.75, 31.49, 159.14),
        [7] = vector4(-168.51, 6248.21, 31.49, 40.0),
        [8] = vector4(-199.88, 6254.38, 31.49, 223.06),
        [9] = vector4(-199.65, 6261.75, 31.49, 44.14),
        [10] = vector4(-188.61, 6267.96, 31.49, 307.15),
        [11] = vector4(-151.04, 6278.81, 31.49, 220.75),
        [12] = vector4(-149.81, 6275.02, 31.54, 135.75),
        [13] = vector4(-158.37, 6290.31, 31.49, 137.22),
        [14] = vector4(-162.78, 6293.73, 31.49, 48.56),
        [15] = vector4(-182.26, 6305.13, 33.22, 159.28),
        [16] = vector4(-204.89, 6287.51, 31.49, 39.28),
        [17] = vector4(-195.68, 6265.3, 31.49, 50.96),
        [18] = vector4(-179.45, 6268.59, 31.72, 312.7),
        [19] = vector4(-180.22, 6262.78, 31.51, 230.26),
        [20] = vector4(-164.34, 6252.91, 31.49, 48.5),
        [21] = vector4(-186.23, 6257.75, 31.75, 228.98),
        [22] = vector4(-193.9, 6250.44, 31.49, 224.61),
        [23] = vector4(-167.16, 6245.05, 38.34, 52.92),
        [24] = vector4(-184.61, 6266.22, 37.08, 35.99),
        [25] = vector4(-199.89, 6292.04, 31.5, 187.71),
        [26] = vector4(-217.21, 6282.98, 31.49, 224.66),
        [27] = vector4(-188.72, 6317.17, 31.49, 153.3),
        [28] = vector4(-201.93, 6308.07, 31.49, 221.08),
        [29] = vector4(-190.15, 6253.12, 31.49, 215.14),
        [30] = vector4(-201.8, 6287.31, 31.5, 232.04),
    },
    ['grandmas'] = { -- Grandmas House
        [1] = vector4(2449.18, 4988.15, 51.57, 214.15),
        [2] = vector4(2428.4, 4970.14, 42.35, 229.9),
        [3] = vector4(2461.48, 4976.97, 46.57, 212.73),
        [4] = vector4(2427.88, 4966.41, 46.94, 278.76),
        [5] = vector4(2455.68, 4983.04, 46.81, 225.23),
        [6] = vector4(2441.08, 4985.97, 46.81, 204.61),
        [7] = vector4(2455.49, 4993.48, 46.81, 139.13),
        [8] = vector4(2454.07, 4984.27, 46.81, 53.24),
        [9] = vector4(2457.24, 4981.06, 46.81, 154.64),
        [10] = vector4(2440.14, 4977.66, 46.81, 222.27),
        [11] = vector4(2433.12, 4961.88, 46.82, 218.9),
        [12] = vector4(2437.38, 4966.72, 42.35, 85.76),
        [13] = vector4(2435.33, 4972.83, 46.83, 227.03),
        [14] = vector4(2444.63, 4988.37, 46.81, 199.43),
        [15] = vector4(2450.86, 4977.31, 46.81, 132.95),
        [16] = vector4(2436.53, 4971.54, 51.56, 269.51),
        [17] = vector4(2449.36, 4988.05, 51.57, 216.42),
        [18] = vector4(2441.79, 4986.27, 51.56, 185.05),
        [19] = vector4(2453.63, 4970.57, 51.57, 317.19),
        [20] = vector4(2453.84, 4980.8, 51.56, 218.81),
        [21] = vector4(2441.3, 4976.22, 51.59, 269.24),
        [22] = vector4(2435.07, 4973.59, 51.57, 314.77),
        [23] = vector4(2458.19, 4976.42, 51.57, 136.77),
        [24] = vector4(2451.08, 4972.56, 51.56, 323.16),
        [25] = vector4(2451.96, 4976.2, 51.56, 318.37),
        [26] = vector4(2449.63, 4981.02, 46.81, 31.04),
        [27] = vector4(2431.04, 4963.0, 42.35, 316.58),
        [28] = vector4(2434.68, 4970.3, 46.81, 196.86),
        [29] = vector4(2447.73, 4973.12, 51.56, 106.92),
        [30] = vector4(2449.91, 4975.02, 51.56, 41.27),
    },
}