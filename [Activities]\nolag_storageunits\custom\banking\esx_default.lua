local sharedConfig = require 'config.shared'
if sharedConfig.banking ~= 'default' or GetResourceState('es_extended') ~= 'started' then return end


---@param society string The society that the money will be added to
---@param price number The amount of money that will be added
---@param playerIdentifier string The identifier of the player that executes the action
function AddMoneyToSociety(society, price, playerIdentifier)
    local success = false
    TriggerEvent('esx_addonaccount:getSharedAccount', 'society_' .. society, function(account)
        if account then
            account.addMoney(price)
            success = true
        else
            lib.print.error(('Couldn\'t find account named %s in esx_addonaccount'):format('society_' .. society))
        end
    end)

    Wait(1000)

    return success
end
