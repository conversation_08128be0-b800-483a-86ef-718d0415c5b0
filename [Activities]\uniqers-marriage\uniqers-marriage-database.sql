CREATE TABLE `uniqers_marriage` (
  `id` int(11) NOT NULL,
  `owner` varchar(255) DEFAULT NULL,
  `married` varchar(255) DEFAULT NULL,
  `date` varchar(50) DEFAULT NULL,
  `reward_claimed` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8mb4_general_ci'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;


CREATE TABLE `uniqers_marriage_engaged` (
  `id` int(11) NOT NULL,
  `owner` varchar(255) DEFAULT NULL,
  `married` varchar(255) DEFAULT NULL,
  `date` varchar(50) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;


CREATE TABLE `uniqers_marriage_prepare` (
  `id` int(11) NOT NULL,
  `owner` varchar(50) NOT NULL,
  `married` varchar(50) NOT NULL,
  `playtime` int(11) DEFAULT NULL,
  `claimed` int(11) DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;


CREATE TABLE `uniqers_marriage_request` (
  `id` int(11) NOT NULL,
  `owner` varchar(255) DEFAULT NULL,
  `married` varchar(255) DEFAULT NULL,
  `date` varchar(50) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;


CREATE TABLE `uniqers_marriage_priests` (
  `id` int(11) NOT NULL,
  `priest` varchar(255) DEFAULT NULL,
  `totalxp` int(11),
  `level` INT (11)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;


CREATE TABLE `uniqers_marriage_cklist` (
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `citizenid` VARCHAR(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;



ALTER TABLE `uniqers_marriage`
  ADD PRIMARY KEY (`id`) USING BTREE;


ALTER TABLE `uniqers_marriage_engaged`
  ADD PRIMARY KEY (`id`) USING BTREE;


ALTER TABLE `uniqers_marriage_prepare`
  ADD PRIMARY KEY (`id`);
 

ALTER TABLE `uniqers_marriage_request`
  ADD PRIMARY KEY (`id`) USING BTREE;  
 
 
ALTER TABLE `uniqers_marriage_priests`
  ADD PRIMARY KEY (`id`) USING BTREE; 
  

ALTER TABLE `uniqers_marriage`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=327;


ALTER TABLE `uniqers_marriage_engaged`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=331;


ALTER TABLE `uniqers_marriage_prepare`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=249;
  
  
ALTER TABLE `uniqers_marriage_request`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=331;
  
  
ALTER TABLE `uniqers_marriage_priests`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=331; 
  
  
  

  
  
  
  