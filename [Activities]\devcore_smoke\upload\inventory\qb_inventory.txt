
    cig_redwood                = { name = 'cig_redwood', label = 'Redwood Cigarette', weight = 0, type = 'item', image = 'cig_redwood.png', unique = false, useable = true, shouldClose = true, combinable = nil, description = '' },
    cig_marlboro               = { name = 'cig_marlboro', label = 'Marlboro Cigarette', weight = 0, type = 'item', image = 'cig_marlboro.png', unique = false, useable = true, shouldClose = true, combinable = nil, description = '' },
    cigar_cuban                = { name = 'cigar_cuban', label = 'Cuban Cigar', weight = 0, type = 'item', image = 'cigar_cuban.png', unique = false, useable = true, shouldClose = true, combinable = nil, description = '' },
    cigar_davidoff             = { name = 'cigar_davidoff', label = 'Davidoff Cigar', weight = 0, type = 'item', image = 'cigar_davidoff.png', unique = false, useable = true, shouldClose = true, combinable = nil, description = '' },
    bong                       = { name = 'bong', label = 'Bong', weight = 0, type = 'item', image = 'bong.png', unique = true, useable = true, shouldClose = true, combinable = nil, description = '' },
    vape                       = { name = 'vape', label = 'Vape', weight = 0, type = 'item', image = 'vape.png', unique = true, useable = true, shouldClose = true, combinable = nil, description = '' },
    puff_vape                  = { name = 'puff_vape', label = 'Puff Vape', weight = 0, type = 'item', image = 'puff_vape.png', unique = true, useable = true, shouldClose = true, combinable = nil, description = '' },
    blue_dream_joint           = { name = 'blue_dream_joint', label = 'Blue Dream joint', weight = 2000, type = 'item', image = 'blue_dream_joint.png', unique = false, useable = true, shouldClose = true, combinable = nil, description = '' },
    purple_haze_joint          = { name = 'purple_haze_joint', label = 'Purple Haze joint', weight = 2000, type = 'item', image = 'purple_haze_joint.png', unique = false, useable = true, shouldClose = true, combinable = nil, description = '' },
    banana_kush_joint          = { name = 'banana_kush_joint', label = 'Banana Kush joint', weight = 2000, type = 'item', image = 'banana_kush_joint.png', unique = false, useable = true, shouldClose = true, combinable = nil, description = '' },
    og_kush_joint              = { name = 'og_kush_joint', label = 'Og Kush Joint ', weight = 2000, type = 'item', image = 'og_kush_joint.png', unique = false, useable = true, shouldClose = true, combinable = nil, description = '' },
    banana_kush_weed           = { name = 'banana_kush_weed', label = 'Banana Kush Weed', weight = 2000, type = 'item', image = 'banana_kush_weed.png', unique = false, useable = true, shouldClose = true, combinable = nil, description = '' },
    purple_haze_weed           = { name = 'purple_haze_weed', label = 'Purple Haze Weed', weight = 2000, type = 'item', image = 'purple_haze_weed.png', unique = false, useable = true, shouldClose = true, combinable = nil, description = '' },
    blue_dream_weed            = { name = 'blue_dream_weed', label = 'Blue Dream Weed', weight = 2000, type = 'item', image = 'blue_dream_weed.png', unique = false, useable = true, shouldClose = true, combinable = nil, description = '' },
    og_kush_weed               = { name = 'og_kush_weed', label = 'Og Kush Weed', weight = 2000, type = 'item', image = 'og_kush_weed.png', unique = false, useable = true, shouldClose = true, combinable = nil, description = '' },
    ocb_paper                  = { name = 'ocb_paper', label = 'OCB Rolling Paper', weight = 0, type = 'item', image = 'ocb_paper.png', unique = true, useable = true, shouldClose = true, combinable = nil, description = '' },
    raw_paper                  = { name = 'raw_paper', label = 'RAW Rolling Paper', weight = 0, type = 'item', image = 'raw_paper.png', unique = true, useable = true, shouldClose = true, combinable = nil, description = '' },
    classic_lighter            = { name = 'classic_lighter', label = 'Classic Lighter', weight = 0, type = 'item', image = 'classic_lighter.png', unique = true, useable = true, shouldClose = true, combinable = nil, description = '' },
    gold_lighter               = { name = 'gold_lighter', label = 'Gold Lighter', weight = 0, type = 'item', image = 'gold_lighter.png', unique = true, useable = true, shouldClose = true, combinable = nil, description = '' },
    redwood_pack               = { name = 'redwood_pack', label = 'Redwood Pack Cigarette', weight = 0, type = 'item', image = 'redwood_pack.png', unique = true, useable = true, shouldClose = true, combinable = nil, description = '' },
    marlboro_pack              = { name = 'marlboro_pack', label = 'Marlboro Pack Cigarette', weight = 0, type = 'item', image = 'redwood_pack.png', unique = true, useable = true, shouldClose = true, combinable = nil, description = '' },
    cherry_liquid              = { name = 'cherry_liquid', label = 'Cherry Liquid', weight = 0, type = 'item', image = 'cherry_liquid.png', unique = false, useable = false, shouldClose = true, combinable = nil, description = '' },
    blueberry_liquid           = { name = 'blueberry_liquid', label = 'Blueberry Liquid', weight = 0, type = 'item', image = 'blueberry_liquid.png', unique = false, useable = false, shouldClose = true, combinable = nil, description = '' },
