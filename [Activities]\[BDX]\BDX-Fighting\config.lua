
--------------------------------------
--<!>-- BODHIX | DEVELOPMENT --<!>--
--------------------------------------

Config = {

    EnablePeds = true,
    spawnCoords = { x = -664.59, y = -1217.61, z = 11.81, heading = 39.08 }, -- Do<PERSON> NPC  
    spawnCoords2 = { x = -1425.27, y = -1152.84, z =  2.31, heading = 80.43 }, -- Boxing NPC
    spawnCoords3 = { x = -342.6486, y = -1955.7771, z = -23.6400, heading = 90.0 }, -- MMA NPC
    Reloadtime = 2800, -- The time you'll need to wait to attack again.
    Damage = 5, -- The damage when you hit.
    FinishDamage = 100, -- The damage taken when you finish someone.
    FinisherMinHealth = 0.8, -- The HP the opponent needs to perform a Finisher.
    ActiveWhitelist = true,
    MultipleStyles = false,
    KillPlayers = false,
    LearnMessage = 'You have learned a new Fight Style!',
    UnlearnMessage = 'You have unlearned your Fight Style.',
    CantLearnMessage = 'You cant learn a new Fight Style.',
    Attack = 140,
    DogeLeft = 264,
    DogeRight = 51,
    Doge = 143,
    Grab = nil --Ifs causing Issues or not Sync that all? Set the key to nil (Function still on Development and will be improved soon).
}

Spawn = {

    Peds = {
        {
            Position = vector4(Config.spawnCoords.x, Config.spawnCoords.y, Config.spawnCoords.z, Config.spawnCoords.heading),
            Model = `a_m_y_ktown_01`,
            Scenario = 'WORLD_HUMAN_YOGA'
        },
        {
            Position = vector4(Config.spawnCoords2.x, Config.spawnCoords2.y, Config.spawnCoords2.z, Config.spawnCoords2.heading),
            Model = `a_m_m_beach_01`,
            Scenario = 'WORLD_HUMAN_MUSCLE_FLEX'
        },
        {
            Position = vector4(Config.spawnCoords3.x, Config.spawnCoords3.y, Config.spawnCoords3.z, Config.spawnCoords3.heading),
            Model = `a_m_m_bevhills_02`,
            Scenario = 'WORLD_HUMAN_CLIPBOARD'
        },
    }
}

