<html>

<head>
	<link rel="stylesheet" type="text/css" href="styles.css" />
	<link href="https://cdn.jsdelivr.net/npm/quasar@2.6.6/dist/quasar.prod.css" rel="stylesheet" type="text/css">
	<link href="https://fonts.googleapis.com/css?family=Roboto:100,300,400,500,700,900|Material+Icons" rel="stylesheet" type="text/css">
	<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.4.1/jquery.min.js"></script>
	<script src="https://cdn.jsdelivr.net/npm/vue@3.2.31/dist/vue.global.prod.js" defer></script>
	<script src="https://cdn.jsdelivr.net/npm/quasar@2.6.6/dist/quasar.umd.prod.min.js" defer></script>
	<script src="app.js" defer></script>
</head>

<body>
	<div id="openmenu">
		<div id="menu">
			<div class="q-mb-md">
				<q-form @submit="onSubmit" class="q-gutter-md">
					<q-input dark hint="Markdown supported (i.e. &lt;b>, &lt;i>)" type="textarea" rows="1"
						v-model="text" class="text" color="field" v-model="text" label="Text">
						<template v-slot:prepend>
							<q-icon name="create"/>
						</template>
					</q-input>

					<q-input dark hint="Text Color" v-model="color" class="my-input"
						color="field" label="Color">
						<template v-slot:prepend>
							<q-icon name="palette"/>
						</template>
						<template v-slot:append>
							<q-icon name="colorize" class="cursor-pointer">
								<q-popup-proxy cover transition-show="scale" transition-hide="scale">
									<q-color v-model="color"></q-color>
								</q-popup-proxy>
							</q-icon>
						</template>
					</q-input>

					<q-item class="slideritem">
						<q-item-section avatar>
							<q-icon color="slider" name="format_size"/>
						</q-item-section>
						<q-item-section>
							<q-slider dark label-always v-model="fontsize" :min="0.1" :max="2.0" :Step="0.1" label color="sliderb" />
						</q-item-section>

					</q-item>
					<q-item-label class="slidertext">Font Size</q-item-label>

					<q-item class="slideritem">
						<q-item-section avatar>
							<q-icon color="slider" name="title"/>
						</q-item-section>
						<q-item-section>
							<q-slider dark label-always v-model="fontstyle" :min="1" :max="5" label color="sliderb"/>
						</q-item-section>
					</q-item>
					<q-item-label class="slidertext">Font style</q-item-label>

					<q-item class="slideritem">
						<q-item-section avatar>
							<q-icon color="slider" name="social_distance"/>
						</q-item-section>
						<q-item-section>
							<q-slider dark label-always v-model="viewdistance" :min="1" :max="25" label color="sliderb" />
						</q-item-section>
					</q-item>
					<q-item-label class="slidertext">View Distance</q-item-label>

					<q-select hint="Expiration Time" dark v-model="expiration" :options="expirationOptions" color="field"
						label="Expiration Time">
						<template v-slot:prepend>
							<q-icon name="schedule"/>
						</template>
					</q-select>

					<div class="row justify-center">
						<q-btn style="margin-right: 1vh;" class="Close" label="Create" type="submit" text-color="black"
							color="button"></q-btn>
						<q-btn v-on:click="onDelete()" style="margin-right: 1vh;" class="delete" label="Delete"
							text-color="black" color="buttondel"></q-btn>
						<q-btn v-on:click="onReset()" style="margin-right: 1vh;" class="cancel" label="Reset"
							text-color="black" color="buttonreset"></q-btn>
					</div>
				</q-form>
			</div>
		</div>
</body>

</html>
