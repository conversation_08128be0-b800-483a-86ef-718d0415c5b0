### ADD TO ITEMS.LUA ###

-- Shared Items

Custom Inventory? Create the item based on this values. 
	
-- QB / ESX:

	['skateboard'] = {['name'] = 'skateboard', ['label'] = 'Skateboard', ['weight'] = 3000, ['type'] = 'item', ['image'] = 'skateboard.png', 			['unique'] = true, ['useable'] = true, ['shouldClose'] = true, ['combinable'] = nil, ['description'] = 'Skateboard'},

-- OX:

	['skateboard'] = {
        label = 'Skateboard',
        weight = 3000,
        close = true,
        stack = false,
    },

### CUSTOM DECK DESIGNS ###

For ya’ll wondering how to add your own Designs to the Store, here are the Steps you’ll need to follow:

1. Set Config.DesignCount to the new value, its “18” by default, which means there is 18 Designs, if you add a new one you’ll need to set it up as “19”. 

2. Inside the resource Stream/Custom-Designs/ you’ll find 2 files “board_18.yft” which it’s the Drawable Model and “board_18.ytd” this the Textures dictionary, this model it’s already set up to replace textures, but if you want to add another duplicated both files and change the name to “board_19”, etc. 

3. You’ll be working with the Texture file, put that “.ytd” file in Codewalker or OpenIV, enable Edit Mode drag and place the file inside the game files (you can create a new folder to organize) then Double Click and you’ll see the texture dictionary, replace it for the new texture that we will making the next step (most have the exact same name). 

4. To Make the new Texture you’ll need to use the png placed in Assets/Custom_Skate_Images, you’ll find 2 bases, “texture_base” it’s the one that we will be using and placing in our Favorite image editor (Photoshop, Móvil Apps, etc) but the new design for board and make it fit in the “texture_base.png” you find a png called “deck_base” you’ll have to do the same, but this images it’s the want that will be showing up in the custom Menu in Game. 

5. Once you have both image, convert the texture from “png” to “dds”, here the page that I use to do that: (PNG to DDS)[aconvert.com/image/png-to-dds/], then proceed to replace the texture as we said in Step #3 and the “deck_base.png” will be located to Nui/Cuatoms and renamed to the correct number of Design, in this case “deck19”. 

Now if everything it’s correctly Set Up, you’ll be able to see the new Design in Game with the Customize Menu.
