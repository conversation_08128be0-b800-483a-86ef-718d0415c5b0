shared_scripts { '@FiniAC/fini_events.js', '@FiniAC/fini_events.lua' }

fx_version 'cerulean'
game 'gta5'

name 'BDX Ice Skate'
description 'Ice Skating Career'
author 'Bodhix'
version '1.0.1'

lua54 'yes'

shared_scripts {
  'config.lua',
}

client_scripts {
  'client/IceSkating.lua',
}

server_scripts {
  'server/server.lua',
  'server/sv.lua',
}

files {
  'stream/bodhix@<EMAIL>',
  'server/version.json',
  'html/index.html',
  'html/Land.MP3',
  'html/Jumps.MP3',
  'html/Speed.MP3',
  'html/Break.MP3',
  'html/index.html',
}

ui_page 'html/index.html'

nui_page 'html/index.html'

data_file('DLC_ITYP_REQUEST')('stream/models/IceSpot.ytyp')

escrow_ignore {
  'config.lua',
}
dependency '/assetpacks'