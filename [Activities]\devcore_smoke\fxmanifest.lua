shared_scripts { '@FiniAC/fini_events.lua' }





--[[ FX Information ]]--
fx_version 'cerulean'
use_experimental_fxv2_oal 'yes'
lua54        'yes'
game         'gta5'

--[[ Resource Information ]]--
name         'realistic-smoking'
author       'devcore'
version      '3.0'
repository   'https://store.devcore.cz/'
description  'https://discord.gg/zcG9KQj3sa'

shared_scripts {
	'@ox_lib/init.lua',
	'configs/*.lua',
}

server_scripts {
	'bridge/esx/server.lua',
	'bridge/qb/server.lua',
	'server/*.lua'
}

escrow_ignore {
	'bridge/esx/*.lua',
	'bridge/qb/*.lua',
	'client/functions.lua',
	'client/client.lua',
	'configs/*.lua',
	'upload/xsound/*.mp4',
	'upload/icons/*.png',
	'upload/inventory/*.txt',
	'fxmanifest.lua',
	'README.md'
}

client_scripts {
	'bridge/qb/client.lua',
	'bridge/esx/client.lua',
	'client/*.lua',
}
dependencies {
    'ox_lib',
}
dependency '/assetpacks'
