local function ReaperExports()
    while GetResourceState("ReaperV4") ~= "started" do
        Wait(200)
    end

    while ExportsLoaded == false and GlobalState.ExportsLoaded ~= true do
        Wait(200)
    end

    return exports['ReaperV4']
end

---@param message string
---@param level "info" | "debug" | "error" | "warn" | "severe"
---@param dataset string
---@param metadata table
---@param disable_console_log boolean|nil
local function CreateNewLog(message, level, dataset, metadata, disable_console_log)
    Citizen.CreateThreadNow(function ()
        local success <const>, err <const> = ReaperExports():InvokeSLogger('NewLog', message, level, dataset, metadata, disable_console_log)

        if not success then
            print("[REAPER] FAILED TO UPLOAD LOG", err)
        end
    end)
end

AddEventHandler("playerJoining", function()
    local source = source
    local name = GetPlayerName(source)
    local license = GetPlayerIdentifierByType(source, 'license'):gsub('license:', '')
    local ip = GetPlayerIdentifierByType(source, 'ip'):gsub('ip:', '')

    CreateNewLog(("^3%s^7 (^3id:%s^7) just joined the server with the ip ^3%s^7"):format(name, source, ip), "info", "player_ips", { name = name, source = source, identifier = license, ip = ip })
end)

-- Weapon Creation Logging
AddEventHandler("weaponDamageEvent", function(sender, data)
    -- This logs when weapons are used/fired
    if sender and data then
        local name = GetPlayerName(sender)
        local license = GetPlayerIdentifierByType(sender, 'license'):gsub('license:', '')

        CreateNewLog(("^3%s^7 (^3id:%s^7) fired weapon ^3%s^7"):format(name, sender, data.weaponType), "info", "weapon_usage", {
            player = sender,
            name = name,
            identifier = license,
            weapon = data.weaponType,
            damage = data.weaponDamage,
            timestamp = os.time()
        })
    end
end)

-- Hook into ox_inventory weapon creation (if using ox_inventory)
RegisterNetEvent('ox_inventory:weaponCreated', function(weaponData)
    local source = source
    if not source then return end

    local name = GetPlayerName(source) or "Unknown"
    local licenseId = GetPlayerIdentifierByType(source, 'license')
    local license = licenseId and licenseId:gsub('license:', '') or "Unknown"

    CreateNewLog(("^3%s^7 (^3id:%s^7) created/received weapon ^3%s^7 (Serial: ^3%s^7)"):format(name, source, weaponData.name or weaponData.weapon, weaponData.serial or "N/A"), "warn", "weapon_creation", {
        player = source,
        name = name,
        identifier = license,
        weapon = weaponData.name or weaponData.weapon,
        serial = weaponData.serial,
        metadata = weaponData.metadata,
        timestamp = os.time()
    })
end)

-- Generic weapon spawn/give logging
RegisterNetEvent('custom:weaponGiven', function(weaponName, weaponHash, serial)
    local source = source
    if not source then return end

    local name = GetPlayerName(source) or "Unknown"
    local licenseId = GetPlayerIdentifierByType(source, 'license')
    local license = licenseId and licenseId:gsub('license:', '') or "Unknown"

    CreateNewLog(("^3%s^7 (^3id:%s^7) was given weapon ^3%s^7 (Hash: ^3%s^7, Serial: ^3%s^7)"):format(name, source, weaponName, weaponHash, serial or "N/A"), "warn", "weapon_creation", {
        player = source,
        name = name,
        identifier = license,
        weapon_name = weaponName,
        weapon_hash = weaponHash,
        serial = serial,
        timestamp = os.time()
    })
end)

-- Admin weapon spawn logging
RegisterNetEvent('custom:adminWeaponSpawn', function(adminSource, targetSource, weaponName, reason)
    local adminName = GetPlayerName(adminSource)
    local targetName = GetPlayerName(targetSource)
    local adminLicense = GetPlayerIdentifierByType(adminSource, 'license'):gsub('license:', '')
    local targetLicense = GetPlayerIdentifierByType(targetSource, 'license'):gsub('license:', '')

    CreateNewLog(("^1ADMIN^7 ^3%s^7 (^3id:%s^7) spawned weapon ^3%s^7 for ^3%s^7 (^3id:%s^7) - Reason: ^3%s^7"):format(adminName, adminSource, weaponName, targetName, targetSource, reason or "No reason"), "severe", "admin_weapon_spawns", {
        admin = adminSource,
        admin_name = adminName,
        admin_identifier = adminLicense,
        target = targetSource,
        target_name = targetName,
        target_identifier = targetLicense,
        weapon = weaponName,
        reason = reason,
        timestamp = os.time()
    })
end)