local function ReaperExports()
    while GetResourceState("ReaperV4") ~= "started" do
        Wait(200)
    end

    while ExportsLoaded == false and GlobalState.ExportsLoaded ~= true do
        Wait(200)
    end

    return exports['ReaperV4']
end

---@param message string
---@param level "info" | "debug" | "error" | "warn" | "severe"
---@param dataset string
---@param metadata table
---@param disable_console_log boolean|nil
local function CreateNewLog(message, level, dataset, metadata, disable_console_log)
    Citizen.CreateThreadNow(function ()
        local success <const>, err <const> = ReaperExports():InvokeSLogger('NewLog', message, level, dataset, metadata, disable_console_log)

        if not success then
            print("[REAPER] FAILED TO UPLOAD LOG", err)
        end
    end)
end

AddEventHandler("playerJoining", function()
    local source = source
    local name = GetPlayerName(source)
    local license = GetPlayerIdentifierByType(source, 'license'):gsub('license:', '')
    local ip = GetPlayerIdentifierByType(source, 'ip'):gsub('ip:', '')

    CreateNewLog(("^3%s^7 (^3id:%s^7) just joined the server with the ip ^3%s^7"):format(name, source, ip), "info", "player_ips", { name = name, source = source, identifier = license, ip = ip })
end)

if GetResourceState("ox_inventory") == "started" then
    exports.ox_inventory:registerHook("createItem", function(payload)
        local item = payload.item
        local metadata = payload.metadata
        
        -- Check if it's a weapon
        if item.weapon and string.match(item.name, "WEAPON_") then
            local source = payload.inventoryId
            if type(source) == "number" then -- Player inventory
                local name = GetPlayerName(source) or "Unknown"
                local licenseId = GetPlayerIdentifierByType(source, 'license')
                local license = licenseId and licenseId:gsub('license:', '') or "Unknown"
                
                CreateNewLog(("^3%s^7 (^3id:%s^7) received weapon ^3%s^7 (Serial: ^3%s^7)"):format(name, source, item.name, metadata.serial or "N/A"), "warn", "weapon_creation", {
                    player = source,
                    name = name,
                    identifier = license,
                    weapon = item.name,
                    serial = metadata.serial,
                    metadata = metadata,
                    timestamp = os.time()
                })
            end
        end
        
        return payload.metadata
    end, {
        -- Optional: Filter only weapon items
        itemFilter = function(itemName)
            return string.match(itemName, "WEAPON_") ~= nil
        end
    })
end
