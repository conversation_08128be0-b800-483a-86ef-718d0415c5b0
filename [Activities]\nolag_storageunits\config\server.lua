return {
    checkForUpdates = true, -- Set to false if you don't want the script to check for updates
    paymentFails = 3, -- How many times a player can fail to pay before the storage unit is locked
    checkForExpiredTime = 30, -- How often the script should check for expired storage units
    paymentMethod = 'bank', -- The method of payment for the storage unit
    deleteExpiredStorage = true, -- Set to true if you want to delete expired storage units
    paymentAttemptInterval = 1, -- How often the script should check for failed payments

    -- You need to have the banking the shared config set to your banking in order to work
    societyPayments = true, -- Set to true if you want society to receive the payments
    society = 'realestate', -- The society that should receive the payments
}