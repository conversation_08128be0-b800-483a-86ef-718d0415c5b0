TABLE_OFFSET = {
    [`prop_pooltable_3b`] = vector2(0.073, -0.128),
    [`prop_pooltable_02`] = vector2(0.0, 0.0),
    [`k4mb1_prop_pooltable_02`] = vector2(0.0, 0.0),
}

ALLOWED_MODELS = {
    Get<PERSON><PERSON><PERSON><PERSON>('prop_pooltable_3b'),
    <PERSON><PERSON><PERSON><PERSON><PERSON>('prop_pooltable_02'),
    <PERSON><PERSON><PERSON><PERSON><PERSON>('k4mb1_prop_pooltable_02'),
}

MODEL_ALIAS = {
    [`k4mb1_prop_pooltable_02`] = `prop_pooltable_02`,
}

POOL_RACKS = {
    `prop_pool_rack_01`,
    `prop_pool_rack_02`,
    `k4mb1_prop_pool_rack_02`,
}


 