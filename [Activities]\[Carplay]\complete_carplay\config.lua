Config = {}

-- === VERSION 3.0 ===

-- === DEBUG ===
Config.Debug                    = false

-- === GENERAL ===
Config.OpenCommand              = 'carplay' -- Console command to open the NUI
Config.OpenKey                  = 'k'
Config.OpenRearCameraCommand    = 'trunk-camera' -- Console command to open the NUI
Config.OpenRearCameraKey        = 'f7' -- Open or close
Config.ReportsDuration          = 18000 -- Seconds before the signaled report vanish

-- === CARPLAY INSTALLATION ===
Config.RequireInstallation      = true -- Without installation, you won't have the carplay display object inside the car
Config.CarplayOnCarNotBought    = false -- Enables or not to use the carplay on admin/npc/not dealership cars (in restricted mode)
Config.DisablePropInstallation  = false -- Disable the prop installation
Config.InstallationCommand      = 'zsedscarplay-install'
Config.UninstallCommand         = 'carplay-uninstall'
Config.ReturnItemOnUninstall    = true -- If true, the item will be returned to the player when uninstalling carplay
Config.RequireMechanic          = false
Config.MechanicJobName          = {
    'mechanic',
}
Config.InstallTime              = 10000 -- milliseconds

-- === PHONE ===
Config.DisablePhone             = false -- Won't search for contacts on database
Config.PhoneType                = 'lbphone' -- Select your phone script from the list below
                                -- gcphone | qbphone | quasar-pro | gksphone | gksphone2 | dphone | highphone | chezza | npwd | roadphone | lbphone
                                -- custom (if you use a custom phone, you need to implement the function SendMessageCustomPhone and SendMessageCustomPhoneS)

-- === NAVIGATOR ===
Config.EnableNavigator          = true
Config.NavigatorAnySeat         = true -- Allow any passenger of the vehicle to use the navigator

-- === MUSIC ===
Config.SoundPlayer              = 'xsound' -- Select your sound player from the list below
                                -- xsound | high_3dsounds | mx-surround
Config.XSoundDistanceOpen       = 15 -- Distance to hear the music if the car doors are open
Config.XSoundDistanceClose      = 3 -- Distance to hear the music if the car doors are closed


-- === TEXT ===
Config.Text = {
    ['no_carplay'] = "Carplay is not installed in this vehicle",
    ['not_registered'] = "This vehicle is not registered",
    ['already_registered'] = "This vehicle is already registered",
    ['installing_carplay'] = "Installing Carplay...",
    ['uninstalling_carplay'] = "Carplay uninstalled",
    ['carplay_installed'] = "Carplay installed",
    ['carplay_open'] = "Carplay is already used by another passenger",
    ['not_mechanic'] = "You are not a mechanic",
}

function OnTrunkCameraOpen()
    if GetResourceState('complete_hud') == 'started' then
        exports['complete_hud']:hideHud()
    end
end

function OnTrunkCameraClose()
    if GetResourceState('complete_hud') == 'started' then
        exports['complete_hud']:showHud()
    end
end

function GetTankHealth(veh)
    local tankHealt = (GetVehiclePetrolTankHealth(veh) / 1000) * 100
    tankHealt = tankHealt >= 0 and tankHealt or 0
    return tankHealt
end

function GetOilLevel(veh)
    return (GetVehicleOilLevel(veh)) * 100
end