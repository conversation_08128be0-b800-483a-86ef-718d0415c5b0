--------------------------------------
--<!>-- BODHIX | STUDIO --<!>--  
--------------------------------------

-- Controls References:    https://docs.fivem.net/docs/game-references/controls/#c
-- Support & Feedback:     https://discord.gg/PjN7AWqkpF

--IMPORTANT
--I HIGHLY Recommend STAY with Default Keys / Controls, SPECIALLY if you play on Controler.
--Also Parameters Options are meant to be this Value, there is no reason to modify but still there is the option.

--Enjoy! -Bodhix

config = {

--Parameters
JumpsHeight       = 8.0,        -- How High Character can jump on Tricks (8.0 By Default I do not recommend change this Value).
VaultsHeight      = 8.0,        -- How High Character can jump on Vaults (8.0 By Default I do not recommend change this Value).
HighTricksHeight  = 7.0,        -- How High Character can jump on High Tricks (7.0 By Default I do not recommend change this Value).
WallrunHeight     = 4.0,        -- How High Character can jump on Wallruns (4.0 By Default I do not recommend change this Value).
WallFlipsHeight   = 8.0,        -- How High Character can jump on Flips (8.0 By Default I do not recommend change this Value).
MaxFallHeight     = 30.0,       -- Max High Character can Survive (30.0 By Default).
CanSlowmo         = false,       -- Toggle (true/false) if you want that Client can perform Slow Motion Ability (Key Q / RB).
ShowScore         = false,      -- Draw Score EveryTime.
ActiveWhitelist   = false,      -- Toggle (true/false) if you want active Whitelist function (Modify in Whitelist.lua).
Stamina           = 1.0,        -- Set the Stamina you want in Parkour Mode (1.0 its the Default).
RunningSpeed      = 1.3,        -- Set the Velocity your Player will run (Only when 'RunSpeed' its pressed, 1.0 its the Default).
Language          = 'English',  -- This can be changed to Supported languages: 'Spanish', 'French', 'Korean', 'Russian', 'Arabic', 'German'.

--Activation Keys
HoldKey           = 137,        -- Caps / A (This is the Button/Key you need to hold for activate).
ActiveKey         = 19,         -- ALT / PAD DOWN (This is the Button/Key you need to Press while ur Holding "HoldKey" for activate).
Alternative       = 327,        -- F5 (This is an Alternatve Key for activate specially for Keyboard).

--Freerunning Keys
PassiveKey        = 22,         -- SPACE / X (This is the Button/Key you need to hold for Front / Side / Gainer / Cork).
FrontFlip         = 32,         -- W / JOYSTCIK UP (Use while Holding the 'PassiveKey' to perform, also Hold it for Doubles if there is enough Height).
SideFlip          = 34,         -- A / JOYSTCIK LEFT (Use while Holding the 'PassiveKey' to perform, also Hold it for Doubles if there is enough Height).
Gainer            = 35,         -- D / JOYSTCIK RIGHT (Use while Holding the 'PassiveKey' to perform, also Hold it for Doubles if there is enough Height).
Cork              = 33,         -- S / JOYSTCIK DOWN (This is the Button/Key you need to Press While Pressing the 'PassiveKey' to activate).
HighTricks        = 25,         -- RIGHT CLICK / LT (This is the Button/Key you need to Press / Hold for activate).

--Tricking Keys
Tricking          = 23,         -- F / Y (This is the Button/Key you need to Press for Tricking also this is the same key for Arabian/Double Arabian).
Fly               = 140,        -- R / B (This is the Button/Key you need to Press for Task While Tricking).
BackFull          = 22,         -- SPACE / X (This is the Button/Key you need to Press for Task While Tricking also this is the same key for Full to Back).
BackStep          = 73,         -- LSHIFT / A (This is the Button/Key you need to Press for Task While Tricking).
Arabian           = 23,         -- F / Y (Only When you are into a Tricking Combo).

--Parkour Keys
Parkour           = 24,         -- LEFT CLICK / RT (Use to Mid, Mid-High, High and Bigger Walls to do Vaults, Fast Climbs and Wallruns).
LandRoll          = 140,        -- R / B (Use While falling from a Big Falls and After any Trick to do your Runs with Flow and more Smoother).
WallFlips         = 38,         -- E / LB (Use in Any Forward Wall to perform a WallFlip).
TicTac            = 38,         -- E / LB (Press the Key while holding 'Parkour Key' when wall its on Left or Right for TicTacs and Lateral Wallruns).
Accuarcy          = 38,         -- E / LB (This is the Button/Key you need to Press While Pressing the 'PassiveKey' to activate).
PressLanding      = 26,         -- C / Right Stick (Only While Performing any Trick Animation). 
Slide             = 26,         -- C / Right Stick (Only While Player its running).

--Ledge Keys
LedgeJump         = 22,         -- Space / X (Only when Player its on Ledge Idle Animation).
LedgeBackFlip     = 140,        -- R / B (Only when Player its on Ledge Idle Animation).
LedgeFrontFlip    = 23,         -- F / Y (Only when Player its on Ledge Idle Animation).
LedgeCastaway     = 38,         -- E / LB (Only when Player its on Ledge Idle Animation).
LedgeClimb        = 21,         -- Shift / A (Only when Player its on Ledge Idle Animation).
LedgeStop         = 47,         -- G / DPad Left (Only when Player its on Ledge Idle Animation).

--Extra Keys
SlowMohKey        = 44,         -- Q / RB (You can use this anytime).
Variations        = 24,         -- Right Click / RT (Hold 'Variations Key' and 'PassiveKey' at the same time, then while holding, Press the Trick Key).
Velocity          = 38,         -- E / LB (Use while performing a Vault or Trick to more Velocity or Distance to the Stunt).
SpeedBoost        = 47,         -- Run Faster for 3 seconds (Boost recharge scoring points).

}

Activities = {

    BeachCoords = { x = -1761.3423, y = -762.6474,  z = 25.7310,  heading = 59.0 },  
    BuildCoords = { x = 164.7125,   y = -67.8510,   z = 92.4993,  heading = 111.0 }, 
    DocksCoords = { x = 316.1499,   y = -3039.1300, z = 26.3422,  heading = 180.0 },     
} 

Parkour = {

    PKPeds = {
        {
            PKPosition = vector4(Activities.BeachCoords.x, Activities.BeachCoords.y, Activities.BeachCoords.z, Activities.BeachCoords.heading),
            PKModel = `a_m_y_musclbeac_02`,
            PKScenario = 'WORLD_HUMAN_BINOCULARS'
        },
        {
            PKPosition = vector4(Activities.BuildCoords.x, Activities.BuildCoords.y, Activities.BuildCoords.z, Activities.BuildCoords.heading),
            PKModel = `cs_beverly`,
            PKScenario = 'WORLD_HUMAN_PAPARAZZI'
        },
        {
            PKPosition = vector4(Activities.DocksCoords.x, Activities.DocksCoords.y, Activities.DocksCoords.z, Activities.DocksCoords.heading),
            PKModel = `cs_dom`,
            PKScenario = 'WORLD_HUMAN_SEAT_LEDGE'
        },
    }
}

function GetActivitiesConfig()
    return Activities
end
exports('GetActivitiesConfig', GetActivitiesConfig)

function GetParkourConfig()
    return Parkour
end
exports('GetParkourConfig', GetParkourConfig)

