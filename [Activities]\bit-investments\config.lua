Config = {}

-- $$$$$$\   $$$$$$\  $$\   $$\ $$$$$$$$\ $$$$$$\  $$$$$$\  $$\   $$\ $$$$$$$\   $$$$$$\ $$$$$$$$\ $$$$$$\  $$$$$$\  $$\   $$\ 
--$$  __$$\ $$  __$$\ $$$\  $$ |$$  _____|\_$$  _|$$  __$$\ $$ |  $$ |$$  __$$\ $$  __$$\\__$$  __|\_$$  _|$$  __$$\ $$$\  $$ |
--$$ /  \__|$$ /  $$ |$$$$\ $$ |$$ |        $$ |  $$ /  \__|$$ |  $$ |$$ |  $$ |$$ /  $$ |  $$ |     $$ |  $$ /  $$ |$$$$\ $$ |
--$$ |      $$ |  $$ |$$ $$\$$ |$$$$$\      $$ |  $$ |$$$$\ $$ |  $$ |$$$$$$$  |$$$$$$$$ |  $$ |     $$ |  $$ |  $$ |$$ $$\$$ |
--$$ |      $$ |  $$ |$$ \$$$$ |$$  __|     $$ |  $$ |\_$$ |$$ |  $$ |$$  __$$< $$  __$$ |  $$ |     $$ |  $$ |  $$ |$$ \$$$$ |
--$$ |  $$\ $$ |  $$ |$$ |\$$$ |$$ |        $$ |  $$ |  $$ |$$ |  $$ |$$ |  $$ |$$ |  $$ |  $$ |     $$ |  $$ |  $$ |$$ |\$$$ |
--\$$$$$$  | $$$$$$  |$$ | \$$ |$$ |      $$$$$$\ \$$$$$$  |\$$$$$$  |$$ |  $$ |$$ |  $$ |  $$ |   $$$$$$\  $$$$$$  |$$ | \$$ |
 --\______/  \______/ \__|  \__|\__|      \______| \______/  \______/ \__|  \__|\__|  \__|  \__|   \______| \______/ \__|  \__|

--Use "esx" or "qb"
Config.Framework = "esx"
--If you are using one of the most recent versions of ESX, set the script name. Default = "es_extended"
Config.ESXExport = ""
--Default ESX: "esx:getSharedObject" | Default QB: "qb-core"
Config.Core = "esx:getSharedObject"
-- Money delivered after 6 hours
Config.sixhours = 9000 
-- Money delivered after 5 hours
Config.fivehours = 7000 
-- Money delivered after 4 hours
Config.fourhours = 5500 
-- Money delivered after 3 hours
Config.threehours = 4000 
-- Money delivered after 2 hours
Config.twohours = 2500 
-- Money delivered after 1 hours
Config.onehour = 1000
-- Type of currency used 
Config.currency = "$" 
-- Command to open investments menu
Config.investmentsCommand = "invest" 
-- 1 for 1 hour, 2 for 2 hours, 3 for 3 hours,... Max. 6. For testing you can use 7, it is for 20 seconds
Config.defaultTime = 2 
-- url of the discord webhook where the logs will be received
Config.webhook = "https://discord.com/api/webhooks/1398375788650303650/vT5rppNVKVIshGYH4vzrKxQTsuVl-GamHfFhd6iv_wUe9SsOM2d-uVkspaNk-FzoqVgk" 
