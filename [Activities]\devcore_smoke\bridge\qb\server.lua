if GetResourceState('qb-core') ~= 'started' then return end
if GetResourceState('qbx_core') ~= 'started' and GetResourceState('qb-core') ~= 'started' then return end

Framework = 'QB'
QBCore = Config.Export()
STATE = {}


RegisterUsableItem = nil



Citizen.CreateThread(function()
    if GetResourceState('ox_inventory') == 'started' then
        STATE.INV = 'ox'
        ox_inventory = exports.ox_inventory
    elseif GetResourceState('qs-inventory') == 'started' then
            STATE.INV = 'qs'
    
        elseif GetResourceState('qb-inventory') == 'started' then
            STATE.INV = 'qb'
    else
        STATE.INV = 'qb'
    end

end)


function GetPlayer(source)
    return QBCore.Functions.GetPlayer(source)
end


function GetPlayerSource(source)
    return QBCore.Functions.GetPlayer(source).PlayerData.source
end

function AddItem(source, item, count, slot, metadata)
    local player = GetPlayer(source)
    if STATE.INV == 'ox' then
        return ox_inventory:AddItem(source, item, count, metadata, slot)
    elseif STATE.INV == 'qs' then
        return exports['qs-inventory']:AddItem(source, item, count, slot, metadata)
    else
        return exports['qb-inventory']:AddItem(source, item, count, slot, metadata)
    end
end

function RemoveItem(source, item, count, slot, metadata)
    local player = GetPlayer(source)
    if STATE.INV == 'ox' then
        return ox_inventory:RemoveItem(source, item, count, metadata, slot)
    elseif STATE.INV == 'qs' then
        return exports['qs-inventory']:RemoveItem(source, item, count, slot)
    else
        return exports['qb-inventory']:RemoveItem(source, item, count, slot)
    end
end


QBCore.Functions.CreateCallback(GetCurrentResourceName()..':server:getInv', function(source, cb)
    local Player = QBCore.Functions.GetPlayer(source)
    local inventory = Player.PlayerData.items
    return cb(inventory)
end)



CreateThread(function()
    Wait(100)
    RegisterUsableItem = function(name, cb)
            QBCore.Functions.CreateUseableItem(name, function(source, data)
                if STATE.INV == 'ox' then
                cb(source, data.metadata, data.slot)
                else
                    cb(source, data.info, data.slot)
                end
            end)
        end
end)