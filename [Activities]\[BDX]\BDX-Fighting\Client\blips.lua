local InterestZones = {

	---{coords = vector3(-285.0569, -1921.7772, 29.9464),  sprite = 439,  display = 4, scale = 1.0, colour = 23, name = 'MMA Stadium'}, --Tattoo Shop
	{coords = vector3(-1425.27, -1152.84, 2.31),  sprite = 311,  display = 4, scale = 1.0, colour = 46, name = 'Boxing Spot'}, --Tattoo Shop
	{coords = vector3(-664.1911, -1218.2490, 11.8142),  sprite = 770,  display = 4, scale = 1.0, colour = 6, name = 'The Dojo'},
}

Citizen.CreateThread(function()
    for k,zone in pairs(InterestZones) do
        local blip = AddBlipForCoord(zone.coords)

        SetBlipSprite(blip, zone.sprite)
        SetBlipDisplay(blip, zone.display)
        SetBlipScale(blip, zone.scale)
        SetBlipColour(blip, zone.colour)
        SetBlipAsShortRange(blip, true)

        BeginTextCommandSetBlipName("STRING")
        AddTextComponentString(zone.name)
        EndTextCommandSetBlipName(blip)
    end
end)

local teleportMarkers = {
    {enter = vector3(-365.8319, -1946.5941, -23.6531), exit = vector3(-285.0569, -1921.7772, 29.9464)},
    {enter = vector3(-285.0569, -1921.7772, 29.9464), exit = vector3(-365.8319, -1946.5941, -23.6531)},
}

local teleportKey = 23 -- Key 'F'
local checkInterval = 2000 -- Initial check every 2 seconds
local notificationShow = false
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(checkInterval)
        local playerPed = PlayerPedId()
        local playerCoords = GetEntityCoords(playerPed)
        local closeToMarker = false

        for _, marker in ipairs(teleportMarkers) do
            -- Check proximity to the enter marker
            if Vdist(playerCoords, marker.enter) < 20.0 then
                closeToMarker = true
                DrawMarker(1, marker.enter.x, marker.enter.y, marker.enter.z - 1.0, 0, 0, 0, 0, 0, 0, 1.5, 1.5, 1.0, 255, 255, 0, 100, false, true, 2, true, nil, nil, false)
                if Vdist(playerCoords, marker.enter) < 1.5 then
                    if not notificationShow then
                    ShowHelpNotification("Press F to enter the MMA Stadium.")
                    notificationShow = true
                    end
                    if IsControlJustReleased(0, teleportKey) then
                        SetEntityCoords(playerPed, marker.exit.x, marker.exit.y, marker.exit.z)
                    end
                end
            end

            -- Check proximity to the exit marker
            if Vdist(playerCoords, marker.exit) < 10.0 then
                closeToMarker = true
                DrawMarker(1, marker.exit.x, marker.exit.y, marker.exit.z - 1.0, 0, 0, 0, 0, 0, 0, 1.5, 1.5, 1.0, 255, 255, 0, 100, false, true, 2, true, nil, nil, false)
                if Vdist(playerCoords, marker.exit) < 1.5 then
                    if not notificationShow then
                    ShowHelpNotification("Press F to exit the MMA Stadium.")
                    notificationShow = true
                    end
                    if IsControlJustReleased(0, teleportKey) then
                        SetEntityCoords(playerPed, marker.enter.x, marker.enter.y, marker.enter.z)
                    end
                end
            end
        end

        -- Adjust the wait time based on proximity to any marker
        if closeToMarker then
            checkInterval = 0
        else
            notificationShow = false
            checkInterval = 2000 -- Return to checking every 2 seconds when far from markers
        end
    end
end)

function ShowHelpNotification(text)
    AddTextEntry('FivemHelpNotification', text)
    BeginTextCommandDisplayHelp('FivemHelpNotification')
    EndTextCommandDisplayHelp(0, false, true, -1)
end
