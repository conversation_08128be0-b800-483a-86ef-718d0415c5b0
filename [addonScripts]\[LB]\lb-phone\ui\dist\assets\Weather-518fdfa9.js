import{u as A,r as d,G as T,q as p,s as g,aU as v,j as e,a as t,L as s,F as P,v as R,aV as W,P as f,A as H}from"./index-a04bc7c5.js";const y={background:"sunny",icon:"sunny",temperature:80,city:"Los Santos",feelsLike:85,windSpeed:4,hourly:[{type:"sunny",temperature:80},{type:"sunny",temperature:75},{type:"sunny",temperature:73},{type:"cloudy",temperature:65}]};function k(){var m;const E=A(f.Settings),o=A(H),[a,u]=d.useState({background:"sunny",icon:"sunny",temperature:0}),[n,h]=d.useState(null);d.useEffect(()=>{T("Weather")&&p("Weather",{action:"getData"},y).then(i=>{if(!i)return g("warning","No weather data received");u(i)})},[o==null?void 0:o.active]),d.useEffect(()=>{v(`./assets/img/backgrounds/default/apps/weather/${a.background}.jpg`).then(i=>{h(i.dominant+"8d")})},[a]);const S=()=>{const r=new Date().getHours();return r>=21||r<=5},c=i=>E.weather&&E.weather.celcius?W(i):i,N={cloudy:s("APPS.WEATHER.TYPES.CLOUDY"),drizzle:s("APPS.WEATHER.TYPES.LIGHT_RAIN"),fog:s("APPS.WEATHER.TYPES.FOG"),"heavy-rain":s("APPS.WEATHER.TYPES.HEAVY_RAIN"),night:s("APPS.WEATHER.TYPES.CLEAR"),"partly-cloudy-night":s("APPS.WEATHER.TYPES.MOSTLY_CLEAR"),"partly-cloudy-sunny":s("APPS.WEATHER.TYPES.MOSTLY_CLEAR"),rain:s("APPS.WEATHER.TYPES.RAIN"),snow:s("APPS.WEATHER.TYPES.SNOW"),thunder:s("APPS.WEATHER.TYPES.THUNDER"),sunny:s("APPS.WEATHER.TYPES.CLEAR"),tornado:s("APPS.WEATHER.TYPES.TORNADO"),windy:s("APPS.WEATHER.TYPES.WINDY")};return e("div",{className:"weather-container",style:{backgroundImage:`url(./assets/img/backgrounds/default/apps/weather/${a.background}.jpg)`},children:[e("div",{className:"weather-info",children:[t("div",{className:"location",children:a.city}),e("div",{className:"temperature",children:[c(a.temperature),"°"]}),t("div",{className:"weather-type",children:N[a==null?void 0:a.icon]})]}),e("div",{className:"hourly-forecast",style:{backgroundColor:n},children:[e("div",{className:"info-header",children:[t("i",{className:"fal fa-clock"}),s("APPS.WEATHER.HOURLY_FORECAST")]}),e("div",{className:"content",children:[e("div",{className:"item",children:[t("div",{className:"time",children:s("APPS.WEATHER.NOW")}),t("img",{src:`./assets/img/icons/weather/${a==null?void 0:a.icon}.png`}),e("div",{className:"temp",children:[c(a==null?void 0:a.temperature),"°"]})]}),(m=a==null?void 0:a.hourly)==null?void 0:m.map((i,r)=>{let l=new Date().getHours();return e("div",{className:"item",children:[e("div",{className:"time",children:[l+r>12?l+r-12:l+r,t("span",{children:l+r>=12?"PM":"AM"})]}),t("img",{src:`./assets/img/icons/weather/${i.type}.png`}),e("div",{className:"temp",children:[c(i.temperature),"°"]})]},r)})]})]}),e("div",{className:"details",children:[e("div",{className:"item",style:{backgroundColor:n},children:[e("div",{className:"item-header",children:[t("i",{className:"fal fa-temperature-low"}),s("APPS.WEATHER.FEELS_LIKE")]}),e("div",{className:"item-content",children:[c(a.feelsLike??a.temperature),"°"]}),t("div",{className:"item-footer",children:(a==null?void 0:a.feelsLike)==a.temperature||!a.feelsLike?s("APPS.WEATHER.FEELS_LIKE_SAME"):a.feelsLike>a.temperature?s("APPS.WEATHER.FEELS_LIKE_HUMIDITY"):s("APPS.WEATHER.FEELS_LIKE_WIND")})]}),t("div",{className:"item",style:{backgroundColor:n},children:S()?e(P,{children:[e("div",{className:"item-header",children:[t("i",{className:"fas fa-sunrise"}),s("APPS.WEATHER.SUNRISE")]}),e("div",{className:"item-content",children:["3:31",t("span",{children:"AM"})]}),e("div",{className:"item-footer",children:[s("APPS.WEATHER.SUNSET"),": 10:09",t("span",{children:"PM"})]})]}):e(P,{children:[e("div",{className:"item-header",children:[t("i",{className:"fas fa-sunrise"}),s("APPS.WEATHER.SUNSET")]}),e("div",{className:"item-content",children:["10:09",t("span",{children:"PM"})]}),e("div",{className:"item-footer",children:[s("APPS.WEATHER.SUNRISE"),": 3:31",t("span",{children:"AM"})]})]})}),e("div",{className:"item",style:{backgroundColor:n},children:[e("div",{className:"item-header",children:[t("i",{className:"fas fa-wind"}),s("APPS.WEATHER.WIND")]}),e("div",{className:"wind-data",children:[R(a.windSpeed,1),t("span",{children:"m/s"})]}),t("img",{src:"./assets/img/icons/weather/wind.png"})]}),e("div",{className:"item",style:{backgroundColor:n},children:[e("div",{className:"item-header",children:[t("i",{className:"fas fa-dewpoint"}),s("APPS.WEATHER.PRECIPITATION")]}),t("div",{className:"item-content",children:"0 mm"}),t("div",{className:"item-subcontent",children:s("APPS.WEATHER.LAST_24H")}),e("div",{className:"item-footer",children:["0 mm ",s("APPS.WEATHER.EXPECTED_24H")]})]})]})]})}export{k as default};
