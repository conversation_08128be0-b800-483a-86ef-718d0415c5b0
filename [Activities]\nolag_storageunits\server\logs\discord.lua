local sharedConfig = lib.load('config.shared')
if sharedConfig.logs ~= 'discord' then return end

-- credit to gpt4 for the discord configuration
-- Replace with your own webhook
local discordWebhook = ''

local discord = {
    send = function(title, description, color)
        local embed = {
            {
                ["title"] = title,
                ["description"] = description,
                ["color"] = color, -- You can set different colors for different log types
            }
        }
        PerformHttpRequest(discordWebhook, function(err, text, headers) end, 'POST', json.encode({ embeds = embed }), { ['Content-Type'] = 'application/json' })
    end
}

Logs = {
    CreateStorage = function(playerIdentifier, storageId, storageLabel, storagePrice)
        local title = "Storage Creation"
        local description = string.format("Player **%s** created storage **%s** (%s) for **%s**.", playerIdentifier, storageId, storageLabel, storagePrice)
        local color = 65280 -- Green color for successful creation
        discord.send(title, description, color)
    end,
    RentStorage = function(playerIdentifier, storageId, storageLabel, rentalDays, storagePrice)
        local title = "Storage Rental"
        local description = string.format("Player **%s** rented storage **%s** (%s) for **%s** days for **%s**.", playerIdentifier, storageId, storageLabel, rentalDays, storagePrice)
        local color = 16776960 -- Yellow color for rental
        discord.send(title, description, color)
    end,
    ChangePassword = function(playerIdentifier, storageId, storageLabel)
        local title = "Storage Password Change"
        local description = string.format("Player **%s** changed password for storage **%s** (%s).", playerIdentifier, storageId, storageLabel)
        local color = 16776960 -- Yellow color for password change
        discord.send(title, description, color)
    end,
    CancelSubscription = function(playerIdentifier, storageId, storageLabel)
        local title = "Storage Subscription Cancellation"
        local description = string.format("Player **%s** canceled subscription for storage **%s** (%s).", playerIdentifier, storageId, storageLabel)
        local color = 16711680 -- Red color for subscription cancellation
        discord.send(title, description, color)
    end,
    TransferStorage = function(playerIdentifier, targetplayerIdentifier, storageId, storageLabel)
        local title = "Storage Transfer"
        local description = string.format("Player **%s** transferred storage **%s** (%s) to **%s**.", playerIdentifier, storageId, storageLabel, targetplayerIdentifier)
        local color = 16776960 -- Yellow color for transfer
        discord.send(title, description, color)
    end,
    DeleteStorage = function(playerIdentifier, storageId, storageLabel)
        local title = "Storage Deletion"
        local description = string.format("Player **%s** deleted storage **%s** (%s).", playerIdentifier, storageId, storageLabel)
        local color = 16711680 -- Red color for deletion
        discord.send(title, description, color)
    end,
    ExpiredStorage = function(storageId, storageLabel)
        local title = "Storage Expiration"
        local description = string.format("Storage **%s** (%s) has expired.", storageId, storageLabel)
        local color = 16711680 -- Red color for expiration
        discord.send(title, description, color)
    end,
    ---@param playerIdentifier string
    ---@param storageId number
    ---@param paymentType string
    ---@param amount number
    HandledPayment = function(playerIdentifier, storageId, paymentType, amount)
        local title = "Payment Handled"
        local description = string.format("Player **%s** handled payment for storage **%s** using **%s** for **%s**.", playerIdentifier, storageId, paymentType, amount)
        local color = 65280 -- Green color for successful payment
        discord.send(title, description, color)
    end,
    ---@param playerIdentifier string
    ---@param storageId number
    ---@param paymentType string
    ---@param amount number
    FailedPayment = function(playerIdentifier, storageId, paymentType, amount)
        local title = "Payment Failure"
        local description = string.format("Player **%s** failed payment for storage **%s** using **%s** for **%s**.", playerIdentifier, storageId, paymentType, amount)
        local color = 16711680 -- Red color for payment failure
        discord.send(title, description, color)
    end,
    BuyStorage = function(playerIdentifier, storageId, storageLabel, storagePrice)
        local title = "Storage Purchase"
        local description = string.format("Player **%s** bought storage **%s** (%s) for **%s**.", playerIdentifier, storageId, storageLabel, storagePrice)
        local color = 65280 -- Green color for successful purchase
        discord.send(title, description, color)
    end,
    RaidStorage = function(playerIdentifier, storageId, storageLabel)
        local title = "Storage Raid"
        local description = string.format("Player **%s** raided storage **%s** (%s).", playerIdentifier, storageId, storageLabel)
        local color = 16711680 -- Red color for raid
        discord.send(title, description, color)
    end,
}
