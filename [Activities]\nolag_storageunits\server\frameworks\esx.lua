if GetResourceState('es_extended') ~= 'started' then
    return
end

lib.print.debug('Loading ESX Framework')
local sharedConfig = require 'config.shared'

ESX = exports.es_extended:getSharedObject()
Framework = ESX

Framework.IsPlayerAuthorized = function(player)
    for jobName, grade in pairs(sharedConfig.realEstateJobs) do
        if jobName == player.job.name and player.job.grade >= grade then
            return true
        end
    end
    return false
end

Framework.IsPlayerAuthorizedToRaid = function(player)
    for jobName, grade in pairs(sharedConfig.policeRaid.jobs) do
        if jobName == player.job.name and player.job.grade >= grade then
            return true
        end
    end
    return false
end

Framework.IsPlayerAuthorizedToDeleteStorage = function(player)
    return Framework.IsPlayerAuthorized(player)
end

Framework.IsPlayerAuthorizedToLockdown = function(player)
    return Framework.IsPlayerAuthorizedToRaid(player)
end

Framework.IsPlayerAuthorizedToManageStorages = function(player)
    return Framework.IsPlayerAuthorized(player)
end

GetOfflinePlayer = function(playerIdentifier)
    local playerData = MySQL.query.await('SELECT * FROM users WHERE identifier = ?', {
        playerIdentifier
    })
    if playerData then
        local xPlayer = {}
        local player = playerData[1]
        if not player then
            return nil
        end
        ---------
        xPlayer.identifier = player.identifier
        ---------
        xPlayer.license = player.license
        ---------
        xPlayer.getName = function()
            if player.firstname and player.lastname then
                return player.firstname .. ' ' .. player.lastname
            elseif player.name then
                return player.name
            else
                return 'Unknown'
            end
        end
        ---------
        xPlayer.removeAccountMoney = function(type, money, reason)
            local playerMoney = json.decode(player.accounts)
            local accountMoney = playerMoney[type]
            if accountMoney >= money then
                accountMoney = accountMoney - money
                MySQL.query('UPDATE users SET money = ? WHERE identifier = ?', {
                    json.encode(player.money),
                    player.identifier
                })
                return true
            else
                return false
            end
        end
        ---------
        xPlayer.getAccount = function(type)
            local playerMoney = json.decode(player.accounts)
            return {
                money = playerMoney[type] or 0
            }
        end

        return xPlayer
    end
    return nil
end
