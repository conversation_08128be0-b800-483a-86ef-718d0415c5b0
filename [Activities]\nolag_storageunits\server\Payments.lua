local config = lib.load('config.server')

-- A cron job to check for expired storages and handle payments
lib.cron.new(('*/%s * * * *'):format(config.checkForExpiredTime), function()
    local storageData = MySQL.query.await([[
        SELECT storages.location,
               storages.owner,
               storages.failed_payments,
               storage_locations.price,
               storage_locations.rental_days,
               TIMESTAMPDIFF(HOUR, CURRENT_TIMESTAMP(), storages.`expiring_date`) AS `timeLeft`,
               TIMESTAMPDIFF(HOUR, CURRENT_TIMESTAMP(), storages.`next_payment_attempt`) AS `payLeft`
        FROM storage_locations
        JOIN storages ON storage_locations.storage = storages.location
        WHERE storage_locations.forever = 0 AND storages.expired = 0
    ]], {})

    lib.print.debug('Checking for expired storages')

    if storageData then
        for _, value in pairs(storageData) do
            local timeLeft = value.timeLeft or 0
            local failedPayments = tonumber(value.failed_payments) or 0
            local payLeft = value.payLeft or 0
            local playerIdentifier = value.owner
            local storageIdentifier = value.location
            local rentCost = value.price
            local rentDays = value.rental_days

            -- Check if storage is expired and failed payments are below limit
            if timeLeft <= 0 and failedPayments < config.paymentFails then
                lib.print.debug('Storage ' .. storageIdentifier .. ' has expired')

                -- Process payment immediately for expired storages with no failed payments
                if failedPayments == 0 then
                    HandlePayment(playerIdentifier, storageIdentifier, rentCost, rentDays)
                    -- For storages with failed payments, check if it's time for the next retry
                elseif payLeft <= 0 then
                    HandlePayment(playerIdentifier, storageIdentifier, rentCost, rentDays)
                else
                    lib.print.debug('Storage ' .. storageIdentifier .. ' payment retry pending for ' .. payLeft .. ' hours')
                end
            elseif failedPayments >= config.paymentFails then
                -- Expire storage if too many failed payments
                StorageData[storageIdentifier]:expiredStorage()
                lib.print.debug('Storage ' .. storageIdentifier .. ' expired due to too many failed payments')
            end
        end
    end
end)

---@param player table
---@param rentCost number
---@param storageIdentifier number
function RentStorage(player, rentCost, storageIdentifier)
    local playerMoney = player.getAccount('bank').money
    if playerMoney >= rentCost then
        player.removeAccountMoney('bank', rentCost, ('Rented storage %s'):format(storageIdentifier))

        if config.societyPayments then
            AddMoneyToSociety(config.society, rentCost, player.identifier)
        end

        return true
    end

    return false
end

---@param player table
---@param storageCost number
---@param storageIdentifier number
function BuyStorage(player, storageCost, storageIdentifier)
    local playerMoney = player.getAccount('bank').money
    if playerMoney >= storageCost then
        player.removeAccountMoney('bank', storageCost, ('Bought storage %s'):format(storageIdentifier))

        if config.societyPayments then
            AddMoneyToSociety(config.society, storageCost, player.identifier)
        end

        return true
    end

    return false
end

-- Helper function to handle payments for specific frameworks
---@param playerIdentifier string
---@param storageIdentifier number
---@param rentCost number
---@param rentDays number
function HandlePayment(playerIdentifier, storageIdentifier, rentCost, rentDays)
    local player = Framework.GetPlayerFromIdentifier(playerIdentifier)

    -- Player is online
    if player then
        lib.print.debug('Player ' .. playerIdentifier .. ' is online')
        ProcessOnlinePlayerPayment(player, storageIdentifier, rentCost, rentDays)
    else
        lib.print.debug('Player ' .. playerIdentifier .. ' is offline')
        ProcessOfflinePlayerPayment(playerIdentifier, storageIdentifier, rentCost, rentDays)
    end
end

-- Function to handle payments for online players
---@param player table
---@param storageIdentifier number
---@param rentCost number
---@param rentDays number
function ProcessOnlinePlayerPayment(player, storageIdentifier, rentCost, rentDays)
    if not player then return end

    local playerMoney = player.getAccount(config.paymentMethod).money
    if playerMoney >= rentCost then
        player.removeAccountMoney(config.paymentMethod, rentCost)
        MySQL.query([[
            UPDATE storages
            SET expiring_date = DATE_ADD(CURRENT_TIMESTAMP, INTERVAL ? DAY),
                failed_payments = 0,
                next_payment_attempt = NULL
            WHERE location = ?
        ]], {
            rentDays,
            storageIdentifier
        })
        Logs.HandledPayment(player.identifier, storageIdentifier, 'Online', rentCost)
        lib.print.debug('Player ' .. player.identifier .. ' has enough money to pay for storage ' .. storageIdentifier .. ' and has been charged')

        if config.societyPayments then
            AddMoneyToSociety(config.society, rentCost, player.identifier)
        end
    else
        MySQL.query([[
            UPDATE storages
            SET failed_payments = failed_payments + 1,
                next_payment_attempt = DATE_ADD(CURRENT_TIMESTAMP, INTERVAL ? DAY)
            WHERE location = ?
        ]], {
            config.paymentAttemptInterval,
            storageIdentifier
        })
        Logs.FailedPayment(player.identifier, storageIdentifier, 'Online', rentCost)
        lib.print.debug('Player ' .. player.identifier .. ' does not have enough money to pay for storage ' .. storageIdentifier .. ' and failed attempt has been added')
    end
end

-- Function to handle payments for offline players
---@param playerIdentifier string
---@param storageIdentifier number
---@param rentCost number
---@param rentDays number
function ProcessOfflinePlayerPayment(playerIdentifier, storageIdentifier, rentCost, rentDays)
    local player = GetOfflinePlayer(playerIdentifier)
    if not player then
        MySQL.query([[
            UPDATE storages
            SET failed_payments = failed_payments + 1,
                next_payment_attempt = DATE_ADD(CURRENT_TIMESTAMP, INTERVAL ? DAY)
            WHERE location = ?
        ]], {
            config.paymentAttemptInterval,
            storageIdentifier
        })
        Logs.FailedPayment(playerIdentifier, storageIdentifier, 'Offline - Player Not Found', rentCost)
        lib.print.debug('Player ' .. playerIdentifier .. ' not found for storage ' .. storageIdentifier .. ' and failed attempt has been added')
        return
    end

    local playerMoney = player.getAccount(config.paymentMethod).money
    if playerMoney >= rentCost then
        player.removeAccountMoney(config.paymentMethod, rentCost)
        MySQL.query([[
            UPDATE storages
            SET expiring_date = DATE_ADD(CURRENT_TIMESTAMP, INTERVAL ? DAY),
                failed_payments = 0,
                next_payment_attempt = NULL
            WHERE location = ?
        ]], {
            rentDays,
            storageIdentifier
        })
        Logs.HandledPayment(player.identifier, storageIdentifier, 'Offline', rentCost)
        lib.print.debug('Player ' .. playerIdentifier .. ' has enough money to pay for storage ' .. storageIdentifier .. ' and has been charged')

        if config.societyPayments then
            AddMoneyToSociety(config.society, rentCost, player.identifier)
        end
    else
        MySQL.query([[
            UPDATE storages
            SET failed_payments = failed_payments + 1,
                next_payment_attempt = DATE_ADD(CURRENT_TIMESTAMP, INTERVAL ? DAY)
            WHERE location = ?
        ]], {
            config.paymentAttemptInterval,
            storageIdentifier
        })
        Logs.FailedPayment(player.identifier, storageIdentifier, 'Offline', rentCost)
        lib.print.debug('Player ' .. playerIdentifier .. ' does not have enough money to pay for storage ' .. storageIdentifier .. ' and failed attempt has been added')
    end
end
