Config.contracts.bobcat = {
    ---@type ContractSettings
    settings = {
        disabled = false,
        chance = 10,
        price = 300,
        requiredExperience = 2000,
        expiry = 11 * 60,
        timeToStart = 3 * 60,
        minMembers = 4,
        -- maxMembers = 10,
        rewards = { crypto = 360, experience = 150 }, -- Other rewards such as items are listed below
        minPolice = 4,
    },

    duration = 45, -- minutes before reset
    image = './contracts/bobcat.png',

    -- Blip shown to the players after the contract is started
    blip = {
        coords = vector3(1397.19, -2616.77, 49.67),
        name = locale('bobcat_security'),
        sprite = 1,
        size = 0.75,
        color = 1
    },
    
    alarm = {
        coords = vector3(1376.18, -2620.07, 53.12),
        duration = 60
    },

    dispatch = {
        enabled = true,
        code = '10-68',
        title = locale('dispatch_bobcat_title'),
        message = locale('dispatch_bobcat_message'),
    },

    entryDoor = {
        model = `v_ilev_gendoor01`,
        heading = 95.0,
        requiredItem = 'thermite',
        -- It's a double door thus there are two coords defined
        locations = {
            vector3(1402.31, -2618.55, 49.83),
            vector3(1402.08, -2615.96, 49.83)
        }
    },

    securityDoor = {
        model = `v_ilev_gendoor01`,
        coords = vector4(1395.92, -2616.8, 49.68, 95.0),
        requiredItem = 'thermite',
    },

    guardedArea = {
        keypad = {
            coords = vector4(1392.62, -2619.77, 49.92, 95.0),
            requiredItem = 'hack_usb',
            removeAfterUse = false
        },
        door = {
            { coords = vector3(1392.43, -2619.42, 49.82), model = `v_ilev_cd_door` },
            { coords = vector3(1392.2, -2616.83, 49.82), model = `v_ilev_cd_door` },
            { coords = vector3(1372.28, -2611.79, 49.83), model = `v_ilev_cd_door` },
            { coords = vector3(1372.05, -2609.2, 49.83), model = `v_ilev_cd_door` },
        },
        guards = {
            enabled = true,
            model = `s_m_m_chemsec_01`,
            ignorePolice = true,
            accuracy = 75, -- from 0 - 100
            combatAbility = 100, -- from 0 - 100, peds can start to run away if less than 50
            looting = {
                enabled = false, -- Players can loot the guards
                progressDuration = 5000
            },
            ---@type GuardWeapon[]
            weapons = {
                { name = 'weapon_carbinerifle', ammo = { name = 'xd_rifle_ammo', min = 60, max = 180 } },
                { name = 'weapon_combatpistol', ammo = { name = 'xd_pistol_ammo', min = 60, max = 180 } },
            },
            locations = {
                vector4(1362.13, -2611.25, 49.67, 235.11),
                vector4(1370.2, -2616.81, 49.67, 152.59),
                vector4(1363.91, -2628.13, 49.67, 332.27),
                vector4(1368.42, -2634.24, 49.67, 270.06),
                vector4(1375.44, -2630.57, 49.67, 241.23),
                vector4(1379.05, -2635.31, 49.67, 297.35),
                vector4(1385.97, -2634.27, 49.67, 0.87),
                vector4(1384.23, -2625.03, 49.67, 336.34),
                vector4(1383.56, -2617.3, 49.67, 244.83),
            },
        },
        vault = {
            coords = vector4(1377.82, -2630.05, 50.03, 4.77),
            requiredItem = 'bomb_c4',
            explosion = {
                delay = 10, -- It blows up 10 seconds after it's planted
                type = 4,
                cameraShake = 5.0
            },
            ---@type LootData
            loot = {
                rewards = {
                    ['cash'] = { item = 'dirtymoney', count = { pile = 12000, trolley = 25000 } },
                    ['gold'] = { item = 'gold_bar', count = { pile = 2, trolley = 5 } },
                },
                goldChance = 20,
                locations = {
                    {
                        coords = vector4(1380.61, -2628.9, 49.67, 8.93),
                        type = 'trolley'
                    },
                    {
                        coords = vector4(1380.92, -2627.34, 49.67, 98.35),
                        type = 'trolley'
                    },
                    {
                        coords = vector4(1380.78, -2625.74, 49.67, 98.35),
                        type = 'trolley'
                    },
                    {
                        coords = vector4(1379.47, -2623.8, 49.67, 91.06),
                        type = 'trolley'
                    },
                    {
                        coords = vector4(1376.88, -2622.61, 49.55, 192.25),
                        type = 'pile'
                    },
                    {
                        coords = vector4(1376.81, -2621.71, 49.55, 4.52),
                        type = 'pile'
                    },
                }
            },
            ---@type { model: string | number, progressDuration: integer, items: { name: string, count: integer | { min: integer, max: integer }, chance: integer?, metadata: any? }[], locations: vector4[] }[]
            weaponsLoot = {
                {
                    model = `ex_prop_crate_expl_bc`,
                    progressDuration = 7500,
                    items = {
                        { name = 'WEAPON_MOSSBERG', count = 1, chance = 50 },
                        { name = 'bandage', count = 20, chance = 50 },
                        { name = 'at_suppressor_light', count = 1, chance = 50 },
                        -- Commented out because these are only default in ox_inventory
                        { name = 'xd_pistol_ammo', count = { min = 50, max = 300 } },
                        { name = 'xd_rifle_ammo', count = { min = 50, max = 300 } },
                        { name = 'painkillers', count = { min = 1, max = 2 }, chance = 50 },
                        { name = 'bandage', count = { min = 1, max = 2 }, chance = 50 },
                    },
                    locations = {
                        vector4(1374.85, -2628.73, 49.61, 96.03)
                    }
                },
                {
                    model = `ex_prop_crate_ammo_sc`,
                    progressDuration = 7500,
                    items = {
                        { name = 'WEAPON_UZI', count = 1, chance = 50 },
                        { name = 'illheavyarmor', count = { min = 5, max = 10 }, chance = 50 },
                        { name = 'xd_rifle_ammo', count = { min = 50, max = 300 } },
                        { name = 'at_suppressor_heavy', count = 1, chance = 25 },
                        
                    },
                    locations = {
                        vector4(1374.41, -2626.11, 49.53, 96.03)
                    }
                },
                {
                    model = `ex_prop_crate_ammo_bc`,
                    progressDuration = 7500,
                    items = {
                        { name = 'illheavyarmor', count = { min = 5, max = 10 }, chance = 50 },
                        { name = 'painkillers', count = { min = 1, max = 2 }, chance = 50 },
                        { name = 'bandage', count = { min = 1, max = 2 }, chance = 50 },
                    },
                    locations = {
                        vector4(1374.17, -2623.63, 49.65, 96.03)
                    }
                },
                {
                    model = `ex_prop_crate_expl_sc`,
                    progressDuration = 7500,
                    items = {
                        { name = 'xd_rifle_ammo', count = { min = 50, max = 300 } },
                        { name = 'painkillers', count = { min = 1, max = 2 }, chance = 50 },
                        { name = 'bandage', count = { min = 1, max = 2 }, chance = 50 },
                    },
                    locations = {
                        vector4(1374.09, -2620.9, 49.74, 96.03)
                    }
                },
            }
        }
    },

    removeProps = {
        -- { coords = vector3(891.336243, -2120.566895, 31.176243), model = `prop_cash_trolly` },
        -- { coords = vector3(891.273071, -2126.502197, 31.176243), model = `prop_cash_trolly` },
    }
}

RegisterNetEvent('lunar_contracts:bobcat:bombPlanted', function()
    local config = Config.contracts.bobcat
    local delay = config.guardedArea.vault.explosion.delay * 1000 + 2866
    Wait(delay)
    local interiorId = GetInteriorAtCoords(1378.81700000, -2622.62700000, 48.67468000)
    ActivateInteriorEntitySet(interiorId, "k4mb1_bobcat_start")
    DeactivateInteriorEntitySet(interiorId, "k4mb1_bobcat_start")
    ActivateInteriorEntitySet(interiorId, "k4mb1_bobcat_destroyed")
    RefreshInterior(interiorId)
end)


RegisterNetEvent('lunar_contracts:bobcat:reset', function()
    local interiorId = GetInteriorAtCoords(1378.81700000, -2622.62700000, 48.67468000)
    DeactivateInteriorEntitySet(interiorId, "k4mb1_bobcat_destroyed")
    ActivateInteriorEntitySet(interiorId, "k4mb1_bobcat_start")
    RefreshInterior(interiorId)
end)