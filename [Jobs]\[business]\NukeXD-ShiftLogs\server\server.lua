local ESX = exports['es_extended']:getSharedObject()
local onDutyTimes = {}
-- ██     ██ ███████ ██████  ██   ██  ██████   ██████  ██   ██
-- ██     ██ ██      ██   ██ ██   ██ ██    ██ ██    ██ ██  ██
-- ██  █  ██ █████   ██████  ███████ ██    ██ ██    ██ █████
-- ██ ███ ██ ██      ██   ██ ██   ██ ██    ██ ██    ██ ██  ██
--  ███ ███  ███████ ██████  ██   ██  ██████   ██████  ██   ██
local JobWebhooks = {
    police = 'https://discord.com/api/webhooks/1170938552037675078/3_8H3gDvK8udmFMxBM0mY17AZo1FSuTTvFNVPiB59IborQH-NxozqxlGUKtccwGJm63i',
    ambulance = 'https://discord.com/api/webhooks/1339486073817927683/oM1Mm4G2rgRMJgEZsjKyOIqvAOPif-rtE2GKaQEPy2AQuyUHC0rvJHS9ODbrL15pq1Hm',
    lplucker = 'https://discord.com/api/webhooks/1345170633641689098/qY0BZuePjMjHlQJ0GfqJd-FTSbPFFBdv3qdPwwgpgU5K2mxDJ5EJ8D9CKb5O4qDsrlRo',
    crumblcafe = 'https://discord.com/api/webhooks/1342953335468200049/y8VaQaBXkpswKKqpEQY6TthHbqp3Ab6kUuiiC4dTeYkAkfWanX3yJphGxLS9HxQ8K_aV',
    harmony = 'https://discord.com/api/webhooks/1370209272973234177/',
    -- hayesauto = 'https://discord.com/api/webhooks/1282086234503643237/Ah9fUs3suu4Jao_Wb24-Xtk1RZxOCiBWZXkoQrOHkhm_vDvoJ8ftoVVx5z2IqfnHQrNn',
    ladysdallas = 'https://discord.com/api/webhooks/1347593961732182107/0N2izHjvMFG_1xZLt8XD_Il0boE0tws7OOPIE9xY26x7cwP7NSt0Kth5m6I3sxrk1TO7',
    redline = 'https://discord.com/api/webhooks/1350329845028229192/0l69K1z9syhlFF9Bp9wvArM5A8khfNWR4x8_rv88ys4yv0JNuFlfyOy53bjdHNjc-WBP',
    jaysautoshop = 'https://discord.com/api/webhooks/1352096430759874580/qYLN_1EAvl-ARD72Ca2u734c_deMl1CKg9TTiNp56h9WOzp1ByIgsWmbdOneE3Hqktdo',
    tacofarmer = 'https://discord.com/api/webhooks/1345170633641689098/qY0BZuePjMjHlQJ0GfqJd-FTSbPFFBdv3qdPwwgpgU5K2mxDJ5EJ8D9CKb5O4qDsrlRo',
    vapeshop = 'https://discord.com/api/webhooks/1355388974046122125/NfnLIioSRAsqK8nMevrenTEQO9_6R9D2CfcSo3_2PEmZwWQBBaV7t0h1rhUZC2H3RNhL',
    thebowlingbean = 'https://discord.com/api/webhooks/1357874464649383987/frs7CURvmiR9pUpNgLM7Hxk86oTR5hE7YvA9W408skZjstl1Rid2nrbKFXJNbfAJ_SAN',
    nailsalon = 'https://discordapp.com/api/webhooks/1362902111871959051/SIZYZ_A3i5kqlD7YWAuAHuTqFlKUszCIkfMDRij-3dkdYoXd9HeD3hDGDTGBIOyU-1zG',
    paradise = 'https://discord.com/api/webhooks/1347593961732182107/0N2izHjvMFG_1xZLt8XD_Il0boE0tws7OOPIE9xY26x7cwP7NSt0Kth5m6I3sxrk1TO7',
    catcafe = 'https://discord.com/api/webhooks/1399233903759458404/LzdeayrjkSMHyl5DZgME2kI34wedvmtZ8j5DMAQz0LohNF_zJQSu4JFBJBrqqHk-7lrS',
    gpub = 'https://discord.com/api/webhooks/1363356092721139822/mfm3ZLUis7CQD8cz0L-jGqfi_TFeBFyxTRXVNdv2Yg7fce30z__7oTVNVU-1eq4ioIjo',
    rexdiner = 'https://discord.com/api/webhooks/1368260630871670944/vh9SNfIeOZIzDC8x-v-vvAxhw8f2Y3Hc-6TzhjAftTKhVbIntzF51o577vr8gC_oycyM',
    benny = 'https://discord.com/api/webhooks/1368597855392235611/735kSnRZMQF97gUOoJWVcEhtHqAtwqxhXuKCVzDukcQUb6hVUGhUM1xNPc5VxFncKTsd',
    hotbox = 'https://discord.com/api/webhooks/1405723461808164915/mEZnpM39e8Ms9oTt7uBaPK3l5pkGQhrfXnLb0ctYqi-L5c_LfB9JSqloL81dRZeOrHKS',
    burgershot = 'https://discord.com/api/webhooks/1377457278579507260/2lHjRYFBYePWVLiVKA7nylQw8shxFr_xFDIBxDhKB4zvgB1CsSBUFdFEuGL68uXbi_aa',
    puffpuff = 'https://discord.com/api/webhooks/1355388974046122125/NfnLIioSRAsqK8nMevrenTEQO9_6R9D2CfcSo3_2PEmZwWQBBaV7t0h1rhUZC2H3RNhL',
    bahamamamas = "",
    bushplanet = 'https://discord.com/api/webhooks/1282086234503643237/Ah9fUs3suu4Jao_Wb24-Xtk1RZxOCiBWZXkoQrOHkhm_vDvoJ8ftoVVx5z2IqfnHQrNn',
    galaxy = 'https://discord.com/api/webhooks/1387784323951759501/kneruq2RDoc957PSaY_LslB51Ux5ZWH2XOKo9V7WTDDNfGnjI7e2MJxe12I1KhmA5D4g',
    bcso = 'https://discord.com/api/webhooks/1395084283831386233/8JTG90n_h2KI4hyXvlyOssbCoQPQT41Aw-L_BXvUeSht3mIZQAJ6pHvrtw0f0ySuNn8j',
    sasp = 'https://discord.com/api/webhooks/1395084759897477191/kehg4qVbmj1XWrKB-5nttKLIgCY0cdC0kxtB1fcTfJIlBKW4ywkAsDlhSRcgutr6JifZ',
    paradise_beach = 'https://discord.com/api/webhooks/1397621431571058689/6lLREFA9_bu_U_JV4UCuokk_IdpzWhRH_JykvQqBTwyH-oHAvtWccGvAqHG9Adlx9u_k',
    cloud9 = 'https://discord.com/api/webhooks/1365067812355309600/oZyFA8H4SbWSEy9KZUNoNGON0gBp_-2nYJIYvjswxEbC7UvJD8D0yiDrJgxN0PFW0pkv',
    whitewidow = 'https://discord.com/api/webhooks/1412610262485176351/O2KFeki0Axq5oB0xjIIhMXBgnfsBhxxgV224A8nTy2IyGS66CtpVnVbvSItC46HL_HUX',
}

function sendDutyTimeWebhook(src, playerName, jobName, jobLabel, playerDropped)
    local endTime = os.time()
    local timeOnDuty = os.difftime(endTime, onDutyTimes[src])
    local xPlayer = ESX.GetPlayerFromId(src)
    local dutyStatus = "Off Duty"
    if playerDropped then
        dutyStatus = "Off Duty - Player Left"
    end
    sendToDiscord(jobName, playerName, nil, timeOnDuty, nil, dutyStatus, 16711680, jobLabel)
end

RegisterNetEvent('nukexd_businesses:toggleDuty')
AddEventHandler('nukexd_businesses:toggleDuty', function(jobName)
    local src = source
    local xPlayer = ESX.GetPlayerFromId(src)
    if not xPlayer then return end

    local currentJobName = xPlayer.getJob().name
    local jobLabel = xPlayer.getJob().label or currentJobName
    local playerName = xPlayer.getName()

    local targetJobName = jobName or currentJobName

    if Config.Jobs[targetJobName] and JobWebhooks[targetJobName] then
        if onDutyTimes[src] then
            sendDutyTimeWebhook(src, playerName, targetJobName, jobLabel)
            onDutyTimes[src] = nil
        else
            onDutyTimes[src] = os.time()
            sendToDiscord(targetJobName, playerName, nil, nil, nil, "On Duty", 65280, jobLabel)
        end
    end
end)

RegisterNetEvent('esx_dutylogger:sendLoginWebhook')
AddEventHandler('esx_dutylogger:sendLoginWebhook', function(jobName)
    local src = source
    local xPlayer = ESX.GetPlayerFromId(src)
    if not xPlayer then return end

    local playerName = xPlayer.getName()
    local jobLabel = xPlayer.getJob().label or jobName

    if JobWebhooks[jobName] then
        local customMessage = "**Player Name:**\n`" .. playerName .. "`\n"
        customMessage = customMessage .. "**Job:**\n`" .. jobLabel .. "`\n"
        customMessage = customMessage .. "\n" .. playerName .. " has loaded into the server and was put on duty."
        sendToDiscord(jobName, playerName, nil, nil, customMessage, "Player Logged In", 65280)
    end
end)

RegisterNetEvent('esx_dutylogger:setInitialDuty')
AddEventHandler('esx_dutylogger:setInitialDuty', function(isOnDuty)
    local src = source
    if isOnDuty then
        onDutyTimes[src] = os.time()
    else
        onDutyTimes[src] = nil
    end
end)

RegisterNetEvent('SetInitialDutyStatus')
AddEventHandler('SetInitialDutyStatus', function(isOnDuty)
    local src = source
    if isOnDuty then
        onDutyTimes[src] = os.time()
    else
        onDutyTimes[src] = nil
    end
end)

RegisterNetEvent('SendOnDutyWebhook')
AddEventHandler('SendOnDutyWebhook', function(jobName)
    local src = source
    local xPlayer = ESX.GetPlayerFromId(src)
    if not xPlayer then return end

    local playerName = xPlayer.getName()
    local jobLabel = xPlayer.getJob().label or jobName

    if JobWebhooks[jobName] then
        local customMessage = "**Player Name:**\n`" .. playerName .. "`\n"
        customMessage = customMessage .. "**Job:**\n`" .. jobLabel .. "`\n"
        customMessage = customMessage .. "\n" .. playerName .. " has loaded into the server and was put on duty."
        sendToDiscord(jobName, playerName, nil, nil, customMessage, "Player Logged In", 65280, jobLabel)
    end
end)

AddEventHandler('playerDropped', function(reason)
    local src = source
    if onDutyTimes[src] then
        local xPlayer = ESX.GetPlayerFromId(src)
        if xPlayer then
            local playerName = xPlayer.getName()
            local jobName = xPlayer.getJob().name
            local jobLabel = xPlayer.getJob().label or jobName
            sendDutyTimeWebhook(src, playerName, jobName, jobLabel, true)
        end
        onDutyTimes[src] = nil
    end
end)

function sendToDiscord(jobName, playerName, callsign, timeOnDuty, customMessage, dutyStatus, color, jobLabel)
    local webhook = JobWebhooks[jobName]
    if not webhook or webhook == 'CHANGEME' then return end

    local title = "Duty Status Update"
    if dutyStatus then
        title = title .. " (" .. dutyStatus .. ")"
    end

    local message = customMessage or ("**Player Name:**\n`" .. playerName .. "`\n" ..
        "**Job:**\n`" .. (jobLabel or jobName) .. "`\n")

    if timeOnDuty then
        local hours = math.floor(timeOnDuty / 3600)
        local minutes = math.floor((timeOnDuty % 3600) / 60)
        local seconds = timeOnDuty % 60
        message = message .. "**Time on Duty:**\n`" .. hours .. "H " .. minutes .. "M " .. seconds .. "S`"
    end

    local currentDateTime = os.date("%m-%d-%Y %H:%M:%S")
    local connect = {
        {
            ["color"] = color or 255,
            ["title"] = title,
            ["description"] = message,
            ["footer"] = {
                ["icon_url"] = "https://i.imgur.com/E1QXcvl.gif",
                ["text"] = "Hollywood Hills RP | " .. currentDateTime,
            },
        }
    }

    PerformHttpRequest(webhook, function(err, text, headers) end, 'POST',
        json.encode({
            username = 'Hollywood Hills RP | Duty Status',
            embeds = connect,
            avatar_url = 'https://i.imgur.com/E1QXcvl.gif'
        }),
        { ['Content-Type'] = 'application/json' })
end
