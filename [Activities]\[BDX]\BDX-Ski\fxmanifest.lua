shared_scripts { '@FiniAC/fini_events.js', '@FiniAC/fini_events.lua' }

fx_version 'cerulean'
game 'gta5'

name 'BDX-Ski'
description 'Ski Career'
author 'Bodhix'
version '1.0.1'

lua54 'yes'

shared_scripts {
  'config.lua',
}

client_scripts {
  'client/ski.lua',
}

server_scripts {
  'server/*.lua',
}

files {
  'stream/bodhix@<EMAIL>',
  'stream/bodhix@<EMAIL>',
  'server/version.json',
  'nui/Main.css',
  'nui/Main.html',
  'nui/fonts/*.ttf',
  'nui/customs/*.png',
  'html/Land.MP3',
  'html/Jump.MP3',
  'html/Speed.MP3',
  'html/Grab.MP3',
  'html/Break.MP3',
}

data_file('DLC_ITYP_REQUEST')('stream/skis.ytyp')

ui_page 'nui/Main.html'

nui_page 'nui/Main.html'

escrow_ignore {
  'config.lua',
}
dependency '/assetpacks'