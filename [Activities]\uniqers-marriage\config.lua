UniqersMarriage = {}
UniqersMarriage.DatabaseSelect = "oxmysql" --change your mysql service oxmysql or mysql-async or ghmattimysql
UniqersMarriage.Framework = "esx" -- "qbcore" or "esx" or "qbus" or "oldesx" - if you need custom framework contact us  https://discord.gg/MyrGhbf9tM
UniqersMarriage.CoreName = "ESX" -- for qbcore = "QBCore", for qbus = "QBCore", for esx = "ESX", forold esx = "ESX", for qbcore or esx variation like ASDCore, Blabla core = "BLABLACore",
UniqersMarriage.CoreName2 = "es_extended" -- for qbcore = "qb-core", for qbus = "QBCore:GetObject", for esx = "es_extended", forold esx = "esx:getSharedObject", for qbcore or esx variation like ASDCore, Blabla core = "blabla-core",
UniqersMarriage.Notify = "esx" -- if u use custom notify write "custom" if u dont write "qbcore" - "esx" -  what u using
UniqersMarriage.Inventory = "esx" -- select your inventory - qb-inventory = "qbcore" or other qbcore inventory = "otherqb" or esx-inventory = "esx"  or other esx inventory = "otheresx"
UniqersMarriage.Clothes = "illenum-apperance" -- select your clothes script "qb-clothing" or "esx-skinchanger" or "fivem-apperance" or "illenum-apperance" or "rcore-clothing" or "tgiann-clothing" or "custom" if u use custom add your custom code below the function function customclothes
UniqersMarriage.ForEsxAvatar = "steam" --- select your for esx framework avatar system "steam" or "discord" or ""
UniqersMarriage.TargetSystem = "ox" -- if you select "ox" working for qbcore and esx to if u select "qb-target" only work for qbcore.
UniqersMarriage.Society = false -- if you want all married pay going to qb-management or esx society set true if u dont want set false.
UniqersMarriage.ClothingChange = true -- if true players can change clothing with target if false players cant change clothings.
UniqersMarriage.NeedTwoRing = true -- if true aach couple needs 1 ring. If false, only the person proposing will need 1 ring.
UniqersMarriage.UseRing = true -- if true copules can use ring if false cant this mean gps at the tag map. 
UniqersMarriage.WeddingPay = 250 -- wedding pay amount
UniqersMarriage.WeddingClothingPay = 250 -- wedding clothes pay amount
UniqersMarriage.PreparationTime = 60 -- minutes
UniqersMarriage.MarriedNotifyVisible = true -- global married notify true or false 
UniqersMarriage.MarriedNotify = 30 -- 30 >> 3 seconds
UniqersMarriage.LevelUpNotify = 40 -- 30 >> 3 seconds
UniqersMarriage.SpouseTopEmojiVisible = true -- your spouse emoji at top head if is true visible if false hidden.
UniqersMarriage.SpouseBlipVisible = true -- your spouse emoji at top head if is true visible if false hidden.
UniqersMarriage.BlipType = "normal" -- "hard" for big server cpu  "normal" for normal
UniqersMarriage.SpouseTopEmoji = "❤️" -- your spouse emoji at top head
UniqersMarriage.SpouseTopEmojiTopValue = 1 -- your spouse emoji at top head top value high number top down number for bottom player a head
UniqersMarriage.PutTheRing = "putthering" -- ring can be put finger your character
UniqersMarriage.PutTheRingOff = "puttheringoff" -- ring can be out finger your character
UniqersMarriage.ManAnimName = "amb@world_human_bum_wash@male@low@idle_a" -- man first anim name
UniqersMarriage.ManAnimName2 = "idle_a" -- man first anim name 2
UniqersMarriage.GirlAnimName = "anim@heists@ornate_bank@chat_manager" --girl first anim name
UniqersMarriage.GirlAnimName2 = "fail" -- -girl first anim name 2
UniqersMarriage.ManRingFirstAnimName = "dollie_mods@propose_001b" -- man first ring used anim name 1
UniqersMarriage.ManRingFirstAnimName2 = "propose_001b" -- man first ring used anim name 2
UniqersMarriage.PutAnimName = "missmic4" -- when use ring anim name 1
UniqersMarriage.PutAnimName2 = "michael_tux_fidget" --when use ring anim name 2
UniqersMarriage.ConsensualDivorce = "yes" -- if no, no need spouse with you for divorce if yes, need online and with you for spouse
UniqersMarriage.TargetIcon1 = "fas fa-heart" --- you can change target icon https://fontawesome.com/v5/search
UniqersMarriage.TargetIcon2 = "fas fa-heart" --- you can change target icon https://fontawesome.com/v5/search
UniqersMarriage.TargetIcon3 = "fas fa-tshirt" --- you can change target icon https://fontawesome.com/v5/search
UniqersMarriage.TargetIcon4 = "fas fa-signature" --- you can change target icon https://fontawesome.com/v5/search
UniqersMarriage.NPCFunction = "yes" -- If you say "yes". The player and the NPC in the priest job can use the marriage and divorce functions. /marriage and divorce command for the player. If you say "no", the NPC cannot use it and everything related to the NPC will be destroyed and everything will remain with the player in the priest profession.
UniqersMarriage.JobsRequest = "no" -- If you set it to yes, the marriage request accepted by priest job. if "no" npc doing everything.
UniqersMarriage.CommandsJobs = "yes" -- priest jobs can use divorce and marriage command
UniqersMarriage.StaffPermissionNames = { -- write your staff names for qbcore like "admin" for esx like group name "superadmin" 
    "admin",
	"superadmin",
	-- "others", -- add more
}
UniqersMarriage.JobsNames = { -- write job name send announce
    "police",
	"anan",
	-- "others", -- add more	
}
UniqersMarriage.offlinepagecommand = "marriedadmin" -- opening offline admin page 
UniqersMarriage.DivorceCommand = "divorce" -- divorce command name
UniqersMarriage.MarriageCommand = "marriage" -- marriage command name
UniqersMarriage.PriestPanelCommand = "priestpanel" -- open priest job panel.
UniqersMarriage.MarriedPanelCommand = "marriedpanel" -- maried panel command
UniqersMarriage.BuyRingCommand = "buyring" -- Ring shopping page command name
UniqersMarriage.Showminihudmarriage = "showuiwedding" -- show minihud
UniqersMarriage.Offminihudmarriage = "offuiwedding" -- hide mini hud
UniqersMarriage.EditUiCommand = "edituiwedding" -- hide mini hud
UniqersMarriage.ResetUiCommand = "resetuiwedding" -- hide mini hud
UniqersMarriage.RingPay = 5000 -- ring pay amount
UniqersMarriage.DeliveryCargoBlipName = "Ring Cargo" -- cargo vehicle blip name
UniqersMarriage.RingBoxProp = "pata_freevalentinesday" -- ring box prop name
UniqersMarriage.RingCargoCarModel = "youga2" -- ring cargo car model
UniqersMarriage.RingCargoCarPlate = "RING" -- ring cargo car plate name
UniqersMarriage.MoneyProp = "prop_anim_cash_note_b" -- money prop name
UniqersMarriage.ActiveDiscordLogs = true -- if u want use discord log write true if u dont want write false
UniqersMarriage.DiscordWebhook = "https://discord.com/api/webhooks/1398374489808441375/EeDEOpgfsu7Ea2T707Nu8E6WTidv-hvLZbdSu7G9PpS18GA8To2xVDyCeLTjBKabjzUz" -- your webhook url
UniqersMarriage.TitleWebhook = "www.uniqers-scripts.tebex.io Best Script" -- info title bottom
UniqersMarriage.WebhookPhoto = "" -- webhook profile picture
UniqersMarriage.DiscordBotToken = "MTE4Mjk2MDc0MDY3NjY3NzcxMg.GRlj0W.kRRtWlAJyUOpYEVmQI3BV8TNhXn3nbFA-RskkY" -- discord bot token
UniqersMarriage.BadgeRewards = {
                ["1"] = 1000, -- month
                ["3"] = 2500, -- month
                ["6"] = 5000, -- month
                ["12"] = 10000 -- year
} -- players marriage badge how much won for each badge.
UniqersMarriage.Adversting = true -- marriage adversting for all players server true or false
UniqersMarriage.AdverstingHours = 3 -- hours display marriage adversting for all players for server
UniqersMarriage.Refreshtime = {
    ["spousetag"] = 5000,
}
UniqersMarriage.LastLevel = 10
UniqersMarriage.LastXP = 1000
UniqersMarriage.Levels = {
    [1] = {
        text = "1 Level",
        level = 1,
        xp = 100,
        money = 100
    },
    [2] = {
        text = "2 Level",
        level = 2,
        xp = 200,
        money = 200
    },
    [3] = {
        text = "3 Level",
        level = 3,
        xp = 300,
        money = 300
    },
    [4] = {
        text = "4 Level",
        level = 4,
        xp = 400,
        money = 400
    },
    [5] = {
        text = "5 Level",
        level = 5,
        xp = 500,
        money = 500
    },
    [6] = {
        text = "6 Level",
        level = 6,
        xp = 600,
        money = 600
    },
    [7] = {
        text = "7 Level",
        level = 7,
        xp = 700,
        money = 700
    },
    [8] = {
        text = "8 Level",
        level = 8,
        xp = 800,
        money = 800
    },
    [9] = {
        text = "9 Level",
        level = 9,
        xp = 900,
        money = 900
    },
    [10] = {
        text = "10 Level",
        level = 10,
        xp = 1000,
        money = 1000
    },
}

UniqersMarriage.Locations = {
    -- City Priest AND target functions and blip functions
    ['citypriest'] = {
        ['label'] = 'Marriage City', -- blip name
        ['coordsdist'] = vector3(-766.6118, -23.75499, 41.080276), -- coords distance
		['coords'] = vector4(-766.6118, -23.75499, 41.080276, 211.11624), --- npc priest coords
        ['ped'] = 'cs_priest', -- priest model name
        ['scenario'] = 'WORLD_HUMAN_GUARD_STAND', -- priest scenario name >> example is here >> https://github.com/DioneB/gtav-scenarios
        ['showblip'] = true, -- blips show or off
        ['blipsprite'] = 621, -- blip sprite
        ['blipscale'] = 0.7, -- blip scale
		['blipdisplay'] = 4, -- blipd display
        ['blipcolor'] = 1, -- blip color https://docs.fivem.net/docs/game-references/blips/
        ['garagelocations'] = {
          vector4(-772.6629, 45.183414, 49.290657, 73.097679),
        },		
    },
    -- Town Priest AND target functions and blip functions
    ['townpriest'] = {
        ['label'] = 'Marriage Town',
		['coordsdist'] = vector3(-330.0823, 6154.3388, 32.313293),
        ['coords'] = vector4(-330.0823, 6154.3388, 32.313293, 46.612247),
        ['ped'] = 'cs_priest',
        ['scenario'] = 'WORLD_HUMAN_GUARD_STAND',
        ['showblip'] = true,
        ['blipsprite'] = 621,
        ['blipscale'] = 0.7,
		['blipdisplay'] = 4,
        ['blipcolor'] = 1,
        ['garagelocations'] = {
           vector4(-323.2235, 6128.6816, 31.492708, 47.394233),
        },			
    }
	-- you can add mooore
    -- ['townpriest'] = {
        -- ['label'] = 'Marriage Town',
		-- ['coordsdist'] = vector3(-330.0823, 6154.3388, 32.313293),
        -- ['coords'] = vector4(-330.0823, 6154.3388, 32.313293, 46.612247),
        -- ['ped'] = 'cs_priest',
        -- ['scenario'] = 'WORLD_HUMAN_GUARD_STAND',
        -- ['showblip'] = true,
        -- ['blipsprite'] = 621,
        -- ['blipscale'] = 0.7,
		-- ['blipdisplay'] = 4,
        -- ['blipcolor'] = 1
    -- }	
}

UniqersMarriage.PermsVehicle = { -- Grade is key, don't add same vehicle in multiple grades. Higher rank can see lower
    [0] = {
        ['romero'] = 'Standart'
    }
}


local function customclothes(manwear, womenwear, manringwear, womenringwear, unwearring)
if manwear then
-- your custom clothes script load clothes trigger like this TriggerEvent('qb-clothing:client:loadOutfit', UniqersMarriage.QBCoreManOutfit)
end
if womenwear then
-- your custom clothes script load clothes trigger like this TriggerEvent('qb-clothing:client:loadOutfit', UniqersMarriage.QBCoreGirlOutfit)
end
if manringwear then
-- your custom clothes script load clothes trigger like this TriggerEvent('qb-clothing:client:loadOutfit', UniqersMarriage.QBCoreManRing)
end
if womenringwear then
-- your custom clothes script load clothes trigger like this TriggerEvent('qb-clothing:client:loadOutfit', UniqersMarriage.QBCoreGirlRing)
end
if unwearring then
-- your custom clothes script load clothes trigger like this TriggerEvent('qb-clothing:client:loadOutfit', UniqersMarriage.QBCoreRingOff)
end
end 

UniqersMarriage.ItemUpdateType = "reload" -- reload or remove items update type if u select reload item will remove and will add 
UniqersMarriage.IDCard = true -- if u use this item and if u want update this item write true if dont want write false
UniqersMarriage.IDCardName = "id_card" -- this item name
UniqersMarriage.DriverCard = true -- if u use this item and if u want update this item write true if dont want write false
UniqersMarriage.DriverCardName = "driver_license"  -- this item name
UniqersMarriage.Temporary = false -- if u use this item and if u want update this item write true if dont want write false
UniqersMarriage.TemporaryName = "temporary"  -- this item name
UniqersMarriage.LawyerPass = false -- if u use this item and if u want update this item write true if dont want write false
UniqersMarriage.LawyerPassName = "lawyerpass"  -- this item name
UniqersMarriage.WeaponLicense = false -- if u use this item and if u want update this item write true if dont want write false
UniqersMarriage.WeaponLicenseName = "weaponlicense"  -- this item name


-- QBCore Ring Data
UniqersMarriage.QBCoreRing = {
    ManRing = {
        outfitData = {
            ['accessory'] = {
                item = 1,
                texture = 0
            },
        }
    },
    GirlRing = {
        outfitData = {
            ['accessory'] = {
                item = 1,
                texture = 0
            },
        }
    },
    RingOff = {
        outfitData = {
            ['accessory'] = {
                item = 0,
                texture = 0
            },
        }
    }
}

-- QBCore Outfit Data
UniqersMarriage.QBCoreOutfit = {
    ManOutfit = {
        outfitData = {
            ['arms'] = { item = 1, texture = 0 },
            ['t-shirt'] = { item = 3, texture = 0 },
            ['torso2'] = { item = 4, texture = 0 },
            ['pants'] = { item = 24, texture = 0 },
            ['shoes'] = { item = 10, texture = 0 },
            ['mask'] = { item = 0, texture = 0 },
            ['hat'] = { item = -1, texture = 0 },
            ['glass'] = { item = 0, texture = 0 },
            ['bag'] = { item = 0, texture = 0 },
            ['decals'] = { item = 0, texture = 0 },
            ['accessory'] = { item = 0, texture = 0 },
            ['vest'] = { item = 0, texture = 0 }
        }
    },
    GirlOutfit = {
        outfitData = {
            ['arms'] = { item = 12, texture = 0 },
            ['t-shirt'] = { item = 2, texture = 0 },
            ['torso2'] = { item = 26, texture = 0 },
            ['pants'] = { item = 36, texture = 0 },
            ['shoes'] = { item = 15, texture = 14 },
            ['mask'] = { item = 0, texture = 0 },
            ['hat'] = { item = -1, texture = 0 },
            ['glass'] = { item = 0, texture = 0 },
            ['bag'] = { item = 0, texture = 0 },
            ['decals'] = { item = 0, texture = 0 },
            ['accessory'] = { item = 0, texture = 0 },
            ['vest'] = { item = 0, texture = 0 }
        }
    }
}

-- ESX Ring and Clothes Data
UniqersMarriage.ESX = {
    RingClothes = {
        male = { ['chain_1'] = 1 },
        female = { ['chain_1'] = 1 }
    },
    RingOff = { ['chain_1'] = 0 },
    Clothes = {
        male = {
            ['arms_1'] = 1,   ['tshirt_1'] = 3,
            ['torso_1']  = 4,  ['torso_2']  = 0, 
            ['pants_1']  = 28,
            ['pants_2']  = 0, ['chain_1']  = 0,
            ['shoes_1'] = 10, ['shoes_2'] = 0,
            ['mask_1'] = 0, ['hat_1']  = -1,
            ['glass_1'] = 0, ['bag_1'] = 0,
            ['decals_1'] = 0, ['accessory_1'] = 0,
            ['vest_1'] = 0
        },
        female = {
            ['arms_1'] = 12, ['arms_2'] = 0,
            ['tshirt_1'] = 2,
            ['torso_1'] = 26, ['torso_2'] = 0,
            ['pants_1'] = 36, ['pants_2'] = 0,
            ['shoes_1'] = 15, ['shoes_2'] = 14,
            ['mask_1'] = 0, ['hat_1'] = -1,
            ['glass_1'] = 0, ['bag_1'] = 0,
            ['decals_1'] = 0, ['accessory_1'] = 0,
            ['vest_1'] = 0, ['chain_1'] = 0
        }
    }
}

-- FivemAppearance and İllenum Apperance Data
UniqersMarriage.FivemAppearance = {
    RingUse = {
        ManClothes = {
            ["components"] = {
                { ["drawable"] = 1, ["texture"] = 0, ["component_id"] = 7 }
            }
        },
        GirlClothes = {
            ["components"] = {
                { ["drawable"] = 1, ["texture"] = 0, ["component_id"] = 7 }
            }
        },
        RingOff = {
            ["components"] = {
                { ["drawable"] = 0, ["texture"] = 0, ["component_id"] = 7 }
            }
        }
    },
    Outfit = {
        ManClothes = {
            ["components"] = {
                { ["drawable"] = 0, ["texture"] = 0, ["component_id"] = 3 },
                { ["drawable"] = 4, ["texture"] = 0, ["component_id"] = 4 },
                { ["drawable"] = 10, ["texture"] = 0, ["component_id"] = 6 },
                { ["drawable"] = 3, ["texture"] = 0, ["component_id"] = 8 },
                { ["drawable"] = 0, ["texture"] = 0, ["component_id"] = 9 },
                { ["drawable"] = 10, ["texture"] = 0, ["component_id"] = 11 }
            }
        },
        GirlClothes = {
            ["components"] = {
                { ["drawable"] = 0, ["texture"] = 0, ["component_id"] = 3 },
                { ["drawable"] = 4, ["texture"] = 0, ["component_id"] = 4 },
                { ["drawable"] = 10, ["texture"] = 0, ["component_id"] = 6 },
                { ["drawable"] = 3, ["texture"] = 0, ["component_id"] = 8 },
                { ["drawable"] = 0, ["texture"] = 0, ["component_id"] = 9 },
                { ["drawable"] = 10, ["texture"] = 0, ["component_id"] = 11 }
            }
        }
    }
}

UniqersMarriage.RandomAvatars = { -- if steam avatar is not available, we will use Initials avatar
    [1] = {background = 'ffffff', color = '308BFF'},
    [2] = {background = 'E2E519', color = '222'},
    [3] = {background = 'FF306E', color = 'ffffff'},
    [4] = {background = 'F000FF', color = 'ffffff'},
    [5] = {background = '2F2730', color = 'ffffff'},
}


function JobCheck(job)
  for k, v in pairs(UniqersMarriage.JobsNames) do
      if job == v then
    return true
      end
  end
  return false
end

function AdminCheck(admin)
  for k, v in pairs(UniqersMarriage.StaffPermissionNames) do
      if admin == v then
    return true
      end
  end
  return false
end

UniqersMarriage.Translate = {
    youwon = "Congratulations, you have won a prize thanks to your marriage, total amount:",
    notmarried2 = "You are not married!",
    nomorebadge = "There are no other rewards for you to receive at this time.",
    yearbadge = "+ year badge.",
    monthbadge = "month badge.",
    rewardinfo = "You can get your badge award for the 1st, 3rd, 6th and 1st anniversary of your marriage here.",
    marriagerewards = "MARRIAGE REWARDS",
    claimreward = "CLAIM REWARD", 
    divorcetext = "Divorce 💔",
    engagedtext = "Engaged 💍",
    marriagetext = "Marriage ❤️",
    engagednotify = "These two beautiful people are now engaged. Congratulations to both of them! ❤️",
    weddingnotify = "They got married and we congratulate them on this happy day! ❤️",
    divorcenotify = "We didnt want to announce this sad news, but these two beautiful people are divorced. 💔",
    updateditem = "This item has been updated due to a name change.",
    custombutton = "Custom",
    customsurname = "If you want to create your surnames specially. Click 'Custom'.",
    dontwantsurname = "If you do not want the surnames to be changed, click 'No'.",
    writesurname = "Please write your new surname below and press the claim button.",
    takespousesurname = "If you want to have the same surname as your spouse, click 'yes'.",
    selectyourspouse = "Select your spouse.",
    yourdocu = "Your documents are being prepared, be here when they are ready and let's finish the process.",
    wantcancel = "If you want to cancel, you can press cancel.",
    cancelbutton = "Cancel",
    fordocument = "for the documents required for marriage and the hassle fee and start the process?",
    payamount = "250$", 
    areyouready = "Are you ready to pay ?",
    douyouagg = "Do you agree to marry",
    mrs = "Mrs",
    mr = "Mr.",
    claim = "Claim",
    getcertif = "You can get a marriage certificate by making a claim.",
    cong = "Your documents are ready. Congratulations on your marriage!",
    minleft = "min left",
    gotmarried = "got married",
    andtext = "and",
    douyouwant = "Do you want to get married?",
    divorce = "Divorce",
    doyoudivorce = "If you want to divorce, you can press Divorce.",
    alreadymarried = "You are already married, if you want a divorce, you know.",
    yesbutton = "Yes",
    nobutton = "No",
    engaged = "You have successfully engaged.",
    notcity = "Your spouse is not in city!",
    withyou = "Your spouse is not with you!",
    divorced = "You have successfully divorced.",
    married = "Congratulations, you got married.",
    canceled = "You have successfully canceled.",
    started = "Documents started to be prepared.",
    deniedrequest = "The request was denied.",
    certifnotyou = "This certificate not own you.",
    blankcert = "Blank certificate",
    notcity = "Your spouse not in city.",
    smartring = "Your spouse marked at map. via smart Ring",
    dontmoney = "You dont have a money for this.",
    noone = "No one nearby.",
    blipname = "Marriage",
    appstatus = "Application Status",
    mariageapp = "Marriage application",
	clotheschange = "Wedding dress",
	divorcename = "Change Name",
    noapp = "There are no applications in process.",
    inorder = "In order to complete the marriage procedures, your spouse must be engaged and approved.",
    yourapp = "Your application has been processed, please check the process section.",
	succesclothes = "Your clothing succes changed.",
	ringputting = "You put the ring on your finger.",
	donthaveitem = "You dont have a ring",
	donthaveitemspouse = "Your spouse dont have a ring",
	needengaged = "You have to be engaged to use the ring.",
	ringoff = "You took the ring off your finger",
	allowcommand = "You are not authorized to do this.",
	idtrue = "Enter the Male and Female IDs correctly and completely.",
	notmarried = "You cannot divorce someone who is not married.",
	requestcancel = "The marriage application was successfully rejected.",
	requestaccept = "The marriage Application was successfully accepted",
	divorcedcommand = "The divorce process is successful.",
	priesthelp = "Open priest panel.",
	offlinepagehelp = "Opening admin panel married",
	yourdocready = "Your documents ready!",
	surname = "Surname",
	whomarried = "Married",
	certifof = "Certificate of Marriage",
	thisiscertif = "This is to certify that",
	wereunited = "were united in marriage at Church of love honesty on",
	marriedpanelhelp = "Open married panel.",
	notengaged = "This couple is not engaged.",
	weddingstyle = "You must choose marriage style.",
	divorcehelp = "Player ID Man and Player ID Women like /divorce 1 2",
	weddinghelp = "Player ID Man and Player ID Women and styl and surname like /givemarriage 1 2 normal or /givemarriage 1 2 custom > UniqersScripts < this is surname",
	marriagestyle = "Marriage Style",
	customsurname = "Custom Surname", 
	manid = "Man ID",
	womenid = "Women ID", 
	ringdeliver = "Cargo employee: I wish you success on this beautiful journey... You have bought our most luxurious ring, have a good day. Here is your ring.",
	onetimering = "You can only buy a ring once at a time.",
	ringbusy = "We are currently serving our other customers, please wait.",
	orderring = "Your order has been confirmed and we are on our way. We come as quickly as possible with our most luxurious service.",
	freedelivery = "Free and Fast Delivery!",
	ringname = "Ring",
	ringprice = "5,000$",
	buyring = "Buy Now",
	justlevelup = "You just level up!",
	levelup = "New Level",
	presse = "Press -E- for open menu.",
	alreadymarried = "This couple is already married.",
    done = "DONE",
	gettingready = "Getting Ready...",
	searchdiv = "🔍 Search...",
	shortestto = "From Shortest Marriage to Longest Marriage",
	longestto = "From Longest Marriage to Shortest Marriage",
	selectoption = "Select Option",
	marriedlist = "<i class='fas fa-edit' style='background: #26262600;color: #fff;padding: 19px;border-radius: 6px;'></i> MARRIED <span>LIST</span>",
	priestpanel = "<i class='fas fa-edit' style='background: #26262600;color: #fff;padding: 19px;border-radius: 6px;'></i> PRIEST <span>PANEL</span>",
	requests = "Requests",
	marrieds = "Marrieds",
	needxpinfo = "Need xp for new level.",
	header = "Total XP",
	leveltext = "LEVEL",
	editmodeon = "EDIT MODE ACTIVE!",
	keybindsrotate = "Press UP - DOWN- LEFT- RIGHT key for change position ui wedding.",
	saveposition = "Press ENTER key for save position ui wedding.",
	defaultposition = "/resetuiwedding command for reset ui wedding position",
	cklists = "CK Lists",
	admindelete = "Delete Marrieds",
	adminadd = "ADD Marrieds",
	suredeleteadmin = "Are you sure you want to delete this marriage?",
	addmarriage = "Add Marriage",
	selectmarried = "Select Married",
	selectowner = "Select Owner",
	selectplayer = "Select Player",
	noempty = "Do not leave blank.",
	sameguy = "The same person should not marry himself",
	onlydivorced = "Only newly divorced people can use this feature once.",
	newlastname = "NEW LASTNAME",
	min3 = "Need minimum 3 character",
	nospace = "No Space",
	onlyletter = "Only Letters",
	nameinfo = "I think you just got divorced or something like that... Don't worry, there is still a long period in your life. This period will be great, maybe you will be a boss or a new doctor in the future. Come on, take the first step now and choose your new surname.",
	changenametext = "Change Name",
	hours = "", -- If you leave it blank it looks like this. 03:25 If you fill it looks like this. > the text you filled out Let's say you wrote hours and minutes > 03hour:23:minutes. or you wrote h and m. > 03h:23:m
	minutes = "", -- -- If you leave it blank it looks like this. 03:25 If you fill it looks like this. > the text you filled out Let's say you wrote hours and minutes > 03hour:23:minutes. or you wrote h and m. > 03h:23:m	
}



function UniqersMarriageNotifyClient(text,status)
if UniqersMarriage.Notify == "qbcore" then
    TriggerEvent('QBCore:Notify', text, status)
elseif UniqersMarriage.Notify == "esx" then 
    UniqersMarriage.CoreName.ShowNotification(text)
elseif UniqersMarriage.Notify == "standalone" then
    TriggerEvent('chat:addMessage', -1, { args = { status, text }, color = 255,255,255 })
elseif UniqersMarriage.Notify == "custom" then -- add your notify trigger
-- you can add here custom code
end
end

function UniqersMarriageNotifyServer(source,text,status)
if UniqersMarriage.Notify == "qbcore" then
    TriggerClientEvent('QBCore:Notify', source, text, status)
elseif UniqersMarriage.Notify == "esx" then 
    TriggerClientEvent('esx:showNotification', source, text)
elseif UniqersMarriage.Notify == "standalone" then 
TriggerClientEvent('chat:addMessage', -1, { args = { status, text }, color = 255,255,255 })
elseif UniqersMarriage.Notify == "custom" then -- add your notify trigger
-- you can add here custom code
end
end


UniqersMarriage.Database = function(plugin,type,query,var)
    local wait = promise.new()
    if type == 'fetchAll' and plugin == 'mysql-async' then
        MySQL.Async.fetchAll(query, var, function(result)
            wait:resolve(result)
        end)
    end
    if type == 'execute' and plugin == 'mysql-async' then
        MySQL.Async.execute(query, var, function(result)
            wait:resolve(result)
        end)
    end
    if type == 'execute' and plugin == 'ghmattimysql' then
        exports['ghmattimysql']:execute(query, var, function(result)
            wait:resolve(result)
        end)
    end
    if type == 'fetchAll' and plugin == 'ghmattimysql' then
        exports.ghmattimysql:execute(query, var, function(result)
            wait:resolve(result)
        end)
    end
    if type == 'execute' and plugin == 'oxmysql' then
        exports.oxmysql:query(query, var, function(result)
            wait:resolve(result)
        end)
    end
    if type == 'single' and plugin == 'oxmysql' then
        exports.oxmysql:single(query, var, function(result)
            wait:resolve(result)
        end)
    end	
    if type == 'fetchAll' and plugin == 'oxmysql' then
        exports.oxmysql:query(query, var, function(result)
            wait:resolve(result)
        end)
    end
    return Citizen.Await(wait)
end