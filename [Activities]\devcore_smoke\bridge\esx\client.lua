if GetResourceState('es_extended') ~= 'started' then return end
Framework = 'ESX'
ESX = Config.Export()
STATE = {}

Citizen.CreateThread(function()
    if GetResourceState('ox_inventory') == 'started' then
        STATE.INV = 'ox'
        ox_inventory = exports.ox_inventory
    elseif GetResourceState('qs-inventory') == 'started' then
            STATE.INV = 'qs'
    else
        STATE.INV = 'other'
    end
    if GetResourceState('xsound') == 'started' then
        xSound = exports.xsound
        end
end)

function GetPlayerItems()
    return ESX.GetPlayerData().inventory
end


function GetClosePlayer()
    return ESX.Game.GetClosestPlayer()
end 

function OpenChargeMenu(item)
    local Options = {}
    local allowed = false
    local idata = {}

    for k, v in pairs(ESX.GetPlayerData().inventory) do
        local items = Config.SmokingItems[item].AllowedItems[v.name]

        if items and v.count > 0 and not idata[v.name] then
            idata[v.name] = true
            allowed = true
        table.insert(Options, {
            title = v.label,
            description = Translations['TEXT']['charge_desc'] ..item,
            event = GetCurrentResourceName()..':client:startFill',
            args = { charge_item = v.name, item = item}
        })
        
        lib.registerContext({
            id = 'charge_menu',
            title = Translations['TEXT']['charge_menu_title'],
            position = 'top-right',
            onExit = function()
                STATE.USING = false
              end,
            options = Options,
        })
            lib.showContext('charge_menu')
        else
            lib.hideMenu('charge_menu')
        end
    end
    if not allowed then
        allowed = false
        STATE.USING = false
        Config.Notify(Translations['TEXT']['dont_have_item'])
    end
end


function GetLighter(item)
    if STATE.INV == 'ox' then
        TriggerServerEvent(GetCurrentResourceName()..':server:getLighter', item)

    elseif STATE.INV == 'other' then
            local allowed = false
            for k, v in pairs(ESX.GetPlayerData().inventory) do
                local items = Config.Lighters[v.name]
                if items then
                    if not allowed then
                        allowed = true
                        TriggerServerEvent(GetCurrentResourceName()..':server:getLighter', v.name)
                    end
                end
            end
            if not allowed then
                STATE.USING = false
                    Config.Notify(Translations['TEXT']['not_lighter'], 'error')
        end
    elseif STATE.INV == 'qs' then
        local allowed = false
        for k, v in pairs(Config.Lighters) do
            local items = exports['qs-inventory']:Search(k)

            if items ~= 0 then
                if not allowed then
                    allowed = true
                    TriggerServerEvent(GetCurrentResourceName()..':server:getLighter', k)
                end
            end
        end
        if not allowed then
            STATE.USING = false
                Config.Notify(Translations['TEXT']['not_lighter'], 'error')
    end
    end
end