

STATE.OTHER_PROP = {}
STATE.MENU = false


RegisterNetEvent(GetCurrentResourceName()..':client:useSmokingItem')
AddEventHandler(GetCurrentResourceName()..':client:useSmokingItem', function(item, data)
    if item ~= nil then
        local i_data = Config.SmokingItems[item]
        if STATE.MENU then
            Config.Notify(Translations['TEXT']['hidden_menu_info'], 'info')
        end
            if data ~= 'EAR' then
                STATE.SELECT_ITEM = item
            PlayAnim('TAKEOUT', item, function()
                STATE.POS = 'HAND'
                STATE.USING = false

            if i_data.Type == 'NEED_LIGHTER' then
                STATE.ACT = 'LIGHTER'
                STATE.SIZE = i_data.Setting.Size
                LighterPanel(item)
            end
            if i_data.Type == 'BONG' or i_data.Type == 'VAPE' then
                if data and STATE.INV ~= 'Other' and data.value ~= nil and data.content  then
                    STATE.SIZE = data.value
                    STATE.CONTENT = data.content
                else
                    STATE.SIZE = 0
                    STATE.CONTENT = 'nil'
                end
                STATE.ACT = 'SMOKING'
                ControlPanel(item)
            end
            if i_data.Type == 'VAPE-PUFF' then
                if data and STATE.INV ~= 'Other' and data.value ~= nil  then
                    STATE.SIZE = data.value
                else
                    STATE.SIZE = i_data.Setting.Size
                end
                STATE.ACT = 'SMOKING'
                ControlPanel(item)
            end

            useSmokingItem(item)
         end)
        else
            if STATE.OTHER_PROP['EAR'] then
                PlayAnim('EAR', item, function()
                DeleteOtherItemProp('EAR')

                STATE.POS = 'HAND'
                STATE.USING = false
                STATE.SELECT_ITEM = item
    
            if i_data.Type == 'NEED_LIGHTER' then
                STATE.ACT = 'LIGHTER'
                STATE.SIZE = i_data.Setting.Size
                LighterPanel(item)
            end
            useSmokingItem(item)
                end)
        end
    end
    end
end)

RegisterNetEvent(GetCurrentResourceName()..':client:useLighterItem')
AddEventHandler(GetCurrentResourceName()..':client:useLighterItem', function(item, data)
    local lighter = Config.Lighters[item]
    local d_item = Config.SmokingItems[STATE.SELECT_ITEM]
    local ped = PlayerPedId()
            if STATE.SELECT_ITEM ~= nil then
                STATE.USING = true
                if d_item.Type == 'NEED_LIGHTER' then
                            if STATE.POS ~= 'MOUTH' then
                            STATE.POS = 'MOUTH'
                            PlayAnim('MOUTH', STATE.SELECT_ITEM, function()
                                CreateMainSmokingProp(STATE.SELECT_ITEM)
                                Citizen.Wait(800)
                                PlayAnim('TAKEOUT', item, function()

                                CreateOtherItemProp(item, 'LIGHTER_ITEM')
                                Citizen.Wait(800)
                                PlayAnim('LIGHT', item, function()
                                STATE.POS = 'HAND'
                                STATE.ACT = 'SMOKING'
                                TriggerServerEvent(GetCurrentResourceName()..':server:startExhaleEff', PedToNet(ped), STATE.SELECT_ITEM, 'Exhale')
                                CreateMainSmokingProp(STATE.SELECT_ITEM)
                                Citizen.Wait(800)
                                TriggerServerEvent(GetCurrentResourceName()..':server:startParticles', STATE.SELECT_ITEM, ObjToNet(STATE.CURRENT_PROP), 'Prop')
                                sizeReduction(STATE.SELECT_ITEM)
                                PlayAnim('TAKEOUT', item, function()
                                DeleteOtherItemProp('LIGHTER_ITEM')
                                    STATE.USING = false
                                    ControlPanel(STATE.SELECT_ITEM)

                                end)
                            end)
                        end)
                    end)
                else
                    PlayAnim('TAKEOUT', item, function()
                        CreateOtherItemProp(item, 'LIGHTER_ITEM')
                        Citizen.Wait(800)
                        PlayAnim('LIGHT', item, function()
                        STATE.POS = 'MOUTH'
                        STATE.ACT = 'SMOKING'
                        CreateMainSmokingProp(STATE.SELECT_ITEM)
                        Citizen.Wait(800)
                        TriggerServerEvent(GetCurrentResourceName()..':server:startParticles', STATE.SELECT_ITEM, ObjToNet(STATE.CURRENT_PROP), 'Prop')
                        sizeReduction(STATE.SELECT_ITEM)
                        PlayAnim('TAKEOUT', item, function()
                        DeleteOtherItemProp('LIGHTER_ITEM')
                            STATE.USING = false
                            ControlPanel(STATE.SELECT_ITEM)
                        end)
                    end)
                end)
            end
        else
            if d_item.Type ~= 'NEED_LIGHTER' then
            if STATE.SIZE > 0 then
                if d_item.Type == 'BONG' then
                    STATE.LIGHTER = item
                    STATE.USING = true
                    CreateOtherItemProp(item, 'LIGHTER_ITEM')

                    PlayAnim('USE', STATE.SELECT_ITEM, function()
                        addStatus(STATE.SELECT_ITEM)
                       
                            RefreshHighEffect(STATE.SELECT_ITEM)
                        

                        TriggerServerEvent(GetCurrentResourceName()..':server:startExhaleEff', PedToNet(ped), STATE.SELECT_ITEM, 'Exhale')
    
                end)

                end
            else
                Config.Notify(Translations['TEXT']['not_filled'])
                end
            end
        end
    end
end)

RegisterNetEvent(GetCurrentResourceName()..':client:ReceiverAccept')
AddEventHandler(GetCurrentResourceName()..':client:ReceiverAccept', function(item, data)
    PlayAnim('GIVE', item, function()
        StopSmoking('GIVE')
    end)
end)

RegisterNetEvent(GetCurrentResourceName()..':client:ReceiverAlreadySmoking')
AddEventHandler(GetCurrentResourceName()..':client:ReceiverAlreadySmoking', function(item, data)
   STATE.USING = false
end)


RegisterNetEvent(GetCurrentResourceName()..':client:Notify')
AddEventHandler(GetCurrentResourceName()..':client:Notify', function(msg, type)
	Config.Notify(msg, type)
end)