shared_scripts { '@FiniAC/fini_events.js', '@FiniAC/fini_events.lua' }

fx_version 'cerulean'
game 'gta5'

name 'BDX-Snowboarding'
description 'Snowboarding Career'
author 'Bodhix'
version '1.0.1'

lua54 'yes'

shared_scripts {
  'config.lua',
}

client_scripts {
  'client/snowboarding.lua',
}

server_scripts {
  'server/server.lua',
  'server/sv.lua',
}

files {
  'stream/bodhix@<EMAIL>',
  'stream/<EMAIL>',
  'server/version.json',
  'html/index.html',
  'html/Land.MP3',
  'html/Jump.MP3',
  'html/Speed.MP3',
  'html/Grab.MP3',
  'html/Break.MP3',
  'html/index.html',
  'data/**/handling.meta',
  'data/**/vehicles.meta',
  'data/**/carvariations.meta',
  'data/**/carcols.meta',
}

ui_page 'html/index.html'

nui_page 'html/index.html'

data_file 'HANDLING_FILE'            'data/**/handling.meta'
data_file 'VEHICLE_METADATA_FILE'    'data/**/vehicles.meta'
data_file 'CARCOLS_FILE'             'data/**/carcols.meta'
data_file 'VEHICLE_VARIATION_FILE'   'data/**/carvariations.meta'

escrow_ignore {
  'config.lua',
}
dependency '/assetpacks'