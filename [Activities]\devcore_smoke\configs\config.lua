Config = {}
-- DISCORD : https://discord.gg/zcG9KQj3sa
-- STORE : https://store.devcore.cz/


-- Framework is automatically detected here you have the option to replace the default exports with your own
Config.Export = function()
    if Framework == 'ESX'  then -- ESX
-- insert your esx export here
    return exports['es_extended']:getSharedObject()
--
    elseif Framework == 'QB'  then -- QB or QBX 
-- insert your qb or qbx export here
    return exports['qb-core']:GetCoreObject()
--
    end
end


Config.Settings = {
    OptionsMenuPosition = 'top-right', -- Position for options menu
    OxDurabilitySystem = false, -- Durability for lighters true for enable (only for ox_inventory)
    TakeEarItemCommand = 'takecig', -- An order to remove a stored smoking item in a position behind the ear..
    DisableCombatButtons = true, -- true if you want disable combat and aim buttons
    SoundEffects = true, -- only with xsound
    
    RollingJointsMinigame = { -- minigame from ox_lib
        Enable = true, -- true if you want to have a mini-game before packing your joints
        Difficulty = 'easy', -- easy / medium / hard
        areaSize = 50,
        speedMultiplier = 1,
        Buttons = {'a', 'd'}
    },
}


Config.HighSystem = { -- High Effect Settings 
    MaxHigh = 1.6, -- Max high value 
    Effect = 'spectator9', -- High effect up to size 1.0
    ShakeCamWhileAiming = true, -- Cam vibration during aiming at high effect above 1.0
    DisableSprint = true, -- disable sprint when high effect is above 1.0
    RemoveHigh = {
        Remove = 0.04, -- Set a rank that is removed every 15 seconds (this makes the effect go away more slowly)
        Time = 15, -- Every 15 seconds 0.04 values are taken from the high effect
        RemoveEffect = 'DrugsDrivingOut', -- The name of the effect that is executed when the high effect is above 1.0, each time the effect is reduced, false for disable. https://forge.plebmasters.de/animpostfx
    },
    HighMovement = { -- Movement will change when Hight value exceeds size 1.0
        enable = true,
        anim = 'move_m@drunk@slightlydrunk',
    }
}


Config.Buttons = { 
    Use = 38, -- LEFT CLICK ON MOUSE
    Accept = 38, -- E 
    Light = 38, -- E 
    Charge = 10, -- PAGEUP
    Options = 310, -- R 
    Throw = 246, -- X 
    Back = 246, -- X 
    Hide = 246, -- X
}


-- STATUS --
Config.Status = { --Settings for adding Armor, remove Stress and High Effect
    ["ARMOR"] = function(data) -- Trigger to increase thirst status
        AddArmourToPed(PlayerPedId(), data.AfterExhale.Status['ARMOR'])
    end,
    ["STRESS"] = function(data) -- Here you can insert an export or trigger for your stress system
        if Framework == 'ESX' then

         TriggerEvent('esx_status:remove', 'stress', data.AfterExhale.Status['STRESS']) -- this trigger only works for the esx_status stress system

        elseif Framework == 'QB' then -- Trigger for QB-Core / QBX

            TriggerServerEvent('hud:server:RelieveStress', data.AfterExhale.Status['STRESS'])
            
        end
    end,
    ["HIGH"] = function(data) -- This is a default function that does not need to be modified
        SetHigh(data.AfterExhale.Status['HIGH']) -- Default function from devcore_smoking
    end,
}

-- NOTIFY FUNCTION --
Config.Notify = function(message, type)
    if Framework == 'ESX' then
        -- For your notify remove ESX.ShowNotification(message, 2000, type) and put your export or trigger 
        ESX.ShowNotification(message, 2000, type) -- Default ESX Notify

    elseif Framework == 'QB' then
        -- For your notify remove QBCore.Functions.Notify(message, type) and put your export or trigger 
        QBCore.Functions.Notify(message, type) -- Default QB Notify

    end
end


--CUSTOM DRAW MENU FUNCTION-- here put your export or trigger for your menu and delete - DrawSmokeMenu(text, menuid)
Config.CustomMenu = function(text, menuid) -- menuid only for default realistic smoking draw menu.
    DrawSmokeMenu(text, menuid) 
end


-- MENU CUSTOMIZE --
Config.Menu = {

    ['MAIN'] = { -- MAIN DRAW MENU
        TextFont = 4,
        TextColor = {255, 255, 255},
        Scale = 0.4,
        Position = {x = 0.50, y = 0.95},
        Background = {
            enable = true,
            color = { r = 35, g = 35, b = 35, alpha = 200 },
        },
    },
    ['STATUS'] = { -- STATUS DRAW MENU
        TextFont = 4,
        TextColor = {255, 255, 255},
        Scale = 0.4,
        Position = {x = 0.984, y = 0.90},
        Background = {
            enable = true,
            color = { r = 35, g = 35, b = 35, alpha = 200 },
        },
    },
    ['OPTIONS'] = { -- OPTIONS DRAW MENU
        TextFont = 4,
        TextColor = {255, 255, 255},
        Scale = 0.4,
        Position = {x = 0.972, y = 0.94},
        Background = {
            enable = true,
            color = { r = 35, g = 35, b = 35, alpha = 200 },
        },
    },

}


-- MENU TEXT AND TEXT --
Translations = {
    ['MENU'] = {
        ['USE'] =  {tag = '~g~[E]~w~ ', label = 'USE'},
        ['OPTIONS'] = {tag = '~g~[R]~w~ ', label = 'OPTIONS'},
        ['LIGHT'] = {tag = '~g~[E]~w~ ', label = 'LIGHT'},
        ['CHARGE'] = {tag = '~g~[PGUP]~w~ ', label = 'CHARGE'},
        ['HIDE'] = {tag = '~g~[Y]~w~ ', label = 'HIDE'},
        ['THROW'] = {tag = '~g~[Y]~w~ ', label = 'THROW'},
        ['BACK'] = {tag = '~g~[Y]~w~ ', label = 'BACK'},
        ['ACCEPT'] = {tag = '~g~[E]~w~ ', label = 'ACCEPT'},
    },
    ['TEXT'] = {
        ['charge_menu_title'] = 'Select an item',
        ['charge_desc'] = 'Click to charge: ',
        ['cigarette_pack_menu_title'] = 'How many cigarettes do you want to take out of the pack?',
        ['cigarette_pack_menu_desc'] = 'Max: ',
        ['cigarette_pack_menu_label'] = 'Cigarette item in pack: ',
        ['papers_quantity'] = 'Quantity of papers',
        ['roll_joints_desc'] = 'Click to start rolling: ',
        ['roll_joints_starting'] = 'Starting rolling joint',
        ['failed_roll'] = 'You failed to roll a joint',
        ['success_roll'] = 'He successfully rolled ',
        ['no_near_you'] = 'Theres no one near you',
        ['dont_have_item'] = 'You dont carry the necessary items to fill',
        ['hidden_menu_info'] = 'You have a hidden menu',
        ['max_high'] = 'Youre already too high. Wait a minute.',
        ['dont_have_item_for_rolling'] = 'You dont have the right item for packing joints ',
        ['already'] = 'Youre already smoking',
        ['player_already'] = 'Player already smoking',
        ['save_position'] = 'To take the item back into your hand, use the command /' ..Config.Settings.TakeEarItemCommand,
        ['not_filled'] = 'You have to fill the bong first',
        ['not_lighter'] = 'You dont have Lighter',
        ['options_menu_title'] = 'Smoking Options',
        ['options_positions_label'] = 'Positions',
        ['options_hide_menu_label'] = 'Menu show/hide',
        ['options_hide_label'] = 'Hide',
        ['options_filled_label'] = 'Filled',
        ['options_show_label'] = 'Show',
        ['options_give_label'] = 'Give',
        ['options_mouth_positions'] = 'Mouth',
        ['options_hand_positions'] = 'Hand',
        ['options_ear_positions']= 'Ear',
        ['status_bong'] = 'g', -- for ox_inventory metadata description
        ['status_vape'] = 'ml', -- for ox_inventory metadata description
        ['status_puff_vape'] = ' PUFFS', -- for ox_inventory metadata description
        ['empty_lighter'] = 'Lighter is empty',
    },
}