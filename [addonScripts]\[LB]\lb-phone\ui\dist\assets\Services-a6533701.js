import{u as v,r as I,G as W,q as h,s as N,a,F as O,j as o,L as t,m as A,c1 as pe,k as J,bm as ue,C as g,c2 as he,ax as ve,bQ as Ce,b as _,K as re,a0 as w,I as Ie,a5 as X,f as y,a2 as Ne,d as ge,bI as $,a7 as be,aa as le,v as te,V as fe,ab as se,ac as Ae,A as B,o as q,P as D,x as Oe,e as ne,g as Re,h as de,E as ie,J as Y,i as me,bl as Se,c3 as Le,c4 as oe,O as Pe,aB as Te,S as Ee,a1 as ye,y as De,c5 as Me,b0 as Ve}from"./index-a04bc7c5.js";import{S as ce}from"./Switch-1ce279b8.js";const U={companies:[{job:"police",name:"Police",icon:"https://cdn-icons-png.flaticon.com/512/7211/7211100.png",canCall:!0,canMessage:!0,bossRanks:["boss","lieutenant"],open:!0,location:{name:"Mission Row",coords:{x:428.9,y:-984.5}}},{job:"ambulance",name:"Ambulance",icon:"https://cdn-icons-png.flaticon.com/128/1032/1032989.png",canCall:!0,canMessage:!0,bossRanks:["boss","doctor"],open:!0,location:{name:"Pillbox",coords:{x:304.2,y:-587}}},{job:"mechanic",name:"Mechanic",icon:"https://cdn-icons-png.flaticon.com/128/10281/10281554.png",canCall:!0,canMessage:!0,bossRanks:["boss","worker"],open:!1,location:{name:"LS Customs",coords:{x:-336.6,y:-134.3}}},{job:"taxi",name:"Taxi",icon:"https://cdn-icons-png.flaticon.com/128/433/433449.png",canCall:!0,canMessage:!0,bossRanks:["boss","driver"],open:!1,location:{name:"Taxi HQ",coords:{x:984.2,y:-219}}}],company:{job:"police",duty:!0,receiveCalls:!0,isBoss:!0,balance:1e6,employees:[{id:1,name:"Breze",grade:1,gradeLabel:"Officer",canInteract:!0,online:!0},{id:2,name:"Loaf Scripts",grade:2,gradeLabel:"Sergeant",canInteract:!0,online:!0},{id:3,name:"Kash",grade:3,gradeLabel:"Lieutenant",canInteract:!0,online:!1}],grades:[{label:"Officer",grade:1},{label:"Sergeant",grade:2},{label:"Lieutenant",grade:3},{label:"Captain",grade:4}]},messageList:[{id:"1",lastMessage:"Is anyone available to help me?",sender:"Breze",number:"**********",company:{name:"Police",icon:"https://cdn-icons-png.flaticon.com/512/7211/7211100.png"},timestamp:Date.now()-1e3*60*60*2},{id:"2",lastMessage:"Hello, how can i help you?",sender:"Loaf Scripts",number:"**********",company:{name:"Police",icon:"https://cdn-icons-png.flaticon.com/512/7211/7211100.png"},timestamp:Date.now()-1e3*60*60*4},{id:"3",lastMessage:"Hey, i need backup at the bank",sender:"Sally",number:"**********",company:{name:"Police",icon:"https://cdn-icons-png.flaticon.com/512/7211/7211100.png"},timestamp:Date.now()-1e3*60*60*24*2}],messages:{1:[{id:"1",sender:"**********",content:"Is anyone available to help me?",timestamp:Date.now()-1e3*60*60*2},{id:"2",sender:"**********",content:"Hey, a unit is on its way to your location",timestamp:Date.now()-1e3*60*60*24*2},{id:"3",sender:"**********",content:"i'd like to report a crime, i'm at the pier",timestamp:Date.now()-1e3*60*60*24*2}],2:[{id:"1",sender:"**********",content:"Hey, can you help me?",timestamp:Date.now()-1e3*60*60*4},{id:"2",sender:"**********",content:"I need a unit to my location",timestamp:Date.now()-1e3*60*60*24*5},{id:"3",sender:"**********",content:"I need backup at the bank",timestamp:Date.now()-1e3*60*60*24*9}],3:[{id:"1",sender:"**********",content:"is there any way you have any job positions available?",timestamp:Date.now()-1e3*60*60*24*2},{id:"2",sender:"**********",content:"hello?",timestamp:Date.now()-1e3*60*60*24*4},{id:"3",sender:"**********",content:"hey, i have a question",timestamp:Date.now()-1e3*60*60*24*5}]},employees:[{firstname:"John",lastname:"Doe",grade:1,gradeLabel:"Officer",number:"**********",online:!0},{firstname:"Loaf",lastname:"Scripts",grade:2,gradeLabel:"Sergeant",number:"**********",online:!0},{firstname:"Kash",grade:3,gradeLabel:"Lieutenant",number:"**********",online:!1},{firstname:"Sally",grade:4,gradeLabel:"Captain",number:"**********",online:!0}]};function we(){var l,S,i,E,u,f,n,p,T,k,K,z,Q;const e=v(_),s=v(C),[r,d]=I.useState(null);I.useEffect(()=>{W("Services")&&h("Services",{action:"getCompany"},U.company).then(m=>{if(!m)return N("warning","Failed to fetch company data");C.set(m)})},[]);const c=()=>{g.PopUp.set({title:t("APPS.SERVICES.DEPOSIT_POPUP.TITLE"),description:t("APPS.SERVICES.DEPOSIT_POPUP.DESCRIPTION"),input:{placeholder:t("APPS.SERVICES.DEPOSIT_POPUP.AMOUNT"),onChange:m=>{d(m)},type:"number"},buttons:[{title:t("APPS.SERVICES.DEPOSIT_POPUP.CANCEL")},{title:t("APPS.SERVICES.DEPOSIT_POPUP.PROCEED"),cb:()=>{d(m=>{/^\d+$/.test(m)&&h("Services",{action:"depositMoney",amount:parseInt(m)},parseInt(m)+s.balance).then(b=>{if(typeof b!="number")return N("warning","Balance is not a number, not updating");C.patch({balance:b})})})}}]})},P=()=>{g.PopUp.set({title:t("APPS.SERVICES.WITHDRAW_POPUP.TITLE"),description:t("APPS.SERVICES.WITHDRAW_POPUP.DESCRIPTION"),input:{placeholder:t("APPS.SERVICES.WITHDRAW_POPUP.AMOUNT"),onChange:m=>{d(m)},type:"number"},buttons:[{title:t("APPS.SERVICES.WITHDRAW_POPUP.CANCEL")},{title:t("APPS.SERVICES.WITHDRAW_POPUP.PROCEED"),cb:()=>{d(m=>{if(/^\d+$/.test(m)){if(s.balance-parseInt(m)<0){g.PopUp.set({title:t("APPS.SERVICES.WITHDRAW_POPUP.TITLE"),description:t("APPS.SERVICES.WITHDRAW_POPUP.NOT_ENOUGH_MONEY").format({amount:m}),buttons:[{title:t("APPS.SERVICES.WITHDRAW_POPUP.PROCEED")}]});return}h("Services",{action:"withdrawMoney",amount:parseInt(m)},s.balance-parseInt(m)).then(b=>{if(typeof b!="number")return N("warning","Balance is not a number, not updating");C.patch({balance:b})})}})}}]})};return a(O,{children:s!=null&&s.job?o("div",{className:"actions-container",children:[a("div",{className:"category-title",children:t("APPS.SERVICES.SETTINGS")}),a("section",{children:o(O,{children:[s.duty!==void 0&&((S=(l=e==null?void 0:e.Companies)==null?void 0:l.Management)==null?void 0:S.Duty)!==!1&&o(A.div,{initial:{opacity:0,scale:.9},whileInView:{opacity:1,scale:1},exit:{opacity:0,scale:.9},className:"item",children:[o("div",{className:"item-data",children:[a(pe,{className:"orange"}),o("div",{className:"item-info",children:[a("div",{className:"title",children:t("APPS.SERVICES.DUTY")}),a("div",{className:"description",children:t("APPS.SERVICES.DUTY_DESCRIPTION")})]})]}),a("div",{className:"item-value",children:a(ce,{checked:s.duty,onChange:()=>{h("Services",{action:"toggleDuty",duty:!s.duty},!0).then(()=>{var m;return C.patch({duty:!((m=C.value)!=null&&m.duty)})})}})})]}),(s.duty===void 0||s.duty===!0)&&o(A.div,{initial:{opacity:0,scale:.9},whileInView:{opacity:1,scale:1},exit:{opacity:0,scale:.9},className:"item",children:[o("div",{className:"item-data",children:[a(J,{className:"green"}),o("div",{className:"item-info",children:[a("div",{className:"title",children:t("APPS.SERVICES.JOB_CALLS")}),o("div",{className:"description",children:[" ",t("APPS.SERVICES.JOB_CALLS_DESCRIPTION").format({toggle:s.receiveCalls?t("APPS.SERVICES.DISABLE"):t("APPS.SERVICES.ENABLE")})]})]})]}),a("div",{className:"item-value",children:a(ce,{checked:s.receiveCalls,onChange:()=>{h("Services",{action:"toggleCalls"},!s.receiveCalls).then(m=>{C.patch({receiveCalls:m})})}})})]})]})}),s.isBoss&&o(O,{children:[a("div",{className:"category-title",children:t("APPS.SERVICES.ACTIONS")}),o("section",{children:[o(A.div,{initial:{opacity:0,scale:.9},whileInView:{opacity:1,scale:1},exit:{opacity:0,scale:.9},className:"item",children:[o("div",{className:"item-data",children:[a(ue,{className:"green"}),o("div",{className:"item-info",children:[a("div",{className:"title",children:t("APPS.SERVICES.BALANCE")}),a("div",{className:"description",children:"Current balance of the company"})]})]}),a("div",{className:"item-value",children:e.CurrencyFormat.replace("%s",(i=s.balance)==null?void 0:i.toLocaleString())})]}),(((u=(E=e==null?void 0:e.Companies)==null?void 0:E.Management)==null?void 0:u.Deposit)!==!1||((n=(f=e==null?void 0:e.Companies)==null?void 0:f.Management)==null?void 0:n.Withdraw)!==!1)&&o("div",{className:"item",children:[((T=(p=e==null?void 0:e.Companies)==null?void 0:p.Management)==null?void 0:T.Deposit)!==!1&&a("div",{className:"button",onClick:c,children:t("APPS.SERVICES.DEPOSIT_POPUP.TITLE")}),((K=(k=e==null?void 0:e.Companies)==null?void 0:k.Management)==null?void 0:K.Withdraw)!==!1&&a("div",{className:"button",onClick:P,children:t("APPS.SERVICES.WITHDRAW_POPUP.TITLE")})]})]}),a("div",{className:"category-title",children:t("APPS.SERVICES.MANAGE_EMPLOYEES")}),o("section",{children:[((Q=(z=e==null?void 0:e.Companies)==null?void 0:z.Management)==null?void 0:Q.Hire)!==!1&&o(A.div,{initial:{opacity:0,scale:.9},whileInView:{opacity:1,scale:1},exit:{opacity:0,scale:.9},className:"item",onClick:()=>{g.PopUp.set({title:t("APPS.SERVICES.HIRE_POPUP.TITLE"),description:t("APPS.SERVICES.HIRE_POPUP.DESCRIPTION"),input:{type:"number",placeholder:t("APPS.SERVICES.HIRE_POPUP.PLACEHOLDER"),onChange:m=>{d(m)}},buttons:[{title:t("APPS.SERVICES.HIRE_POPUP.CANCEL")},{title:t("APPS.SERVICES.HIRE_POPUP.PROCEED"),cb:()=>{d(m=>{if(/^\d+$/.test(m))return h("Services",{action:"hireEmployee",source:parseInt(m)},{id:m,name:"Joe Doe"}).then(b=>{var x,V;b&&C.patch({employees:[...(x=C.value)==null?void 0:x.employees,{id:b.id,name:b.name,grade:0,gradeLabel:(V=C.value)==null?void 0:V.grades[0].label,canInteract:!0}]})}),""})}}]})},children:[o("div",{className:"item-data",children:[a(he,{className:"blue"}),o("div",{className:"item-info",children:[a("div",{className:"title",children:t("APPS.SERVICES.HIRE")}),a("div",{className:"description",children:t("APPS.SERVICES.HIRE_POPUP.DESCRIPTION")})]})]}),a("div",{className:"item-value",children:a(ve,{})})]}),s.employees.map((m,b)=>o(A.div,{initial:{opacity:0,scale:.9},whileInView:{opacity:1,scale:1},exit:{opacity:0,scale:.9},className:"item",onClick:()=>{var V,Z,ee,ae;if(!m.canInteract||e.Companies.Management.Fire===!1&&e.Companies.Management.Promote===!1)return;const x=[((Z=(V=e==null?void 0:e.Companies)==null?void 0:V.Management)==null?void 0:Z.Promote)!==!1&&{title:t("APPS.SERVICES.SET_GRADE"),cb:()=>{setTimeout(()=>{g.ContextMenu.set({buttons:[...s.grades.map(R=>({title:R.label,cb:()=>{h("Services",{action:"setGrade",employee:m.id,grade:R.grade},!0).then(H=>{var j;if(!H)return N("warning","Failed to set grade");C.patch({employees:(j=C.value)==null?void 0:j.employees.map(F=>(F.id===m.id&&(F.grade=R.grade,F.gradeLabel=R.label),F))})})}}))]})},300)}},((ae=(ee=e==null?void 0:e.Companies)==null?void 0:ee.Management)==null?void 0:ae.Fire)!==!1&&{title:t("APPS.SERVICES.FIRE_POPUP.TITLE"),color:"red",cb:()=>{g.PopUp.set({title:t("APPS.SERVICES.FIRE_POPUP.TITLE"),description:t("APPS.SERVICES.FIRE_POPUP.DESCRIPTION").format({name:m.name}),buttons:[{title:t("APPS.SERVICES.FIRE_POPUP.CANCEL")},{title:t("APPS.SERVICES.FIRE_POPUP.PROCEED"),color:"red",cb:()=>{h("Services",{action:"fireEmployee",employee:m.id},!0).then(R=>{var H;if(!R)return N("warning","Failed to fire employee");C.patch({employees:(H=C.value)==null?void 0:H.employees.filter(j=>j.id!==m.id)})})}}]})}}];g.ContextMenu.set({buttons:x.filter(R=>R)})},children:[o("div",{className:"item-data",children:[a(Ce,{}),o("div",{className:"item-info",children:[a("div",{className:"title",children:m.name}),o("div",{className:"description",children:[" ",m.gradeLabel]})]})]}),a("div",{className:"item-value",children:a("div",{className:"status","data-online":m.online})})]},b))]})]})]}):a("div",{className:"no-job",children:t("APPS.SERVICES.UNEMPLOYED")})})}function _e(){const e=v(D.Settings),s=v(D.PhoneNumber),r=v(L),[d,c]=I.useState([]),P=I.useRef(null),[l,S]=I.useState({content:"",attachments:[]});I.useEffect(()=>{W("Services")&&h("Services",{action:"getMessages",page:0,id:r.id,company:r.job},U.messages[r.id]).then(n=>{if(n&&n.length>0){c([...n.reverse()]);let p=document.querySelector(".chat-container");p.scrollTop=p.scrollHeight}})},[]);const i=()=>{if(l.content.length>0){let n={sender:s,id:r.id,company:r.job,content:l.content,timestamp:new Date};if(!le())return c(p=>[...p,{...n,delivered:!1}]);h("Services",{action:"sendMessage",...n},!0).then(p=>{p||N("error","Failed to send message"),c(T=>[...T,{...n,delivered:p}]),S({content:"",attachments:[]}),P.current.value=""})}},E=()=>{g.PopUp.set({title:t("APPS.MESSAGES.SEND_LOCATION_POPUP.TITLE"),description:t("APPS.MESSAGES.SEND_LOCATION_POPUP.TEXT"),buttons:[{title:t("APPS.MESSAGES.SEND_LOCATION_POPUP.CANCEL")},{title:t("APPS.MESSAGES.SEND_LOCATION_POPUP.SEND"),cb:()=>{h("Maps",{action:"getCurrentLocation"},{x:"0",y:"0"}).then(n=>{if(!n)return N("error","Failed to get location");let p={sender:s,content:`<!SENT-LOCATION-X=${te(n.x,2)}Y=${te(n.y,2)}!>`,attachments:[],id:r.id,company:r.job,timestamp:new Date};h("Services",{action:"sendMessage",...p},!0).then(T=>{c(k=>[...k,T?p:{...p,delivered:!1}])})})}}]})},{handleScroll:u}=re({fetchData:n=>h("Services",{action:"getMessages",id:r.id,company:r.job,page:n}),onDataFetched:n=>c([...n.reverse(),...d]),isReversed:!0,perPage:25});w("services:newMessage",n=>{if(r.id!==n.channelId||n.sender===s)return;c([...d,{...n,timestamp:new Date}]);let p=document.querySelector(".chat-container");p.scrollTop=p.scrollHeight},{waitUntilService:!0});const f=n=>{if(n)return/<!SENT-LOCATION-X=(-?\d*\.?\d*)Y=(-?\d*\.?\d*)!>/.test(n)};return o(O,{children:[o("div",{className:"chat-header",children:[a("div",{className:"back",onClick:()=>{M.set("messages"),L.reset()},children:a(Ie,{})}),o("div",{className:"user",children:[a(X,{src:(r==null?void 0:r.icon)??`./assets/img/avatar-placeholder-${e.display.theme}.svg`,className:"avatar"}),a("div",{className:"name",children:r.company??y(r.number)})]}),a(Ne,{className:"hidden"})]}),a("div",{className:"chat-container",onScroll:u,children:a("div",{className:"chat-body",children:d.map((n,p)=>a(Ue,{index:p,messages:d,message:n,func:{isLocation:f}}))})}),a("div",{className:"chat-bottom",children:o("div",{className:"input",children:[a(ge,{placeholder:t("APPS.MESSAGES.PLACEHOLDER"),ref:P,value:l.content,onChange:n=>{S({content:n.target.value,attachments:l.attachments})},onKeyDown:n=>{n.key=="Enter"&&i()}}),o("div",{className:"buttons",children:[a("div",{className:"location",children:a($,{onClick:E})}),l.content.length>0&&a("div",{className:"send",onClick:i,children:a(be,{})})]})]})})]})}const Ue=({messages:e,message:s,index:r,func:d})=>{var f;const c=v(D.PhoneNumber),P=v(L);let l,S,i,E=s.sender===c?"self":"other",u=((f=e[r+1])==null?void 0:f.sender)===c?"self":"other";if(e[r+1]?S=Math.abs(s.timestamp-e[r+1].timestamp)/36e5:u=void 0,d.isLocation(s.content)){let n=s.content.match(/X=(-?\d*\.?\d*)Y/)[1],p=s.content.match(/Y=(-?\d*\.?\d*)!>/)[1];i={x:n,y:p}}return o("div",{className:fe("message",E),"data-id":s.id,children:[l,s.delivered===!1?o("div",{className:"content-wrapper",children:[a("div",{className:"content",children:se(s.content)}),a(Ae,{})]}):i?o("div",{className:"location",onClick:()=>{B.patch({active:{name:"Maps",data:{location:[i.y,i.x],name:`${y(s.sender)}'s location`,icon:P.icon}}})},children:[a("div",{className:"img",style:{backgroundImage:'url("https://img.gta5-mods.com/q95/images/mirror-park-garden/2b72f9-20160428154103_1.jpg")'}}),E==="other"&&o("div",{className:"sender",children:[y(s.sender)," ",t("APPS.MESSAGES.SENT_LOCATION")]})]}):s.content&&a("div",{className:"content",children:se(s.content)}),s.delivered===!1?a("div",{className:"status",children:t("APPS.MESSAGES.NOT_DELIVERED")}):e[r+1]&&S>6?a("div",{className:"date",children:q(s.timestamp)}):E!==u&&a("div",{className:"date",children:q(s.timestamp)})]},r)},G=Y(null);function ke(){var l,S;const e=v(G),[s,r]=I.useState("all"),[d,c]=I.useState(""),P=i=>{if(!i)return N("error","No number provided");g.PopUp.set({title:t("APPS.SERVICES.CALL_POPUP.TITLE"),description:t("APPS.SERVICES.CALL_POPUP.DESCRIPTION").format({name:y(i)}),buttons:[{title:t("APPS.SERVICES.CALL_POPUP.CANCEL")},{title:t("APPS.SERVICES.CALL_POPUP.PROCEED"),cb:()=>{let E=ie(i);me({...E,number:i})}}]})};return o(A.div,{initial:{opacity:0,y:250},animate:{opacity:1,y:0},exit:{opacity:0,y:250},transition:{duration:.2,ease:"easeInOut"},className:"employee-list",children:[o("div",{className:"employee-list-header",children:[a("div",{className:"close",onClick:()=>G.set(null)}),a("div",{className:"selector",children:["all","available"].map((i,E)=>a("div",{className:"option","data-active":s===i,onClick:()=>r(i),children:t(`APPS.SERVICES.${i.toUpperCase()}`)},E))}),a(Oe,{placeholder:t("APPS.SERVICES.SEARCH_EMPLOYEES"),onChange:i=>c(i.target.value)})]}),a("div",{className:"employee-list-body",children:(S=(l=e==null?void 0:e.filter(i=>{var E;return(s==="all"?!0:i.online)&&((E=ne(i.firstname,i.lastname))==null?void 0:E.toLowerCase().includes(d==null?void 0:d.toLowerCase()))}))==null?void 0:l.sort((i,E)=>E.grade-i.grade))==null?void 0:S.map((i,E)=>o(A.div,{initial:{opacity:0,scale:.9},whileInView:{opacity:1,scale:1},viewport:{once:!0},className:"employee-item",children:[o("div",{className:"user",children:[a("div",{className:"avatar",children:Re(i.firstname,i.lastname)}),o("div",{className:"info",children:[ne(i.firstname,i.lastname),a("span",{children:i.gradeLabel})]})]}),a("div",{className:"buttons",children:o(O,{children:[a(J,{className:i.online&&i.number?"green":"red",onClick:()=>P(i.number)}),a(de,{className:i.online&&i.number?"green":"red",onClick:()=>{if(!i.number)return N("error","No number provided");let u=ie(i.number);B.patch({active:{name:"Messages",data:{number:i.number,name:u==null?void 0:u.name,avatar:u==null?void 0:u.avatar,view:"messages"}}})}})]})})]},E))})]})}function xe(){var d;const[e,s]=I.useState([]),r=(d=v(B))==null?void 0:d.active;return I.useEffect(()=>{W("Services")&&h("Services",{action:"getCompanies"},U.companies).then(c=>{if(!c)return N("warning","Failed to fetch companies");s(c)})},[r]),w("services:updateOpen",c=>{if(!c)return;const{job:P,open:l}=c;s(S=>{const i=S.findIndex(E=>E.job===P);return i===-1?S:(S[i].open=l,[...S])})}),a(O,{children:a("div",{className:"companies-list",children:e.sort((c,P)=>c.open&&!P.open?-1:!c.open&&P.open?1:0).map(c=>a(He,{company:c},c.name))})})}const He=({company:e})=>{var i,E,u,f;const s=v(_),r=()=>{h("Services",{action:"getEmployees",company:e.job},U.employees).then(n=>{if(!n)return N("warning","You do not have permission to view employees.");G.set(n)})},[d,c]=I.useState(!1),P=n=>{var p;!((p=n==null?void 0:n.location)!=null&&p.coords)||!n.name||B.patch({active:{name:"Maps",data:{location:[n.location.coords.y,n.location.coords.x],name:n.name,icon:n.icon}}})},l=(n,p)=>{s.Companies.Enabled&&g.PopUp.set({title:t("APPS.SERVICES.CALL_POPUP.TITLE"),description:t("APPS.SERVICES.CALL_POPUP.DESCRIPTION").format({name:Te(n)}),buttons:[{title:t("APPS.SERVICES.CALL_POPUP.CANCEL")},{title:t("APPS.SERVICES.CALL_POPUP.PROCEED"),cb:()=>{me({company:n,companylabel:p})}}]})},S=n=>{h("Services",{action:"getChannelId",company:n.job},"1").then(p=>{if(!p)return N("error","Failed to get channel id");L.set({icon:n.icon,company:n.name,job:n.job,id:p}),M.set("chat")})};return o("div",{className:"item",onMouseEnter:()=>c(!0),onMouseLeave:()=>c(!1),"data-open":e.open,onClick:r,children:[o("div",{className:"info",children:[e.icon&&a("div",{className:"icon",children:a(X,{src:e.icon})}),o("div",{className:"company-details",children:[a("div",{className:"title",children:e.name}),o("div",{className:"location",children:[a(Se,{}),(i=e.location)==null?void 0:i.name]})]})]}),e.open?o("div",{className:"actions",children:[e.customIcon&&a("div",{className:"custom-icon",onClick:n=>{n.stopPropagation(),h("Services",{action:"customIconClick",company:e.job})},children:Le[e.customIcon]()}),((E=e.location)==null?void 0:E.coords)&&a($,{className:"orange",onClick:n=>{n.stopPropagation(),P(e)}}),((u=s.Companies)==null?void 0:u.Enabled)!==!1&&e.canCall!==!1&&a(J,{className:"green",onClick:n=>{n.stopPropagation(),l(e.job,e.name)}}),e.canMessage!==!1&&a(oe,{className:"green",onClick:n=>{n.stopPropagation(),S(e)}})]}):o(O,{children:[o("div",{className:"actions",onMouseEnter:()=>c(!1),onMouseLeave:()=>c(!0),children:[((f=e.location)==null?void 0:f.coords)&&a($,{className:"orange",onClick:n=>{n.stopPropagation(),P(e)}}),e.canMessage!==!1&&s.Companies.MessageOffline&&a(oe,{className:"green",onClick:n=>{n.stopPropagation(),S(e)}})]}),a(Pe,{children:d&&a(A.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},transition:{duration:.2,ease:"easeInOut"},className:"closed",children:t("APPS.SERVICES.CLOSED")})})]})]},e.name)};function je(){const e=v(D.PhoneNumber),[s,r]=I.useState({personal:[],job:[]}),[d,c]=I.useState("personal");I.useEffect(()=>{W("Services")&&h("Services",{action:"getRecentMessages",page:0},U.messageList).then(l=>{if(!l)return N("warning","No recent messages found");r({personal:[...l.filter(S=>S.number===e)],job:[...l.filter(S=>S.number!==e)]})})},[]);const{handleScroll:P}=re({fetchData:l=>h("Services",{action:"getRecentMessages",page:l}),onDataFetched:l=>r({...s,[d]:[...s[d],...l]}),perPage:25});return w("services:channelDeleted",l=>{if(!l)return N("error","No channel ID provided");r({personal:s.personal.filter(S=>S.id!==l),job:s.job.filter(S=>S.id!==l)})}),a(O,{children:o("div",{className:"company-messages-container",children:[a("div",{className:"company-messages-header",children:a("div",{className:"selector",children:["personal","job"].map((l,S)=>a("div",{className:"option","data-active":d===l,onClick:()=>c(l),children:t(`APPS.SERVICES.${l.toUpperCase()}`)},S))})}),a("div",{className:"company-messages-content",onScroll:P,children:a(A.section,{...Ee(d==="personal"?"right":"left",d,.2),children:(s==null?void 0:s[d])&&(s==null?void 0:s[d].map((l,S)=>a(Fe,{channel:l,type:d},S)))})})]})})}const Fe=({channel:e,type:s})=>{var P,l,S;const r=v(D.Settings),d=ye(()=>{var i;(i=_)!=null&&i.value.Companies.DeleteConversations&&s!=="personal"&&g.ContextMenu.set({buttons:[{title:t("APPS.MESSAGES.DELETE_CONVERSATION.TITLE"),color:"red",cb:()=>{setTimeout(()=>{g.PopUp.set({title:t("APPS.MESSAGES.DELETE_CONVERSATION.TITLE"),description:t("APPS.MESSAGES.DELETE_CONVERSATION.DESCRIPTION"),buttons:[{title:t("APPS.MESSAGES.DELETE_CONVERSATION.CANCEL")},{title:t("APPS.MESSAGES.DELETE_CONVERSATION.DELETE"),color:"red",cb:()=>{h("Services",{action:"deleteChannel",id:e.id},!0).then(E=>{if(!E)return N("error","Failed to delete channel, deleteChannel returned false")})}}]})},100)}}]})}),c=i=>{if(i)return/<!SENT-LOCATION-X=(-?\d*\.?\d*)Y=(-?\d*\.?\d*)!>/.test(i)};return o("div",{...d,className:"item",onClick:()=>{var i,E,u;L.set({...L==null?void 0:L.value,id:e.id,icon:s==="personal"&&((i=e==null?void 0:e.company)!=null&&i.icon)?e.company.icon:`./assets/img/avatar-placeholder-${r.display.theme}.svg`,company:s==="personal"&&((E=e==null?void 0:e.company)!=null&&E.name)?e.company.name:y(e.number),job:(u=e.company)==null?void 0:u.job,number:e.number}),M.set("chat")},children:[s==="personal"&&((P=e==null?void 0:e.company)!=null&&P.icon)?a(X,{src:(l=e==null?void 0:e.company)==null?void 0:l.icon}):a("img",{src:`./assets/img/avatar-placeholder-${r.display.theme}.svg`}),o("div",{className:"info",children:[o("div",{className:"info-header",children:[a("div",{className:"user",children:s==="personal"&&((S=e==null?void 0:e.company)!=null&&S.name)?e.company.name:y(e.number)}),o("div",{className:"right",children:[a("div",{className:"time",children:q(e.timestamp)}),a(De,{})]})]}),a("div",{className:"message",children:c(e.lastMessage)?t("APPS.MESSAGES.SENT_LOCATION_SHORT"):e.lastMessage})]})]})};function Ge(){const e=v(_),s=v(M),[r,d]=I.useState([{icon:a(Se,{}),title:t("APPS.SERVICES.COMPANIES"),value:"companies"},{icon:a(de,{}),title:t("APPS.SERVICES.MESSAGES"),value:"messages"}]);return I.useEffect(()=>{var c,P;((P=(c=e==null?void 0:e.Companies)==null?void 0:c.Management)==null?void 0:P.Enabled)!==!1&&d([...r,{icon:a(Me,{}),title:t("APPS.SERVICES.ACTIONS"),value:"actions"}])},[]),a("div",{className:"companies-footer",children:r.map((c,P)=>o("div",{className:"item","data-active":s===c.value,onClick:()=>M.set(c.value),children:[c==null?void 0:c.icon,c.title]},P))})}const M=Y("companies"),L=Y(null),C=Y(null);function Ye(){const e=v(M),s=v(G),r=v(D.Styles.TextColor),d={companies:a(xe,{}),actions:a(we,{}),messages:a(je,{}),chat:a(_e,{})};let c={actions:t("APPS.SERVICES.ACTIONS"),messages:t("APPS.SERVICES.MESSAGES"),companies:t("APPS.SERVICES.COMPANIES")};return w("services:setCompany",P=>C.set(P)),w("services:setDuty",P=>C.patch({duty:P})),a("div",{className:"companies-container","data-view":e,children:!le()&&!_.value.Companies.AllowNoService?a("div",{className:"loading",children:a(Ve,{size:40,lineWeight:5,speed:2,color:r})}):o(O,{children:[a(Pe,{children:s&&a(ke,{})}),o(A.div,{...Ee(e==="chat"?"left":"right",e,.2),className:"companies-wrapper",children:[e!=="chat"&&a("div",{className:"companies-header",children:a("div",{className:"title",children:c[e]})}),a("div",{className:"companies-body",children:d[e]})]}),e!=="chat"&&a(Ge,{})]})})}export{C as CompanyData,L as Data,M as View,Ye as default};
