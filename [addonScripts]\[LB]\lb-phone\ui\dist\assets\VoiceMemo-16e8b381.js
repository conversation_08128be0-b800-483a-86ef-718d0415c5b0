import{r as n,u as M,t as h,q as N,s as g,C as R,L as P,a9 as U,j as v,a as i,x as $,J as G,G as b,m as k,d as x,o as F,F as j,O as B,n as Y,bw as H,Q as W,R as q,bx as J,T as K,P as L,U as Q,A as z,v as X}from"./index-a04bc7c5.js";import{A as Z}from"./audioRecorder-c5089776.js";const ee={voiceMemos:[{id:1,title:"Voice Memo 1",timestamp:Date.now()-1e3*60*60*2,duration:1,src:"https://loaf-scripts.com/fivem/lb-phone/mockdata/one.mp3"},{id:2,title:"Birds Chirping",timestamp:Date.now()-1e3*60*60*5,duration:22,src:"https://loaf-scripts.com/fivem/lb-phone/mockdata/birds-chirping.mp3"}]};const _=G(null);function re(){n.useRef(null);const p=n.useRef(null),[a,l]=n.useState(""),[O,s]=n.useState(!1),c=M(h.APPS.VOICEMEMO.voicememos);n.useEffect(()=>(p.current=new Z,(!h.APPS.VOICEMEMO.voicememos.value||h.APPS.VOICEMEMO.voicememos.value.length===0)&&N("VoiceMemo",{action:"get"},ee.voiceMemos).then(t=>{if(!t)return g("error","Failed to fetch voice memos");h.APPS.VOICEMEMO.voicememos.set(t)}),()=>{p.current&&p.current.stop()}),[]);const I=()=>{let t=P("APPS.VOICEMEMO.DEFAULT_NAME"),o=c.map(r=>{let d=r.title;if(d.startsWith(t)){let u=d.split(" ");if(u.length>1&&/^\d+$/.test(u[u.length-1]))return parseInt(u[u.length-1])}return null}).filter(r=>r!==null).sort((r,d)=>r-d);if(o.length===0)return c.some(r=>r.title===t)?t+" 2":t;for(let r=0;r<o.length;r++)if(o[r]!==r+2)return t+" "+(r+2);return t+" "+(o[o.length-1]+1)},S=async t=>{};return n.useEffect(()=>{if(!O||!p.current)return;const t=p.current;return t.start(100,S),()=>{t.stop().then(async o=>{if(!o){R.PopUp.set({title:P("APPS.VOICEMEMO.EMPTY_RECORDING_POPUP.TITLE"),description:P("APPS.VOICEMEMO.EMPTY_RECORDING_POPUP.DESCRIPTION"),buttons:[{title:P("APPS.VOICEMEMO.EMPTY_RECORDING_POPUP.OK")}]});return}let r;try{r=await U("Audio",o.blob)}catch(V){g("error","Failed to upload voice memo",V);return}const d=I(),u=await N("VoiceMemo",{action:"upload",data:{src:r,duration:o.duration,title:d}},Math.floor(Math.random()*100).toString());u&&(h.APPS.VOICEMEMO.voicememos.set([{id:u,title:d,timestamp:new Date().getTime(),duration:o.duration,src:r},...c]),g("info",`Saved voice memo ${u}`))})}},[O]),v("div",{className:"voicememo-container",children:[v("div",{className:"voicememo-wrapper",children:[i("div",{className:"title",children:P("APPS.VOICEMEMO.VOICE_RECORDINGS")}),i($,{placeholder:P("APPS.VOICEMEMO.SEARCH_RECORDINGS"),onChange:t=>l(t.target.value)}),i("div",{className:"voicememo-items",children:c.filter(t=>{var o;return(o=t.title)==null?void 0:o.toLowerCase().includes(a==null?void 0:a.toLowerCase())}).sort((t,o)=>o.timestamp-t.timestamp).map(t=>i(te,{data:t,delete:()=>h.APPS.VOICEMEMO.voicememos.set(c.filter(o=>o.id!==t.id))},t.id))})]}),i("div",{className:"voicememo-footer",children:i("div",{className:"button-record",onClick:()=>s(!O),children:i("div",{className:"button-inner","data-recording":O})})})]})}const te=p=>{const{data:a}=p,l=M(L.Settings),O=M(h.APPS.VOICEMEMO.voicememos),s=n.useRef(null),[c,I]=n.useState(!1),[S,t]=n.useState(!1),[o,r]=n.useState(0),d=M(_),[u,V]=n.useState(a.title),[ie,w]=n.useState(!1),[E,D]=n.useState(null),C=M(Q),y=M(z);M(L.PhoneNumber),n.useEffect(()=>{var e;return s.current=new Audio(a.src),s.current.src=a.src,s.current.volume=((e=l==null?void 0:l.sound)==null?void 0:e.volume)!==void 0?l.sound.volume:.5,s.current.addEventListener("ended",()=>{t(!1)}),s.current.addEventListener("timeupdate",()=>{r(s.current.currentTime)}),()=>{var f,m;(f=s.current)==null||f.pause(),(m=s.current)==null||m.remove(),s.current=null}},[]),n.useEffect(()=>{var e;(C!=null&&C.visible||!b("VoiceMemo"))&&((e=s.current)==null||e.pause(),t(!1))},[C==null?void 0:C.visible,y==null?void 0:y.active]),n.useEffect(()=>{var e;d&&d!==a.id&&((e=s.current)==null||e.pause(),t(!1),I(!1))},[d]),n.useEffect(()=>{l.sound.volume&&(s.current.volume=l.sound.volume)},[l.sound.volume]),n.useEffect(()=>{t(!1),r(0),c&&_.set(a.id)},[c]);const T=e=>{const f=Math.floor(e/60),m=X(e-f*60,0);return`${f}:${m<10?"0"+m:m}`},A={light:{active:"#333333",slider:"#cccccc"},dark:{active:"#FFFFFF",slider:"#999999"}};return v(k.div,{className:"voicememo-item",initial:{opacity:0,scale:.9},whileInView:{opacity:1,scale:1},viewport:{once:!0},onClick:()=>I(!c),"data-expanded":c,children:[c?i(x,{className:"voicememo-title",defaultValue:u,onClick:e=>e.stopPropagation(),onLoad:e=>{e.target.style.width=e.target.value.length+"ch"},onChange:e=>{V(e.target.value),e.target.style.width=e.target.value.length+"ch"},onBlur:e=>{N("VoiceMemo",{action:"rename",id:a.id,title:e.target.value},!0).then(f=>{if(!f)return g("warning","Failed to rename voice memo");h.APPS.VOICEMEMO.voicememos.set(O.map(m=>m.id===a.id?{...m,title:e.target.value}:m))})}}):i("div",{className:"voicememo-title",children:u}),c&&i("div",{className:"subtitle",children:F(a.timestamp)}),v("div",{className:"voicememo-info",children:[!c&&v(j,{children:[i("div",{className:"date",children:F(a.timestamp)}),i("div",{className:"duration",children:T(a.duration)})]}),i(B,{children:c&&v(k.div,{className:"voicememo-actions",initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},children:[v("div",{className:"voicememo-duration-slider",onClick:e=>e.stopPropagation(),children:[i(x,{type:"range",min:0,max:100,value:E||o/a.duration*100,style:{background:`linear-gradient(to right, ${A[l.display.theme].active} 0%, ${A[l.display.theme].active} ${E||o/a.duration*100}%, ${A[l.display.theme].slider} ${E||o/a.duration*100}%, ${A[l.display.theme].slider} 100%)`},onMouseDown:e=>{b("VoiceMemo")&&(e.stopPropagation(),w(!0))},onMouseUp:e=>{if(e.stopPropagation(),w(!1),E){if(!s.current)return g("warning","Audio ref is null");s.current.currentTime=a.duration/100*E,D(null)}},onChange:e=>{e.stopPropagation(),D(e.target.value)}}),v("div",{className:"duration",children:[i("div",{children:T(o)??"0:00"}),i("div",{children:T(a.duration)})]})]}),v("div",{className:"voicememo-item-footer",children:[i("div",{className:"share",children:i(Y,{onClick:e=>{e.stopPropagation(),R.Share.set({type:"voicememo",data:a})}})}),v("div",{className:"controls",children:[i(H,{className:"disabled"}),i("span",{onClick:e=>{if(e.stopPropagation(),!s.current)return g("warning","Audio ref is null");S?(s.current.pause(),t(!1)):(s.current.play(),t(!0))},children:S?i(W,{}):i(q,{})}),i(J,{className:"disabled"})]}),i("div",{className:"delete",children:i(K,{onClick:e=>{e.stopPropagation(),R.PopUp.set({title:P("APPS.VOICEMEMO.DELETE_POPUP.TITLE"),description:P("APPS.VOICEMEMO.DELETE_POPUP.DESCRIPTION"),buttons:[{title:P("APPS.VOICEMEMO.DELETE_POPUP.CANCEL")},{title:P("APPS.VOICEMEMO.DELETE_POPUP.PROCEED"),color:"red",cb:()=>{N("VoiceMemo",{action:"delete",id:a.id}).then(f=>{if(!f)return g("warning","Failed to delete voice memo");p.delete()})}}]})}})})]})]})})]})]})};export{re as default};
