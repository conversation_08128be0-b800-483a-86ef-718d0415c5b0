<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title>Basketball Menu</title>
    <link rel="stylesheet" href="style.css">
</head>

<body>
    <div class="menu-container" id="basketballMenu">
        <!-- X only on main menu -->
        <div class="close-button" id="closeBtn" onclick="closeMenu()">X</div>
        <div class="back-button" id="backBtn" onclick="goBack()">↩</div>

        <div class="logo">
            <img src="images/basketball_logo.png" alt="Basketball Logo" class="logo-image">
        </div>

        <!-- Main Menu -->
        <div class="main-menu" id="modeSelection">
            <div class="section-title">Game Mode</div>
            <button class="mode-button" onclick="showSolo()">SOLO</button>
            <button class="mode-button" onclick="showPVP()">PVP</button>
        </div>

        <!-- SOLO -->
        <div class="solo-menu" id="soloSection">
            <div class="section-title">SOLO</div>
            <div class="input-row centered">
                <span class="input-label">Time</span>
                <input type="number" id="soloTimeInput" min="1" placeholder="Minutes">
            </div>
            <div class="centered">
                <button class="start-button" onclick="startSolo()">START</button>
            </div>
        </div>

        <!-- PVP -->
        <div class="pvp-menu" id="pvpSection">
            <div class="section-title">PVP</div>

            <div class="pvp-settings-row">
                <div class="pvp-setting">
                    <label for="pvpMinutes">Minutes</label>
                    <input type="number" id="pvpMinutes" min="1" placeholder="1-99">
                </div>
                <div class="pvp-setting">
                    <label for="pvpRounds">Rounds</label>
                    <input type="number" id="pvpRounds" min="1" placeholder="1-10">
                </div>
            </div>


            <div class="team-box blue">
                <input id="blueNameInput" placeholder="Blue Team" />
                <button class="register-btn blue" onclick="registerTeam('blue')">Register</button>
            </div>
            <div class="team-box red">
                <input id="redNameInput" placeholder="Red Team" />
                <button class="register-btn red" onclick="registerTeam('red')">Register</button>
            </div>
        </div>
    </div>
    <div class="hud-container" id="basketballHud" style="display: none;">
        <div class="hud-left">
            <!-- Solo Mode: This one stays visible -->
            <div class="hud-team solo-only" id="soloScoreContainer" style="display: none;">
                <span class="hud-score-label">Score</span>: <span class="hud-score" id="soloScore">0</span>
            </div>

            <!-- PvP Mode: These get hidden during solo -->
            <div class="hud-team blue" id="teamBlueContainer">
                <span id="hudBlueName">Blue</span>: <span class="hud-score" id="hudBlueScore">0</span>
            </div>
            <div class="hud-team red" id="teamRedContainer">
                <span id="hudRedName">Red</span>: <span class="hud-score" id="hudRedScore">0</span>
            </div>
        </div>
        <div class="hud-right">
            <div class="hud-timer">⏱ <span id="hudTimer">00:00</span></div>
            <div class="hud-round" id="hudRoundContainer">🏀 Round <span id="hudRound">1</span> / <span
                    id="hudTotalRounds">1</span></div>
        </div>
    </div>
    <div id="lobbyHud" class="lobby-container" style="display: none;">
        <div class="lobby-title">
            🏀 <span>Match Lobby</span>
        </div>
        <div class="lobby-teams-row">
            <div class="lobby-team lobby-blue">
                <span id="lobbyBlueName">Blue Team</span> <span class="lobby-count" id="lobbyBlueCount">0</span>
            </div>
            <div class="lobby-team lobby-red">
                <span id="lobbyRedName">Red Team</span> <span class="lobby-count" id="lobbyRedCount">0</span>
            </div>
        </div>
        <div class="lobby-button-wrapper">
            <button id="startMatchBtn" class="lobby-start" disabled>Start Match</button>
        </div>
    </div>
    <div id="resultOverlay" class="hidden">
        <div id="resultMessage"></div>
    </div>
    <div id="storeMenu" class="store-box" style="display: none;">
        <!-- Logo centered above menu -->
        <div class="logo-wrapper">
            <img src="images/basketball_logo.png" class="store-logo">
        </div>

        <!-- Main Buttons -->
        <div class="store-buttons" id="mainButtons">
            <button onclick="proceedToBallMenu()">Ball</button>
            <button onclick="closeShopMenu()">Close</button>
        </div>

        <!-- Ball selection grid (scrollable) -->
        <div id="ballOptions" class="ball-grid hidden"></div>

        <!-- Bottom Buttons -->
        <div class="store-buttons hidden" id="ballButtons">
            <button onclick="goBackToMainShop()">↩ Back</button>
            <button id="purchaseBtn" class="purchase-button hidden" onclick="purchaseBall()">Purchase</button>
        </div>
    </div>

    <script>
        let blueTeam = null, redTeam = null;
        let hasRegisteredTeam = null;
        let globalRegisteredBy = { blue: null, red: null };
        let hudInterval = null;
        let currentHudSpot = null;
        let currentLobbySpot = null;
        let myClientID = null;
        let selectedBallIndex = null;

        window.addEventListener('message', function (event) {
            const data = event.data;

            if (data.transactionType === "playSound") {
                const audio = new Audio(data.transactionFile);
                audio.volume = 0.2;
                audio.play().catch(err => console.warn("Audio play failed:", err));
            }
            if (data.type === "show") {
                if (data.spot === undefined) return; // ✅ Skip if spot not defined
                currentHudSpot = data.spot;

                if (data.myID !== undefined) myClientID = data.myID;
                if (data.registeredBy) globalRegisteredBy = data.registeredBy;
                if (data.registered) hasRegisteredTeam = data.registered;

                document.getElementById('basketballMenu').style.display = 'block';

                updateTeamNames(data.teams || {});

                if (!hasRegisteredTeam) {
                    document.getElementById('modeSelection').style.display = 'block';
                    document.getElementById('pvpSection').style.display = 'none';
                    document.getElementById('soloSection').style.display = 'none';
                } else {
                    document.getElementById('modeSelection').style.display = 'none';
                    document.getElementById('pvpSection').style.display = 'block';
                    document.getElementById('soloSection').style.display = 'none';
                }

                document.getElementById('backBtn').style.display = hasRegisteredTeam ? 'none' : 'block';
                document.getElementById('closeBtn').style.display = 'block';
            }
            else if (data.type === "hide") {
                closeMenu();
            }
            else if (data.type === "updateTeams") {
                if (data.registeredBy) globalRegisteredBy = data.registeredBy;
                updateTeamNames(data.teams);
            }
            else if (data.type === "resetUI") {
                blueTeam = null;
                redTeam = null;
                hasRegisteredTeam = null;
                globalRegisteredBy = { blue: null, red: null };

                document.getElementById('blueNameInput').value = "";
                document.getElementById('redNameInput').value = "";
                document.getElementById('pvpMinutes').value = "";
                document.getElementById('pvpRounds').value = "";

                updateTeamNames({});
            } else if (data.type === "showSoloHud") {
                currentHudSpot = data.spot;

                const hud = document.getElementById('basketballHud');
                const soloContainer = document.getElementById('soloScoreContainer');
                const soloScore = document.getElementById('soloScore');
                const blueContainer = document.getElementById('teamBlueContainer');
                const redContainer = document.getElementById('teamRedContainer');
                const roundContainer = document.getElementById('hudRoundContainer');

                hud.style.display = 'flex';
                soloContainer.style.display = 'block';

                soloScore.textContent = data.soloScore || 0;

                blueContainer.style.display = 'none';
                redContainer.style.display = 'none';
                roundContainer.style.display = 'none';

                const timerElement = document.getElementById('hudTimer');
                if (hudInterval) clearInterval(hudInterval);
                let remaining = data.time || 0;
                const updateTimer = () => {
                    const mins = Math.floor(remaining / 60);
                    const secs = remaining % 60;
                    timerElement.textContent = `${String(mins).padStart(2, '0')}:${String(secs).padStart(2, '0')}`;
                    if (remaining > 0) remaining--;
                };
                updateTimer();
                hudInterval = setInterval(updateTimer, 1000);
            } else if (data.type === "hideSoloHud" && data.spot === currentHudSpot) {
                document.getElementById("basketballHud").style.display = "none";
                currentHudSpot = null;
            } else if (data.type === "updateScoreHud" && data.spot === currentHudSpot) {
                document.getElementById("hudBlueScore").innerText = data.teamA;
                document.getElementById("hudRedScore").innerText = data.teamB;
            } else if (data.type === "updateSoloScore" && data.spot === currentHudSpot) {
                document.getElementById('soloScore').textContent = data.score || 0;
            } else if (data.type === "showLobby") {
                currentLobbySpot = data.spot;

                document.getElementById('lobbyHud').style.display = 'flex';
                document.getElementById('lobbyBlueName').textContent = data.blueName || 'Blue Team';
                document.getElementById('lobbyRedName').textContent = data.redName || 'Red Team';
                document.getElementById('lobbyBlueCount').textContent = data.teamA || 0;
                document.getElementById('lobbyRedCount').textContent = data.teamB || 0;

                const btn = document.getElementById('startMatchBtn');
                btn.disabled = !data.buttonEnabled;
                btn.classList.toggle('enabled', data.buttonEnabled);
            } else if (data.type === "hideLobby" && data.spot === currentLobbySpot) {
                document.getElementById("lobbyHud").style.display = "none";
                currentLobbySpot = null;
            } else if (data.type === "showHud") {
                currentHudSpot = data.spot;

                document.getElementById("basketballHud").style.display = "flex";

                document.getElementById("soloScoreContainer").style.display = "none"; // Hide solo mode score
                document.getElementById("teamBlueContainer").style.display = "block";
                document.getElementById("teamRedContainer").style.display = "block";
                document.getElementById("hudRoundContainer").style.display = "block";

                document.getElementById("hudBlueName").textContent = data.blueName || "Blue";
                document.getElementById("hudRedName").textContent = data.redName || "Red";
                document.getElementById("hudBlueScore").textContent = data.blueScore || 0;
                document.getElementById("hudRedScore").textContent = data.redScore || 0;
                document.getElementById("hudRound").textContent = data.round || 1;
                document.getElementById("hudTotalRounds").textContent = data.totalRounds || 1;

                const timerElement = document.getElementById("hudTimer");
                let remaining = data.time || 0;

                if (hudInterval) clearInterval(hudInterval);

                const updateTimer = () => {
                    const mins = Math.floor(remaining / 60);
                    const secs = remaining % 60;
                    timerElement.textContent = `${String(mins).padStart(2, '0')}:${String(secs).padStart(2, '0')}`;
                    if (remaining > 0) remaining--;
                };

                updateTimer();
                hudInterval = setInterval(updateTimer, 1000);
            } else if (data.type === "hideHud" && data.spot === currentHudSpot) {
                document.getElementById("basketballHud").style.display = "none";
                currentHudSpot = null;
            } else if (data.type === "showResult") {
                showResultMessage(data.result);
            } else if (data.action === "start:showMainMenu") {
                document.getElementById("storeMenu").style.display = "block";
            } else if (data.type === "updateLobbyButton" && data.spot === currentLobbySpot) {
                const btn = document.getElementById('startMatchBtn');
                btn.disabled = !data.buttonEnabled;
                btn.classList.toggle('enabled', data.buttonEnabled);
            } 
        });


        function showBasketballHUD(data) {
            document.getElementById('basketballHud').style.display = 'flex';
            document.getElementById('hudBlueName').textContent = data.blueName || 'Blue';
            document.getElementById('hudRedName').textContent = data.redName || 'Red';
            document.getElementById('hudBlueScore').textContent = data.blueScore || 0;
            document.getElementById('hudRedScore').textContent = data.redScore || 0;
            document.getElementById('hudRound').textContent = data.round || 1;
            document.getElementById('hudTotalRounds').textContent = data.totalRounds || 1;

            // Timer countdown (in seconds)
            if (hudInterval) clearInterval(hudInterval);
            let remaining = data.time || 0;
            const timerElement = document.getElementById('hudTimer');
            const updateTimer = () => {
                const mins = Math.floor(remaining / 60);
                const secs = remaining % 60;
                timerElement.textContent = `${String(mins).padStart(2, '0')}:${String(secs).padStart(2, '0')}`;
                if (remaining > 0) remaining--;
            };
            updateTimer();
            hudInterval = setInterval(updateTimer, 1000);
        }

        function hideBasketballHUD() {
            document.getElementById('basketballHud').style.display = 'none';
            if (hudInterval) clearInterval(hudInterval);
        }


        function closeMenu() {
            document.getElementById('basketballMenu').style.display = 'none';
            fetch(`https://${GetParentResourceName()}/closeMenu`, { method: 'POST' });
        }

        function goBack() {
            document.getElementById('soloSection').style.display = 'none';
            document.getElementById('pvpSection').style.display = 'none';
            document.getElementById('modeSelection').style.display = 'block';
            document.getElementById('backBtn').style.display = 'none';
            document.getElementById('closeBtn').style.display = 'block';
        }

        function showSolo() {
            document.getElementById('modeSelection').style.display = 'none';
            document.getElementById('soloSection').style.display = 'block';
            document.getElementById('backBtn').style.display = 'block';
            document.getElementById('closeBtn').style.display = 'none';
        }

        function showPVP() {
            document.getElementById('modeSelection').style.display = 'none';
            document.getElementById('pvpSection').style.display = 'block';
            document.getElementById('backBtn').style.display = 'block';
            document.getElementById('closeBtn').style.display = 'none';
        }

        function startSolo() {
            const time = parseInt(document.getElementById('soloTimeInput').value);
            if (!isNaN(time) && time > 0) {
                fetch(`https://${GetParentResourceName()}/startSolo`, {
                    method: 'POST',
                    body: JSON.stringify({ time: time })
                });
            }
        }
        function registerTeam(team) {
            const inputId = team === 'blue' ? 'blueNameInput' : 'redNameInput';
            const nameField = document.getElementById(inputId);
            const name = nameField.value.trim();

            const minutes = parseInt(document.getElementById('pvpMinutes').value) || 0;
            const rounds = parseInt(document.getElementById('pvpRounds').value) || 0;

            // Allow registration to existing teams (name already set) if player not registered yet
            if (!hasRegisteredTeam) {
                // Use team name from input if it's still enabled, otherwise fallback to existing
                const finalName = nameField.disabled ? (team === "blue" ? blueTeam : redTeam) : name;

                if (finalName) {
                    fetch(`https://${GetParentResourceName()}/registerTeam`, {
                        method: 'POST',
                        body: JSON.stringify({ team, name: finalName, minutes, rounds })
                    });

                    hasRegisteredTeam = team;
                }
            }
        }

        function proceedToBallMenu() {
            fetch(`https://${GetParentResourceName()}/Basketball-proceedToMenu`, {
                method: "POST"
            });

            showBallOptions(); // Display designs

            document.getElementById("mainButtons").classList.add("hidden");
            document.getElementById("ballButtons").classList.remove("hidden");
        }

        function showBallOptions() {
            const container = document.getElementById("ballOptions");
            container.innerHTML = "";
            container.classList.remove("hidden");

            fetch(`https://${GetParentResourceName()}/Basketball-getDesignCount`, {
                method: "POST",
                body: JSON.stringify({})
            })
                .then(response => response.json())
                .then(data => {
                    const count = data.designCount || 16;
                    for (let i = 1; i <= count; i++) {
                        const img = document.createElement("img");
                        img.src = `images/ball_png/ball_${i}.png`;
                        img.onclick = () => {
                            selectedBallIndex = i;
                            fetch(`https://${GetParentResourceName()}/Basketball-designSelected`, {
                                method: "POST",
                                body: JSON.stringify({ ballIndex: i })
                            });
                            document.getElementById("purchaseBtn").classList.remove("hidden");
                        };
                        container.appendChild(img);
                    }
                });
        }

        function goBackToMainShop() {
            fetch(`https://${GetParentResourceName()}/Basketball-goBack`, {
                method: "POST",
                body: JSON.stringify({ currentPage: "mainMenu" })
            });

            document.getElementById("ballOptions").classList.add("hidden");
            document.getElementById("purchaseBtn").classList.add("hidden");

            document.getElementById("mainButtons").classList.remove("hidden");
            document.getElementById("ballButtons").classList.add("hidden");
        }


        function closeShopMenu() {
            document.getElementById("storeMenu").style.display = "none"; // ✅ Hide the store manually
            fetch(`https://${GetParentResourceName()}/Basketball-closeMenu`, { method: "POST" });
        }


        function purchaseBall() {
            if (selectedBallIndex !== null) {
                fetch(`https://${GetParentResourceName()}/Basketball-purchaseItem`, {
                    method: "POST",
                    body: JSON.stringify({
                        type: "Ball",
                        ballIndex: selectedBallIndex
                    })
                });
            }
        }

        window.onload = () => {
            document.getElementById("resultOverlay").classList.add("hidden");
            document.getElementById("resultMessage").textContent = ""; // Clear any default text
        };
        function showResultMessage(result) {
            const overlay = document.getElementById("resultOverlay");
            const message = document.getElementById("resultMessage");

            overlay.classList.remove("hidden");

            if (result === "win") {
                message.textContent = "🏆 Your Team Wins!";
                message.style.color = "#00FF99";
            } else if (result === "lose") {
                message.textContent = "💀 Your Team Loses!";
                message.style.color = "#FF4444";
            } else if (result === "draw") {
                message.textContent = "⚖️ It's a Draw!";
                message.style.color = "#FFD700";
            }

            setTimeout(() => {
                overlay.classList.add("hidden");
            }, 7000);
        }

        function updateTeamNames(teams) {
            blueTeam = teams.blue || null;
            redTeam = teams.red || null;

            const blueInput = document.getElementById('blueNameInput');
            const redInput = document.getElementById('redNameInput');
            const minutesInput = document.getElementById('pvpMinutes');
            const roundsInput = document.getElementById('pvpRounds');

            const blueButton = document.querySelector('.register-btn.blue');
            const redButton = document.querySelector('.register-btn.red');
            const soloStart = document.querySelector('.start-button');

            const lockSettings = !!globalRegisteredBy.blue || !!globalRegisteredBy.red;

            const alreadyRegistered = hasRegisteredTeam !== null;

            // Lock team name inputs if names are already set
            blueInput.disabled = !!blueTeam;
            redInput.disabled = !!redTeam;

            // Logic to allow joining existing teams
            blueButton.disabled = (hasRegisteredTeam && hasRegisteredTeam !== "blue") || false;
            redButton.disabled = (hasRegisteredTeam && hasRegisteredTeam !== "red") || false;

            // If this player is already registered on a team, disable both buttons
            if (hasRegisteredTeam === "blue") {
                blueButton.disabled = true;
            } else if (hasRegisteredTeam === "red") {
                redButton.disabled = true;
            }

            // Gray out disabled buttons
            blueButton.classList.toggle("disabled", blueButton.disabled);
            redButton.classList.toggle("disabled", redButton.disabled);

            // Lock match config settings after either team is registered
            minutesInput.disabled = lockSettings;
            roundsInput.disabled = lockSettings;
            soloStart.disabled = lockSettings;
        }

        function closeMenu() {
            document.getElementById('basketballMenu').style.display = 'none';
            fetch(`https://${GetParentResourceName()}/closeMenu`, { method: 'POST' });
        }

        document.getElementById('startMatchBtn').addEventListener('click', () => {
            if (!currentLobbySpot) return;

            const btn = document.getElementById('startMatchBtn');
            btn.disabled = true;
            btn.classList.remove('enabled');
            btn.classList.add('disabled'); // optional: if you have a gray style class

            fetch(`https://${GetParentResourceName()}/startMatch`, {
                method: 'POST',
                body: JSON.stringify({ spot: currentLobbySpot })
            });
        });

    </script>
</body>

</html>