return {
    Debug = false,                                         --Debug true for debug mode
    UseModels = false,                                    --UseModels true for all prop instances
    Target = 'lib',                                        --Target 'qb' or 'ox' or 'lib'
    Poles = {
        --{ position = vec4(-1388.7698974609, -674.28186035156, 27.856121063232, 0.0), spawn = true }, -- position required, job optional, spawn optional
        -- <PERSON><PERSON>
        { position = vector4(-679.1885, 5788.8213, 19.05-0.6, 295.05),  job = '' },
        { position = vector4(-678.0805, 5791.1772, 19.05-0.6, 126),  job = '' },
        { position = vector4(-680.3241, 5792.1963, 19.05-0.6, 126), job = '' },
        { position = vector4(-681.4163, 5789.8462, 19.05-0.6, 30), job = '' },
        { position = vector4(-671.1758, 5789.8486, 19.05-0.9, 30), job = '' },
        { position = vector4(-674.071, 5783.5669, 19.05-0.9, 126), job = '' },
        { position = vector4(-677.0071, 5777.2871, 19.05-0.9, 126), job = '' },
        { position = vector4(-692.1483, 5784.5283, 19.05-0.9, 295.05), job = '' },
        { position = vector4(-689.2521, 5790.8101, 19.05-0.9, 295.05), job = '' },
        { position = vector4(-689.2266, 5790.8066, 19.05-0.9, 295.05), job = '' },
        { position = vector4(-686.3172, 5797.0264, 19.05-0.9, 295.05), job = '' },
        { position = vector4(-680.6696, 5798.4175, 10.1339+0.1, 295.05), job = '' },
        { position = vector4(-669.172, 5792.0957, 10.1339+0.1, 295.05), job = '' },
        { position = vector4(-679.8477, 5784.4004, 10.1339+0.1, 295.05), job = '' },

         -- Vanilla Unicorn Pole
        { position = vector4(121.7083, -1289.5712, 28.3568, 295.05),  job = '' },
        { position = vector4(116.6406, -1292.4683, 28.2805, 126),  job = '' },
        { position = vector4(111.4787, -1295.4523, 28.2349, 126), job = '' },
        { position = vector4(112.1854, -1310.2836, 28.8968, 30), job = '' },
        { position = vector4(109.5418, -1311.8064, 28.9372, 30), job = '' },
        { position = vector4(106.6742, -1313.452, 28.8515, 126), job = '' },
        { position = vector4(120.3549, -1282.6559, 20.7081, 126), job = '' },
        { position = vector4(114.4738, -1278.1023, 20.6402, 295.05), job = '' },
        { position = vector4(109.5256, -1288.899, 20.8057, 295.05), job = '' },
        { position = vector4(108.6502, -1281.4652, 20.5466, 295.05), job = '' },
        { position = vector4(116.2863, -1300.6135, 20.7782, 295.05), job = '' },
        { position = vector4(122.2587, -1305.0753, 20.4949, 295.05), job = '' },
        { position = vector4(127.1185, -1294.3568, 20.8153, 295.05), job = '' },
        { position = vector4(128.0656, -1301.6824, 20.54, 295.05), job = '' },
        
        --- Lust Resort
        { position = vector4(-568.3611, 216.244, 82.6959, 295.05), job = '' },
        { position =  vector4(-572.2365, 216.5013, 82.6959, 295.05), job = '' },


        { position = vector4(-27.044, -1657.215, 28.492, 291.108), job = 'hotbox', spawn = true }, -- Hot Box      

        --- Pink Trap
       { position =  vector4(-1231.272, -1236.9124, 3.0956, 295.05), job = '' },
       { position =  vector4(-1232.7894, -1232.483, 3.0912, 295.05), job = '' },

       

        


    }
    
}
