<?xml version="1.0" encoding="UTF - 8"?>

<CWeaponInfoBlob>
	<SlotNavigateOrder>
		<Item>
			<WeaponSlots>
				<Item>
					<OrderNumber value="432"/>
					<Entry>SLOT_WEAPON_CODY</Entry>
				</Item>
			</WeaponSlots>
		</Item>
	</SlotNavigateOrder>
	<Infos>
		<Item>
			<Infos>
				<Item type="CWeaponInfo">
					<Name>WEAPON_CODY</Name>
					<Model>w_sb_cody</Model>
					<Audio>AUDIO_ITEM_MICROSMG</Audio>
					<Slot>SLOT_WEAPON_CODY</Slot>
					<DamageType>BULLET</DamageType>
					<Explosion>
						<Default>DONTCARE</Default>
						<HitCar>DONTCARE</HitCar>
						<HitTruck>DONTCARE</HitTruck>
						<HitBike>DONTCARE</HitBike>
						<HitBoat>DONTCARE</HitBoat>
						<HitPlane>DONTCARE</HitPlane>
					</Explosion>
					<FireType>INSTANT_HIT</FireType>
					<WheelSlot>WHEEL_SMG</WheelSlot>
					<Group>GROUP_SMG</Group>
					<AmmoInfo ref="AMMO_SMG"/>
					<AimingInfo ref="SMG_2H_BASE_STRAFE"/>
					<ClipSize value="30"/>
					<AccuracySpread value="3.500000"/>
					<AccurateModeAccuracyModifier value="0.500000"/>
					<RunAndGunAccuracyModifier value="2.000000"/>
					<RunAndGunAccuracyMaxModifier value="1.000000"/>
					<RecoilAccuracyMax value="0.500000"/>
					<RecoilErrorTime value="3.000000"/>
					<RecoilRecoveryRate value="1.000000"/>
					<RecoilAccuracyToAllowHeadShotAI value="1000.000000"/>
					<MinHeadShotDistanceAI value="1000.000000"/>
					<MaxHeadShotDistanceAI value="1000.000000"/>
					<HeadShotDamageModifierAI value="1000.000000"/>
					<RecoilAccuracyToAllowHeadShotPlayer value="0.175000"/>
					<MinHeadShotDistancePlayer value="5.000000"/>
					<MaxHeadShotDistancePlayer value="65.000000"/>
					<HeadShotDamageModifierPlayer value="9.000000"/>
					<Damage value="16.000000"/>
					<DamageTime value="0.000000"/>
					<DamageTimeInVehicle value="0.000000"/>
					<DamageTimeInVehicleHeadShot value="0.000000"/>
					<HitLimbsDamageModifier value="0.500000"/>
					<NetworkHitLimbsDamageModifier value="0.800000"/>
					<LightlyArmouredDamageModifier value="0.750000"/>
					<Force value="40.000000"/>
					<ForceHitPed value="120.000000"/>
					<ForceHitVehicle value="750.000000"/>
					<ForceHitFlyingHeli value="750.000000"/>
					<OverrideForces>
						<Item>
							<BoneTag>BONETAG_HEAD</BoneTag>
							<ForceFront value="80.000000"/>
							<ForceBack value="90.000000"/>
						</Item>
						<Item>
							<BoneTag>BONETAG_NECK</BoneTag>
							<ForceFront value="40.000000"/>
							<ForceBack value="90.000000"/>
						</Item>
						<Item>
							<BoneTag>BONETAG_L_THIGH</BoneTag>
							<ForceFront value="40.000000"/>
							<ForceBack value="1.000000"/>
						</Item>
						<Item>
							<BoneTag>BONETAG_R_THIGH</BoneTag>
							<ForceFront value="40.000000"/>
							<ForceBack value="1.000000"/>
						</Item>
						<Item>
							<BoneTag>BONETAG_L_CALF</BoneTag>
							<ForceFront value="70.000000"/>
							<ForceBack value="80.000000"/>
						</Item>
						<Item>
							<BoneTag>BONETAG_R_CALF</BoneTag>
							<ForceFront value="60.000000"/>
							<ForceBack value="100.000000"/>
						</Item>
					</OverrideForces>
					<ForceMaxStrengthMult value="1.000000"/>
					<ForceFalloffRangeStart value="0.000000"/>
					<ForceFalloffRangeEnd value="50.000000"/>
					<ForceFalloffMin value="1.000000"/>
					<ProjectileForce value="0.000000"/>
					<FragImpulse value="500.000000"/>
					<Penetration value="0.010000"/>
					<VerticalLaunchAdjustment value="0.000000"/>
					<DropForwardVelocity value="0.000000"/>
					<Speed value="2000.000000"/>
					<BulletsInBatch value="1"/>
					<BatchSpread value="0.000000"/>
					<ReloadTimeMP value="-1.000000"/>
					<ReloadTimeSP value="-1.000000"/>
					<VehicleReloadTime value="0.750000"/>
					<AnimReloadRate value="1.000000"/>
					<BulletsPerAnimLoop value="1"/>
					<TimeBetweenShots value="0.100000"/>
					<TimeLeftBetweenShotsWhereShouldFireIsCached value="-1.000000"/>
					<SpinUpTime value="0.000000"/>
					<SpinTime value="0.000000"/>
					<SpinDownTime value="0.000000"/>
					<AlternateWaitTime value="-1.000000"/>
					<BulletBendingNearRadius value="0.000000"/>
					<BulletBendingFarRadius value="0.750000"/>
					<BulletBendingZoomedRadius value="0.375000"/>
					<FirstPersonBulletBendingNearRadius value="0.000000"/>
					<FirstPersonBulletBendingFarRadius value="0.750000"/>
					<FirstPersonBulletBendingZoomedRadius value="0.375000"/>
					<Fx>
						<EffectGroup>WEAPON_EFFECT_GROUP_SMG</EffectGroup>
						<FlashFx>muz_smg</FlashFx>
						<FlashFxAlt>muz_alternate_star</FlashFxAlt>
						<FlashFxFP>muz_smg_fp</FlashFxFP>
						<FlashFxAltFP/>
						<MuzzleSmokeFx>muz_smoking_barrel</MuzzleSmokeFx>
						<MuzzleSmokeFxFP>muz_smoking_barrel_fp</MuzzleSmokeFxFP>
						<MuzzleSmokeFxMinLevel value="0.300000"/>
						<MuzzleSmokeFxIncPerShot value="0.100000"/>
						<MuzzleSmokeFxDecPerSec value="0.250000"/>
						<ShellFx>eject_auto</ShellFx>
						<ShellFxFP>eject_smg_fp</ShellFxFP>
						<TracerFx>bullet_tracer</TracerFx>
						<PedDamageHash>BulletSmall</PedDamageHash>
						<TracerFxChanceSP value="0.150000"/>
						<TracerFxChanceMP value="0.750000"/>
						<FlashFxChanceSP value="1.000000"/>
						<FlashFxChanceMP value="1.000000"/>
						<FlashFxAltChance value="0.200000"/>
						<FlashFxScale value="1.000000"/>
						<FlashFxLightEnabled value="true"/>
						<FlashFxLightCastsShadows value="false"/>
						<FlashFxLightOffsetDist value="0.000000"/>
						<FlashFxLightRGBAMin x="255.000000" y="93.000000" z="25.000000"/>
						<FlashFxLightRGBAMax x="255.000000" y="100.000000" z="50.000000"/>
						<FlashFxLightIntensityMinMax x="1.000000" y="2.000000"/>
						<FlashFxLightRangeMinMax x="2.00000" y="2.500000"/>
						<FlashFxLightFalloffMinMax x="1024.000000" y="1536.000000"/>
						<GroundDisturbFxEnabled value="false"/>
						<GroundDisturbFxDist value="5.000000"/>
						<GroundDisturbFxNameDefault/>
						<GroundDisturbFxNameSand/>
						<GroundDisturbFxNameDirt/>
						<GroundDisturbFxNameWater/>
						<GroundDisturbFxNameFoliage/>
					</Fx>
					<InitialRumbleDuration value="86"/>
					<InitialRumbleIntensity value="0.366000"/>
					<InitialRumbleIntensityTrigger value="0.950000"/>
					<RumbleDuration value="96"/>
					<RumbleIntensity value="0.100000"/>
					<RumbleIntensityTrigger value="0.800000"/>
					<RumbleDamageIntensity value="1.000000"/>
					<InitialRumbleDurationFps value="106"/>
					<InitialRumbleIntensityFps value="0.700000"/>
					<RumbleDurationFps value="95"/>
					<RumbleIntensityFps value="0.650000"/>
					<NetworkPlayerDamageModifier value="1.000000"/>
					<NetworkPedDamageModifier value="1.000000"/>
					<NetworkHeadShotPlayerDamageModifier value="2.500000"/>
					<LockOnRange value="55.000000"/>
					<WeaponRange value="165.000000"/>
					<BulletDirectionOffsetInDegrees value="0.000000"/>
					<AiSoundRange value="-1.000000"/>
					<AiPotentialBlastEventRange value="-1.000000"/>
					<DamageFallOffRangeMin value="40.000000"/>
					<DamageFallOffRangeMax value="120.000000"/>
					<DamageFallOffModifier value="0.300000"/>
					<VehicleWeaponHash/>
					<DefaultCameraHash>DEFAULT_THIRD_PERSON_PED_AIM_CAMERA</DefaultCameraHash>
					<CoverCameraHash>DEFAULT_THIRD_PERSON_PED_AIM_IN_COVER_CAMERA</CoverCameraHash>
					<CoverReadyToFireCameraHash/>
					<RunAndGunCameraHash>DEFAULT_THIRD_PERSON_PED_RUN_AND_GUN_CAMERA</RunAndGunCameraHash>
					<CinematicShootingCameraHash>DEFAULT_THIRD_PERSON_PED_CINEMATIC_SHOOTING_CAMERA</CinematicShootingCameraHash>
					<AlternativeOrScopedCameraHash/>
					<RunAndGunAlternativeOrScopedCameraHash/>
					<CinematicShootingAlternativeOrScopedCameraHash/>
					<CameraFov value="45.000000"/>
					<FirstPersonScopeFov value="35.00000"/>
					<FirstPersonScopeAttachmentFov value="30.00000"/>
					<FirstPersonRNGOffset x="0.000000" y="0.000000" z="0.000000"/>
					<FirstPersonRNGRotationOffset x="0.000000" y="0.000000" z="0.000000"/>
					<FirstPersonLTOffset x="0.000000" y="0.000000" z="0.000000"/>
					<FirstPersonLTRotationOffset x="0.000000" y="0.000000" z="0.000000"/>
					<FirstPersonScopeOffset x="0.00000" y="-0.0400" z="0.00000"/>
					<FirstPersonScopeAttachmentOffset x="0.0020" y="-0.02000" z="-0.0160"/>
					<FirstPersonScopeRotationOffset x="0.0000" y="0.0000" z="0.2000"/>
					<FirstPersonScopeAttachmentRotationOffset x="0.00000" y="0.00000" z="0.2000"/>
					<FirstPersonAsThirdPersonIdleOffset x="0.10" y="-0.030000" z="0.000000"/>
					<FirstPersonAsThirdPersonRNGOffset x="-0.050" y="0.000000" z="-0.04"/>
					<FirstPersonAsThirdPersonLTOffset x="0.0250" y="0.000000" z="-0.050"/>
					<FirstPersonAsThirdPersonScopeOffset x="0.077000" y="0.000000" z="-0.0350000"/>
					<FirstPersonAsThirdPersonWeaponBlockedOffset x="0.0000000" y="0.000000" z="0.000000"/>
					<FirstPersonDofSubjectMagnificationPowerFactorNear value="1.025000"/>
					<FirstPersonDofMaxNearInFocusDistance value="0.000000"/>
					<FirstPersonDofMaxNearInFocusDistanceBlendLevel value="0.300000"/>
					<ZoomFactorForAccurateMode value="1.300000"/>
					<RecoilShakeHash>SMG_RECOIL_SHAKE</RecoilShakeHash>
					<RecoilShakeHashFirstPerson>FPS_SMG_RECOIL_SHAKE</RecoilShakeHashFirstPerson>
					<AccuracyOffsetShakeHash>DEFAULT_THIRD_PERSON_ACCURACY_OFFSET_SHAKE</AccuracyOffsetShakeHash>
					<MinTimeBetweenRecoilShakes value="150"/>
					<RecoilShakeAmplitude value="0.666000"/>
					<ExplosionShakeAmplitude value="-1.000000"/>
					<IkRecoilDisplacement value="0.0"/>
					<IkRecoilDisplacementScope value="0.001"/>
					<IkRecoilDisplacementScaleBackward value="1.0"/>
					<IkRecoilDisplacementScaleVertical value="0.4"/>
					<ReticuleHudPosition x="0.000000" y="0.000000"/>
					<AimOffsetMin x="0.260000" y="0.125000" z="0.300000"/>
					<AimProbeLengthMin value="0.300000"/>
					<AimOffsetMax x="0.250000" y="-0.255000" z="0.325000"/>
					<AimProbeLengthMax value="0.285000"/>
					<AimOffsetMinFPSIdle x="0.178000" y="0.392000" z="0.135000"/>
					<AimOffsetMedFPSIdle x="0.169000" y="0.312000" z="0.420000"/>
					<AimOffsetMaxFPSIdle x="0.187000" y="0.064000" z="0.649000"/>
					<AimOffsetEndPosMinFPSIdle x="0.208000" y="0.700000" z="0.003000"/>
					<AimOffsetEndPosMedFPSIdle x="0.203000" y="0.604000" z="0.553000"/>
					<AimOffsetEndPosMaxFPSIdle x="0.20700" y="-0.040000" z="0.942000"/>
					<AimOffsetMinFPSLT x="0.009000" y="0.334000" z="0.555000"/>
					<AimOffsetMaxFPSLT x="0.062000" y="-0.164000" z="0.588000"/>
					<AimOffsetMinFPSRNG x="0.114000" y="0.390000" z="0.485000"/>
					<AimOffsetMaxFPSRNG x="0.113000" y="-0.263000" z="0.586000"/>
					<AimOffsetMinFPSScope x="0.009000" y="0.421000" z="0.462000"/>
					<AimOffsetMaxFPSScope x="0.037000" y="-0.224000" z="0.639000"/>
					<TorsoAimOffset x="-1.000000" y="0.550000"/>
					<TorsoCrouchedAimOffset x="0.200000" y="0.100000"/>
					<LeftHandIkOffset x="0.000000" y="0.000000" z="0.000000"/>
					<ReticuleMinSizeStanding value="0.750000"/>
					<ReticuleMinSizeCrouched value="0.600000"/>
					<ReticuleScale value="0.300000"/>
					<ReticuleStyleHash>WEAPONTYPE_SMG</ReticuleStyleHash>
					<FirstPersonReticuleStyleHash/>
					<PickupHash>PICKUP_WEAPON_MICROSMG</PickupHash>
					<MPPickupHash>PICKUP_AMMO_BULLET_MP</MPPickupHash>
					<HumanNameHash>WEAPON_CODY</HumanNameHash>
					<MovementModeConditionalIdle>MMI_1Handed</MovementModeConditionalIdle>
					<StatName>MICROSMG</StatName>
					<KnockdownCount value="-1"/>
					<KillshotImpulseScale value="1.000000"/>
					<NmShotTuningSet>AutomaticSMG</NmShotTuningSet>
					<AttachPoints>
						<Item>
							<AttachBone>WAPSupp</AttachBone>
							<Components>
								<Item>
									<Default value="true"/>
									<Name>COMPONENT_AT_CODY_SUPP</Name>
								</Item>
							</Components>
						</Item>
						<Item>
							<AttachBone>WAPClip</AttachBone>
							<Components>
								<Item>
									<Default value="true"/>
									<Name>COMPONENT_CODY_CLIP_01</Name>
								</Item>
							</Components>
						</Item>
					</AttachPoints>
					<GunFeedBone/>
					<TargetSequenceGroup/>
					<WeaponFlags>CarriedInHand Automatic Gun CanLockonOnFoot CanLockonInVehicle CanFreeAim AnimReload AnimCrouchFire TreatAsOneHandedInCover UsableOnFoot UsableInCover AllowEarlyExitFromFireAnimAfterBulletFired NoLeftHandIKWhenBlocked AllowCloseQuarterKills HasLowCoverReloads HasLowCoverSwaps UseLeftHandIkWhenAiming QuitTransitionToIdleIntroOnWeaponChange DisableLeftHandIkWhenOnFoot UseFPSAimIK UseFPSSecondaryMotion UseAlternateFPDrivebyClipset</WeaponFlags>
					<TintSpecValues ref="TINT_DEFAULT"/>
					<FiringPatternAliases ref="FIRING_PATTERN_SMG"/>
					<ReloadUpperBodyFixupExpressionData ref="default"/>
					<AmmoDiminishingRate value="3"/>
					<AimingBreathingAdditiveWeight value="1.000000"/>
					<FiringBreathingAdditiveWeight value="1.000000"/>
					<StealthAimingBreathingAdditiveWeight value="1.000000"/>
					<StealthFiringBreathingAdditiveWeight value="1.000000"/>
					<AimingLeanAdditiveWeight value="1.000000"/>
					<FiringLeanAdditiveWeight value="1.000000"/>
					<StealthAimingLeanAdditiveWeight value="1.000000"/>
					<StealthFiringLeanAdditiveWeight value="1.000000"/>
					<ExpandPedCapsuleRadius value="0.000000"/>
					<AudioCollisionHash/>
					<HudDamage value="21"/>
					<HudSpeed value="60"/>
					<HudCapacity value="20"/>
					<HudAccuracy value="30"/>
					<HudRange value="25"/>
				</Item>
			</Infos>
		</Item>
	</Infos>
	<Name>AR</Name>
</CWeaponInfoBlob>
