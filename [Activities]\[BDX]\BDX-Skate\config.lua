--------------------------------------
-- <!>--    BODHIX | STUDIO     --<!>--
--------------------------------------
--------------------------------------
-- <!>--     SKATE | CAREER     --<!>--
--------------------------------------
-- Support & Feedback: https://discord.gg/PjN7AWqkpF
-- How to: 
-- Use E to Pickup the Skateboard or put in your back 
-- Use G to Ride the Skateboard or put it in you Hand
-- For Tricks, set the Keys in Settings / Key Binding / FiveM
-- You need a the Trigger Event for custom inventory? Use this one: TriggerClientEvent('bodhix-skating:client:start', source, item)
Config = {}

Config.Debug = false -- True / False for Debug System

Config.Framework = "esx" -- Write your Framework: "qb" or "esx" or "vrp" or "custom". 

-- Settings
Config.ItemName = 'skateboard'
Config.Target = "ox" -- Write your Target System: "qb" or "ox" or "none".
Config.TextFont = 4
Config.FrameworkResourceName = nil
Config.MaxSpeedKmh = 40 -- This does not really change that much unless you get a boost somehow.
Config.maxJumpHeigh = 5.0 -- We suggest not to mess to much with this (And yes, you can jump very high).
Config.maxFallSurvival = 45.0
Config.LoseConnectionDistance = 2.0 -- This is the distance from you to the skateboard (Don't mess with this, unless you know, what you are doing).
Config.MinimumSkateSpeed = 2.0
Config.MinGroundHeight = 1.0
Config.ShowScore = false
Config.EnablePeds = true
Config.PickupKey = 38
Config.ConnectPlayer = 113
Config.DesignCount = 18
Config.DeckPrice = 1500
Config.TrucksPrice = 1000
Config.WheelsPrice = 500

Config.ModernBack = -0.25 -- Adjust if the Modern Skateboard doesn't fit when you put it in your back.
Config.ClassicBack = -0.32 -- Adjust if the Classic Skateboard doesn't fit when you put it in your back (DLC Only).

Config.Language = {
    Info = {
        ['controls'] = 'Press E to Pickup | Press G to Ride',
        ['warning'] = 'The Workshop is currently in use by another player.',
        ['purchase'] = 'You have successfully purchased this item!',
        ['failed'] = 'You dont have enough money.',
        ['error'] = 'You already own this Item.'
    },
    Store = {
        ['target'] = 'Open Skate Shop.',
        ['text'] = '[E] Open Skate Shop.'
    },
    Menu = {
        ["equipment"] = "Equipment",
        ["gear"] = "GEAR",
        ["whats_new"] = "WHAT'S NEW",
        ["skateboard"] = "Skateboard",
        ["deck"] = "Deck",
        ["trucks"] = "Trucks",
        ["wheels"] = "Wheels",
        ["purchase"] = "Purchase"
    }
}
Config.Coords = {
    Peds = {
        VeniceBeach = {
            x = -1394.5432,
            y = -1322.8885,
            z = 4.2830,
            heading = 256.2731
        },
        Vinewood = {
            x = 273.6808,
            y = 136.0932,
            z = 104.4110,
            heading = 338.3990
        }
    },
    Skate = {
        VeniceBeach = {
            x = -1385.7,
            y = -1325.45,
            z = 3.91,
            heading = 258.3,
            angle = -17.0
        },
        Vinewood = {
            x = 277.35,
            y = 138.9,
            z = 104.24,
            heading = 159.0,
            angle = -17.0
        }
    },
    Camera = {
        VeniceBeach = {
            x = -1386.4010,
            y = -1326.0321,
            z = 4.6827,
            heading = 320.2581,
            angle = -9.0
        },
        Vinewood = {
            x = 276.8861,   
            y = 139.7246,
            z = 104.88,
            heading = 213.8,
            angle = -8.0
        }
    }
}
Config.Shops = {
    ShopPeds = {{
        Position = vector4(Config.Coords.Peds.VeniceBeach.x, Config.Coords.Peds.VeniceBeach.y,
            Config.Coords.Peds.VeniceBeach.z, Config.Coords.Peds.VeniceBeach.heading),
        Model = 'a_m_m_skater_01',
        Scenarios = 'WORLD_HUMAN_AA_COFFEE',
        StoreName = "VeniceBeach" -- Unique store ID
    }, {
        Position = vector4(Config.Coords.Peds.Vinewood.x, Config.Coords.Peds.Vinewood.y, Config.Coords.Peds.Vinewood.z,
            Config.Coords.Peds.Vinewood.heading),
        Model = 'a_m_m_skater_01',
        Scenarios = 'WORLD_HUMAN_AA_COFFEE',
        StoreName = "Vinewood" -- Unique store ID
    }}
}

Games = {}

Games.EnablePeds = true

-- Coords of Spawning when Minigame Starts
Games.Spawnlocations = {
    BeachPark = {
        x = -1368.0792,
        y = -1396.2998,
        z = 3.4674
    },
    Park = {
        x = -947.6646,
        y = -782.7368,
        z = 15.9212
    }
}

-- NPC For Minigames Spawn Location
Games.skate = {
    BeachParkCoords = {
        x = -1365.0873,
        y = -1417.0070,
        z = 3.6691,
        heading = 97.1605
    },
    ParkCoords = {
        x = -931.6293,
        y = -789.1327,
        z = 15.9210,
        heading = 209.3628
    }
}

Games.SkateSpawn = {
    SkatePeds = {{
        Position = vector4(Games.skate.BeachParkCoords.x, Games.skate.BeachParkCoords.y, Games.skate.BeachParkCoords.z,
            Games.skate.BeachParkCoords.heading),
        Model = a_m_m_skater_01,
        Scenario = 'WORLD_HUMAN_YOGA'
    }, {
        Position = vector4(Games.skate.ParkCoords.x, Games.skate.ParkCoords.y, Games.skate.ParkCoords.z,
            Games.skate.ParkCoords.heading),
        Model = a_m_m_skater_01,
        Scenario = 'WORLD_HUMAN_MUSCLE_FLEX'
    }}
}
