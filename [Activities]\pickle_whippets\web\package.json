{"name": "web", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build --base=/web/dist", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@mantine/core": "^7.13.2", "@mantine/hooks": "^7.13.2", "react": "^18.3.1", "react-dom": "^18.3.1"}, "devDependencies": {"@eslint/js": "^9.11.1", "@types/react": "^18.3.10", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.2", "eslint": "^9.11.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.12", "globals": "^15.9.0", "postcss": "^8.4.47", "postcss-preset-mantine": "^1.17.0", "postcss-simple-vars": "^7.0.1", "typescript": "^5.5.3", "typescript-eslint": "^8.7.0", "vite": "^5.4.8"}}