@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@700&family=Bebas+Neue&family=Roboto+Condensed:wght@300;400&display=swap');

@keyframes slideIn {
    from {
        transform: translateY(-50%) translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateY(-50%) translateX(0);
        opacity: 1;
    }
}

@keyframes fadeInUp {
    from {
        transform: translateY(20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes scaleIn {
    from {
        transform: scale(0.8);
        opacity: 0;
    }
    to {
        transform: scale(1);
        opacity: 1;
    }
}

body {
    margin: 0;
    padding: 0;
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    /* background: url('bg.png') no-repeat center center fixed; */
    background-size: cover;
    font-family: 'Bebas Neue', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.container {
    position: relative;
    padding: 1.5rem;
    border-radius: 15px;
    color: white;
    text-align: center;
    width: 300px;
    height: 600px;
    position: fixed;
    right: 20px;
    top: 50%;
    transform: translateY(-50%) translateX(100%);
    animation: slideIn 0.8s ease-out forwards;
    display: none;
    flex-direction: column;
    justify-content: space-between;
    gap: 1rem;
    background: rgba(0, 0, 0, 0.6);
    border: 2px solid rgba(255, 255, 255, 0.623);
    box-shadow: 
        0 0 15px rgba(0, 0, 0, 0.3),
        inset 0 0 30px rgba(255, 255, 255, 0.05);
}

.container::before,
.container::after {
    content: '';
    position: absolute;
    top: -1px;
    left: -1px;
    right: -1px;
    bottom: -1px;
    border-radius: 15px;
    z-index: -1;
}

.title {
    font-family: 'Orbitron', sans-serif;
    font-weight: 700;
    font-size: 1.7rem;
    margin-top: 0.5rem;
    margin-bottom: 1rem;
    color: #fff;
    text-transform: uppercase;
    letter-spacing: 3px;
    text-shadow: 0 2px 4px rgba(0,0,0,0.4);
    position: relative;
    padding: 0.5rem 1rem;
    opacity: 0;
    animation: scaleIn 0.6s ease-out 0.8s forwards;
}

.title::before,
.title::after,
.title > span::before,
.title > span::after {
    content: '';
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    height: 2px;
}

.title::before {
    bottom: 6px;
    width: 60%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.8), transparent);
}

.title::after {
    bottom: 0px;
    width: 30%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
}

.title > span::before {
    top: 6px;
    width: 60%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.8), transparent);
}

.title > span::after {
    top: 1px;
    width: 30%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
}

.info-text {
    font-family: 'Roboto Condensed', sans-serif;
    text-align: left;
    margin-top: -0.5rem;
    margin-bottom: 1.5rem;
    color: rgba(255, 255, 255, 0.9);
    font-size: 0.95rem;
    padding: 0 0.5rem;
    position: relative;
    z-index: 5;
    background: rgba(0, 0, 0, 0);
    border-radius: 8px;
    padding: 1rem;
}

.info-text::before {
    content: '';
    position: absolute;
    left: 0rem;
    top: 1rem;
    bottom: 1rem;
    width: 2px;
    background: linear-gradient(to bottom,
        rgba(255, 255, 255, 0.1) 0%,
        rgba(255, 255, 255, 0.1) 100%);
}

.info-text::after {
    content: '';
    position: absolute;
    left: 0rem;
    top: 1rem;
    bottom: 1rem;
    width: 2px;
    background: repeating-linear-gradient(to bottom,
        rgba(255, 255, 255, 0) 0,
        rgba(255, 255, 255, 0) 4px,
        rgba(255, 255, 255, 0.15) 4px,
        rgba(255, 255, 255, 0.15) 8px
    );
}

.info-text ul {
    margin: 0;
    padding-left: 2rem;
    list-style-type: none;
    position: relative;
}

.info-text li {
    opacity: 0;
    animation: fadeInUp 0.5s ease-out forwards;
    margin-bottom: 0.5rem;
    position: relative;
}

.info-text li:nth-child(1) { animation-delay: 1.0s; }
.info-text li:nth-child(2) { animation-delay: 1.2s; }
.info-text li:nth-child(3) { animation-delay: 1.4s; }
.info-text li:nth-child(4) { animation-delay: 1.6s; }

.info-text li {
    position: relative;
    padding: 0.5rem 0;
}

.info-text li::before {
    content: '✓';
    position: absolute;
    left: -2.0rem;
    top: 50%;
    color: rgb(65, 105, 225);
    font-size: 0.91rem;
    transform: translateY(-50%);
}

.info-text li::after {
    content: '';
    position: absolute;
    left: -0.8rem;
    top: 50%;
    width: 0.4rem;
    height: 1px;
    background: rgb(255, 255, 255);
    transform: translateY(-50%);
}

.height-controls {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    width: 100%;
    padding: 0 2rem;
    background: rgba(255, 255, 255, 0.05);
    border: 3px solid #b9b9b97d;
    padding: 1rem;
    border-radius: 8px;
    box-sizing: border-box;
}

.slider-container {
    position: relative;
    width: 100%;
    display: flex;
    align-items: center;
    gap: 10px;
}

.height-value {
    font-family: 'Orbitron', sans-serif;
    font-size: 0.85rem;
    font-weight: 500;
    color: #ffffff;
    background: rgba(0, 0, 0, 0.4);
    padding: 3px 8px;
    border: 3px solid #b9b9b97d;
    border-radius: 3px;
    min-width: 35px;
    width: 35px;
    text-align: center;
    white-space: nowrap;
    outline: 0;
}

.height-value::-webkit-outer-spin-button,
.height-value::-webkit-inner-spin-button {
    display: none;
    width: 0;
    height: 0;
    -webkit-appearance: none;
    margin: 0;
}

button {
    font-family: 'Bebas Neue';
    border: none;
    padding: 8px 15px;
    color: white;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 1.1rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.slider {
    -webkit-appearance: none;
    appearance: none;
    width: 100%;
    height: 4px;
    border: 1px solid #b9b9b97d;
    border-radius: 2px;
    background: linear-gradient(90deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.2) 100%);
    outline: none;
    transition: all 0.2s;
}

.slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: #ffffff;
    cursor: pointer;
    transition: all 0.2s;
    border: 3px solid #4169E1;
}

.slider::-moz-range-thumb {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: #ffffff;
    cursor: pointer;
    transition: all 0.2s;
    border: 3px solid #4169E1;
}

.slider::-webkit-slider-thumb:hover,
.slider::-moz-range-thumb:hover {
    background: #4169E1;
    transform: scale(1.1);
}

.action-buttons {
    display: flex;
    justify-content: space-around;
    margin-top: auto;
    padding: 1rem 0;
}

.button-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.1rem;
}

.button-label {
    font-family: 'Roboto Condensed', sans-serif;
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.8);
}

#saveBtn {
    background: rgba(76, 175, 80, 0.8);
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
}

#resetBtn {
    background: rgba(244, 67, 54, 0.8);
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
}

#saveBtn:hover {
    background: rgba(76, 175, 80, 1);
    transform: translateY(-3px);
}

#resetBtn:hover {
    background: rgba(244, 67, 54, 1);
    transform: translateY(-3px);
}

button:active {
    transform: translateY(1px);
}
