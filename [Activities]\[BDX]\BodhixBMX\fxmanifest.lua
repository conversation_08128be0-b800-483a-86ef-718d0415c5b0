shared_scripts { '@FiniAC/fini_events.js', '@FiniAC/fini_events.lua' }

fx_version 'cerulean'
game 'gta5'

name 'BodhixBMX'
description 'BMX Career'
author 'Bodhix'
version '1.1.0'

lua54 'yes'

shared_scripts {
    'config.lua',
}

client_scripts {
    'Client/*.lua',
}

server_scripts {
    'server/*.lua',
    'Whitelist.lua'
}

data_file 'HANDLING_FILE' 'data/handling.meta'

files {
    'data/handling.meta',
    'stream/bodhix@<EMAIL>',
    'stream/bodhix@<EMAIL>',
    'stream/<EMAIL>',
    'html/index.html',
    'server/version.json',
    'html/LAADS-LoveYou.mp3',
    'html/MaxBrhon-AIMidtempo.mp3',
    "stream/NotzfireLeft.ydr",
	"stream/NotzfireRight.ydr",	
    'html/<PERSON><PERSON><PERSON>&<PERSON>-NeverKnewMe.mp3',
    'html/RobbieMendez-HomeHouse.mp3',
    'html/Ghostnaps-GrowApartGarage.mp3',
    'html/Score.mp3',
    'html/MissScore.mp3',
}

ui_page 'html/index.html'

nui_page 'html/index.html'

data_file "DLC_ITYP_REQUEST" "stream/NotzfireLeft.ytyp"
data_file "DLC_ITYP_REQUEST" "stream/NotzfireRight.ytyp"

escrow_ignore {
    'config.lua',
    'Whitelist.lua',
}





dependency '/assetpacks'