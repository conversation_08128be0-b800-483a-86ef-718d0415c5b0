Config = Config or {}

Config.Emotes = {

    ["dbsoda2"] = {
       "amb@code_human_wander_drinking@male@base",
        "static",
        "Soda - Dumb Bitch",
        AnimationOptions = {
            Prop = 'dumbbitchjuice',
            PropBone = 28422,
            PropPlacement = {
                0.0060,
               -0.0020,
               -0.0700,
              180.0000,
              180.0000,
              -10.0000
            },
            
        }
    },







    -- BEAN MACHINE FOOD

    ["cheesecake_a"] = {
        "anim@scripted@island@special_peds@pavel@hs4_pavel_ig5_caviar_p1",
        "base_idle",
        "Plain Cheesecake",
        AnimationOptions = {
            Prop = "cheesecake_a",
            PropBone = 60309,
            PropPlacement = {
                0.02,    -- xPos
                0.0300,  -- yPos
                -0.0050, -- zPos
                0.0,     -- xRot
                0.0,     -- yRot
                0.0      -- zRot
            },
            SecondProp = 'fork',
            SecondPropBone = 28422,
            SecondPropPlacement = {
                0.058,  -- xPos
                -0.025, -- yPos
                0.0,    -- zPos
                0.0,    -- xRot
                0.0,    -- yRot
                130.0   -- zRot
            },
            EmoteLoop = true,
            EmoteMoving = true
        }
    },
    ["coffee_cake"] = {
        "anim@scripted@island@special_peds@pavel@hs4_pavel_ig5_caviar_p1",
        "base_idle",
        "Coffee Cake",
        AnimationOptions = {
            Prop = "coffee_cake",
            PropBone = 60309,
            PropPlacement = {
                0.02,    -- xPos
                0.0300,  -- yPos
                -0.0050, -- zPos
                0.0,     -- xRot
                0.0,     -- yRot
                0.0      -- zRot
            },
            SecondProp = 'fork',
            SecondPropBone = 28422,
            SecondPropPlacement = {
                0.058,  -- xPos
                -0.025, -- yPos
                0.0,    -- zPos
                0.0,    -- xRot
                0.0,    -- yRot
                130.0   -- zRot
            },
            EmoteLoop = true,
            EmoteMoving = true
        }
    },
    ["bean_bagel_a"] = {
        "mp_player_inteat@burger",
        "mp_player_int_eat_burger",
        "Egg And Cheese Bagel",
        AnimationOptions = {
            Prop = 'bean_bagel_a',
            PropBone = 60309,
            PropPlacement = {
                0.0000,    -- xPos
                0.0010,    -- yPos
                -0.050,    -- zPos
                30.0000,   -- xRot
                0.0000,    -- yRot
                -80.0000,  -- zRot
            },
            EmoteLoop = true,
            EmoteMoving = true
        }
    },
    ["bean_bagel_b"] = {
        "mp_player_inteat@burger",
        "mp_player_int_eat_burger",
        "Blueberry Cream Cheese Bagel",
        AnimationOptions = {
            Prop = 'bean_bagel_b',
            PropBone = 60309,
            PropPlacement = {
                0.0000,    -- xPos
                0.0010,    -- yPos
                -0.050,    -- zPos
                30.0000,   -- xRot
                0.0000,    -- yRot
                -80.0000,  -- zRot
            },
            EmoteLoop = true,
            EmoteMoving = true
        }
    },
    ["bean_bagel_c"] = {
        "mp_player_inteat@burger",
        "mp_player_int_eat_burger",
        "Cream Cheese Bagel",
        AnimationOptions = {
            Prop = 'bean_bagel_c',
            PropBone = 60309,
            PropPlacement = {
                0.0000,    -- xPos
                0.0010,    -- yPos
                -0.050,    -- zPos
                30.0000,   -- xRot
                0.0000,    -- yRot
                -80.0000,  -- zRot
            },
            EmoteLoop = true,
            EmoteMoving = true
        }
    },
    ["bean_c_roll"] = {
        "mp_player_inteat@burger",
        "mp_player_int_eat_burger",
        "Cinnamon Roll",
        AnimationOptions = {
            Prop = 'bean_c_roll',
            PropBone = 60309,
            PropPlacement = {
                0.0000,    -- xPos
                0.0010,    -- yPos
                -0.050,    -- zPos
                30.0000,   -- xRot
                0.0000,    -- yRot
                -80.0000,  -- zRot
            },
            EmoteLoop = true,
            EmoteMoving = true
        }
    },
    ["bean_danish_a"] = {
        "mp_player_inteat@burger",
        "mp_player_int_eat_burger",
        "Cheese Danish",
        AnimationOptions = {
            Prop = 'bean_danish_a',
            PropBone = 60309,
            PropPlacement = {
                0.0000,    -- xPos
                0.0010,    -- yPos
                -0.050,    -- zPos
                30.0000,   -- xRot
                0.0000,    -- yRot
                -80.0000,  -- zRot
            },
            EmoteLoop = true,
            EmoteMoving = true
        }
    },
    ["bean_danish_b"] = {
        "mp_player_inteat@burger",
        "mp_player_int_eat_burger",
        "Strawberry Danish",
        AnimationOptions = {
            Prop = 'bean_danish_b',
            PropBone = 60309,
            PropPlacement = {
                0.0000,    -- xPos
                0.0010,    -- yPos
                -0.050,    -- zPos
                30.0000,   -- xRot
                0.0000,    -- yRot
                -80.0000,  -- zRot
            },
            EmoteLoop = true,
            EmoteMoving = true
        }
    },
    ["bean_donut_a"] = {
        "mp_player_inteat@burger",
        "mp_player_int_eat_burger",
        "Iced Donut",
        AnimationOptions = {
            Prop = 'bean_donut_a',
            PropBone = 60309,
            PropPlacement = {
                0.0000,    -- xPos
                0.0010,    -- yPos
                0.050,     -- zPos
                30.0000,   -- xRot
                0.0090,    -- yRot
                -80.0000,  -- zRot
            },
            EmoteLoop = true,
            EmoteMoving = true
        }
    },
    ["bean_donut_b"] = {
        "mp_player_inteat@burger",
        "mp_player_int_eat_burger",
        "Chocolate Sprinkled Donut",
        AnimationOptions = {
            Prop = 'bean_donut_b',
            PropBone = 60309,
            PropPlacement = {
                0.0000,    -- xPos
                0.0010,    -- yPos
                -0.050,    -- zPos
                30.0000,   -- xRot
                0.0000,    -- yRot
                -80.0000,  -- zRot
            },
            EmoteLoop = true,
            EmoteMoving = true
        }
    },
    ["bean_muffin_a"] = {
        "mp_player_inteat@burger",
        "mp_player_int_eat_burger",
        "Chocolate Muffin",
        AnimationOptions = {
            Prop = 'bean_muffin_a',
            PropBone = 60309,
            PropPlacement = {
                0.0000,    -- xPos
                0.0010,    -- yPos
                -0.050,    -- zPos
                30.0000,   -- xRot
                0.0000,    -- yRot
                -80.0000,  -- zRot
            },
            EmoteLoop = true,
            EmoteMoving = true
        }
    },
    ["bean_muffin_b"] = {
        "mp_player_inteat@burger",
        "mp_player_int_eat_burger",
        "Blueberry Muffin",
        AnimationOptions = {
            Prop = 'bean_muffin_b',
            PropBone = 60309,
            PropPlacement = {
                0.0000,    -- xPos
                0.0010,    -- yPos
                -0.050,    -- zPos
                30.0000,   -- xRot
                0.0000,    -- yRot
                -80.0000,  -- zRot
            },
            EmoteLoop = true,
            EmoteMoving = true
        }
    },
    ["bean_scone"] = {
        "mp_player_inteat@burger",
        "mp_player_int_eat_burger",
        "Iced Scone",
        AnimationOptions = {
            Prop = 'bean_scone',
            PropBone = 60309,
            PropPlacement = {
                0.0000,    -- xPos
                0.0010,    -- yPos
                -0.050,    -- zPos
                30.0000,   -- xRot
                0.0000,    -- yRot
                -80.0000,  -- zRot
            },
            EmoteLoop = true,
            EmoteMoving = true
        }
    },


    -- BEAN MACHINE DRINKS


    ["bean_iced_a"] = {
        "smo@milkshake_idle",
        "milkshake_idle_clip",
        "Iced Latte",
        AnimationOptions = {
            Prop = 'bean_iced_a',
            PropBone = 28422,
            PropPlacement = {
                0.0170,   -- xPos
                -0.070,   -- yPos
                -0.0700,  -- zPos
                -88.0263, -- xRot
                -25.0367, -- yRot
                -27.3898  -- zRot
            },
            EmoteLoop = true,
            EmoteMoving = true
        }
    },
    ["bean_iced_b"] = {
        "smo@milkshake_idle",
        "milkshake_idle_clip",
        "Iced Mocha Latte",
        AnimationOptions = {
            Prop = 'bean_iced_b',
            PropBone = 28422,
            PropPlacement = {
                0.0170,   -- xPos
                -0.070,   -- yPos
                -0.0700,  -- zPos
                -88.0263, -- xRot
                -25.0367, -- yRot
                -27.3898  -- zRot
            },
            EmoteLoop = true,
            EmoteMoving = true
        }
    },
    ["bean_frappe_a"] = {
        "smo@milkshake_idle",
        "milkshake_idle_clip",
        "Iced Mocha Frappe",
        AnimationOptions = {
            Prop = 'bean_frappe_a',
            PropBone = 28422,
            PropPlacement = {
                0.0170,   -- xPos
                -0.070,   -- yPos
                -0.0700,  -- zPos
                -88.0263, -- xRot
                -25.0367, -- yRot
                -27.3898  -- zRot
            },
            EmoteLoop = true,
            EmoteMoving = true
        }
    },
    ["bean_frappe_b"] = {
        "smo@milkshake_idle",
        "milkshake_idle_clip",
        "Iced Cheesecake Frappe",
        AnimationOptions = {
            Prop = 'bean_frappe_b',
            PropBone = 28422,
            PropPlacement = {
                0.0170,   -- xPos
                -0.070,   -- yPos
                -0.0700,  -- zPos
                -88.0263, -- xRot
                -25.0367, -- yRot
                -27.3898  -- zRot
            },
            EmoteLoop = true,
            EmoteMoving = true
        }
    },
    ["bean_fruit"] = {
        "smo@milkshake_idle",
        "milkshake_idle_clip",
        "The Big Fruit",
        AnimationOptions = {
            Prop = 'bean_fruit',
            PropBone = 28422,
            PropPlacement = {
                0.0170,   -- xPos
                -0.070,   -- yPos
                -0.0700,  -- zPos
                -88.0263, -- xRot
                -25.0367, -- yRot
                -27.3898  -- zRot
            },
            EmoteLoop = true,
            EmoteMoving = true
        }
    },
    ["bean_togo_s"] = {
        "amb@world_human_drinking@coffee@male@idle_a",
        "idle_c",
        "Small Coffee To Go",
        AnimationOptions = {
            Prop = 'bean_togo_s',
            PropBone = 28422,
            PropPlacement = {
                0.0, -- xPos
                0.0, -- yPos
                -0.06, -- zPo
                0.0, -- xRot
                0.0, -- yRot
                -85.0 -- zRot
            },
            EmoteLoop = true,
            EmoteMoving = true
        }
    },
    ["bean_togo_m"] = {
        "amb@world_human_drinking@coffee@male@idle_a",
        "idle_c",
        "Medium Coffee To Go",
        AnimationOptions = {
            Prop = 'bean_togo_m',
            PropBone = 28422,
            PropPlacement = {
                0.0,     -- xPos
                0.0,     -- yPos
                -0.06,   -- zPo
                0.0,     -- xRot
                0.0,     -- yRot
                -85.0    -- zRot
            },
            EmoteLoop = true,
            EmoteMoving = true
        }
    },
    ["bean_togo_l"] = {
        "amb@world_human_drinking@coffee@male@idle_a",
        "idle_c",
        "Large Coffee To Go",
        AnimationOptions = {
            Prop = 'bean_togo_l',
            PropBone = 28422,
            PropPlacement = {
                0.0,    -- xPos
                0.0,    -- yPos
                -0.06,  -- zPos
                0.0,    -- xRot
                0.0,    -- yRot
                -85.0   -- zRot
            },
            EmoteLoop = true,
            EmoteMoving = true
        }
    },
    ["bean_espresso"] = {
        "amb@world_human_drinking@coffee@male@idle_a",
        "idle_c",
        "Bean Espresso",
        AnimationOptions = {
            Prop = 'bean_espresso',
            PropBone = 28422,
            PropPlacement = {
                0.0,   -- xPos
                0.0,   -- yPos
                -0.02, -- zPos
                0.0,   -- xRot
                0.0,   -- yRot
                -105.0 -- zRot
            },
            EmoteLoop = true,
            EmoteMoving = true
        }
    },
    ["bean_cappuccino"] = {
        "amb@world_human_drinking@coffee@male@idle_a",
        "idle_c",
        "Bean Cappuccino",
        AnimationOptions = {
            Prop = 'bean_cappuccino',
            PropBone = 28422,
            PropPlacement = {
                0.0,  -- xPos
                0.0,  -- yPos
                0.00, -- zPos
                0.0,  -- xRot
                0.0,  -- yRot
                -85.0 -- zRot
            },
            EmoteLoop = true,
            EmoteMoving = true
        }
    },
    ["bean_latte"] = {
        "amb@world_human_drinking@coffee@male@idle_a",
        "idle_c",
        "Bean Latte",
        AnimationOptions = {
            Prop = 'bean_latte',
            PropBone = 28422,
            PropPlacement = {
                0.0,   -- xPos
                0.0,   -- yPos
                -0.03, -- zPos
                0.0,   -- xRot
                0.0,   -- yRot
                115.0  -- zRot
            },
            EmoteLoop = true,
            EmoteMoving = true
        }
    },
    ["bean_coffee"] = {
        "amb@world_human_drinking@coffee@male@idle_a",
        "idle_c",
        "Bean Coffee",
        AnimationOptions = {
            Prop = 'bean_coffee',
            PropBone = 28422,
            PropPlacement = {
                0.0,       -- xPos
                0.0,       -- yPos
                -0.03,     -- zPos
                0.0,       -- xRot
                0.0,       -- yRot
                115.0      -- zRot
            },
            EmoteLoop = true,
            EmoteMoving = true
        }
    },
    ["bean_matcha"] = {
        "amb@world_human_drinking@coffee@male@idle_a",
        "idle_c",
        "Bean Matcha",
        AnimationOptions = {
            Prop = 'bean_matcha',
            PropBone = 28422,
            PropPlacement = {
                0.0,   -- xPos
                0.0,   -- yPos
                -0.03, -- zPos
                0.0,   -- xRot
                0.0,   -- yRot
                115.0  -- zRot
            },
            EmoteLoop = true,
            EmoteMoving = true
        }
    },
    ["bean_hotchoc"] = {
        "amb@world_human_drinking@coffee@male@idle_a",
        "idle_c",
        "Bean Hot Chocolate",
        AnimationOptions = {
            Prop = 'bean_hotchoc',
            PropBone = 28422,
            PropPlacement = {
                0.0,   -- xPos
                0.0,   -- yPos
                -0.03, -- zPos
                0.0,   -- xRot
                0.0,   -- yRot
                115.0  -- zRot
            },
            EmoteLoop = true,
            EmoteMoving = true
        }
    },



    ["coffee_cake"] = {
        "anim@scripted@island@special_peds@pavel@hs4_pavel_ig5_caviar_p1",
        "base_idle",
        "Coffee Cake",
        AnimationOptions = {
            Prop = "coffee_cake",
            PropBone = 60309,
            PropPlacement = {
                0.02,    -- xPos
                0.0300,  -- yPos
                -0.0050, -- zPos
                0.0,     -- xRot
                0.0,     -- yRot
                0.0      -- zRot
            },
            SecondProp = 'fork',
            SecondPropBone = 28422,
            SecondPropPlacement = {
                0.058,  -- xPos
                -0.025, -- yPos
                0.0,    -- zPos
                0.0,    -- xRot
                0.0,    -- yRot
                130.0   -- zRot
            },
            EmoteLoop = true,
            EmoteMoving = true
        }
    },

    ["soda"] = {
        "amb@world_human_drinking@coffee@male@idle_a",
        "idle_c",
        "Soda - eCola Can",
        AnimationOptions = {
            Prop = 'prop_ecola_can',
            PropBone = 28422,
            PropPlacement = {
                0.0,
                0.0,
                0.0,
                0.0,
                0.0,
                130.0
            },
            EmoteLoop = true,
            EmoteMoving = true
        }
    },
    ["wine2"] = {
        "amb@world_human_drinking@beer@female@idle_a",
        "idle_e",
        "Wine 2 - Bottle",
        AnimationOptions = {
            Prop = 'prop_wine_rose',
            PropBone = 28422,
            PropPlacement = {
                -0.0,
                0.04,
                -0.19,
                10.0,
                0.0,
                0.0
            },
            EmoteLoop = true,
            EmoteMoving = true,
            ---       PropPlacement = {-0.0, 0.03, -0.20, 5.0, 0.0, 0.0},
            ---     F&B   L&R   U&D  R.F&B
        }
    },
    ["soda2"] = {
        "amb@world_human_drinking@coffee@male@idle_a",
        "idle_c",
        "Soda 2 - Sprunk Can",
        AnimationOptions = {
            Prop = 'ng_proc_sodacan_01b',
            PropBone = 28422,
            PropPlacement = {
                0.0050,
                -0.0010,
                -0.0800,
                0.0,
                0.0,
                160.0
            },
            EmoteLoop = true,
            EmoteMoving = true
        }
    },

    ["pizzas4"] = { --- Custom Prop by knjgh
        "mp_player_inteat@burger",
        "mp_player_int_eat_burger",
        "Pizza Slice - Margherita",
        AnimationOptions = {
            Prop = 'knjgh_pizzaslice4',
            PropBone = 60309,
            PropPlacement = {
                0.0500,
                -0.0200,
                -0.0200,
                73.6928,
                -66.7427,
                68.3677
            },
            EmoteMoving = true
        }
    },
    ["pizzas2"] = { --- Custom Prop by knjgh
        "mp_player_inteat@burger",
        "mp_player_int_eat_burger",
        "Pizza Slice - Tomato And Pesto",
        AnimationOptions = {
            Prop = 'knjgh_pizzaslice2',
            PropBone = 60309,
            PropPlacement = {
                0.0500,
                -0.0200,
                -0.0200,
                73.6928,
                -66.7427,
                68.3677
            },
            EmoteMoving = true
        }
    },
    ["xmasic"] = {
        "anim@scripted@island@special_peds@pavel@hs4_pavel_ig5_caviar_p1",
        "base_idle",
        "Xmas Ice Cream",
        AnimationOptions = {
            Prop = "pata_christmasfood7",
            PropBone = 60309,
            PropPlacement = {
                -0.0460,
                0.0000,
                -0.0300,
                0.0,
                0.0,
                -50.0000
            },
            SecondProp = 'h4_prop_h4_coke_spoon_01',
            SecondPropBone = 28422,
            SecondPropPlacement = {
                0.0,
                0.0,
                0.0,
                0.0,
                20.0,
                0.0
            },
            EmoteLoop = true,
            EmoteMoving = true
        }
    },
    ["macaroon"] = { --- Custom Prop by Bzzzi
        "mp_player_inteat@burger",
        "mp_player_int_eat_burger",
        "Macaroon",
        AnimationOptions = {
            Prop = 'bzzz_food_xmas_macaroon_a',
            PropBone = 18905,
            PropPlacement = {
                0.15,
                0.07,
                0.00,
                38.0,
                7.0,
                7.0
            },
            EmoteMoving = true
        }
    },
    ["pizzas3"] = { --- Custom Prop by knjgh
        "mp_player_inteat@burger",
        "mp_player_int_eat_burger",
        "Pizza Slice - Mushroom",
        AnimationOptions = {
            Prop = 'knjgh_pizzaslice3',
            PropBone = 60309,
            PropPlacement = {
                0.0500,
                -0.0200,
                -0.0200,
                73.6928,
                -66.7427,
                68.3677
            },
            EmoteMoving = true
        }
    },
    ["pizzas5"] = { --- Custom Prop by knjgh
        "mp_player_inteat@burger",
        "mp_player_int_eat_burger",
        "Pizza Slice - Double Peperoni",
        AnimationOptions = {
            Prop = 'knjgh_pizzaslice5',
            PropBone = 60309,
            PropPlacement = {
                0.0500,
                -0.0200,
                -0.0200,
                73.6928,
                -66.7427,
                68.3677
            },
            EmoteMoving = true
        }
    },
    ["xmascc"] = { --- Custom Prop by PataMods
        "mp_player_inteat@burger",
        "mp_player_int_eat_burger",
        "Xmas Cupcake",
        AnimationOptions = {
            Prop = 'pata_christmasfood6',
            PropBone = 60309,
            PropPlacement = {
                0.0100,
                0.0200,
                -0.0100,
                -170.1788,
                87.6716,
                30.0540
            },
            EmoteMoving = true
        }
    },
    ["pizzaslice"] = { --- Custom Prop by knjgh
        "mp_player_inteat@burger",
        "mp_player_int_eat_burger",
        "Pizza Slice - Jalapeño And Peperoni",
        AnimationOptions = {
            Prop = 'knjgh_pizzaslice1',
            PropBone = 60309,
            PropPlacement = {
                0.0500,
                -0.0200,
                -0.0200,
                73.6928,
                -66.7427,
                68.3677
            },
            EmoteMoving = true
        }
    },

    ["matcha"] = {
        "mp_player_intdrink",
        "loop_bottle",
        "UwU - Matcha",
        AnimationOptions =
        {
            Prop = "em_matcha",
            PropBone = 18905,
            PropPlacement = {
                0.09,
                0.007,
                0.04,
                240.0,
                -60.0
            },
            EmoteMoving = true,
            EmoteLoop = true
        }
    },

    ["katsu"] = {
        "anim@scripted@island@special_peds@pavel@hs4_pavel_ig5_caviar_p1",
        "base_idle",
        "UwU - Katsu",
        AnimationOptions = {
            Prop = "em_katsu",
            PropBone = 60309,
            PropPlacement = {
                0.0,
                0.00,
                -0.0100,
                0.0,
                0.0,
                0.0
            },
            SecondProp = 'prop_cs_fork',
            SecondPropBone = 28422,
            SecondPropPlacement = {
                0.0,
                0.0,
                0.0,
                0.0,
                0.0,
                180.0
            },
            EmoteLoop = true,
            EmoteMoving = true
        }
    },

    ["flan"] = {
        "anim@scripted@island@special_peds@pavel@hs4_pavel_ig5_caviar_p1",
        "base_idle",
        "UwU - Flan",
        AnimationOptions = {
            Prop = "em_flan",
            PropBone = 60309,
            PropPlacement = {
                0.0,
                0.0300,
                0.000,
                0.0,
                0.0,
                0.0
            },
            SecondProp = 'prop_cs_fork',
            SecondPropBone = 28422,
            SecondPropPlacement = {
                -0.01,
                0.01,
                0.0,
                0.0,
                0.0,
                180.0
            },
            EmoteLoop = true,
            EmoteMoving = true
        }
    },

    ["bento"] = {
        "anim@scripted@island@special_peds@pavel@hs4_pavel_ig5_caviar_p1",
        "base_idle",
        "UwU - Bento",
        AnimationOptions = {
            Prop = "em_bento",
            PropBone = 60309,
            PropPlacement = {
                0.0,
                0.0300,
                0.0100,
                0.0,
                0.0,
                0.0
            },
            SecondProp = 'prop_cs_fork',
            SecondPropBone = 28422,
            SecondPropPlacement = {
                0.0,
                0.0,
                0.0,
                0.0,
                0.0,
                180.0
            },
            EmoteLoop = true,
            EmoteMoving = true
        }
    },

    ["redvelvet"] = {
        "mp_player_intdrink",
        "loop_bottle",
        "UwU - Red Velvet Latte",
        AnimationOptions =
        {
            Prop = "em_redvelvet",
            PropBone = 18905,
            PropPlacement = {
                0.12,
                0.008,
                0.03,
                240.0,
                -60.0
            },
            EmoteMoving = true,
            EmoteLoop = true
        }
    },

    ["smokecigar"] = {
        "amb@world_human_aa_smoke@male@idle_a",
        "idle_b",
        "Smoke Cigar",
        AnimationOptions =
        {
            Prop = 'prop_cigar_02',
            PropBone = 28422,
            PropPlacement = { 0.0, 0.0, 0.0, 0.0, 180.0, 0.0 },
            EmoteLoop = true,
            EmoteMoving = true,
        }
    },



    ["icecreamb"] = {
        "mp_player_inteat@burger",
        "mp_player_int_eat_burger",
        "Ice cream Chocolate",
        AnimationOptions = {
            Prop = 'bzzz_icecream_chocolate',
            PropBone = 18905,
            PropPlacement = {
                0.14,
                0.03,
                0.01,
                85.0,
                70.0,
                -203.0
            },
            EmoteMoving = true
        }
    },
    ["donut3"] = { --- Custom Prop by Bzzzi
        "mp_player_inteat@burger",
        "mp_player_int_eat_burger",
        "Donut Raspberry",
        AnimationOptions = {
            Prop = 'bzzz_foodpack_donut001',
            PropBone = 60309,
            PropPlacement = {
                0.0000,
                -0.0300,
                -0.0100,
                10.0000,
                0.0000,
                -1.0000
            },
            EmoteMoving = true
        }
    },
    ["foodbowl"] = { "amb@world_human_drinking@coffee@male@idle_a", "idle_c", "bowl", AnimationOptions = { Prop = "prop_cs_bowl_01", PropBone = 28422, PropPlacement = { 0.0, 0.0, 0.050, 0.0, 0.0, 0.0 }, EmoteMoving = true, EmoteLoop = true, } },
    ["cluckinpie"] = { "mp_player_inteat@burger", "mp_player_int_eat_burger", "", AnimationOptions = { Prop = 'idrp_cluckin_pie', PropBone = 18905, PropPlacement = { 0.16, 0.06, 0.03, -225.0, 0.0, 80.0 }, EmoteLoop = true, EmoteMoving = true, } },
    ["cluckinbigburger"] = { "mp_player_inteat@burger", "mp_player_int_eat_burger", "", AnimationOptions = { Prop = 'idrp_cluckin_dblfillet', PropBone = 18905, PropPlacement = { 0.13, 0.05, 0.02, 130.0, 16.0, 60.0 }, EmoteLoop = true, EmoteMoving = true, } },
    ["cluckinbucket"] = { "mp_player_inteat@burger", "mp_player_int_eat_burger", "", AnimationOptions = { Prop = 'idrp_cluckin_bucket', PropBone = 18905, PropPlacement = { 0.13, 0.05, 0.02, 130.0, 16.0, 60.0 }, EmoteLoop = true, EmoteMoving = true, } },
    ["cluckindrummy"] = { "mp_player_inteat@burger", "mp_player_int_eat_burger", "", AnimationOptions = { Prop = 'idrp_cluckin_drummy', PropBone = 18905, PropPlacement = { 0.13, 0.05, 0.02, -20.0, 40.0, 60.0 }, EmoteLoop = true, EmoteMoving = true, } },
    ["cluckinsoup"] = { "amb@world_human_drinking@coffee@male@idle_a", "idle_c", "", AnimationOptions = { Prop = 'idrp_cluckin_soup', PropBone = 28422, PropPlacement = { 0.0, 0.0, 0.0, 0.0, 0.0, 130.0 }, EmoteLoop = true, EmoteMoving = true, } },
    ["cluckinburger"] = { "mp_player_inteat@burger", "mp_player_int_eat_burger", "", AnimationOptions = { Prop = 'idrp_cluckin_fillet', PropBone = 18905, PropPlacement = { 0.13, 0.05, 0.02, 130.0, 16.0, 60.0 }, EmoteLoop = true, EmoteMoving = true, } },
    ["cluckinwrap"] = { "mp_player_inteat@burger", "mp_player_int_eat_burger", "", AnimationOptions = { Prop = 'idrp_cluckin_wrap', PropBone = 18905, PropPlacement = { 0.13, 0.05, 0.02, 130.0, 16.0, 60.0 }, EmoteLoop = true, EmoteMoving = true, } },
    ["cluckinbuffwings"] = { "mp_player_inteat@burger", "mp_player_int_eat_burger", "", AnimationOptions = { Prop = 'idrp_cluckin_buffwings', PropBone = 60309, PropPlacement = { -0.03, 0.0, -0.01, 0.0, 0.0, 0.0 }, EmoteLoop = true, EmoteMoving = true, } },
    ["cluckinbuffwing"] = { "mp_player_inteat@burger", "mp_player_int_eat_burger", "", AnimationOptions = { Prop = 'idrp_cluckin_buffwing', PropBone = 18905, PropPlacement = { 0.13, 0.05, 0.02, -20.0, 40.0, 60.0 }, EmoteLoop = true, EmoteMoving = true, } },
    ["cluckinwedges"] = { "mp_player_inteat@burger", "mp_player_int_eat_burger", "", AnimationOptions = { Prop = 'idrp_cluckin_wedges', PropBone = 60309, PropPlacement = { -0.03, 0.0, -0.01, 0.0, 0.0, 0.0 }, EmoteLoop = true, EmoteMoving = true, } },
    ["cluckinwingdings"] = { "mp_player_inteat@burger", "mp_player_int_eat_burger", "", AnimationOptions = { Prop = 'idrp_cluckin_wingdings', PropBone = 60309, PropPlacement = { -0.03, 0.0, -0.01, 0.0, 0.0, 0.0 }, EmoteLoop = true, EmoteMoving = true, } },
    ["cluckinpinkdrink"] = { "amb@world_human_drinking@coffee@male@idle_a", "idle_c", "", AnimationOptions = { Prop = 'idrp_cluckin_sberry', PropBone = 28422, PropPlacement = { 0.0, 0.0, 0.0, 0.0, 0.0, 130.0 }, EmoteLoop = true, EmoteMoving = true, } },
    ["cluckinyellowdrink"] = { "amb@world_human_drinking@coffee@male@idle_a", "idle_c", "", AnimationOptions = { Prop = 'idrp_cluckin_sbana', PropBone = 28422, PropPlacement = { 0.0, 0.0, 0.0, 0.0, 0.0, 130.0 }, EmoteLoop = true, EmoteMoving = true, } },

    ["fries"] = { "mp_player_inteat@burger", "mp_player_int_eat_burger_fp", "Fries", AnimationOptions = { Prop = "prop_food_cb_chips", PropBone = 18905, PropPlacement = { 0.020000, 0.020000, -0.020000, 0.0, 0.0 }, EmoteMoving = true, EmoteLoop = true, } },

    -- Hornys
    ["atomdrink"] = { "amb@world_human_drinking@coffee@male@idle_a", "idle_c", "", AnimationOptions = { Prop = 'ng_proc_sodacup_01c', PropBone = 28422, PropPlacement = { 0.0, 0.0, 0.0, 0.0, 0.0, 130.0 }, EmoteLoop = true, EmoteMoving = true, } },
    ["atomhotdog"] = { "amb@world_human_drinking@coffee@male@idle_a", "idle_c", "", AnimationOptions = { Prop = 'prop_food_bs_burger2', PropBone = 28422, PropPlacement = { 0.0, 0.0, 0.0, 0.0, 0.0, 130.0 }, EmoteLoop = true, EmoteMoving = true, } },
    ["atomfries"] = { "amb@world_human_drinking@coffee@male@idle_a", "idle_c", "", AnimationOptions = { Prop = 'prop_food_chips', PropBone = 28422, PropPlacement = { 0.0, 0.0, 0.0, 0.0, 0.0, 130.0 }, EmoteLoop = true, EmoteMoving = true, } },
    ["breadslice"] = { "mp_player_inteat@burger", "mp_player_int_eat_burger", "Bread Slice", AnimationOptions = { Prop = 'v_res_fa_bread03', PropBone = 18905, PropPlacement = { 0.16, 0.08, -0.01, -225.0, -10.0, 0.0 }, EmoteLoop = true, EmoteMoving = true, } },
    ["ecola"] = { "mp_player_intdrink", "loop_bottle", "e-cola", AnimationOptions = { Prop = "prop_ecola_can", PropBone = 18905, PropPlacement = { 0.12, 0.008, 0.03, 240.0, -60.0 }, EmoteMoving = true, EmoteLoop = true, } },
    ["sprunk"] = { "mp_player_intdrink", "loop_bottle", "sprunk", AnimationOptions = { Prop = "v_res_tt_can03", PropBone = 18905, PropPlacement = { 0.12, 0.008, 0.03, 240.0, -60.0 }, EmoteMoving = true, EmoteLoop = true, } },


    --Jim-VanillaUnicorn
    ["whiskeyb"] = { "mp_player_intdrink", "loop_bottle", "(Don't Use) Whiskey Bottle", AnimationOptions = { Prop = "prop_cs_whiskey_bottle", PropBone = 60309, PropPlacement = { 0.0, 0.0, 0.0, 0.0, 0.0 }, EmoteMoving = true, EmoteLoop = true } },
    ["rumb"] = { "mp_player_intdrink", "loop_bottle", "(Don't Use) Rum Bottle", AnimationOptions = { Prop = "prop_rum_bottle", PropBone = 18905, PropPlacement = { 0.03, -0.18, 0.10, 240.0, -60.0 }, EmoteMoving = true, EmoteLoop = true } },
    ["icream"] = { " mp_player_intdrink", "loop_bottle", "Irish Cream Bottle", AnimationOptions = { Prop = "prop_bottle_brandy", PropBone = 18905, PropPlacement = { 0.00, -0.26, 0.10, 240.0, -60.0 }, EmoteMoving = true, EmoteLoop = true } },
    ["ginb"] = { "mp_player_intdrink", "loop_bottle", "(Don't Use) Gin Bottle", AnimationOptions = { Prop = "prop_tequila_bottle", PropBone = 18905, PropPlacement = { 0.00, -0.26, 0.10, 240.0, -60.0 }, EmoteMoving = true, EmoteLoop = true } },
    ["vodkab"] = { "mp_player_intdrink", "loop_bottle", "(Don't Use) Vodka Bottle", AnimationOptions = { Prop = 'prop_vodka_bottle', PropBone = 18905, PropPlacement = { 0.00, -0.26, 0.10, 240.0, -60.0 }, EmoteMoving = true, EmoteLoop = true } },
    ["crisps"] = { "amb@world_human_drinking@coffee@male@idle_a", "idle_c", "Chrisps", AnimationOptions = { Prop = 'v_ret_ml_chips2', PropBone = 28422, PropPlacement = { 0.01, -0.05, -0.1, 0.0, 0.0, 90.0 }, EmoteLoop = true, EmoteMoving = true, } },
    ["beer1"] = { "mp_player_intdrink", "loop_bottle", "Dusche", AnimationOptions = { Prop = "prop_beerdusche", PropBone = 18905, PropPlacement = { 0.04, -0.14, 0.10, 240.0, -60.0 }, EmoteMoving = true, EmoteLoop = true, } },
    ["beer2"] = { "mp_player_intdrink", "loop_bottle", "Logger", AnimationOptions = { Prop = "prop_beer_logopen", PropBone = 18905, PropPlacement = { 0.03, -0.18, 0.10, 240.0, -60.0 }, EmoteMoving = true, EmoteLoop = true, } },
    ["beer3"] = { "mp_player_intdrink", "loop_bottle", "AM Beer", AnimationOptions = { Prop = "prop_beer_amopen", PropBone = 18905, PropPlacement = { 0.03, -0.18, 0.10, 240.0, -60.0 }, EmoteMoving = true, EmoteLoop = true, } },
    ["beer4"] = { "mp_player_intdrink", "loop_bottle", "Pisswasser1", AnimationOptions = { Prop = "prop_beer_pissh", PropBone = 18905, PropPlacement = { 0.03, -0.18, 0.10, 240.0, -60.0 }, EmoteMoving = true, EmoteLoop = true, } },
    ["beer5"] = { "mp_player_intdrink", "loop_bottle", "Pisswasser2", AnimationOptions = { Prop = "prop_amb_beer_bottle", PropBone = 18905, PropPlacement = { 0.12, 0.008, 0.03, 240.0, -60.0 }, EmoteMoving = true, EmoteLoop = true, } },
    ["beer6"] = { "mp_player_intdrink", "loop_bottle", "Pisswasser3", AnimationOptions = { Prop = "prop_cs_beer_bot_02", PropBone = 18905, PropPlacement = { 0.12, 0.008, 0.03, 240.0, -60.0 }, EmoteMoving = true, EmoteLoop = true, } },
    ["ecola"] = { "mp_player_intdrink", "loop_bottle", "E-cola", AnimationOptions = { Prop = "prop_ecola_can", PropBone = 18905, PropPlacement = { 0.12, 0.008, 0.03, 240.0, -60.0 }, EmoteMoving = true, EmoteLoop = true, } },
    ["sprunk"] = { "mp_player_intdrink", "loop_bottle", "Sprunk", AnimationOptions = { Prop = "v_res_tt_can03", PropBone = 18905, PropPlacement = { 0.12, 0.008, 0.03, 240.0, -60.0 }, EmoteMoving = true, EmoteLoop = true, } },
    ["wine"] = { "anim@heists@humane_labs@finale@keycards", "ped_a_enter_loop", "Wine", AnimationOptions = { Prop = 'prop_drink_redwine', PropBone = 18905, PropPlacement = { 0.10, -0.03, 0.03, -100.0, 0.0, -10.0 }, EmoteMoving = true, EmoteLoop = true } },


    ["drink"] = {
        "mp_player_intdrink",
        "loop_bottle",
        "Drink",
        AnimationOptions =
        {
            Prop = "prop_ld_flow_bottle",
            PropBone = 18905,
            PropPlacement = { 0.12, 0.008, 0.03, 240.0, -60.0 },
            EmoteMoving = true,
            EmoteLoop = true,
        }
    },
    ["coffee"] = {
        "amb@world_human_drinking@coffee@male@idle_a",
        "idle_c",
        "Coffee",
        AnimationOptions =
        {
            Prop = 'p_amb_coffeecup_01',
            PropBone = 28422,
            PropPlacement = { 0.0, 0.0, 0.0, 0.0, 0.0, 0.0 },
            EmoteLoop = true,
            EmoteMoving = true
        }
    },
    ["burger"] = {
        "mp_player_inteat@burger",
        "mp_player_int_eat_burger",
        "Burger",
        AnimationOptions =
        {
            Prop = 'prop_cs_burger_01',
            PropBone = 18905,
            PropPlacement = { 0.13, 0.05, 0.02, -50.0, 16.0, 60.0 },
            EmoteMoving = true
        }
    },
    ["beer"] = {
        "amb@world_human_drinking@beer@male@idle_a",
        "idle_c",
        "Beer",
        AnimationOptions =
        {
            Prop = 'prop_amb_beer_bottle',
            PropBone = 28422,
            PropPlacement = { 0.0, 0.0, 0.06, 0.0, 15.0, 0.0 },
            EmoteLoop = true,
            EmoteMoving = true
        }
    },
    ["egobar"] = {
        "mp_player_inteat@burger",
        "mp_player_int_eat_burger",
        "Ego Bar",
        AnimationOptions =
        {
            Prop = 'prop_choc_ego',
            PropBone = 60309,
            PropPlacement = { 0.0, 0.0, 0.0, 0.0, 0.0, 0.0 },
            EmoteMoving = true
        }
    },
    ["sandwich"] = {
        "mp_player_inteat@burger",
        "mp_player_int_eat_burger",
        "Sandwich",
        AnimationOptions =
        {
            Prop = 'prop_sandwich_01',
            PropBone = 18905,
            PropPlacement = { 0.13, 0.05, 0.02, -50.0, 16.0, 60.0 },
            EmoteMoving = true
        }
    },
    ["smoke3"] = {
        "amb@world_human_aa_smoke@male@idle_a",
        "idle_b",
        "Smoke 3",
        AnimationOptions =
        {
            Prop = 'prop_cs_ciggy_01',
            PropBone = 28422,
            PropPlacement = { 0.0, 0.0, 0.0, 0.0, 0.0, 0.0 },
            EmoteLoop = true,
            EmoteMoving = true
        }
    },
    ["whiskey"] = {
        "amb@world_human_drinking@coffee@male@idle_a",
        "idle_c",
        "Whiskey",
        AnimationOptions =
        {
            Prop = 'prop_drink_whisky',
            PropBone = 28422,
            PropPlacement = { 0.01, -0.01, -0.06, 0.0, 0.0, 0.0 },
            EmoteLoop = true,
            EmoteMoving = true
        }
    },
    --Drugs
    ["coke"] = {
        "switch@trevor@trev_smoking_meth",
        "trev_smoking_meth_loop",
        "Coke",
        AnimationOptions =
        { EmoteLoop = true, EmoteMoving = true, }
    },
    ["oxy"] = {
        "mp_suicide",
        "pill",
        "Oxy",
        AnimationOptions =
        { EmoteLoop = true, EmoteMoving = true, }
    },
    ["cigar"] = {
        "amb@world_human_smoking@male@male_a@enter",
        "enter",
        "Cigar",
        AnimationOptions =
        {
            Prop = 'prop_cigar_02',
            PropBone = 47419,
            PropPlacement = { 0.010, 0.0, 0.0, 50.0, 0.0, -80.0 },
            EmoteMoving = true,
            EmoteDuration = 2600
        }
    },
    ["cigar2"] = {
        "amb@world_human_smoking@male@male_a@enter",
        "enter",
        "Cigar 2",
        AnimationOptions =
        {
            Prop = 'prop_cigar_01',
            PropBone = 47419,
            PropPlacement = { 0.010, 0.0, 0.0, 50.0, 0.0, -80.0 },
            EmoteMoving = true,
            EmoteDuration = 2600
        }
    },
    ["joint"] = {
        "amb@world_human_smoking@male@male_a@enter",
        "enter",
        "Joint",
        AnimationOptions =
        {
            Prop = 'p_cs_joint_02',
            PropBone = 47419,
            PropPlacement = { 0.015, -0.009, 0.003, 55.0, 0.0, 110.0 },
            EmoteMoving = true,
            EmoteDuration = 2600
        }
    },
    ["cig"] = {
        "amb@world_human_smoking@male@male_a@enter",
        "enter",
        "Cig",
        AnimationOptions =
        {
            Prop = 'prop_amb_ciggy_01',
            PropBone = 47419,
            PropPlacement = { 0.015, -0.009, 0.003, 55.0, 0.0, 110.0 },
            EmoteMoving = true,
            EmoteDuration = 2600
        }
    },
    ["cup"] = {
        "amb@world_human_drinking@coffee@male@idle_a",
        "idle_c",
        "Cup",
        AnimationOptions = {
            Prop = 'prop_plastic_cup_02',
            PropBone = 28422,
            PropPlacement = {
                0.0,
                0.0,
                0.0,
                0.0,
                0.0,
                0.0
            },
            EmoteLoop = true,
            EmoteMoving = true
        }
    },

    -- UWU EMOTE START

    ["uwu1"] = { "amb@world_human_drinking@coffee@male@idle_a", "idle_c", "", AnimationOptions = { Prop = 'uwu_sml_drink', PropBone = 28422, PropPlacement = { 0.0, 0.0, 0.0, 0.0, 0.0, 130.0 }, EmoteLoop = true, EmoteMoving = true, } },
    ["uwu2"] = { "amb@world_human_drinking@coffee@male@idle_a", "idle_c", "", AnimationOptions = { Prop = 'uwu_lrg_drink', PropBone = 28422, PropPlacement = { 0.03, 0.0, -0.08, 0.0, 0.0, 130.0 }, EmoteLoop = true, EmoteMoving = true, } },
    ["uwu3"] = { "amb@world_human_drinking@coffee@male@idle_a", "idle_c", "", AnimationOptions = { Prop = 'uwu_cup_straw', PropBone = 28422, PropPlacement = { 0.0, 0.0, 0.0, 0.0, 0.0, 130.0 }, EmoteLoop = true, EmoteMoving = true, } },
    ["uwu4"] = { "amb@world_human_drinking@coffee@male@idle_a", "idle_c", "", AnimationOptions = { Prop = 'uwu_mug', PropBone = 28422, PropPlacement = { 0.0, 0.0, 0.0, 0.0, 0.0, 130.0 }, EmoteLoop = true, EmoteMoving = true, } },
    ["uwu5"] = { "mp_player_inteat@burger", "mp_player_int_eat_burger", "", AnimationOptions = { Prop = 'uwu_pastry', PropBone = 18905, PropPlacement = { 0.16, 0.06, -0.03, -50.0, 16.0, 60.0 }, EmoteMoving = true, } },
    ["uwu6"] = { "mp_player_inteat@burger", "mp_player_int_eat_burger", "", AnimationOptions = { Prop = 'uwu_cookie', PropBone = 18905, PropPlacement = { 0.16, 0.08, -0.01, -225.0, 20.0, 60.0 }, EmoteMoving = true, } },
    ["uwu7"] = { "mp_player_inteat@burger", "mp_player_int_eat_burger", "", AnimationOptions = { Prop = 'uwu_sushi', PropBone = 18905, PropPlacement = { 0.18, 0.03, 0.02, -50.0, 16.0, 60.0 }, EmoteMoving = true, } },
    ["uwu8"] = { "amb@world_human_seat_wall_eating@male@both_hands@idle_a", "idle_c", "", AnimationOptions = { Prop = 'uwu_eggroll', PropBone = 60309, PropPlacement = { 0.10, 0.03, 0.08, -95.0, 60.0, 0.0 }, EmoteMoving = true, } },
    ["uwu9"] = { "anim@scripted@island@special_peds@pavel@hs4_pavel_ig5_caviar_p1", "base_idle", "", AnimationOptions = { Prop = "uwu_salad_bowl", PropBone = 60309, PropPlacement = { 0.0, 0.0300, 0.0100, 0.0, 0.0, 0.0 }, SecondProp = 'uwu_salad_spoon', SecondPropBone = 28422, SecondPropPlacement = { 0.0, 0.0, 0.0, 0.0, 0.0, 0.0 }, EmoteLoop = true, EmoteMoving = true, } },
    ["uwu10"] = { "mp_player_inteat@burger", "mp_player_int_eat_burger", "", AnimationOptions = { Prop = 'uwu_sandy', PropBone = 18905, PropPlacement = { 0.16, 0.08, 0.05, -225.0, 20.0, 60.0 }, EmoteMoving = true, } },
    ["uwu11"] = { "amb@world_human_drinking@coffee@male@idle_a", "idle_c", "", AnimationOptions = { Prop = 'uwu_cupcake', PropBone = 28422, PropPlacement = { 0.0, 0.0, -0.03, 0.0, 0.0, 130.0 }, EmoteLoop = true, EmoteMoving = true, } },
    ["uwu12"] = { "amb@world_human_drinking@coffee@male@idle_a", "idle_c", "", AnimationOptions = { Prop = 'uwu_btea', PropBone = 28422, PropPlacement = { 0.02, 0.0, -0.05, 0.0, 0.0, 130.0 }, EmoteLoop = true, EmoteMoving = true, } },
    ["uwu13"] = { "mp_player_inteat@burger", "mp_player_int_eat_burger", "", AnimationOptions = { Prop = 'uwu_gdasik', PropBone = 18905, PropPlacement = { 0.16, 0.08, 0.02, -225.0, 20.0, 60.0 }, EmoteMoving = true, } },
    ["flute"] = { "anim@heists@humane_labs@finale@keycards", "ped_a_enter_loop", "Flute", AnimationOptions = { Prop = 'prop_champ_flute', PropBone = 18905, PropPlacement = { 0.10, -0.03, 0.03, -100.0, 0.0, -10.0 }, EmoteMoving = true, EmoteLoop = true } },

    -- UWU EMOTE END

    --BURGER SHOT EMOTE START
    ["torpedo"] = { "mp_player_inteat@burger", "mp_player_int_eat_burger_fp", "Torpedo", AnimationOptions = { Prop = "prop_food_bs_burger2", PropBone = 18905, PropPlacement = { 0.10, -0.07, 0.091, 15.0, 135.0 }, EmoteMoving = true, EmoteLoop = true, } },
    ["bsfries"] = { "mp_player_inteat@burger", "mp_player_int_eat_burger_fp", "Fries", AnimationOptions = { Prop = "prop_food_bs_chips", PropBone = 18905, PropPlacement = { 0.09, -0.06, 0.05, 300.0, 150.0 }, EmoteMoving = true, EmoteLoop = true, } },
    ["donut"] = { "mp_player_inteat@burger", "mp_player_int_eat_burger", "Donut", AnimationOptions = { Prop = 'prop_amb_donut', PropBone = 18905, PropPlacement = { 0.13, 0.05, 0.02, -50.0, 16.0, 60.0 }, EmoteMoving = true } },
    ["bscoke"] = { "mp_player_intdrink", "loop_bottle", "BS Coke", AnimationOptions = { Prop = "prop_food_bs_juice01", PropBone = 18905, PropPlacement = { 0.04, -0.10, 0.10, 240.0, -60.0 }, EmoteMoving = true, EmoteLoop = true, } },
    ["bscoffee"] = { "mp_player_intdrink", "loop_bottle", "BS Coffee", AnimationOptions = { Prop = "prop_food_bs_coffee", PropBone = 18905, PropPlacement = { 0.08, -0.10, 0.10, 240.0, -60.0 }, EmoteMoving = true, EmoteLoop = true, } },

    -- BURGER SHOT EMOTE END

    ["djs_blackberry_martini"] = {
        "amb@world_human_drinking@beer@male@idle_a",
        "idle_c",
        "Black Berry Martini",
        AnimationOptions = {
            Prop = 'djs_blackberry_martini',
            PropBone = 28422,
            PropPlacement = { 0.01, 0.01, 0.06, 5.0, 5.0, -180.5 },
            EmoteLoop = true,
            EmoteMoving = true
        }
    },
    ["djs_cherrypineapple_margarita"] = {
        "amb@world_human_drinking@beer@male@idle_a",
        "idle_c",
        "Cherry Pineapple Margarita",
        AnimationOptions = {
            Prop = 'djs_cherrypineapple_margarita',
            PropBone = 28422,
            PropPlacement = { 0.01, 0.01, 0.06, 5.0, 5.0, -180.5 },
            EmoteLoop = true,
            EmoteMoving = true
        }
    },
    ["djs_mochiplate"] = {
        "anim@djs@food@plated",
        "djs_food_plate_bite_loop",
        "Mochi Plate",
        AnimationOptions = {
            Prop = 'djs_mochiplate',
            PropBone = 18905,
            PropPlacement = { 0.140000, 0.030000, 0.020000, -13.146003, 0.000000, 4.382000 },
            SecondProp = 'djs_prop_mochigreen',
            SecondPropBone = 57005,
            SecondPropPlacement = { 0.140000, 0.050000, -0.040000, -76.907990, 0.000000, 0.000000 },
            EmoteLoop = true,
            EmoteMoving = true
        }
    },
    ["djs_sushiplate_multi"] = {
        "anim@djs@food@plated",
        "djs_food_plate_bite_loop",
        "Sushi Plate",
        AnimationOptions = {
            Prop = 'djs_sushiplate_multi',
            PropBone = 18905,
            PropPlacement = { 0.152000, 0.040000, 0.012000, 0.000000, 0.000000, 0.000000 },
            SecondProp = 'djs_chopstick',
            SecondPropBone = 57005,
            SecondPropPlacement = { 0.136000, 0.027000, 0.000000, 80.100433, 0.000000, 0.000000 },
            EmoteLoop = true,
            EmoteMoving = true
        }
    },
     ["djs_plate_tempurashirmp"] = {
        "anim@djs@food@plated",
        "djs_food_plate_bite_loop",
        "Tempura Shrimp",
        AnimationOptions = {
            Prop = 'djs_plate_tempurashirmp',
            PropBone = 18905,
            PropPlacement = { 0.152000, 0.040000, 0.012000, 0.000000, 0.000000, 0.000000 },
            SecondProp = 'djs_prop_tempurashirmp',
            SecondPropBone = 57005,
            SecondPropPlacement = { 0.100000, 0.040000, -0.032000, 0.000000, -48.313988, 0.000000 },
            EmoteLoop = true,
            EmoteMoving = true
        }
    },
      ["djs_sushi_multi"] = {
        "anim@djs@food@plated",
        "djs_food_plate_bite_loop",
        "Sushi Plate",
        AnimationOptions = {
            Prop = 'djs_sushi_multi',
            PropBone = 18905,
            PropPlacement = { 0.152000, 0.040000, 0.012000, 0.000000, 0.000000, 0.000000 },
            SecondProp = 'djs_chopstick',
            SecondPropBone = 57005,
            SecondPropPlacement = { 0.136000, 0.027000, 0.000000, 80.100433, 0.000000, 0.000000 },
            EmoteLoop = true,
            EmoteMoving = true
        }
    },
    ["djs_sushiboard_multi2"] = {
        "anim@djs@food@plated",
        "djs_food_plate_bite_loop",
        "Sushi Board",
        AnimationOptions = {
            Prop = 'djs_sushiboard_multi2',
            PropBone = 18905,
            PropPlacement = { 0.152000, 0.040000, 0.012000, 0.000000, 0.000000, 0.000000 },
            SecondProp = 'djs_chopstick',
            SecondPropBone = 57005,
            SecondPropPlacement = { 0.136000, 0.027000, 0.000000, 80.100433, 0.000000, 0.000000 },
            EmoteLoop = true,
            EmoteMoving = true
        }
    },


    
}