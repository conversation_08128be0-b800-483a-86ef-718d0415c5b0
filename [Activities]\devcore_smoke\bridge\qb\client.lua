if GetResourceState('qb-core') ~= 'started'  then return end
if GetResourceState('qbx_core') ~= 'started' and GetResourceState('qb-core') ~= 'started' then return end

Framework = 'QB'
QBCore = Config.Export()
STATE = {}

Citizen.CreateThread(function()
    if GetResourceState('ox_inventory') == 'started' then
        STATE.INV = 'ox'
        ox_inventory = exports.ox_inventory
    elseif GetResourceState('qs-inventory') == 'started' then
            STATE.INV = 'qs'
    
        elseif GetResourceState('qb-inventory') == 'started' then
            STATE.INV = 'qb'
    else
        STATE.INV = 'qb'
    end
    if GetResourceState('xsound') == 'started' then
        xSound = exports.xsound
        end
end)

function GetClosePlayer()
    return QBCore.Functions.GetClosestPlayer()
end 


function GetPlayerItems()
    return QBCore.Functions.GetPlayerData().items
end

function OpenChargeMenu(item)
local Options = {}
local allowed = false
    local idata = {}


        QBCore.Functions.TriggerCallback(GetCurrentResourceName()..':server:getInv', function(inventory)
        local PlyInv = inventory
        for k, v in pairs(PlyInv) do
            
            local items = Config.SmokingItems[item].AllowedItems[v.name]
    
            if items and not idata[v.name] then
             idata[v.name] = true
                allowed = true 
            table.insert(Options, {
                title = v.label,
                description = Translations['TEXT']['charge_desc'] ..item,
                event = GetCurrentResourceName()..':client:startFill',
                args = { charge_item = v.name, item = item}
            })
            
            lib.registerContext({
                id = 'charge_menu',
                title = Translations['TEXT']['charge_menu_title'],
                position = 'top-right',
                onExit = function()
                    STATE.USING = false
                  end,
                options = Options,
            })
                lib.showContext('charge_menu')
            else
                lib.hideMenu('charge_menu')
            end
        end
        if not allowed then
            allowed = false
            STATE.USING = false
            Config.Notify(Translations['TEXT']['dont_have_item'])
        end
    end)
end


function GetLighter(item)
    if STATE.INV == 'ox' then
        TriggerServerEvent(GetCurrentResourceName()..':server:getLighter', item)
    else
            local allowed = false
            local items = nil
            for k, v in pairs(Config.Lighters) do
                if STATE.INV == 'qb' then
                 items = QBCore.Functions.HasItem(k)
                elseif STATE.INV == 'qs' then
                     items = exports['qs-inventory']:Search(k)
                end
                if STATE.INV ~= 'qs' and items or STATE.INV == 'qs' and items ~= 0 then
                    if not allowed then
                        allowed = true
                        TriggerServerEvent(GetCurrentResourceName()..':server:getLighter', k)
                    end
                end
            end
            if not allowed then
            STATE.USING = false
                    Config.Notify(Translations['TEXT']['not_lighter'], 'error')
            end
    end
end