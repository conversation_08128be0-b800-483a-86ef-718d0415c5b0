--------------------------------------
-- <!>--    BODHIX | STUDIO     --<!>--
--------------------------------------
--------------------------------------
-- <!>--     SKATE | CAREER     --<!>--
--------------------------------------
-- Support & Feedback: https://discord.gg/PjN7AWqkpF
-- How to: 
-- Use E to Pickup the Skateboard or put in your back 
-- Use G to Ride the Skateboard or put it in you Hand
-- For Tricks, set the Keys in Settings / Key Binding / FiveM
-- You need a the Trigger Event for custom inventory? Use this one: TriggerClientEvent('bodhix-skating:client:start', source, item)
Config = {}

Config.Debug = false -- True / False for Debug System

Config.Framework = "esx" -- Write your Framework: "qb" or "esx" or "vrp" or "custom". 

-- Settings
Config.ItemName = 'hoverboard'
Config.Target = "ox" -- Write your Target System: "qb" or "ox" or "none".
Config.TextFont = 4
Config.FrameworkResourceName = nil
Config.MaxSpeedKmh = 40 -- This does not really change that much unless you get a boost somehow.
Config.maxJumpHeigh = 0.5 -- We suggest not to mess to much with this (And yes, you can jump very high).
Config.maxFallSurvival = 745.0
Config.MinimumHoverSpeed = 1.0
Config.LoseConnectionDistance = 2.0 -- This is the distance from you to the skateboard (Don't mess with this, unless you know, what you are doing).
Config.MinimumSkateSpeed = 2.0
Config.MinGroundHeight = 1.0
Config.ShowScore = false
Config.EnablePeds = true
Config.PickupKey = 38
Config.ConnectPlayer = 113
Config.DesignCount = 16
Config.hoverboardPrice = 20000
Config.maxBoostTime = 20000

Config.UltraBack = -0.213 -- Adjust if the Modern Skateboard doesn't fit when you put it in your back.
Config.RetroBack = -0.213 -- Adjust if the Classic Skateboard doesn't fit when you put it in your back (DLC Only).

Config.Language = {
    Info = {
        ['controls'] = 'Press E to Pickup | Press G to Ride',
        ['warning'] = 'The Workshop is currently in use by another player.',
        ['purchase'] = 'You have successfully purchased this item!',
        ['failed'] = 'You dont have enough money.',
        ['error'] = 'You already own this Item.'
    },
    Store = {
        ['target'] = 'Open Hover Shop.',
        ['text'] = '[E] Open Hover Shop.'
    },
    Menu = {
        ["equipment"] = "Equipment",
        ["gear"] = "GEAR",
        ["whats_new"] = "WHAT'S NEW",
        ["hoverboard"] = "Hoverboard",
        ["purchase"] = "Purchase",
        ["ultra"] = "Ultra",
        ["retro"] = "Retro"
    }
}
Config.Modern_TXD = {
	[1]  = { gif = 'https://giphy.com/embed/c0PX1PqTeqhbtKMekM', square = false },
	[2]  = { gif = 'https://giphy.com/embed/l3c614V12UA82q1vG',  square = true  },
	[3]  = { gif = 'https://giphy.com/embed/129rZZ3a4anp0Q',     square = false },
	[4]  = { gif = 'https://giphy.com/embed/nEVpcuF6XyBVVZc6I7', square = true  },
	[5]  = { gif = 'https://giphy.com/embed/fDX4d4jYvepqEJUqKC', square = true  },
	[6]  = { gif = 'https://giphy.com/embed/pXwqzefGaF9OoJDpNe', square = true  },
	[7]  = { gif = 'https://giphy.com/embed/RGRR5C5Pa5v0I',      square = false },
	[8]  = { gif = 'https://giphy.com/embed/5n5SSGcNnFIMwwMcKG', square = true  },
	[9]  = { gif = 'https://giphy.com/embed/Ed46JnKn4dwY0',      square = true  },
	[10] = { gif = 'https://giphy.com/embed/13ZEwDgIZtK1y',      square = true  },
	[11] = { gif = 'https://giphy.com/embed/l41m4rC9j8kMjXB0k',  square = true  },
	[12] = { gif = 'https://giphy.com/embed/0mSbmyxpgBhNfliH1p', square = true  },
	[13] = { gif = 'https://giphy.com/embed/O0Hp9ACdYWqAg',      square = true  },
	[14] = { gif = 'https://giphy.com/embed/d0XuEToIwURJ6',      square = true  },
	[15] = { gif = 'https://giphy.com/embed/9DMHJ6bgj84LGdAeAN', square = true  },
	[16] = { gif = 'https://giphy.com/embed/3o6fJ9uxj9z5XOfxD2', square = false },
}
Config.Coords = {
    Peds = {
        VeniceBeach = {
            x = -1403.8314,   
            y = -1417.6881,
            z = 6.6386,
            heading = 2.9339
        },
    },
    Hover = {
        VeniceBeach = {
            x = -1403.1, 
            y = -1413.65,
            z = 6.0,
            heading = 29.0 ,
            angle = 205.0
        },
    },
    Camera = {
        VeniceBeach = {
            x = -1404.0228,   
            y = -1414.1089,
            z = 7.1,
            heading = 296.6042,
            angle = -14.0
        },
    }
}
Config.Shops = {
    ShopPeds = {{
        Position = vector4(Config.Coords.Peds.VeniceBeach.x, Config.Coords.Peds.VeniceBeach.y,
            Config.Coords.Peds.VeniceBeach.z, Config.Coords.Peds.VeniceBeach.heading),
        Model = 'a_m_m_skater_01',
        Scenarios = 'WORLD_HUMAN_AA_COFFEE',
        StoreName = "VeniceBeach" -- Unique store ID
    },}
}
