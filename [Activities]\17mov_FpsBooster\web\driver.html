<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.4.1/jquery.min.js"></script>
    <style>
        body {
            display: none
        }
    </style>
</head>
<body>
</body>
<script>
    window.addEventListener('message', function (event) {
        if (event.data.ui == "new") {
            $("body").load('newui/index.html')
            setTimeout(function() {
                $("body").css("display", "block")
            }, 1000)
        } else if (event.data.ui == "old") {
            $("body").load('oldui/index.html')
            setTimeout(function() {
                $("body").css("display", "block")
            }, 1000)
        }
    })
</script>
</html>