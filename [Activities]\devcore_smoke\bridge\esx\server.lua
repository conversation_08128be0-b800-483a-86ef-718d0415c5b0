if GetResourceState('es_extended') ~= 'started' then return end
Framework = 'ESX'
ESX = Config.Export()
STATE = {}


RegisterUsableItem = nil

Citizen.CreateThread(function()
    if GetResourceState('ox_inventory') == 'started' then
        STATE.INV = 'ox'
        ox_inventory = exports.ox_inventory
    elseif GetResourceState('qs-inventory') == 'started' then
            STATE.INV = 'qs'
    else
        STATE.INV = 'other'
    end

end)



function GetPlayer(source)
    return ESX.GetPlayerFromId(source)
end

function GetPlayerSource(source)
    return ESX.GetPlayerFromId(source).source
end

function AddItem(source, item, count, slot, metadata)
    local player = GetPlayer(source)
    if STATE.INV == 'ox' then
    return ox_inventory:AddItem(source, item, count, metadata, slot)
    elseif STATE.INV == 'qs' then
        return exports['qs-inventory']:AddItem(source, item, count, slot, metadata)
    else
        return player.addInventoryItem(item, count, metadata, slot)
    end
end

function RemoveItem(source, item, count, slot, metadata)
    local player = GetPlayer(source)
    if STATE.INV == 'ox' then
        return ox_inventory:RemoveItem(source, item, count, metadata, slot)
    elseif STATE.INV == 'qs' then
        return exports['qs-inventory']:RemoveItem(source, item, count, slot)
    else
         player.removeInventoryItem(item, count, metadata, slot)
    end
end


CreateThread(function()
    Wait(100)
    RegisterUsableItem = function(name, cb)
        if STATE.INV == 'ox' then
            ESX.RegisterUsableItem(name, function(source, item, data)
                cb(source, data.metadata, data.slot)
            end)
        elseif STATE.INV == 'qs' then
            exports['qs-inventory']:CreateUsableItem(name, function(source, item)
                cb(source, item.info, item.slot)
            end)
        else
            ESX.RegisterUsableItem(name, function(source)
                cb(source)
            end)
        end
    end
end)


