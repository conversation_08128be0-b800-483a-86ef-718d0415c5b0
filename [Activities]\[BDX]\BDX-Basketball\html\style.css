@font-face {
    font-family: 'Canada_Real';
    src: url('fonts/Canada_Real.ttf') format('truetype');
    font-weight: normal;
    font-style: normal;
}
@font-face {
    font-family: 'Kiwi-Fruit';
    src: url('fonts/Kiwi-Fruit.otf') format('truetype');
    font-weight: normal;
    font-style: normal;
}

body {
    margin: 0;
    background: transparent;
    font-family: Arial, sans-serif;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
}


.menu-container {
    width: 460px;
    background-color: #555;
    color: white;
    border-radius: 30px;
    padding: 25px;
    display: none;
    position: relative;
    z-index: 10;
}

.menu-container::after {
    content: "";
    position: absolute;
    top: 10px;
    left: 10px;
    right: 10px;
    bottom: 10px;
    border: 2px solid white;
    border-radius: 26px;
    pointer-events: none;
    z-index: -1;
    box-shadow:
        0 0 15px rgba(255, 128, 0, 0.4),
        0 0 30px rgba(255, 102, 0, 0.6),
        0 0 50px rgba(255, 64, 0, 0.8);
    animation: glowLoop 2s infinite ease-in-out;
}

@keyframes glowLoop {
    0% {
        box-shadow:
            0 0 15px rgba(255, 102, 0, 0.4),
            0 0 30px rgba(255, 128, 0, 0.5),
            0 0 50px rgba(255, 64, 0, 0.6);
    }
    50% {
        box-shadow:
            0 0 25px rgba(255, 160, 0, 0.6),
            0 0 50px rgba(255, 128, 0, 0.8),
            0 0 70px rgba(255, 90, 0, 1);
    }
    100% {
        box-shadow:
            0 0 15px rgba(255, 102, 0, 0.4),
            0 0 30px rgba(255, 128, 0, 0.5),
            0 0 50px rgba(255, 64, 0, 0.6);
    }
}

.section-title {
    font-size: 40px;
    font-family: 'Canada_Real', sans-serif;
    font-weight: bold;
    text-align: center;
    color: white;
    margin: 10px 0 20px;
}

.logo {
    font-size: 28px;
    text-align: center;
    font-weight: bold;
    margin-top: 20px;
}

.subtitle {
    font-size: 14px;
    color: gold;
    font-weight: normal;
}

.centered {
    text-align: center;
    justify-content: center;
    display: flex;
}

.close-button {
    position: absolute;
    top: 18px;
    left: 18px;
    font-size: 22px;
    cursor: pointer;
    z-index: 2;
}

.back-button {
    position: absolute;
    top: 18px;
    left: 18px;
    font-size: 22px;
    cursor: pointer;
    z-index: 2;
    display: none;
}

.mode-button {
    font-family: 'Kiwi-Fruit', sans-serif;
    background: #ff4500;
    color: white;
    border: none;
    border-radius: 20px;
    padding: 10px;
    margin: 10px auto;
    width: 120px;
    display: block;
    font-size: 34px;
    cursor: pointer;
}

.input-row {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 15px 0;
}

.input-label {
    font-size: 28px;
    font-family: 'Kiwi-Fruit', sans-serif;
    background-color: #ff4500;
    padding: 5px 10px;
    border-radius: 15px;
    margin-right: 10px;
}

input[type="number"],
input[type="text"] {
    padding: 5px;
    border: none;
    border-radius: 5px;
    width: 100px;
}

.pvp-settings-row {
    display: flex;
    justify-content: center;
    align-items: flex-end;
    gap: 40px;
    margin-bottom: 20px;
}

.pvp-setting {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.pvp-setting label {
    font-size: 24px;
    font-family: 'Kiwi-Fruit', sans-serif;
    color: white;
    margin-bottom: 5px;
}

.pvp-setting input {
    width: 70px;
    padding: 6px;
    text-align: center;
    border-radius: 6px;
    border: none;
}

.start-button {
    font-family: 'Kiwi-Fruit', sans-serif;
    background-image: url('basketball-icon.png'); /* Optional image icon if needed */
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    padding: 30px;
    width: 80px;
    height: 80px;
    margin: 0 auto;
    border: none;
    border-radius: 50%;
    color: white;
    font-weight: bold;
    text-align: center;
    font-size: 20px;
    cursor: pointer;
    background-color: orange;
}

.pvp-menu {
    display: none;
    text-align: center;
}

.team-box {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 10px;
    gap: 10px;
}

.team-box input {
    width: 120px;
    padding: 6px;
    border-radius: 5px;
    border: none;
}

.register-btn {
    padding: 6px 12px;
    font-weight: bold;
    border-radius: 8px;
    border: 2px solid black;
    cursor: pointer;
    font-size: 14px;
}

.register-btn.blue {
    background-color: blue;
    color: white;
}

.register-btn.red {
    background-color: red;
    color: white;
}

.register-btn.disabled {
    background-color: gray !important;
    cursor: not-allowed;
    color: white !important;
}

.disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.logo-image {
    max-width: 100%;
    height: auto;
    display: block;
    margin: 0 auto 10px auto;
}

.team-box button {
    width: 120px;
    padding: 5px;
    border-radius: 10px;
    font-weight: bold;
    color: white;
    border: none;
    cursor: pointer;
}

.team-box.blue button {
    font-size: 24px;
    font-family: 'Kiwi-Fruit', sans-serif;
    background-color: blue;
}

.team-box.red button {
    font-size: 24px;
    font-family: 'Kiwi-Fruit', sans-serif;
    background-color: red;
}

/* Visibility control */
.solo-menu, .pvp-menu {
    display: none;
}

#basketballHud {
    display: none; /* ✅ hidden by default */
}


.hud-container {
    position: absolute;
    top: 30px;
    left: 50%;
    transform: translateX(-50%);
    display: none;
    background-color: rgba(0, 0, 0, 0.7);
    border-radius: 20px;
    padding: 15px 30px;
    color: white;
    font-family: Arial, sans-serif;
    font-size: 18px;
    display: flex;
    justify-content: space-between;
    min-width: 500px;
    z-index: 9999;
    box-shadow: 0 0 15px rgba(255, 128, 0, 0.4);
    gap: 60px;
}

.hud-left, .hud-right {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.hud-team.blue {
    color: #3399ff;
}

.hud-team.red {
    color: #ff4d4d;
}

.lobby-container {
    background-color: #111;
    border: 2px solid #ffa500;
    box-shadow: 0 0 20px #ffa500;
    padding: 15px 30px;
    border-radius: 10px;
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 300px;
    position: absolute;
    top: 5%; /* Move near the top of the screen */
    bottom: unset; /* Remove any bottom anchor */
    left: 50%;
    transform: translateX(-50%);
    font-family: 'Arial', sans-serif;
    z-index: 1000;
}

.lobby-title {
    font-size: 20px;
    font-weight: bold;
    color: white;
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.lobby-teams-row {
    display: flex;
    justify-content: space-between;
    width: 100%;
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 12px;
}

.lobby-team {
    display: flex;
    gap: 5px;
    align-items: center;
}

.lobby-blue {
    color: #3399ff;
}

.lobby-red {
    color: #ff4d4d;
}

.lobby-count {
    color: white;
}

.lobby-button-wrapper {
    text-align: center;
    width: 100%;
}

.lobby-start {
    background-color: #444;
    color: white;
    padding: 8px 18px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    cursor: not-allowed;
    opacity: 0.6;
}

.lobby-start.enabled {
    background-color: orange;
    cursor: pointer;
    opacity: 1;
}

#resultOverlay {
    position: absolute;
    top: 35%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 999;
    display: flex;
    justify-content: center;
    align-items: center;
    pointer-events: none;
}

#resultMessage {
    font-size: 64px;
    font-family: 'Orbitron', sans-serif;
    animation: pulse 1.5s infinite, glow 1.5s ease-in-out infinite alternate;
    text-shadow: 0 0 20px rgba(0,0,0,0.8);
    transition: color 0.5s;
}

.hidden {
    display: none !important;
    opacity: 0;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

@keyframes glow {
    0% { text-shadow: 0 0 5px #fff, 0 0 10px #fff; }
    100% { text-shadow: 0 0 20px #fff, 0 0 30px #ff00ff; }
}

.store-box {
    position: absolute;
    top: 10%;
    left: 3%;
    width: 300px;
    background-color: rgba(10, 10, 10, 0.85);
    padding: 20px;
    border-radius: 12px;
    box-shadow: 0 0 10px #000;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.store-logo {
    width: 120px;
    margin-bottom: 15px;
}

.store-buttons button,
.purchase-button {
    margin: 5px;
    padding: 10px 15px;
    width: 90%;
    font-size: 16px;
    border: none;
    border-radius: 6px;
    background-color: #444;
    color: white;
    transition: background-color 0.3s;
}

.store-buttons button:hover,
.purchase-button:hover {
    background-color: #666;
}

.ball-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
    margin-top: 10px;
}

.ball-grid img {
    width: 100%;
    border-radius: 8px;
    cursor: pointer;
    transition: transform 0.2s;
}

.ball-grid img:hover {
    transform: scale(1.05);
}

.hidden {
    display: none;
}
.logo-wrapper {
    text-align: center;
    margin-bottom: 15px;
}

.store-logo {
    width: 220px;
    height: auto;
}

.store-buttons {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

.ball-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-auto-rows: 110px;
    gap: 10px;
    overflow-y: auto;
    max-height: 360px;
    padding: 10px;
    margin-bottom: 15px;
}

.ball-grid img {
    width: 100px;
    height: 100px;
    object-fit: cover;
    cursor: pointer;
    border: 2px solid transparent;
    border-radius: 8px;
    transition: 0.2s ease;
}

.ball-grid img:hover {
    border-color: #FFD700;
}

button.disabled {
    background-color: gray;
    cursor: not-allowed;
    opacity: 0.6;
}
.hud-score {
    color: white;
    font-weight: bold;
}
.solo-hud {
    position: absolute;
    top: 10%;
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
    z-index: 999;
}

.hud-score.solo {
    font-size: 32px;
    color: white;
}

.hud-timer.solo {
    font-size: 32px;
    color: white;
}

/* Make Score text match size of score number */
.hud-team {
    font-size: 24px;
    font-weight: bold;
    color: white;
    margin: 0;
}


/* Hide round section when in solo mode */
#hudRoundContainer.solo {
    display: none;
}

/* Remove red dots (was probably ::before or decoration) */
.hud-left::before,
.hud-left::after {
    content: none !important;
}
/* Make solo score large */
.hud-score-label {
    font-size: 24px;
    font-weight: bold;
    color: white;
    margin-right: 6px;
}

.hud-score {
    font-size: 24px;
    font-weight: bold;
    color: white;
}

/* Make timer bigger */
.hud-timer {
    font-size: 24px;
    font-weight: bold;
    color: white;
}

.hud-round {
    font-size: 24px; /* make it same size as .hud-timer */
    font-weight: bold;
    color: white;
}